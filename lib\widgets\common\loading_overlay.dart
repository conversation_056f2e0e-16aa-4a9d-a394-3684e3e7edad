import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';

/// A full-screen loading overlay with customizable text and color.
///
/// This widget is designed to be used as a child of a Stack to show a loading
/// indicator over the entire screen or a specific area.
class LoadingOverlay extends StatelessWidget {
  /// The text to display below the loading indicator.
  final String text;

  /// The color of the loading indicator and text.
  final Color? color;

  /// The size of the loading indicator.
  final double indicatorSize;

  /// The stroke width of the loading indicator.
  final double strokeWidth;

  /// The font size of the text.
  final double fontSize;

  /// The opacity of the background overlay (0.0 to 1.0).
  final double backgroundOpacity;

  /// Creates a loading overlay with customizable properties.
  const LoadingOverlay({
    super.key,
    required this.text,
    this.color,
    this.indicatorSize = 30.0,
    this.strokeWidth = 5.0,
    this.fontSize = 16.0,
    this.backgroundOpacity = 0.7,
  });

  @override
  Widget build(BuildContext context) {
    final themeColor = color ?? Theme.of(context).colorScheme.primary;

    return Positioned.fill(
      child: Container(
        color: Colors.black.withOpacity(backgroundOpacity),
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.xl),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(70),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: indicatorSize,
                  width: indicatorSize,
                  child: CircularProgressIndicator(
                    strokeWidth: strokeWidth,
                    valueColor: AlwaysStoppedAnimation<Color>(themeColor),
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Text(
                  text,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: fontSize,
                    color: themeColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
