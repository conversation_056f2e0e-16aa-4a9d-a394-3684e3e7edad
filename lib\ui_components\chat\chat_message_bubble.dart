import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../../models/message.dart';

/// A customizable chat message bubble component
class ChatMessageBubble extends StatelessWidget {
  final Message message;
  final bool showAvatar;
  final bool showTimestamp;
  final VoidCallback? onLongPress;
  final EdgeInsets? margin;

  const ChatMessageBubble({
    super.key,
    required this.message,
    this.showAvatar = true,
    this.showTimestamp = true,
    this.onLongPress,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.role == MessageRole.user;
    final bubbleColor = isUser ? AppTheme.primaryColor : AppTheme.surfaceColor;
    final textColor =
        isUser ? AppTheme.textLightColor : AppTheme.textPrimaryColor;
    final alignment =
        isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start;
    final bubbleMargin = margin ??
        EdgeInsets.only(
          top: AppTheme.spacingS,
          bottom: AppTheme.spacingS,
          left: isUser ? AppTheme.spacingXl : AppTheme.spacingS,
          right: isUser ? AppTheme.spacingS : AppTheme.spacingXl,
        );

    return Padding(
      padding: bubbleMargin,
      child: Column(
        crossAxisAlignment: alignment,
        children: [
          if (showAvatar && !isUser) _buildAvatar(),
          GestureDetector(
            onLongPress: onLongPress,
            child: Container(
              decoration: BoxDecoration(
                color: bubbleColor,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withAlpha(13),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingM,
                vertical: AppTheme.spacingM,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SelectableText(
                    message.content,
                    style: AppTheme.bodyMedium.copyWith(
                      color: textColor,
                    ),
                  ),
                  if (showTimestamp) ...[
                    const SizedBox(height: AppTheme.spacingXs),
                    Text(
                      _formatTimestamp(message.timestamp),
                      style: AppTheme.bodySmall.copyWith(
                        color: isUser
                            ? AppTheme.textLightColor.withAlpha(179)
                            : AppTheme.textSecondaryColor,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    final avatarColor = message.role == MessageRole.assistant
        ? AppTheme.primaryColor
        : AppTheme.secondaryColor;
    final avatarIcon = message.role == MessageRole.assistant
        ? Icons.smart_toy_outlined
        : Icons.person_outline;

    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingXs),
      child: CircleAvatar(
        backgroundColor: avatarColor,
        radius: 16,
        child: Icon(
          avatarIcon,
          color: AppTheme.textLightColor,
          size: 16,
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(
      timestamp.year,
      timestamp.month,
      timestamp.day,
    );

    if (messageDate == today) {
      // Today, show time only
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      // Yesterday
      return 'Yesterday, ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      // Other days
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}, ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }
}

/// A typing indicator component for chat
class ChatTypingIndicator extends StatefulWidget {
  final Color? backgroundColor;
  final Color? dotColor;
  final EdgeInsets? margin;

  const ChatTypingIndicator({
    super.key,
    this.backgroundColor,
    this.dotColor,
    this.margin,
  });

  @override
  State<ChatTypingIndicator> createState() => _ChatTypingIndicatorState();
}

class _ChatTypingIndicatorState extends State<ChatTypingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.backgroundColor ?? AppTheme.surfaceColor;
    final dotColor = widget.dotColor ?? AppTheme.primaryColor;
    final bubbleMargin = widget.margin ??
        const EdgeInsets.only(
          top: AppTheme.spacingS,
          bottom: AppTheme.spacingS,
          left: AppTheme.spacingS,
          right: AppTheme.spacingXl,
        );

    return Padding(
      padding: bubbleMargin,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          _buildAvatar(),
          const SizedBox(width: AppTheme.spacingXs),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingM,
              vertical: AppTheme.spacingS,
            ),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withAlpha(13),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Row(
                  children: List.generate(3, (index) {
                    final delay = index * 0.2;
                    final animation = Tween<double>(begin: 0, end: 1).animate(
                      CurvedAnimation(
                        parent: _animationController,
                        curve: Interval(
                          delay,
                          delay + 0.5,
                          curve: Curves.easeInOut,
                        ),
                      ),
                    );

                    return Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingXs / 2,
                      ),
                      child: Transform.scale(
                        scale: 0.7 + (0.3 * animation.value),
                        child: Opacity(
                          opacity: 0.5 + (0.5 * animation.value),
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: dotColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      backgroundColor: AppTheme.primaryColor,
      radius: 16,
      child: const Icon(
        Icons.smart_toy_outlined,
        color: AppTheme.textLightColor,
        size: 16,
      ),
    );
  }
}
