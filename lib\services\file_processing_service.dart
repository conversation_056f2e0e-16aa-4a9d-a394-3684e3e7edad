import 'package:flutter/material.dart';
import 'package:nsl/models/multimedia/ocr_response_model.dart';
import 'package:nsl/services/file_upload_service.dart';
import 'package:nsl/services/ocr_service.dart';
import 'package:nsl/utils/logger.dart';

/// A service that handles file processing, including upload and OCR
class FileProcessingService {
  final FileUploadService _fileUploadService = FileUploadService();
  final OcrService _ocrService = OcrService();

  /// Process a file by picking, uploading, and performing OCR in one operation
  ///
  /// Returns a Future that completes with a Map containing:
  /// - success: Whether the operation was successful
  /// - fileName: The name of the uploaded file
  /// - filePath: The path of the uploaded file
  /// - extractedText: The text extracted from the image (if OCR was successful)
  /// - errorMessage: An error message (if an error occurred)
  Future<Map<String, dynamic>> processFile() async {
    try {
      // Pick a file
      final file = await _fileUploadService.pickFile();

      if (file == null) {
        // User canceled the picker
        return {
          'success': false,
          'errorMessage': 'File selection canceled',
          'isCanceled': true,
        };
      }

      // Upload the file
      final uploadResult = await _fileUploadService.uploadFile(file);

      if (!uploadResult['success']) {
        // Handle upload error
        return {
          'success': false,
          'errorMessage': 'Failed to upload file: ${uploadResult['message']}',
        };
      }

      // Get the file name and path
      final fileName = file.name;

      // Get the file path from the result
      // Use the FileUploadResponse model if available
      final filePath = uploadResult['file_upload_response'] != null
          ? uploadResult['file_upload_response'].filePath
          : uploadResult['data']['file_path'];

      // Process OCR on the uploaded file
      try {
        final ocrResult = await _ocrService.processOcrFromPath(filePath);

        if (ocrResult['success']) {
          // Get the OCR response model
          final ocrResponseModel =
              ocrResult['ocr_response_model'] as OcrResponseModel;

          // Get the extracted text
          final extractedText = ocrResult['text'];

          // Log the OCR response model
          Logger.info('OCR Response Model: $ocrResponseModel');

          // Return success with all the information
          return {
            'success': true,
            'fileName': fileName,
            'filePath': filePath,
            'extractedText': extractedText,
            'ocrResponseModel': ocrResponseModel,
            'outputFilePath': ocrResponseModel.output.filePath,
          };
        } else {
          // Get the error message
          final errorMessage = ocrResult['message'];

          // Get the OCR response model if available
          final ocrResponseModel =
              ocrResult['ocr_response_model'] as OcrResponseModel;

          // Return error
          return {
            'success': false,
            'fileName': fileName,
            'filePath': filePath,
            'errorMessage': 'Failed to extract text from image: $errorMessage',
            'ocrResponseModel': ocrResponseModel,
          };
        }
      } catch (e) {
        // Handle OCR error
        Logger.error('Error processing OCR: $e');
        return {
          'success': false,
          'fileName': fileName,
          'filePath': filePath,
          'errorMessage': 'Error processing image: $e',
        };
      }
    } catch (e) {
      // Handle general error
      Logger.error('Error in file processing service: $e');
      return {
        'success': false,
        'errorMessage': 'Error processing file: $e',
      };
    }
  }

  /// Show a success overlay notification
  void showSuccessOverlay(BuildContext context, String message) {
    // Create an overlay entry
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 50,
        right: 50,
        child: Material(
          elevation: 4.0,
          borderRadius: BorderRadius.circular(8),
          color: Colors.black87,
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  message,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the overlay
    Overlay.of(context).insert(overlayEntry);

    // Remove the overlay after 2 seconds
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }
}
