import 'package:flutter/material.dart';

class ObjectsPage extends StatefulWidget {
  const ObjectsPage({super.key});

  @override
  State<ObjectsPage> createState() => _ObjectsPageState();
}

class _ObjectsPageState extends State<ObjectsPage> {
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Side Navigation
          SideNavigation(selectedIndex: 1),
          
          // Main Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top Bar with stats and back button
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    border: Border(bottom: BorderSide(color: Colors.grey.shade300))
                  ),
                  child: Row(
                    children: [
                      const Spacer(),
                      _buildStatItem(Icons.book, '12 Books'),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.article_outlined, '35 Solution'),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.category, '102 Object'),
                      const SizedBox(width: 32),
                      IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.blue),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                
                // Page Header with search and create
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Text(
                        'My Objects',
                        style: TextStyle(
                          fontSize: 24, 
                          fontWeight: FontWeight.bold
                        ),
                      ),
                      const Spacer(),
                      // Search box
                      Container(
                        width: 300,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search',
                            border: InputBorder.none,
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.tune),
                              onPressed: () {},
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Create Object Button
                      ElevatedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.add),
                        label: const Text('Create Object'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.black,
                          elevation: 1,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Grid of Objects - matching 12-column grid from design
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // Calculate columns based on available width
                        // Default to 5 columns for larger screens matching first page grid
                        int crossAxisCount = 5;
                        
                        // Desktop layout with 5 columns
                        if (constraints.maxWidth >= 1400) {
                          crossAxisCount = 5;
                        } 
                        // Medium width with 4 columns
                        else if (constraints.maxWidth >= 1100) {
                          crossAxisCount = 4;
                        }
                        // Smaller screens with 3 columns 
                        else if (constraints.maxWidth >= 800) {
                          crossAxisCount = 3;
                        }
                        // Mobile layout with 2 columns
                        else if (constraints.maxWidth >= 600) {
                          crossAxisCount = 2;
                        }
                        // Very small screens with 1 column
                        else {
                          crossAxisCount = 1;
                        }
                        
                        return GridView.builder(
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: crossAxisCount,
                            childAspectRatio: 1.1,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: objectsList.length,
                          itemBuilder: (context, index) {
                            final objectItem = objectsList[index];
                            return ObjectCard(objectItem: objectItem);
                          },
                        );
                      }
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStatItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey.shade700),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(color: Colors.grey.shade700, fontSize: 14),
        ),
      ],
    );
  }
}

class ObjectCard extends StatelessWidget {
  final ObjectItem objectItem;
  
  const ObjectCard({super.key, required this.objectItem});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Object Rectangle
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Center(
                  child: Icon(
                    _getIconForObject(objectItem.title),
                    size: 40,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ),
            
            // Object Info
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    objectItem.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    objectItem.version,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  IconData _getIconForObject(String title) {
    switch (title.toLowerCase()) {
      case 'customer':
        return Icons.person;
      case 'product':
        return Icons.shopping_bag;
      case 'address':
        return Icons.location_on;
      case 'employee':
        return Icons.badge;
      case 'new launch':
        return Icons.rocket_launch;
      default:
        return Icons.category;
    }
  }
}

class SideNavigation extends StatelessWidget {
  final int selectedIndex;
  
  const SideNavigation({super.key, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 16),
          // Logo
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                'nb',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Chat Icon
          _buildNavItem(context, Icons.chat_bubble_outline, 0),
          
          // App Icon (Selected)
          _buildNavItem(context, Icons.apps, 1, isSelected: selectedIndex == 1),
          
          // Briefcase Icon
          _buildNavItem(context, Icons.work_outline, 2),
          
          // Document Icon
          _buildNavItem(context, Icons.description_outlined, 3),
          
          // Calendar Icon
          _buildNavItem(context, Icons.calendar_today, 4),
          
          // Notification Icon
          _buildNavItem(context, Icons.notifications_none, 5),
          
          const Spacer(),
          
          // Profile Icon
          _buildNavItem(context, Icons.person_outline, 6),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
  
  Widget _buildNavItem(BuildContext context, IconData icon, int index, {bool isSelected = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade100 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: () {},
        icon: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey.shade600,
        ),
      ),
    );
  }
}

// Model class for Object
class ObjectItem {
  final String id;
  final String title;
  final String version;
  
  ObjectItem({
    required this.id,
    required this.title,
    required this.version,
  });
}

// Sample data for objects
final List<ObjectItem> objectsList = [
  ObjectItem(id: '1', title: 'Customer', version: 'V00172'),
  ObjectItem(id: '2', title: 'Product', version: 'V00172'),
  ObjectItem(id: '3', title: 'Address', version: 'V00172'),
  ObjectItem(id: '4', title: 'Employee', version: 'V00172'),
  ObjectItem(id: '5', title: 'New Launch', version: 'V00172'),
  ObjectItem(id: '6', title: 'Customer', version: 'V00172'),
  ObjectItem(id: '7', title: 'Product', version: 'V00172'),
  ObjectItem(id: '8', title: 'Address', version: 'V00172'),
  ObjectItem(id: '9', title: 'Employee', version: 'V00172'),
  ObjectItem(id: '10', title: 'New Launch', version: 'V00172'),
];
