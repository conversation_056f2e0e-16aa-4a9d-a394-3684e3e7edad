import 'package:flutter/material.dart';

enum ResponseSection {
  prescriptiveText,
  javaCode,
  yamlOutput,
  deployRuntime,
  showWorkflows,
}

extension ResponseSectionExtension on ResponseSection {
  String get title {
    switch (this) {
      case ResponseSection.prescriptiveText:
        return 'Prescriptive';
      case ResponseSection.javaCode:
        return 'Java';
      case ResponseSection.yamlOutput:
        return 'YAML';
      case ResponseSection.deployRuntime:
        return 'Deploy to Runtime';
      case ResponseSection.showWorkflows:
        return 'Node View';
    }
  }

  IconData get icon {
    switch (this) {
      case ResponseSection.prescriptiveText:
        return Icons.description_outlined;
      case ResponseSection.javaCode:
        return Icons.code;
      case ResponseSection.yamlOutput:
        return Icons.data_object_outlined;
      case ResponseSection.deployRuntime:
        return Icons.rocket_launch;
      case ResponseSection.showWorkflows:
        return Icons.account_tree_outlined;
    }
  }
}
