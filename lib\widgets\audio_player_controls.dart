import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';

/// A reusable widget for audio player controls that displays play/pause, progress bar, and stop buttons.
class AudioPlayerControls extends StatefulWidget {
  /// The audio player instance.
  final AudioPlayer audioPlayer;

  /// The current position in the audio.
  final Duration currentPosition;

  /// The total duration of the audio.
  final Duration totalDuration;

  /// Whether the audio is currently paused.
  final bool isPaused;

  /// Callback when the play/pause button is tapped.
  final VoidCallback onPlayPause;

  /// Callback when the stop button is tapped.
  final VoidCallback onStop;

  /// Callback when the seek slider is changed.
  final Function(Duration) onSeek;

  const AudioPlayerControls({
    super.key,
    required this.audioPlayer,
    required this.currentPosition,
    required this.totalDuration,
    required this.isPaused,
    required this.onPlayPause,
    required this.onStop,
    required this.onSeek,
  });

  @override
  State<AudioPlayerControls> createState() => _AudioPlayerControlsState();
}

class _AudioPlayerControlsState extends State<AudioPlayerControls> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Play/Pause button
          InkWell(
            onTap: () {
              // Log the action for debugging
              Logger.info(
                  'Play/Pause button tapped. isPaused: ${widget.isPaused}');
              widget.onPlayPause();
            },
            child: Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                shape: BoxShape.circle,
              ),
              child: Icon(
                widget.isPaused ? Icons.play_arrow : Icons.pause,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
          SizedBox(width: 8),

          // Progress bar with time
          Container(
            padding: EdgeInsets.symmetric(vertical: AppSpacing.xxs),
            width: 120,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Slider for seeking
                SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 3,
                    thumbShape: RoundSliderThumbShape(enabledThumbRadius: 5),
                    overlayShape: RoundSliderOverlayShape(overlayRadius: 12),
                    activeTrackColor: Theme.of(context).colorScheme.primary,
                    inactiveTrackColor: Colors.grey.shade300,
                    thumbColor: Theme.of(context).colorScheme.primary,
                    overlayColor:
                        Theme.of(context).colorScheme.primary.withAlpha(50),
                  ),
                  child: Slider(
                    min: 0,
                    max: widget.totalDuration.inMilliseconds.toDouble(),
                    value: widget.currentPosition.inMilliseconds.toDouble(),
                    onChanged: (value) {
                      final position = Duration(milliseconds: value.toInt());
                      widget.onSeek(position);
                    },
                  ),
                ),
                // Time indicator
                Text(
                  '${_formatDuration(widget.currentPosition)} / ${_formatDuration(widget.totalDuration)}',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 8),

          // Stop button
          InkWell(
            onTap: widget.onStop,
            child: Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.stop,
                color: Colors.black87,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Format duration for display
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
