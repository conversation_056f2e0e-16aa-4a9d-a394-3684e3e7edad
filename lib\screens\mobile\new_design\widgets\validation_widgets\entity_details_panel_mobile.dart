import 'dart:async';
import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:nsl/models/entities_data.dart' as entities_model;
import 'package:nsl/models/conversation_response.dart';

/// Constants for consistent styling across the entity details panel
class _EntityDetailsPanelMobileConstants {
  // Private constructor to prevent instantiation
  const _EntityDetailsPanelMobileConstants._();

  // Layout constants
  static const double borderRadius = 20.0;
  static const double sectionVerticalPadding = 16.0;

  // Avatar constants
  static const double avatarRadius = 12.0;
  static const double avatarIconSize = 24.0;

  // Navigation chip constants
  static const double chipBorderRadius = 6.0;
  static const double chipSpacing = 8.0;

  // Spacing constants
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 12.0;

  // Text constants
  static const String fontFamily = "TiemposText";
  static const double titleFontSize = 16.0;
  static const double sectionTitleFontSize = 11.0;
  static const double contentFontSize = 14.0;
  static const double chipFontSize = 12.0;

  // Colors
  static const Color avatarBackgroundColor = Color(0xffE6F7FF);
  static const Color avatarIconColor = Color(0xff1890FF);
  static const Color activeChipColor = Color(0xffE4EDFF);
  static const Color inactiveChipColor = Color(0xffF6F6F6);

  // Animation constants
  static const Duration scrollAnimationDuration = Duration(milliseconds: 300);
  static const Duration postFrameDelay = Duration(milliseconds: 50);
  static const Duration snackBarDuration = Duration(seconds: 1);

  // Layout constants
  static const double defaultBottomSpacerHeight = 200.0;
  static const double expandedBottomSpacerOffset = 100.0;
  static const int lastSectionsThreshold = 2;
}

/// A mobile-optimized entity details panel widget that displays detailed information about a selected entity.
///
/// This widget shows entity information including attributes, business rules, relationships,
/// and validations in a structured format optimized for mobile screens with touch-friendly interactions.
class EntityDetailsPanelMobile extends StatefulWidget {
  /// The entity information to display
  final entities_model.Entity entity;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  /// Global entity elements for additional data
  /// Currently unused but kept for future functionality
  // ignore: unused_field
  final Map<String, EntityElement> globalEntityElements;

  const EntityDetailsPanelMobile({
    super.key,
    required this.entity,
    this.onClose,
    this.chatController,
    this.onSendMessage,
    this.globalEntityElements = const {},
  });

  @override
  State<EntityDetailsPanelMobile> createState() =>
      _EntityDetailsPanelMobileState();
}

class _EntityDetailsPanelMobileState extends State<EntityDetailsPanelMobile> {
  /// Track active section for navigation styling
  String? _activeSectionId;

  /// Global keys for sections - persists across builds
  late final Map<String, GlobalKey> _sectionKeys;

  /// ScrollController for the content area
  late final ScrollController _scrollController;

  /// Map to store visibility information for each section
  final Map<String, VisibilityInfo> _sectionVisibilityInfo = {};

  /// Dynamic bottom spacer height for better scrolling
  double _bottomSpacerHeight =
      _EntityDetailsPanelMobileConstants.defaultBottomSpacerHeight;

  /// Cached section definitions to avoid repeated computation
  late final List<Map<String, String>> _sectionDefinitions;

  /// Timer for debouncing visibility updates
  Timer? _visibilityUpdateTimer;

  /// Flag to prevent updates during programmatic scrolling
  bool _isProgrammaticScrolling = false;

  /// Debounce duration for visibility updates
  static const Duration _visibilityUpdateDelay = Duration(milliseconds: 150);

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeSectionData();
    _setInitialActiveSection();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _visibilityUpdateTimer?.cancel();
    super.dispose();
  }

  /// Initialize controllers and listeners
  void _initializeControllers() {
    _scrollController = ScrollController()..addListener(_onScroll);
  }

  /// Initialize section keys and definitions
  void _initializeSectionData() {
    _sectionDefinitions = _buildSectionDefinitions();
    _sectionKeys = Map.fromEntries(
      _sectionDefinitions
          .map((section) => MapEntry(section['id']!, GlobalKey())),
    );
  }

  /// Set the initial active section after frame is built
  void _setInitialActiveSection() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _sectionDefinitions.isNotEmpty) {
        setState(() {
          _activeSectionId = _sectionDefinitions.first['id'];
        });
      }
    });
  }

  /// Handle scroll events for active section detection
  void _onScroll() {
    // Only update if not programmatically scrolling
    if (!_isProgrammaticScrolling) {
      _debouncedVisibilityUpdate();
    }
  }

  /// Builds section definitions for entity details
  List<Map<String, String>> _buildSectionDefinitions() {
    return const [
      {'id': 'attributes', 'label': 'Attributes', 'short': 'ATT'},
      {'id': 'business_rules', 'label': 'Business Rules', 'short': 'BR'},
      {'id': 'relationships', 'label': 'Relationships', 'short': 'REL'},
      {
        'id': 'constants_validations',
        'label': 'Constants & Validations',
        'short': 'CV'
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: _buildContainerDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildMainContentWithNavigation(),
        ],
      ),
    );
  }

  /// Builds the main container decoration
  BoxDecoration _buildContainerDecoration() {
    return const BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(_EntityDetailsPanelMobileConstants.borderRadius),
      ),
    );
  }

  /// Builds the header section with avatar, title, and close button
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 10, 20, 20),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          _buildAvatar(),
          const SizedBox(
              width: _EntityDetailsPanelMobileConstants.spacingMedium),
          _buildTitle(),
          _buildCloseButton(),
        ],
      ),
    );
  }

  /// Builds the entity avatar
  Widget _buildAvatar() {
    return const CircleAvatar(
      backgroundColor: _EntityDetailsPanelMobileConstants.avatarBackgroundColor,
      radius: _EntityDetailsPanelMobileConstants.avatarRadius,
      child: Icon(
        Icons.person_outline,
        color: _EntityDetailsPanelMobileConstants.avatarIconColor,
        size: _EntityDetailsPanelMobileConstants.avatarIconSize,
      ),
    );
  }

  /// Builds the entity title
  Widget _buildTitle() {
    return Expanded(
      child: Text(
        widget.entity.title ?? 'Entity Details',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: _EntityDetailsPanelMobileConstants.titleFontSize,
          fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds the close button
  Widget _buildCloseButton() {
    return GestureDetector(
      onTap: widget.onClose,
      child: const Icon(Icons.close, color: Colors.black, size: 24),
    );
  }

  /// Builds the main content area with vertical navigation chips
  Widget _buildMainContentWithNavigation() {
    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildVerticalNavigationChips(),
          Expanded(
            child: _buildContentArea(),
          ),
        ],
      ),
    );
  }

  /// Builds the vertical navigation chips section
  Widget _buildVerticalNavigationChips() {
    return Container(
      width: 60,
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: _EntityDetailsPanelMobileConstants.spacingMedium,
      ),
      child: Column(
        children: _buildNavigationChipsList(),
      ),
    );
  }

  /// Builds the main content area
  Widget _buildContentArea() {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAttributesSection(context, _sectionKeys),
          _buildBusinessRulesSection(context, _sectionKeys),
          _buildRelationshipsSection(context, _sectionKeys),
          _buildConstantsValidationsSection(context, _sectionKeys),
          SizedBox(height: _bottomSpacerHeight),
        ],
      ),
    );
  }

  /// Scrolls to a specific entity section
  void _scrollToEntitySection(String sectionId) {
    final key = _sectionKeys[sectionId];
    if (key?.currentContext == null) return;

    // Set programmatic scrolling flag to prevent interference
    _isProgrammaticScrolling = true;

    setState(() {
      _activeSectionId = sectionId;
      _updateBottomSpacerHeight(sectionId);
    });

    Future.delayed(_EntityDetailsPanelMobileConstants.postFrameDelay, () {
      if (_scrollController.hasClients && key!.currentContext != null) {
        Scrollable.ensureVisible(
          key.currentContext!,
          alignment: 0.0,
          duration: _EntityDetailsPanelMobileConstants.scrollAnimationDuration,
          curve: Curves.easeInOut,
        ).then((_) {
          // Reset flag after animation completes
          Future.delayed(const Duration(milliseconds: 100), () {
            _isProgrammaticScrolling = false;
          });
        });
      }
    });
  }

  /// Updates bottom spacer height based on section position
  void _updateBottomSpacerHeight(String sectionId) {
    final sectionIndex =
        _sectionDefinitions.indexWhere((section) => section['id'] == sectionId);

    if (sectionIndex >=
        (_sectionDefinitions.length -
            _EntityDetailsPanelMobileConstants.lastSectionsThreshold)) {
      _bottomSpacerHeight = MediaQuery.of(context).size.height -
          _EntityDetailsPanelMobileConstants.expandedBottomSpacerOffset;
    } else {
      _bottomSpacerHeight =
          _EntityDetailsPanelMobileConstants.defaultBottomSpacerHeight;
    }
  }

  /// Builds the list of navigation chips
  List<Widget> _buildNavigationChipsList() {
    return _sectionDefinitions
        .map((section) => Padding(
              padding: const EdgeInsets.only(
                  bottom: _EntityDetailsPanelMobileConstants.chipSpacing),
              child: _buildNavigationChip(section),
            ))
        .toList();
  }

  /// Builds an individual navigation chip
  Widget _buildNavigationChip(Map<String, String> section) {
    final isActive = _activeSectionId == section['id'];
    final sectionId = section['id']!;

    return GestureDetector(
      onTap: () => _scrollToEntitySection(sectionId),
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: isActive
              ? _EntityDetailsPanelMobileConstants.activeChipColor
              : _EntityDetailsPanelMobileConstants.inactiveChipColor,
          borderRadius: BorderRadius.circular(
              _EntityDetailsPanelMobileConstants.chipBorderRadius),
        ),
        child: Center(
          child: Text(
            section['short']!,
            style: TextStyle(
              fontSize: _EntityDetailsPanelMobileConstants.chipFontSize,
              fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
              color: isActive ? Colors.blue.shade700 : Colors.black,
              fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds a generic section widget with VisibilityDetector for mobile
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        _sectionVisibilityInfo[sectionId] = info;
        // Use debounced update to prevent flickering
        _debouncedVisibilityUpdate();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: const EdgeInsets.symmetric(
            vertical:
                _EntityDetailsPanelMobileConstants.sectionVerticalPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(title),
            const SizedBox(
                height: _EntityDetailsPanelMobileConstants.spacingSmall),
            hasData ? contentBuilder() : _buildNoDataWidget(title),
            const SizedBox(
                height: _EntityDetailsPanelMobileConstants.spacingSmall),
            _buildSectionDivider(),
          ],
        ),
      ),
    );
  }

  /// Builds the section title
  Widget _buildSectionTitle(String title) {
    return Text(
      '$title:',
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: _EntityDetailsPanelMobileConstants.sectionTitleFontSize,
        fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
        color: Colors.black,
      ),
    );
  }

  /// Builds the section divider
  Widget _buildSectionDivider() {
    return Container(
      height: 1,
      color: Colors.grey.shade200,
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        'No $sectionName data available.',
        style: const TextStyle(
          fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
          fontSize: _EntityDetailsPanelMobileConstants.contentFontSize,
          fontStyle: FontStyle.italic,
          color: Colors.grey,
        ),
      ),
    );
  }

  /// Debounced visibility update to prevent flickering
  void _debouncedVisibilityUpdate() {
    _visibilityUpdateTimer?.cancel();
    _visibilityUpdateTimer = Timer(_visibilityUpdateDelay, () {
      _updateActiveSectionFromVisibility();
    });
  }

  /// Method to update active section based on visibility
  void _updateActiveSectionFromVisibility() {
    if (_sectionVisibilityInfo.isEmpty || _isProgrammaticScrolling) return;

    String? bestSection;

    // Simple approach: Find the section that is most prominently visible
    // Priority order:
    // 1. Any section that is 100% visible (perfect for small sections)
    // 2. Section with highest visibility that's also well-positioned

    double bestVisibility = 0;
    String? bestFullyVisibleSection;

    // First, check for any fully or nearly fully visible sections
    for (final sectionDef in _sectionDefinitions) {
      final sectionId = sectionDef['id']!;
      final visibilityInfo = _sectionVisibilityInfo[sectionId];

      if (visibilityInfo != null) {
        final visibleFraction = visibilityInfo.visibleFraction;
        final bounds = visibilityInfo.visibleBounds;

        // Perfect visibility - this is ideal for small sections
        if (visibleFraction >= 0.99) {
          bestFullyVisibleSection = sectionId;
          break; // Found a perfect match, use it immediately
        }

        // High visibility with good positioning
        if (visibleFraction >= 0.8 && bounds.top <= 0.2) {
          if (visibleFraction > bestVisibility) {
            bestVisibility = visibleFraction;
            bestSection = sectionId;
          }
        }

        // For sections with decent visibility, check if they're prominently positioned
        if (visibleFraction >= 0.5) {
          // Section starts near the top of viewport - good candidate
          if (bounds.top <= 0.1) {
            if (visibleFraction > bestVisibility) {
              bestVisibility = visibleFraction;
              bestSection = sectionId;
            }
          }
          // Section is well-centered in viewport
          else if (bounds.top >= 0.1 &&
              bounds.bottom <= 0.9 &&
              visibleFraction >= 0.7) {
            if (visibleFraction > bestVisibility) {
              bestVisibility = visibleFraction;
              bestSection = sectionId;
            }
          }
        }
      }
    }

    // Use fully visible section if found (prioritizes small sections)
    if (bestFullyVisibleSection != null) {
      bestSection = bestFullyVisibleSection;
    }

    // If no good candidate found, fall back to highest visibility
    if (bestSection == null) {
      double maxVisibility = 0;
      for (final sectionDef in _sectionDefinitions) {
        final sectionId = sectionDef['id']!;
        final visibilityInfo = _sectionVisibilityInfo[sectionId];

        if (visibilityInfo != null &&
            visibilityInfo.visibleFraction > maxVisibility) {
          maxVisibility = visibilityInfo.visibleFraction;
          bestSection = sectionId;
        }
      }
    }

    // Update active section with minimal resistance for small sections
    if (bestSection != null && bestSection != _activeSectionId) {
      // For fully visible sections, update immediately
      if (bestFullyVisibleSection != null) {
        if (mounted) {
          setState(() {
            _activeSectionId = bestSection;
          });
        }
      } else {
        // For other sections, check if change is significant enough
        final currentInfo = _activeSectionId != null
            ? _sectionVisibilityInfo[_activeSectionId!]
            : null;
        final newInfo = _sectionVisibilityInfo[bestSection];

        if (currentInfo == null || newInfo == null) {
          // No current info or new info, safe to update
          if (mounted) {
            setState(() {
              _activeSectionId = bestSection;
            });
          }
        } else {
          // Only switch if new section is significantly more visible or better positioned
          final currentVisibility = currentInfo.visibleFraction;
          final newVisibility = newInfo.visibleFraction;
          final newBounds = newInfo.visibleBounds;

          bool shouldSwitch = false;

          // New section is much more visible
          if (newVisibility > currentVisibility + 0.2) {
            shouldSwitch = true;
          }
          // New section is at top of viewport and reasonably visible
          else if (newBounds.top <= 0.1 &&
              newVisibility >= 0.6 &&
              newVisibility > currentVisibility + 0.1) {
            shouldSwitch = true;
          }
          // Current section is barely visible and new section is decent
          else if (currentVisibility < 0.3 && newVisibility >= 0.5) {
            shouldSwitch = true;
          }

          if (shouldSwitch && mounted) {
            setState(() {
              _activeSectionId = bestSection;
            });
          }
        }
      }
    }
  }

  /// Builds the attributes section
  Widget _buildAttributesSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'attributes',
      'Attributes',
      widget.entity.attributeMetaDataList != null &&
          widget.entity.attributeMetaDataList!.isNotEmpty,
      () => _buildAttributesContent(context),
    );
  }

  /// Builds the attributes content
  Widget _buildAttributesContent(BuildContext context) {
    final attributes = widget.entity.attributeMetaDataList;
    if (attributes == null || attributes.isEmpty) {
      return _buildNoDataWidget('attributes');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: attributes
          .map((attribute) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: GestureDetector(
                  onTap: () => _showChatDialog(context, 'attributes'),
                  child: _generateAttributeSentence(attribute),
                ),
              ))
          .toList(),
    );
  }

  /// Builds the business rules section
  Widget _buildBusinessRulesSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'business_rules',
      'Business Rules',
      widget.entity.relationshipProperties != null &&
          widget.entity.relationshipProperties!.isNotEmpty,
      () => _buildBusinessRulesContent(context),
    );
  }

  /// Builds the business rules content
  Widget _buildBusinessRulesContent(BuildContext context) {
    final relationshipProperties = widget.entity.relationshipProperties;
    if (relationshipProperties == null || relationshipProperties.isEmpty) {
      return _buildNoDataWidget('business rules');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: relationshipProperties
          .map((relationShipProp) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: GestureDetector(
                  onTap: () => _showChatDialog(context, 'business rules'),
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: relationShipProp.sourceEntity,
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        const TextSpan(
                          text: ' and ',
                          style: TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        TextSpan(
                          text: relationShipProp.targetEntity,
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        TextSpan(
                          text:
                              ' have relationship. those are  ${relationShipProp.onDelete} and  ${relationShipProp.onUpdate}.',
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  /// Builds the relationships section
  Widget _buildRelationshipsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'relationships',
      'Relationships',
      widget.entity.relationships != null &&
          widget.entity.relationships!.isNotEmpty,
      () => _buildRelationshipsContent(context),
    );
  }

  /// Builds the relationships content
  Widget _buildRelationshipsContent(BuildContext context) {
    final relationships = widget.entity.relationships;
    if (relationships == null || relationships.isEmpty) {
      return _buildNoDataWidget('relationships');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: relationships
          .map((relation) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: GestureDetector(
                  onTap: () => _showChatDialog(context, 'relationships'),
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: relation.sourceEntity,
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        TextSpan(
                          text: ' has ${relation.type} relationship with ',
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        TextSpan(
                          text: relation.targetEntity,
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        const TextSpan(
                          text: ' using ',
                          style: TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        TextSpan(
                          text: relation.joinCondition.replaceAll("=", "to"),
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        const TextSpan(
                          text: '.',
                          style: TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  /// Builds the constants & validations section
  Widget _buildConstantsValidationsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'constants_validations',
      'Constants & Validations',
      widget.entity.validationsList != null &&
          widget.entity.validationsList!.isNotEmpty,
      () => _buildConstantsValidationsContent(context),
    );
  }

  /// Builds the constants & validations content
  Widget _buildConstantsValidationsContent(BuildContext context) {
    final validations = widget.entity.validationsList;
    if (validations == null || validations.isEmpty) {
      return _buildNoDataWidget('constants & validations');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: validations
          .map((validation) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: GestureDetector(
                  onTap: () =>
                      _showChatDialog(context, 'constants & validations'),
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: validation.attribute,
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        TextSpan(
                          text: '  ${validation.constraintText}.',
                          style: const TextStyle(
                            fontSize: _EntityDetailsPanelMobileConstants
                                .contentFontSize,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily:
                                _EntityDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  /// Generate attribute sentence from attribute data
  Widget _generateAttributeSentence(dynamic attribute) {
    final valuesText =
        attribute.values != "N/A" ? " ( ${attribute.values} )" : "";

    return Text.rich(
      TextSpan(
        text: "${attribute.displayName} ",
        style: const TextStyle(
          fontSize: _EntityDetailsPanelMobileConstants.contentFontSize,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
        ),
        children: <InlineSpan>[
          TextSpan(
            text: "is ${attribute.keyType} key and ${attribute.description}. "
                "Data type is ${attribute.dataType}$valuesText. "
                "Error message: \"${attribute.errorMessage}\". "
                "${attribute.required} field.",
            style: const TextStyle(
              fontSize: _EntityDetailsPanelMobileConstants.contentFontSize,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
            ),
          )
        ],
      ),
    );
  }

  /// Show chat dialog (placeholder for now)
  void _showChatDialog(BuildContext context, String section) {
    // Placeholder for chat functionality
    // This would typically open a chat interface or send a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Chat about $section'),
        duration: _EntityDetailsPanelMobileConstants.snackBarDuration,
      ),
    );
  }
}
