class NslTreeHierarchy {
    int? status;
    String? message;
    Result? result;

    NslTreeHierarchy({
        this.status,
        this.message,
        this.result,
    });

    NslTreeHierarchy copyWith({
        int? status,
        String? message,
        Result? result,
    }) => 
        NslTreeHierarchy(
            status: status ?? this.status,
            message: message ?? this.message,
            result: result ?? this.result,
        );
}

class Result {
    List<Node>? nodes;

    Result({
        this.nodes,
    });

    Result copyWith({
        List<Node>? nodes,
    }) => 
        Result(
            nodes: nodes ?? this.nodes,
        );
}

class Node {
    String? id;
    String? displayId;
    List<String>? parentIds;
    List<String>? children;
    NodeType? nodeType;
    Level? level;
    int? npValue;

    Node({
        this.id,
        this.displayId,
        this.parentIds,
        this.children,
        this.nodeType,
        this.level,
        this.npValue,
    });

    Node copyWith({
        String? id,
        String? displayId,
        List<String>? parentIds,
        List<String>? children,
        NodeType? nodeType,
        Level? level,
        int? npValue,
    }) => 
        Node(
            id: id ?? this.id,
            displayId: displayId ?? this.displayId,
            parentIds: parentIds ?? this.parentIds,
            children: children ?? this.children,
            nodeType: nodeType ?? this.nodeType,
            level: level ?? this.level,
            npValue: npValue ?? this.npValue,
        );
}

enum Level {
    M1,
    M2,
    M3,
    M4
}

enum NodeType {
    MODULE,
    USER
}
