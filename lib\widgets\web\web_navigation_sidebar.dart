import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../providers/build_provider.dart';
import '../../providers/screen_content_provider.dart';
import '../../utils/navigation_service.dart';
import '../../utils/logger.dart';

class WebNavigationSidebar extends StatefulWidget {
  final String currentScreen;
  final Function(BuildContext)? onLogoutPressed;

  const WebNavigationSidebar({
    super.key,
    required this.currentScreen,
    this.onLogoutPressed,
  });

  @override
  State<WebNavigationSidebar> createState() => _WebNavigationSidebarState();
}

class _WebNavigationSidebarState extends State<WebNavigationSidebar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 70,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Logo or App Name
          Container(
            height: 64,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              context.tr('navigation.drawer.appName'),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),

          // Main Navigation Items (Scrollable)
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Main navigation items
                _buildNavItem(
                  context,
                  Icons.chat_bubble_outline,
                  context.tr('navigation.chat'),
                  widget.currentScreen == 'chat',
                  () {
                    if (widget.currentScreen != 'chat') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('chat');
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.build_outlined,
                  context.tr('navigation.build'),
                  widget.currentScreen == 'create',
                  () {
                    final buildProvider =
                        Provider.of<BuildProvider>(context, listen: false);
                    buildProvider.clearChat();
                    buildProvider.updateChatViewFlag(false);

                    if (widget.currentScreen != 'create') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('create');
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.swap_horiz_outlined,
                  context.tr('navigation.transact'),
                  widget.currentScreen == 'transact',
                  () {
                    if (widget.currentScreen != 'transact') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('transact');
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.build_outlined,
                  context.tr('navigation.build'),
                  widget.currentScreen == 'build',
                  () {
                    if (widget.currentScreen != 'build') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('build');
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.code,
                  context.tr('navigation.code'),
                  widget.currentScreen == 'code',
                  () {
                    if (widget.currentScreen != 'code') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('code');
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.settings_outlined,
                  context.tr('navigation.settings'),
                  widget.currentScreen == 'settings',
                  () {
                    if (widget.currentScreen != 'settings') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('settings');
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.widgets_outlined,
                  context.tr('navigation.components'),
                  widget.currentScreen == 'components',
                  () {
                    if (widget.currentScreen != 'components') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('components');
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.help_outline,
                  context.tr('navigation.help'),
                  widget.currentScreen == 'help',
                  () {
                    if (widget.currentScreen != 'help') {
                      // For now, just show a snackbar since we don't have a Help screen yet
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content:
                              Text(context.tr('navigation.helpComingSoon')),
                        ),
                      );
                    }
                  },
                ),
                _buildNavItem(
                  context,
                  Icons.widgets_outlined,
                  context.tr('navigation.widgetBinder'),
                  widget.currentScreen == 'widgetBinder',
                  () {
                    if (widget.currentScreen != 'widgetBinder') {
                      Provider.of<ScreenContentProvider>(context, listen: false)
                          .setScreen('widgetBinder');
                    }
                  },
                ),
                // Add more navigation items here as needed
              ],
            ),
          ),

          // Fixed bottom items (Profile and Logout)
          Consumer<AuthProvider>(
            builder: (context, authProvider, _) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Divider
                  Divider(
                      height: 1,
                      thickness: 1,
                      color: Theme.of(context).dividerColor.withAlpha(25)),

                  // Logout item
                  _buildNavItem(
                    context,
                    Icons.logout,
                    context.tr('navigation.logout'),
                    false,
                    () {
                      if (widget.onLogoutPressed != null) {
                        widget.onLogoutPressed!(context);
                      } else {
                        _showLogoutConfirmation(context, authProvider);
                      }
                    },
                    color: Theme.of(context).colorScheme.error,
                  ),

                  // Bottom padding
                  const SizedBox(height: 8),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, IconData icon, String label,
      bool isSelected, VoidCallback onTap,
      {Color? color}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        decoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              width: 3,
            ),
          ),
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withAlpha(10)
              : Colors.transparent,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color ??
                  (isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).iconTheme.color),
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: color ??
                    (isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).textTheme.bodySmall?.color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutConfirmation(
      BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.logout, color: Theme.of(context).colorScheme.error),
              const SizedBox(width: 8),
              Text(context.tr('navigation.logout')),
            ],
          ),
          content: Text(context.tr('navigation.logoutConfirmation')),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(context.tr('common.cancel')),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();

                // Loading state is now handled in WebHomeScreen

                Logger.info('Logging out user');

                try {
                  // Perform logout
                  final success = await authProvider.logout();

                  if (success) {
                    // Navigate to login screen
                    NavigationService.navigateToLogin();
                  } else {
                    Logger.error('Logout failed');

                    // We'll handle the error in the finally block
                  }
                } catch (e) {
                  Logger.error('Error during logout: $e');
                  // We'll handle the error in the finally block
                } finally {
                  // Loading state is now handled in WebHomeScreen
                }
              },
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error,
              ),
              child: Text(context.tr('navigation.logout')),
            ),
          ],
        );
      },
    );
  }
}
