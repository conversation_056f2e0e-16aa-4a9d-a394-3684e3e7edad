import 'package:flutter/material.dart';
import '../../widgets/navigation_drawer.dart';
import '../../ui_components/theme/app_theme.dart';
import '../../widgets/responsive_builder.dart';
import '../../models/dashboard_config.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late Future<DashboardConfig> _dashboardConfig;

  @override
  void initState() {
    super.initState();
    _dashboardConfig = _loadDashboardConfig();
  }

  Future<DashboardConfig> _loadDashboardConfig() async {
    // In a real app, this would come from an API or local storage
    final json = {
      "sections": [
        {
          "title": "Active Solutions",
          "type": "metric",
          "data": {
            "value": "12",
            "label": "Total Solutions",
            "trend": "+2 this week"
          },
          "color": 0xFFE3F2FD
        },
        {
          "title": "Workflows",
          "type": "metric",
          "data": {
            "value": "5",
            "label": "Active Workflows",
            "trend": "3 pending"
          },
          "color": 0xFFE8F5E9
        },
        {
          "title": "Deployments",
          "type": "metric",
          "data": {
            "value": "8",
            "label": "Successful Deployments",
            "trend": "98% success rate"
          },
          "color": 0xFFFFF3E0
        },
        {
          "title": "System Health",
          "type": "healthIndicator",
          "data": {
            "percentage": 0.95,
            "label": "System Uptime",
            "status": "Healthy"
          },
          "color": 0xFFF3E5F5
        },
        {
          "title": "Recent Activity",
          "type": "list",
          "data": {
            "items": [
              {"title": "New solution deployed", "timestamp": "2 hours ago"},
              {"title": "Workflow completed", "timestamp": "3 hours ago"},
              {"title": "System update", "timestamp": "5 hours ago"}
            ]
          },
          "color": 0xFFE0F2F1
        },
        {
          "title": "Resource Usage",
          "type": "chart",
          "data": {
            "type": "linear",
            "values": [65, 75, 85, 70, 80, 90, 85],
            "label": "CPU Usage (%)"
          },
          "color": 0xFFEFEBE9
        }
      ]
    };

    return DashboardConfig.fromJson(json);
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          drawer: const AppNavigationDrawer(currentRoute: 'dashboard'),
          appBar: AppBar(
            title: const Text('Dashboard'),
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  setState(() {
                    _dashboardConfig = _loadDashboardConfig();
                  });
                },
              ),
            ],
          ),
          body: FutureBuilder<DashboardConfig>(
            future: _dashboardConfig,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Error loading dashboard: ${snapshot.error}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                );
              }

              return _buildDashboard(context, snapshot.data!, deviceType);
            },
          ),
        );
      },
    );
  }

  Widget _buildDashboard(
      BuildContext context, DashboardConfig config, DeviceType deviceType) {
    final crossAxisCount = deviceType == DeviceType.mobile
        ? 1
        : deviceType == DeviceType.tablet
            ? 2
            : 3;

    return Padding(
      padding: EdgeInsets.all(AppTheme.spacingM),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: AppTheme.spacingM,
          mainAxisSpacing: AppTheme.spacingM,
          childAspectRatio: 1.2,
        ),
        itemCount: config.sections.length,
        itemBuilder: (context, index) {
          final section = config.sections[index];
          return _DashboardCard(
            title: section.title,
            content: _buildWidgetForType(section),
            color: section.color ?? Colors.white,
          );
        },
      ),
    );
  }

  Widget _buildWidgetForType(DashboardSection section) {
    switch (section.type) {
      case DashboardWidgetType.metric:
        return _buildMetricWidget(
          section.data['value'],
          section.data['label'],
          section.data['trend'],
        );
      case DashboardWidgetType.healthIndicator:
        return _buildHealthIndicator(
          section.data['percentage'],
          section.data['label'],
          section.data['status'],
        );
      case DashboardWidgetType.list:
        return _buildListWidget(section.data['items']);
      case DashboardWidgetType.chart:
        return _buildChartWidget(
          section.data['values'],
          section.data['label'],
          section.data['type'],
        );
      default:
        return const Center(child: Text('Unsupported widget type'));
    }
  }

  Widget _buildMetricWidget(String value, String label, String? trend) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(138),
          ),
          textAlign: TextAlign.center,
        ),
        if (trend != null) ...[
          const SizedBox(height: 4),
          Text(
            trend,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildHealthIndicator(double percentage, String label, String status) {
    final color = percentage > 0.8
        ? Theme.of(context).colorScheme.primary
        : percentage > 0.6
            ? Theme.of(context).colorScheme.secondary
            : Theme.of(context).colorScheme.error;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            CircularProgressIndicator(
              value: percentage,
              backgroundColor:
                  Theme.of(context).colorScheme.onSurface.withAlpha(38),
              color: color,
              strokeWidth: 10,
            ),
            Text(
              '${(percentage * 100).toInt()}%',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(138),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          status,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildListWidget(List<dynamic> items) {
    return ListView.builder(
      itemCount: items.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final item = items[index];
        return ListTile(
          dense: true,
          leading: Icon(Icons.circle,
              size: 8, color: Theme.of(context).colorScheme.primary),
          title: Text(
            item['title'],
            style: const TextStyle(fontSize: 13),
          ),
          trailing: Text(
            item['timestamp'],
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
            ),
          ),
        );
      },
    );
  }

  Widget _buildChartWidget(List<dynamic> values, String label, String type) {
    // Simplified chart representation
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 100,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: values.map((value) {
              return Container(
                width: 8,
                height: (value as num).toDouble(),
                color: Theme.of(context).primaryColor.withAlpha(179),
              );
            }).toList(),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(138),
          ),
        ),
      ],
    );
  }
}

class _DashboardCard extends StatelessWidget {
  final String title;
  final Widget content;
  final Color color;

  const _DashboardCard({
    required this.title,
    required this.content,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: color,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            Expanded(child: content),
          ],
        ),
      ),
    );
  }
}
