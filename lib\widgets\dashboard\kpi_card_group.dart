import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';

/// Data for a KPI card
class KpiCardData {
  final String label;
  final dynamic value;
  final String format;
  final String icon;
  final String? comparisonLabel;
  final dynamic comparisonValue;

  KpiCardData({
    required this.label,
    required this.value,
    required this.format,
    required this.icon,
    this.comparisonLabel,
    this.comparisonValue,
  });
}

/// A group of KPI cards
class KpiCardGroup extends StatelessWidget {
  final String title;
  final List<KpiCardData> cards;

  const KpiCardGroup({
    super.key,
    required this.title,
    required this.cards,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleLarge,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children:
                  cards.map((card) => _buildKpiCard(context, card)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKpiCard(BuildContext context, KpiCardData card) {
    // Format the value based on the format type
    String formattedValue = _formatValue(card.value, card.format);

    // Format the comparison value if it exists
    String? formattedComparison;
    if (card.comparisonValue != null) {
      formattedComparison = _formatComparisonValue(card.comparisonValue);
    }

    // Determine icon
    IconData iconData = _getIconData(card.icon);

    // Determine comparison color
    Color comparisonColor = _getComparisonColor(card.comparisonValue);

    return Container(
      width: 200,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // Equivalent to opacity 0.1
            blurRadius: 4.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  card.label,
                  style: Theme.of(context).textTheme.titleSmall,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                iconData,
                color: AppColors.primaryIndigo,
                size: 24.0,
              ),
            ],
          ),
          const SizedBox(height: 8.0),
          Text(
            formattedValue,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          if (card.comparisonLabel != null && formattedComparison != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                mainAxisSize: MainAxisSize.min, // Use minimum space
                children: [
                  Flexible(
                    child: Text(
                      card.comparisonLabel!,
                      style: Theme.of(context).textTheme.bodySmall,
                      overflow:
                          TextOverflow.ellipsis, // Add ellipsis for overflow
                    ),
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    formattedComparison,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: comparisonColor,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  String _formatValue(dynamic value, String format) {
    if (value == null) return 'N/A';

    switch (format) {
      case 'currency':
        return '\$${value.toStringAsFixed(2)}';
      case 'number':
        return value.toString();
      case 'percentage':
        return '${value.toStringAsFixed(1)}%';
      default:
        return value.toString();
    }
  }

  String _formatComparisonValue(dynamic value) {
    if (value == null) return '';

    if (value is double || value is num) {
      final double numValue = value.toDouble();
      final String sign = numValue >= 0 ? '+' : '';
      return '$sign${numValue.toStringAsFixed(1)}%';
    }

    return value.toString();
  }

  IconData _getIconData(String icon) {
    switch (icon) {
      case 'trending_up':
        return Icons.trending_up;
      case 'trending_down':
        return Icons.trending_down;
      case 'inventory':
        return Icons.inventory;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'people':
        return Icons.people;
      case 'attach_money':
        return Icons.attach_money;
      default:
        return Icons.analytics;
    }
  }

  Color _getComparisonColor(dynamic value) {
    if (value == null) return Colors.grey;

    if (value is double || value is num) {
      final double numValue = value.toDouble();
      if (numValue > 0) {
        return Colors.green;
      } else if (numValue < 0) {
        return Colors.red;
      }
    }

    return Colors.grey;
  }
}
