import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/providers/library_counts_provider.dart';

class AgentsMobile {
  final String title;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;
  final DateTime lastUpdated;

  AgentsMobile({
    required this.title,
    required this.imageUrl,
    required this.lastUpdated,
    this.isDraft = false,
    this.imageWidth = 107.0,
    this.imageHeight = 107.0,
  });

  factory AgentsMobile.fromJson(Map<String, dynamic> json) {
    return AgentsMobile(
      title: json['title'] as String,
      imageUrl: json['imageUrl'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 107.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 107.0,
    );
  }
}

class AgentsLibraryMobile extends StatefulWidget {
  const AgentsLibraryMobile({
    super.key,
    this.showNavigationBar = true,
    this.searchQuery,
  });

  final bool showNavigationBar;
  final String? searchQuery;

  @override
  State<AgentsLibraryMobile> createState() => _AgentsLibraryMobileState();
}

class _AgentsLibraryMobileState extends State<AgentsLibraryMobile>
    with TickerProviderStateMixin {
  // Constants
  static const double _agentsPerViewNormal = 2.25;
  static const double _agentsPerViewCompact = 3.0;
  static const double _agentAspectRatio = 1.0; // width / height (square)
  static const double _titleHeight = 32.0;
  static const double _verticalSpacing = 12.0;
  static const double _agentSpacing = 30.0;
  static const double _horizontalPadding = 24.0;
  static const int _recentAgentsLimit = 10;

  // Text Styles
  static const TextStyle _sectionHeadingStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _agentTitleStyle = TextStyle(
    fontWeight: FontWeight.w500,
    fontSize: 12,
    height: 1.334,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _emptyStateStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey,
    fontFamily: "TiemposText",
  );

  // Data
  List<AgentsMobile> agents = [];
  List<AgentsMobile> recentAgents = [];
  List<AgentsMobile> allAgents = [];
  List<AgentsMobile> filteredRecentAgents = [];
  List<AgentsMobile> filteredAllAgents = [];
  bool isLoading = true;

  // Controllers
  late CarouselController _recentAgentsController;
  late CarouselController _allAgentsController;
  late AnimationController _loadingAnimationController;

  // Only needed when showNavigationBar is true
  FocusNode? _searchFocusNode;
  TextEditingController? _searchController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;
  String _searchQuery = '';

  /// Get agents per view based on keyboard visibility
  double _getAgentsPerView() {
    return _isKeyboardVisible ? _agentsPerViewCompact : _agentsPerViewNormal;
  }

  // JSON string containing agent data with lastUpdated dates
  static const String agentsJsonString = '''
{
  "agents": [
    {
      "title": "Agent-1",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-25T10:15:00Z"
    },
    {
      "title": "Agent-2",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-24T14:30:00Z"
    },
    {
      "title": "Agent-3",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": true,
      "lastUpdated": "2024-12-28T09:45:00Z"
    },
    {
      "title": "Agent-4",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-23T16:20:00Z"
    },
    {
      "title": "Agent-5",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-21T11:55:00Z"
    },
    {
      "title": "Agent-6",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": true,
      "lastUpdated": "2024-12-20T13:40:00Z"
    },
    {
      "title": "Agent-7",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-25T08:25:00Z"
    },
    {
      "title": "Agent-8",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-09T15:10:00Z"
    },
    {
      "title": "Agent-9",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-20T12:35:00Z"
    },
    {
      "title": "Agent-10",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-16T17:50:00Z"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadAgents();
  }

  @override
  void didUpdateWidget(AgentsLibraryMobile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _searchQuery = widget.searchQuery ?? '';
      _filterAgents();
    }
  }

  void _initializeControllers() {
    _recentAgentsController = CarouselController();
    _allAgentsController = CarouselController();
    _searchQuery = widget.searchQuery ?? '';
    if (widget.showNavigationBar) {
      _searchFocusNode = FocusNode();
      _searchController = TextEditingController();
      _searchFocusNode!.addListener(_onSearchFocusChange);
      _searchController!.addListener(_onSearchChanged);
    }
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {});
  }

  void _onSearchChanged() {
    final query = _searchController?.text.toLowerCase().trim() ?? '';
    setState(() {
      _searchQuery = query;
      _filterAgents();
    });
  }

  void _filterAgents() {
    if (_searchQuery.isEmpty) {
      filteredRecentAgents = recentAgents;
      filteredAllAgents = allAgents;
    } else {
      filteredRecentAgents = recentAgents.where((agent) {
        return agent.title.toLowerCase().contains(_searchQuery);
      }).toList();

      filteredAllAgents = allAgents.where((agent) {
        return agent.title.toLowerCase().contains(_searchQuery);
      }).toList();
    }
  }

  void _loadAgents() {
    try {
      final data = json.decode(agentsJsonString);
      final loadedAgents = (data['agents'] as List<dynamic>)
          .map((agentJson) =>
              AgentsMobile.fromJson(agentJson as Map<String, dynamic>))
          .toList();

      final originalOrderAgents = List<AgentsMobile>.from(loadedAgents);
      loadedAgents.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      setState(() {
        agents = originalOrderAgents;
        recentAgents = loadedAgents.take(_recentAgentsLimit).toList();
        allAgents = originalOrderAgents;
        filteredRecentAgents = recentAgents; // Initialize filtered lists
        filteredAllAgents = allAgents;
        isLoading = false;
      });

      // Update the library counts provider with actual count
      if (mounted) {
        Provider.of<LibraryCountsProvider>(context, listen: false)
            .updateAgentsCount(agents.length);
      }

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        agents = <AgentsMobile>[];
        recentAgents = <AgentsMobile>[];
        allAgents = <AgentsMobile>[];
        isLoading = false;
      });
      debugPrint('Error loading agents: $e');
    }
  }

  @override
  void dispose() {
    _recentAgentsController.dispose();
    _allAgentsController.dispose();
    if (widget.showNavigationBar) {
      _searchController?.removeListener(_onSearchChanged);
      _searchController?.dispose();
      _searchFocusNode?.removeListener(_onSearchFocusChange);
      _searchFocusNode?.dispose();
    }
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildAgentsLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
  }

  Widget _buildAgentsLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: null,
      appBar: null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAgentsContent(),
        ],
      ),
      floatingActionButton: null,
    );
  }

  Widget _buildAgentsContent() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 0, 16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeading("Recent Agents"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildRecentAgentsCarousel(),
                ),
                const SizedBox(height: 24),
                _buildSectionHeading("All Agents"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildAllAgentsCarousel(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeading(String title) {
    return Text(title, style: _sectionHeadingStyle);
  }

  Widget _buildRecentAgentsCarousel() {
    final agentsToShow =
        _searchQuery.isEmpty ? recentAgents : filteredRecentAgents;
    return _buildCarousel(
      agents: agentsToShow,
      controller: _recentAgentsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No recent agents found'
          : 'No recent agents match your search',
    );
  }

  Widget _buildAllAgentsCarousel() {
    final agentsToShow = _searchQuery.isEmpty ? allAgents : filteredAllAgents;
    return _buildCarousel(
      agents: agentsToShow,
      controller: _allAgentsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No agents found'
          : 'No agents match your search',
    );
  }

  Widget _buildCarousel({
    required List<AgentsMobile> agents,
    required CarouselController controller,
    required String emptyMessage,
  }) {
    if (agents.isEmpty) {
      return Center(
        child: Text(emptyMessage, style: _emptyStateStyle),
      );
    }

    final itemExtent = _calculateItemExtent();
    return CarouselView(
      padding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      controller: controller,
      itemExtent: itemExtent,
      enableSplash: false,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      shrinkExtent: itemExtent,
      children: agents.asMap().entries.map((entry) {
        return _buildAgentItem(entry.value, entry.key);
      }).toList(),
    );
  }

  double _calculateItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final agentsPerView = _getAgentsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    return availableWidth / agentsPerView;
  }

  Widget _buildAgentItem(AgentsMobile agent, int agentIndex) {
    return GestureDetector(
      onTap: () => _navigateToAgentDetails(agentIndex),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildAgentContent(agent),
          ),
        ),
      ),
    );
  }

  Widget _buildAgentContent(AgentsMobile agent) {
    final agentDimensions = _calculateAgentDimensions(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAgentCover(agent, agentDimensions),
        const SizedBox(height: 8),
        _buildAgentTitle(agent.title, agentDimensions['width']!),
      ],
    );
  }

  Map<String, double> _calculateAgentDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final agentsPerView = _getAgentsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    final itemExtent = availableWidth / agentsPerView;

    double agentWidth = itemExtent - _agentSpacing;

    if (_isKeyboardVisible) {
      agentWidth = agentWidth.clamp(85.0, 140.0);
    } else {
      agentWidth = agentWidth.clamp(110.0, 170.0);
    }

    final agentHeight = agentWidth / _agentAspectRatio;

    return {
      'width': agentWidth,
      'height': agentHeight,
      'spacing': _agentSpacing,
    };
  }

  double _calculateCarouselHeight() {
    final agentDimensions = _calculateAgentDimensions(context);
    final agentHeight = agentDimensions['height']!;
    return agentHeight + _verticalSpacing + _titleHeight + 6;
  }

  void _navigateToAgentDetails(int agentIndex) {
    debugPrint('Navigate to agent at index: $agentIndex');
  }

  Widget _buildAgentCover(AgentsMobile agent, Map<String, double> dimensions) {
    final agentWidth = dimensions['width']!;
    final agentHeight = dimensions['height']!;

    return Stack(
      children: [
        Container(
          width: agentWidth,
          height: agentHeight,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: SvgPicture.asset(
            agent.imageUrl,
            width: agentWidth,
            height: agentHeight,
            fit: BoxFit.cover,
          ),
        ),
        if (agent.isDraft)
          Positioned(
            top: agentHeight * 0.08,
            right: agentWidth * 0.08,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'Draft',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAgentTitle(String title, double agentWidth) {
    return SizedBox(
      width: agentWidth,
      child: Text(
        title,
        style: _agentTitleStyle,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
