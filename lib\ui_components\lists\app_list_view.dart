import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable list view component that follows the app's design system
class AppListView extends StatelessWidget {
  /// The list of items to display
  final List<Widget> children;

  /// The padding around the list
  final EdgeInsetsGeometry padding;

  /// The spacing between items
  final double spacing;

  /// Whether to show dividers between items
  final bool showDividers;

  /// The color of the dividers
  final Color? dividerColor;

  /// The thickness of the dividers
  final double dividerThickness;

  /// The indent of the dividers
  final double dividerIndent;

  /// The end indent of the dividers
  final double dividerEndIndent;

  /// The scroll physics of the list
  final ScrollPhysics? physics;

  /// The scroll controller of the list
  final ScrollController? controller;

  /// Whether the list should be scrollable
  final bool scrollable;

  /// The background color of the list
  final Color? backgroundColor;

  /// The border radius of the list
  final BorderRadius? borderRadius;

  /// The elevation of the list
  final double elevation;

  /// The border of the list
  final Border? border;

  const AppListView({
    super.key,
    required this.children,
    this.padding = const EdgeInsets.all(AppTheme.spacingM),
    this.spacing = AppTheme.spacingM,
    this.showDividers = false,
    this.dividerColor,
    this.dividerThickness = 1.0,
    this.dividerIndent = 0.0,
    this.dividerEndIndent = 0.0,
    this.physics,
    this.controller,
    this.scrollable = true,
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 0,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    final List<Widget> listItems = [];

    // Add items with dividers if needed
    for (int i = 0; i < children.length; i++) {
      // Add the item
      listItems.add(children[i]);

      // Add spacing or divider after all items except the last one
      if (i < children.length - 1) {
        if (showDividers) {
          listItems.add(
            Divider(
              color: dividerColor,
              thickness: dividerThickness,
              indent: dividerIndent,
              endIndent: dividerEndIndent,
              height: spacing,
            ),
          );
        } else {
          listItems.add(SizedBox(height: spacing));
        }
      }
    }

    // Create the list content
    Widget listContent = ListView(
      padding: padding,
      physics: scrollable ? physics : const NeverScrollableScrollPhysics(),
      controller: controller,
      shrinkWrap: !scrollable,
      children: listItems,
    );

    // Apply container styling if needed
    if (backgroundColor != null ||
        borderRadius != null ||
        elevation > 0 ||
        border != null) {
      return Container(
        decoration: BoxDecoration(
          color: backgroundColor ?? AppTheme.backgroundColor,
          borderRadius: borderRadius,
          border: border,
          boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: elevation * 2,
                    offset: Offset(0, elevation),
                  ),
                ]
              : null,
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.zero,
          child: listContent,
        ),
      );
    }

    return listContent;
  }
}

/// A customizable dynamic list view component with builder pattern
class AppDynamicListView<T> extends StatelessWidget {
  /// The list of items to display
  final List<T> items;

  /// The builder function for each item
  final Widget Function(BuildContext, T, int) itemBuilder;

  /// The builder function for the empty state
  final Widget Function(BuildContext)? emptyBuilder;

  /// The padding around the list
  final EdgeInsetsGeometry padding;

  /// The spacing between items
  final double spacing;

  /// Whether to show dividers between items
  final bool showDividers;

  /// The color of the dividers
  final Color? dividerColor;

  /// The thickness of the dividers
  final double dividerThickness;

  /// The indent of the dividers
  final double dividerIndent;

  /// The end indent of the dividers
  final double dividerEndIndent;

  /// The scroll physics of the list
  final ScrollPhysics? physics;

  /// The scroll controller of the list
  final ScrollController? controller;

  /// Whether the list should be scrollable
  final bool scrollable;

  /// The background color of the list
  final Color? backgroundColor;

  /// The border radius of the list
  final BorderRadius? borderRadius;

  /// The elevation of the list
  final double elevation;

  /// The border of the list
  final Border? border;

  /// Callback when an item is tapped
  final Function(T)? onItemTap;

  const AppDynamicListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.emptyBuilder,
    this.padding = const EdgeInsets.all(AppTheme.spacingM),
    this.spacing = AppTheme.spacingM,
    this.showDividers = false,
    this.dividerColor,
    this.dividerThickness = 1.0,
    this.dividerIndent = 0.0,
    this.dividerEndIndent = 0.0,
    this.physics,
    this.controller,
    this.scrollable = true,
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 0,
    this.border,
    this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    // Show empty state if the list is empty
    if (items.isEmpty && emptyBuilder != null) {
      return emptyBuilder!(context);
    }

    final List<Widget> listItems = [];

    // Add items with dividers if needed
    for (int i = 0; i < items.length; i++) {
      // Add the item with tap handler if needed
      final item = items[i];
      Widget itemWidget = itemBuilder(context, item, i);

      if (onItemTap != null) {
        itemWidget = InkWell(
          onTap: () => onItemTap!(item),
          child: itemWidget,
        );
      }

      listItems.add(itemWidget);

      // Add spacing or divider after all items except the last one
      if (i < items.length - 1) {
        if (showDividers) {
          listItems.add(
            Divider(
              color: dividerColor,
              thickness: dividerThickness,
              indent: dividerIndent,
              endIndent: dividerEndIndent,
              height: spacing,
            ),
          );
        } else {
          listItems.add(SizedBox(height: spacing));
        }
      }
    }

    // Create the list content
    Widget listContent = ListView(
      padding: padding,
      physics: scrollable ? physics : const NeverScrollableScrollPhysics(),
      controller: controller,
      shrinkWrap: !scrollable,
      children: listItems,
    );

    // Apply container styling if needed
    if (backgroundColor != null ||
        borderRadius != null ||
        elevation > 0 ||
        border != null) {
      return Container(
        decoration: BoxDecoration(
          color: backgroundColor ?? AppTheme.backgroundColor,
          borderRadius: borderRadius,
          border: border,
          boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: elevation * 2,
                    offset: Offset(0, elevation),
                  ),
                ]
              : null,
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.zero,
          child: listContent,
        ),
      );
    }

    return listContent;
  }
}

/// A customizable list item component
class AppListItem extends StatelessWidget {
  /// The title of the list item
  final Widget title;

  /// The subtitle of the list item
  final Widget? subtitle;

  /// The leading widget of the list item
  final Widget? leading;

  /// The trailing widget of the list item
  final Widget? trailing;

  /// The padding inside the list item
  final EdgeInsetsGeometry padding;

  /// The background color of the list item
  final Color? backgroundColor;

  /// The border radius of the list item
  final BorderRadius? borderRadius;

  /// The elevation of the list item
  final double elevation;

  /// The border of the list item
  final Border? border;

  /// Callback when the list item is tapped
  final VoidCallback? onTap;

  /// Callback when the list item is long pressed
  final VoidCallback? onLongPress;

  /// Whether to show a highlight when the list item is tapped
  final bool showHighlight;

  const AppListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.padding = const EdgeInsets.all(AppTheme.spacingM),
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 0,
    this.border,
    this.onTap,
    this.onLongPress,
    this.showHighlight = true,
  });

  @override
  Widget build(BuildContext context) {
    final Widget content = Padding(
      padding: padding,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (leading != null) ...[
            leading!,
            SizedBox(width: AppTheme.spacingM),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                title,
                if (subtitle != null) ...[
                  SizedBox(height: AppTheme.spacingXs),
                  subtitle!,
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            SizedBox(width: AppTheme.spacingM),
            trailing!,
          ],
        ],
      ),
    );

    // Apply container styling if needed
    final bool hasDecoration = backgroundColor != null ||
        borderRadius != null ||
        elevation > 0 ||
        border != null;

    Widget listItem = hasDecoration
        ? Container(
            decoration: BoxDecoration(
              color: backgroundColor ?? AppTheme.backgroundColor,
              borderRadius: borderRadius,
              border: border,
              boxShadow: elevation > 0
                  ? [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: elevation * 2,
                        offset: Offset(0, elevation),
                      ),
                    ]
                  : null,
            ),
            child: content,
          )
        : content;

    // Apply tap handlers if needed
    if (onTap != null || onLongPress != null) {
      return InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: borderRadius,
        highlightColor: showHighlight ? null : Colors.transparent,
        splashColor: showHighlight ? null : Colors.transparent,
        child: listItem,
      );
    }

    return listItem;
  }
}
