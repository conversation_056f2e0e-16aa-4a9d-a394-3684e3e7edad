import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:provider/provider.dart';

import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/providers/library_counts_provider.dart';

class ObjectMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String versionNumber;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;
  final DateTime lastUpdated;

  ObjectMobile({
    required this.title,
    this.subtitle = '',
    required this.imageUrl,
    required this.versionNumber,
    required this.lastUpdated,
    this.isDraft = false,
    this.imageWidth = 107.0,
    this.imageHeight = 107.0,
  });

  factory ObjectMobile.fromJson(Map<String, dynamic> json) {
    return ObjectMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String? ?? '',
      imageUrl: json['imageUrl'] as String,
      versionNumber: json['versionNumber'] as String? ?? '',
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 107.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 107.0,
    );
  }
}

class ObjectsLibraryMobile extends StatefulWidget {
  const ObjectsLibraryMobile({
    super.key,
    this.showNavigationBar = true,
    this.searchQuery,
  });

  final bool showNavigationBar;
  final String? searchQuery;

  @override
  State<ObjectsLibraryMobile> createState() => _ObjectsLibraryMobileState();
}

class _ObjectsLibraryMobileState extends State<ObjectsLibraryMobile>
    with TickerProviderStateMixin {
  // Constants
  static const double _objectsPerViewNormal = 2.25;
  static const double _objectsPerViewCompact = 3.0;
  static const double _objectAspectRatio = 1.0; // width / height (square)
  static const double _titleHeight = 32.0;
  static const double _subtitleHeight = 16.0;
  static const double _verticalSpacing = 12.0;
  static const double _objectSpacing = 30.0;
  static const double _horizontalPadding = 24.0;
  static const int _recentObjectsLimit = 10;

  // Text Styles
  static const TextStyle _sectionHeadingStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _objectTitleStyle = TextStyle(
    fontWeight: FontWeight.w500,
    fontSize: 12,
    height: 1.334,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _objectSubtitleStyle = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: 11,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _emptyStateStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey,
    fontFamily: "TiemposText",
  );

  // Data
  List<ObjectMobile> objects = [];
  List<ObjectMobile> recentObjects = [];
  List<ObjectMobile> allObjects = [];
  List<ObjectMobile> filteredRecentObjects = [];
  List<ObjectMobile> filteredAllObjects = [];
  bool isLoading = true;

  // Controllers
  late CarouselController _recentObjectsController;
  late CarouselController _allObjectsController;
  late AnimationController _loadingAnimationController;

  // Only needed when showNavigationBar is true
  FocusNode? _searchFocusNode;
  TextEditingController? _searchController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;
  String _searchQuery = '';

  /// Get objects per view based on keyboard visibility
  double _getObjectsPerView() {
    return _isKeyboardVisible ? _objectsPerViewCompact : _objectsPerViewNormal;
  }

  // JSON string containing object data with lastUpdated dates
  static const String objectsJsonString = '''
{
  "objects": [
    {
      "title": "Customer Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00201",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-21T10:15:00Z"
    },
    {
      "title": "Product Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00202",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-23T14:30:00Z"
    },
    {
      "title": "Address Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00203",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-19T09:45:00Z"
    },
    {
      "title": "Order Object",
      "subtitle": "Business Entity",
      "versionNumber": "V00204",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-25T16:20:00Z"
    },
    {
      "title": "Payment Object",
      "subtitle": "Financial Entity",
      "versionNumber": "V00205",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-16T11:55:00Z"
    },
    {
      "title": "Inventory Object",
      "subtitle": "Business Entity",
      "versionNumber": "V00206",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-22T13:40:00Z"
    },
    {
      "title": "User Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00207",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-20T08:25:00Z"
    },
    {
      "title": "Category Object",
      "subtitle": "Classification Entity",
      "versionNumber": "V00208",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-24T15:10:00Z"
    },
    {
      "title": "Review Object",
      "subtitle": "Content Entity",
      "versionNumber": "V00209",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-18T12:35:00Z"
    },
    {
      "title": "Notification Object",
      "subtitle": "System Entity",
      "versionNumber": "V00210",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-17T17:50:00Z"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadObjects();
  }

  @override
  void didUpdateWidget(ObjectsLibraryMobile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _searchQuery = widget.searchQuery ?? '';
      _filterObjects();
    }
  }

  void _initializeControllers() {
    _recentObjectsController = CarouselController();
    _allObjectsController = CarouselController();
    _searchQuery = widget.searchQuery ?? '';
    if (widget.showNavigationBar) {
      _searchFocusNode = FocusNode();
      _searchController = TextEditingController();
      _searchFocusNode!.addListener(_onSearchFocusChange);
      _searchController!.addListener(_onSearchChanged);
    }
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {});
  }

  void _onSearchChanged() {
    final query = _searchController?.text.toLowerCase().trim() ?? '';
    setState(() {
      _searchQuery = query;
      _filterObjects();
    });
  }

  void _filterObjects() {
    if (_searchQuery.isEmpty) {
      filteredRecentObjects = recentObjects;
      filteredAllObjects = allObjects;
    } else {
      filteredRecentObjects = recentObjects.where((object) {
        return object.title.toLowerCase().contains(_searchQuery) ||
            object.subtitle.toLowerCase().contains(_searchQuery) ||
            object.versionNumber.toLowerCase().contains(_searchQuery);
      }).toList();

      filteredAllObjects = allObjects.where((object) {
        return object.title.toLowerCase().contains(_searchQuery) ||
            object.subtitle.toLowerCase().contains(_searchQuery) ||
            object.versionNumber.toLowerCase().contains(_searchQuery);
      }).toList();
    }
  }

  void _loadObjects() {
    try {
      final data = json.decode(objectsJsonString);
      final loadedObjects = (data['objects'] as List<dynamic>)
          .map((objectJson) =>
              ObjectMobile.fromJson(objectJson as Map<String, dynamic>))
          .toList();

      final originalOrderObjects = List<ObjectMobile>.from(loadedObjects);
      loadedObjects.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      setState(() {
        objects = originalOrderObjects;
        recentObjects = loadedObjects.take(_recentObjectsLimit).toList();
        allObjects = originalOrderObjects;
        filteredRecentObjects = recentObjects;
        filteredAllObjects = allObjects;
        isLoading = false;
      });

      if (mounted) {
        Provider.of<LibraryCountsProvider>(context, listen: false)
            .updateObjectsCount(objects.length);
      }

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        objects = <ObjectMobile>[];
        recentObjects = <ObjectMobile>[];
        allObjects = <ObjectMobile>[];
        isLoading = false;
      });
      debugPrint('Error loading objects: $e');
    }
  }

  @override
  void dispose() {
    _recentObjectsController.dispose();
    _allObjectsController.dispose();
    if (widget.showNavigationBar) {
      _searchController?.removeListener(_onSearchChanged);
      _searchController?.dispose();
      _searchFocusNode?.removeListener(_onSearchFocusChange);
      _searchFocusNode?.dispose();
    }
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildObjectsLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
  }

  Widget _buildObjectsLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: null,
      appBar: null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildObjectsContent(),
        ],
      ),
      floatingActionButton: null,
    );
  }

  Widget _buildObjectsContent() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 0, 16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeading("Recent Objects"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildRecentObjectsCarousel(),
                ),
                const SizedBox(height: 24),
                _buildSectionHeading("All Objects"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildAllObjectsCarousel(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeading(String title) {
    return Text(title, style: _sectionHeadingStyle);
  }

  Widget _buildRecentObjectsCarousel() {
    final objectsToShow =
        _searchQuery.isEmpty ? recentObjects : filteredRecentObjects;
    return _buildCarousel(
      objects: objectsToShow,
      controller: _recentObjectsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No recent objects found'
          : 'No recent objects match your search',
    );
  }

  Widget _buildAllObjectsCarousel() {
    final objectsToShow =
        _searchQuery.isEmpty ? allObjects : filteredAllObjects;
    return _buildCarousel(
      objects: objectsToShow,
      controller: _allObjectsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No objects found'
          : 'No objects match your search',
    );
  }

  Widget _buildCarousel({
    required List<ObjectMobile> objects,
    required CarouselController controller,
    required String emptyMessage,
  }) {
    if (objects.isEmpty) {
      return Center(
        child: Text(emptyMessage, style: _emptyStateStyle),
      );
    }

    final itemExtent = _calculateItemExtent();
    return CarouselView(
      padding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      controller: controller,
      itemExtent: itemExtent,
      enableSplash: false,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      shrinkExtent: itemExtent,
      children: objects.asMap().entries.map((entry) {
        return _buildObjectItem(entry.value, entry.key);
      }).toList(),
    );
  }

  double _calculateItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final objectsPerView = _getObjectsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    return availableWidth / objectsPerView;
  }

  Widget _buildObjectItem(ObjectMobile object, int objectIndex) {
    return GestureDetector(
      onTap: () => _navigateToObjectDetails(objectIndex),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildObjectContent(object),
          ),
        ),
      ),
    );
  }

  Widget _buildObjectContent(ObjectMobile object) {
    final objectDimensions = _calculateObjectDimensions(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildObjectCover(object, objectDimensions),
        const SizedBox(height: 8),
        _buildObjectTitle(object.title, objectDimensions['width']!),
        const SizedBox(height: 4),
        _buildObjectSubtitle(object.versionNumber, objectDimensions['width']!),
      ],
    );
  }

  Map<String, double> _calculateObjectDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final objectsPerView = _getObjectsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    final itemExtent = availableWidth / objectsPerView;

    double objectWidth = itemExtent - _objectSpacing;

    if (_isKeyboardVisible) {
      objectWidth = objectWidth.clamp(85.0, 140.0);
    } else {
      objectWidth = objectWidth.clamp(110.0, 170.0);
    }

    final objectHeight = objectWidth / _objectAspectRatio;

    return {
      'width': objectWidth,
      'height': objectHeight,
      'spacing': _objectSpacing,
    };
  }

  double _calculateCarouselHeight() {
    final objectDimensions = _calculateObjectDimensions(context);
    final objectHeight = objectDimensions['height']!;
    return objectHeight + _verticalSpacing + _titleHeight + _subtitleHeight + 6;
  }

  void _navigateToObjectDetails(int objectIndex) {
    debugPrint('Navigate to object at index: $objectIndex');
  }

  Widget _buildObjectCover(
      ObjectMobile object, Map<String, double> dimensions) {
    final objectWidth = dimensions['width']!;
    final objectHeight = dimensions['height']!;

    return Stack(
      children: [
        Container(
          width: objectWidth,
          height: objectHeight,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(object.imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
        if (object.isDraft)
          Positioned(
            top: objectHeight * 0.08,
            right: objectWidth * 0.08,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'Draft',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildObjectTitle(String title, double objectWidth) {
    return SizedBox(
      width: objectWidth,
      child: Text(
        title,
        style: _objectTitleStyle,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildObjectSubtitle(String versionNumber, double objectWidth) {
    return SizedBox(
      width: objectWidth,
      child: Text(
        versionNumber,
        style: _objectSubtitleStyle,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
