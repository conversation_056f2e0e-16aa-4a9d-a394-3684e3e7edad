// To parse this JSON data, do
//
//     final solutionStatusModel = solutionStatusModelFromJson(jsonString);

import 'dart:convert';

SolutionStatusModel solutionStatusModelFromJson(String str) =>
    SolutionStatusModel.fromJson(json.decode(str));

String solutionStatusModelToJson(SolutionStatusModel data) =>
    json.encode(data.toJson());

class SolutionStatusModel {
  String? sessionId;
  String? conversationId;
  String? userId;
  Status? status;
  bool? enhancedIntelligence;
  DateTime? timestamp;

  SolutionStatusModel({
    this.sessionId,
    this.conversationId,
    this.userId,
    this.status,
    this.enhancedIntelligence,
    this.timestamp,
  });

  SolutionStatusModel copyWith({
    String? sessionId,
    String? conversationId,
    String? userId,
    Status? status,
    bool? enhancedIntelligence,
    DateTime? timestamp,
  }) =>
      SolutionStatusModel(
        sessionId: sessionId ?? this.sessionId,
        conversationId: conversationId ?? this.conversationId,
        userId: userId ?? this.userId,
        status: status ?? this.status,
        enhancedIntelligence: enhancedIntelligence ?? this.enhancedIntelligence,
        timestamp: timestamp ?? this.timestamp,
      );

  factory SolutionStatusModel.fromJson(Map<String, dynamic> json) =>
      SolutionStatusModel(
        sessionId: json["session_id"],
        conversationId: json["conversation_id"],
        userId: json["user_id"],
        status: json["status"] == null ? null : Status.fromJson(json["status"]),
        enhancedIntelligence: json["enhanced_intelligence"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
      );

  Map<String, dynamic> toJson() => {
        "session_id": sessionId,
        "conversation_id": conversationId,
        "user_id": userId,
        "status": status?.toJson(),
        "enhanced_intelligence": enhancedIntelligence,
        "timestamp": timestamp?.toIso8601String(),
      };
}

class Status {
  String? status;
  int? totalNodes;
  int? completedNodes;
  int? mandatoryNodes;
  int? completedMandatory;
  double? currentWeight;
  double? completionPercentage;
  bool? canProceed;
  bool? isComplete;

  Status({
    this.status,
    this.totalNodes,
    this.completedNodes,
    this.mandatoryNodes,
    this.completedMandatory,
    this.currentWeight,
    this.completionPercentage,
    this.canProceed,
    this.isComplete,
  });

  Status copyWith({
    String? status,
    int? totalNodes,
    int? completedNodes,
    int? mandatoryNodes,
    int? completedMandatory,
    double? currentWeight,
    double? completionPercentage,
    bool? canProceed,
    bool? isComplete,
  }) =>
      Status(
        status: status ?? this.status,
        totalNodes: totalNodes ?? this.totalNodes,
        completedNodes: completedNodes ?? this.completedNodes,
        mandatoryNodes: mandatoryNodes ?? this.mandatoryNodes,
        completedMandatory: completedMandatory ?? this.completedMandatory,
        currentWeight: currentWeight ?? this.currentWeight,
        completionPercentage: completionPercentage ?? this.completionPercentage,
        canProceed: canProceed ?? this.canProceed,
        isComplete: isComplete ?? this.isComplete,
      );

  factory Status.fromJson(Map<String, dynamic> json) => Status(
        status: json["status"],
        totalNodes: json["total_nodes"],
        completedNodes: json["completed_nodes"],
        mandatoryNodes: json["mandatory_nodes"],
        completedMandatory: json["completed_mandatory"],
        currentWeight: json["current_weight"]?.toDouble(),
        completionPercentage: json["completion_percentage"]?.toDouble(),
        canProceed: json["can_proceed"],
        isComplete: json["is_complete"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "total_nodes": totalNodes,
        "completed_nodes": completedNodes,
        "mandatory_nodes": mandatoryNodes,
        "completed_mandatory": completedMandatory,
        "current_weight": currentWeight,
        "completion_percentage": completionPercentage,
        "can_proceed": canProceed,
        "is_complete": isComplete,
      };
}
