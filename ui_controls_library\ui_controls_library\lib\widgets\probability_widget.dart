import 'package:flutter/material.dart';

/// A widget that calculates and displays probability-related information.
///
/// This widget allows users to input probability values and perform various
/// probability calculations such as odds conversion, probability of events,
/// and basic probability operations.
class ProbabilityWidget extends StatefulWidget {
  /// Initial probability value (between 0 and 1)
  final double? initialProbability;

  /// Initial odds value (can be decimal or fractional format)
  final String? initialOdds;

  /// The display mode for the widget
  final ProbabilityDisplayMode displayMode;

  /// Whether to show the probability as a percentage
  final bool showAsPercentage;

  /// Whether to show the odds conversion
  final bool showOdds;

  /// Whether to show the probability distribution visualization
  final bool showDistribution;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color for the probability bar
  final Color probabilityColor;

  /// The color for the complementary probability
  final Color complementaryColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the probability value changes
  final Function(double)? onProbabilityChanged;

  /// Callback when the odds value changes
  final Function(String)? onOddsChanged;

  /// Creates a probability widget.
  const ProbabilityWidget({
    super.key,
    this.initialProbability,
    this.initialOdds,
    this.displayMode = ProbabilityDisplayMode.decimal,
    this.showAsPercentage = true,
    this.showOdds = true,
    this.showDistribution = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.probabilityColor = Colors.blue,
    this.complementaryColor = Colors.grey,
    this.width,
    this.height,
    this.onProbabilityChanged,
    this.onOddsChanged,
  });

  @override
  State<ProbabilityWidget> createState() => _ProbabilityWidgetState();
}

/// Enum for probability display modes
enum ProbabilityDisplayMode {
  /// Display as decimal (e.g., 0.25)
  decimal,

  /// Display as percentage (e.g., 25%)
  percentage,

  /// Display as fraction (e.g., 1/4)
  fraction,

  /// Display as odds (e.g., 1:3 or 3:1)
  odds
}

class _ProbabilityWidgetState extends State<ProbabilityWidget> {
  final TextEditingController _probabilityController = TextEditingController();
  final TextEditingController _oddsController = TextEditingController();

  double _probability = 0.5; // Default to 50%
  String _odds = '1:1'; // Default to even odds
  String? _inputError;
  ProbabilityDisplayMode _displayMode = ProbabilityDisplayMode.decimal;

  @override
  void initState() {
    super.initState();

    _displayMode = widget.displayMode;

    // Initialize with provided probability or odds
    if (widget.initialProbability != null) {
      _probability = widget.initialProbability!.clamp(0.0, 1.0);
      _updateOddsFromProbability();
      _updateProbabilityDisplay();
    } else if (widget.initialOdds != null) {
      _odds = widget.initialOdds!;
      _updateProbabilityFromOdds();
      _updateOddsDisplay();
    } else {
      _updateProbabilityDisplay();
      _updateOddsDisplay();
    }
  }

  @override
  void dispose() {
    _probabilityController.dispose();
    _oddsController.dispose();
    super.dispose();
  }

  void _updateProbabilityDisplay() {
    switch (_displayMode) {
      case ProbabilityDisplayMode.decimal:
        _probabilityController.text = _probability.toStringAsFixed(4);
        break;
      case ProbabilityDisplayMode.percentage:
        _probabilityController.text = '${(_probability * 100).toStringAsFixed(2)}%';
        break;
      case ProbabilityDisplayMode.fraction:
        _probabilityController.text = _convertToFraction(_probability);
        break;
      case ProbabilityDisplayMode.odds:
        _probabilityController.text = _odds;
        break;
    }
  }

  void _updateOddsDisplay() {
    _oddsController.text = _odds;
  }

  void _updateProbabilityFromInput() {
    final input = _probabilityController.text.trim();

    try {
      double newProbability;

      switch (_displayMode) {
        case ProbabilityDisplayMode.decimal:
          newProbability = double.parse(input);
          break;
        case ProbabilityDisplayMode.percentage:
          // Remove % sign if present
          final percentStr = input.endsWith('%') ? input.substring(0, input.length - 1) : input;
          newProbability = double.parse(percentStr) / 100;
          break;
        case ProbabilityDisplayMode.fraction:
          // Parse fraction like "1/4"
          if (input.contains('/')) {
            final parts = input.split('/');
            if (parts.length == 2) {
              final numerator = double.parse(parts[0]);
              final denominator = double.parse(parts[1]);
              if (denominator != 0) {
                newProbability = numerator / denominator;
              } else {
                throw Exception('Denominator cannot be zero');
              }
            } else {
              throw Exception('Invalid fraction format');
            }
          } else {
            newProbability = double.parse(input);
          }
          break;
        case ProbabilityDisplayMode.odds:
          // Parse odds like "1:3" or "3:1"
          _odds = input;
          _updateProbabilityFromOdds();
          return;
      }

      // Validate probability is between 0 and 1
      if (newProbability < 0 || newProbability > 1) {
        setState(() {
          _inputError = 'Probability must be between 0 and 1';
        });
        return;
      }

      setState(() {
        _probability = newProbability;
        _inputError = null;
      });

      _updateOddsFromProbability();

      if (widget.onProbabilityChanged != null) {
        widget.onProbabilityChanged!(_probability);
      }
    } catch (e) {
      setState(() {
        _inputError = 'Invalid input format';
      });
    }
  }

  void _updateOddsFromInput() {
    final input = _oddsController.text.trim();

    try {
      _odds = input;
      _updateProbabilityFromOdds();

      setState(() {
        _inputError = null;
      });

      if (widget.onOddsChanged != null) {
        widget.onOddsChanged!(_odds);
      }
    } catch (e) {
      setState(() {
        _inputError = 'Invalid odds format';
      });
    }
  }

  void _updateProbabilityFromOdds() {
    try {
      // Parse odds like "1:3" or "3:1"
      if (_odds.contains(':')) {
        final parts = _odds.split(':');
        if (parts.length == 2) {
          final a = double.parse(parts[0]);
          final b = double.parse(parts[1]);

          if (a >= 0 && b >= 0) {
            // Convert odds to probability
            _probability = b / (a + b);
            _updateProbabilityDisplay();
          } else {
            throw Exception('Odds values must be non-negative');
          }
        } else {
          throw Exception('Invalid odds format');
        }
      } else {
        throw Exception('Invalid odds format');
      }
    } catch (e) {
      setState(() {
        _inputError = 'Invalid odds format';
      });
    }
  }

  void _updateOddsFromProbability() {
    try {
      if (_probability <= 0 || _probability >= 1) {
        // Handle edge cases
        _odds = _probability <= 0 ? '0:1' : '1:0';
      } else {
        // Convert probability to odds
        final p = _probability;
        final q = 1 - p;

        // Simplify the ratio
        final gcd = _findGCD(p, q);
        final simplifiedP = (p / gcd).round();
        final simplifiedQ = (q / gcd).round();

        _odds = '$simplifiedQ:$simplifiedP';
      }

      _updateOddsDisplay();
    } catch (e) {
      setState(() {
        _inputError = 'Error converting probability to odds';
      });
    }
  }

  double _findGCD(double a, double b) {
    // Scale to integers for GCD calculation
    const scale = 10000;
    int intA = (a * scale).round();
    int intB = (b * scale).round();

    // Euclidean algorithm
    while (intB != 0) {
      final temp = intB;
      intB = intA % intB;
      intA = temp;
    }

    return intA / scale;
  }

  String _convertToFraction(double decimal) {
    if (decimal == 0) return '0/1';
    if (decimal == 1) return '1/1';

    // Find a reasonable denominator
    const maxDenominator = 1000;
    int bestDenominator = 1;
    double bestError = double.infinity;

    for (int denominator = 1; denominator <= maxDenominator; denominator++) {
      final numerator = (decimal * denominator).round();
      final error = (numerator / denominator - decimal).abs();

      if (error < bestError) {
        bestError = error;
        bestDenominator = denominator;

        // If error is very small, we've found a good approximation
        if (error < 1e-10) break;
      }
    }

    final numerator = (decimal * bestDenominator).round();

    // Simplify the fraction
    int gcd = _findGCDInt(numerator, bestDenominator);
    return '${numerator ~/ gcd}/${bestDenominator ~/ gcd}';
  }

  int _findGCDInt(int a, int b) {
    while (b != 0) {
      final temp = b;
      b = a % b;
      a = temp;
    }
    return a;
  }

  void _setDisplayMode(ProbabilityDisplayMode mode) {
    setState(() {
      _displayMode = mode;
    });
    _updateProbabilityDisplay();
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Display Mode Selection
          Row(
            children: [
              Text(
                'Display Mode:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: [
                    _buildDisplayModeChip(ProbabilityDisplayMode.decimal, 'Decimal'),
                    _buildDisplayModeChip(ProbabilityDisplayMode.percentage, 'Percentage'),
                    _buildDisplayModeChip(ProbabilityDisplayMode.fraction, 'Fraction'),
                    _buildDisplayModeChip(ProbabilityDisplayMode.odds, 'Odds'),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Probability Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Probability:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _probabilityController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: _displayMode == ProbabilityDisplayMode.decimal
                      ? 'Enter probability (e.g., 0.25)'
                      : _displayMode == ProbabilityDisplayMode.percentage
                          ? 'Enter percentage (e.g., 25%)'
                          : _displayMode == ProbabilityDisplayMode.fraction
                              ? 'Enter fraction (e.g., 1/4)'
                              : 'Enter odds (e.g., 3:1)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _updateProbabilityFromInput();
                },
              ),
            ],
          ),

          // Odds Input (if enabled)
          if (widget.showOdds) ...[
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Odds:',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                    fontWeight: widget.fontWeight,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _oddsController,
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter odds (e.g., 3:1)',
                    hintStyle: TextStyle(
                      color: effectiveTextColor.withAlpha(128),
                      fontSize: widget.fontSize,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  enabled: !widget.isDisabled && !widget.isReadOnly,
                  onChanged: (value) {
                    _updateOddsFromInput();
                  },
                ),
              ],
            ),
          ],

          // Error Message
          if (_inputError != null || widget.errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _inputError ?? widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize - 2,
              ),
            ),
          ],

          // Probability Distribution Visualization
          if (widget.showDistribution) ...[
            const SizedBox(height: 16),
            Text(
              'Probability Distribution:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.borderRadius / 2 - widget.borderWidth / 2),
                child: Row(
                  children: [
                    // Probability bar
                    Expanded(
                      flex: (_probability * 100).round(),
                      child: Container(
                        color: widget.probabilityColor,
                        child: Center(
                          child: _probability >= 0.25 ? Text(
                            widget.showAsPercentage
                                ? '${(_probability * 100).toStringAsFixed(1)}%'
                                : _probability.toStringAsFixed(3),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: widget.fontSize - 2,
                              fontWeight: FontWeight.bold,
                            ),
                          ) : null,
                        ),
                      ),
                    ),
                    // Complementary probability bar
                    Expanded(
                      flex: ((1 - _probability) * 100).round(),
                      child: Container(
                        color: widget.complementaryColor,
                        child: Center(
                          child: (1 - _probability) >= 0.25 ? Text(
                            widget.showAsPercentage
                                ? '${((1 - _probability) * 100).toStringAsFixed(1)}%'
                                : (1 - _probability).toStringAsFixed(3),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: widget.fontSize - 2,
                              fontWeight: FontWeight.bold,
                            ),
                          ) : null,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // Summary
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: widget.backgroundColor.withAlpha(179),
              borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              border: Border.all(
                color: widget.borderColor.withAlpha(128),
                width: widget.borderWidth / 2,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Probability Summary:',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Decimal: ${_probability.toStringAsFixed(4)}',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                ),
                Text(
                  'Percentage: ${(_probability * 100).toStringAsFixed(2)}%',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                ),
                Text(
                  'Fraction: ${_convertToFraction(_probability)}',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                ),
                Text(
                  'Odds: $_odds',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                ),
              ],
            ),
          ),

          // Helper Text
          if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDisplayModeChip(ProbabilityDisplayMode mode, String label) {
    final bool isSelected = _displayMode == mode;

    return ChoiceChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : widget.textColor,
          fontSize: widget.fontSize - 2,
        ),
      ),
      selected: isSelected,
      onSelected: widget.isDisabled || widget.isReadOnly
          ? null
          : (selected) {
              if (selected) {
                _setDisplayMode(mode);
              }
            },
      backgroundColor: widget.backgroundColor,
      selectedColor: widget.probabilityColor,
      disabledColor: Colors.grey.withAlpha(30),
    );
  }
}