import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Types of cards available in the app
enum AppCardType {
  elevated,
  outlined,
  filled,
}

/// A customizable card component that follows the app's design system
class AppCard extends StatelessWidget {
  final Widget child;
  final AppCardType type;
  final Color? backgroundColor;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? borderRadius;
  final double? elevation;
  final VoidCallback? onTap;
  final bool isFullWidth;
  final bool hasShadow;
  final BorderSide? borderSide;

  const AppCard({
    super.key,
    required this.child,
    this.type = AppCardType.elevated,
    this.backgroundColor,
    this.padding,
    this.margin,
    this.borderRadius,
    this.elevation,
    this.onTap,
    this.isFullWidth = true,
    this.hasShadow = true,
    this.borderSide,
  });

  @override
  Widget build(BuildContext context) {
    final cardBorderRadius = borderRadius ?? AppTheme.borderRadiusM;
    final cardElevation = _getElevation();
    final cardBackgroundColor = backgroundColor ?? _getBackgroundColor();
    final cardBorderSide = borderSide ?? _getBorderSide();
    final cardPadding = padding ?? const EdgeInsets.all(AppTheme.spacingM);
    final cardMargin = margin ?? const EdgeInsets.all(0);

    Widget cardContent = Padding(
      padding: cardPadding,
      child: child,
    );

    if (isFullWidth) {
      cardContent = SizedBox(
        width: double.infinity,
        child: cardContent,
      );
    }

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(cardBorderRadius),
        child: _buildCard(
          cardContent,
          cardBorderRadius,
          cardElevation,
          cardBackgroundColor,
          cardBorderSide,
          cardMargin,
        ),
      );
    }

    return _buildCard(
      cardContent,
      cardBorderRadius,
      cardElevation,
      cardBackgroundColor,
      cardBorderSide,
      cardMargin,
    );
  }

  Widget _buildCard(
    Widget content,
    double borderRadius,
    double elevation,
    Color backgroundColor,
    BorderSide borderSide,
    EdgeInsets margin,
  ) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border: borderSide.width > 0 ? Border.fromBorderSide(borderSide) : null,
        boxShadow: hasShadow && elevation > 0
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: elevation * 2,
                  offset: Offset(0, elevation),
                ),
              ]
            : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: content,
      ),
    );
  }

  double _getElevation() {
    if (elevation != null) return elevation!;

    switch (type) {
      case AppCardType.elevated:
        return AppTheme.elevationM;
      case AppCardType.outlined:
      case AppCardType.filled:
        return 0;
    }
  }

  Color _getBackgroundColor() {
    switch (type) {
      case AppCardType.elevated:
      case AppCardType.outlined:
        return AppTheme.backgroundColor;
      case AppCardType.filled:
        return AppTheme.surfaceColor;
    }
  }

  BorderSide _getBorderSide() {
    switch (type) {
      case AppCardType.outlined:
        return const BorderSide(
          color: Color(0xFFE5E5E5),
          width: 1,
        );
      case AppCardType.elevated:
      case AppCardType.filled:
        return BorderSide.none;
    }
  }
}

/// A card with a header, content, and optional footer
class AppCardWithHeader extends StatelessWidget {
  final Widget content;
  final Widget header;
  final Widget? footer;
  final AppCardType type;
  final Color? backgroundColor;
  final EdgeInsets? contentPadding;
  final EdgeInsets? headerPadding;
  final EdgeInsets? footerPadding;
  final EdgeInsets? margin;
  final double? borderRadius;
  final double? elevation;
  final VoidCallback? onTap;
  final bool hasDividers;
  final bool isFullWidth;
  final bool hasShadow;
  final BorderSide? borderSide;

  const AppCardWithHeader({
    super.key,
    required this.content,
    required this.header,
    this.footer,
    this.type = AppCardType.elevated,
    this.backgroundColor,
    this.contentPadding,
    this.headerPadding,
    this.footerPadding,
    this.margin,
    this.borderRadius,
    this.elevation,
    this.onTap,
    this.hasDividers = true,
    this.isFullWidth = true,
    this.hasShadow = true,
    this.borderSide,
  });

  @override
  Widget build(BuildContext context) {
    final headerPaddingValue =
        headerPadding ?? const EdgeInsets.all(AppTheme.spacingM);
    final contentPaddingValue =
        contentPadding ?? const EdgeInsets.all(AppTheme.spacingM);
    final footerPaddingValue =
        footerPadding ?? const EdgeInsets.all(AppTheme.spacingM);

    return AppCard(
      type: type,
      backgroundColor: backgroundColor,
      padding: EdgeInsets.zero,
      margin: margin,
      borderRadius: borderRadius,
      elevation: elevation,
      onTap: onTap,
      isFullWidth: isFullWidth,
      hasShadow: hasShadow,
      borderSide: borderSide,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: headerPaddingValue,
            child: header,
          ),
          if (hasDividers) const Divider(height: 1),
          Padding(
            padding: contentPaddingValue,
            child: content,
          ),
          if (footer != null) ...[
            if (hasDividers) const Divider(height: 1),
            Padding(
              padding: footerPaddingValue,
              child: footer!,
            ),
          ],
        ],
      ),
    );
  }
}
