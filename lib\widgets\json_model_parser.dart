// models.dart - Model classes for all entities in the app

import 'dart:convert';
import 'package:flutter/material.dart';

// Main app data model
class AppData {
  final AppMetrics metrics;
  final Navigation navigation;
  final BooksData books;
  final SolutionsData solutions;
  final ObjectsData objects;
  final GridLayout gridLayout;
  final AppStyles styles;

  AppData({
    required this.metrics,
    required this.navigation,
    required this.books,
    required this.solutions,
    required this.objects,
    required this.gridLayout,
    required this.styles,
  });

  factory AppData.fromJson(Map<String, dynamic> json) {
    return AppData(
      metrics: AppMetrics.fromJson(json['appMetrics']),
      navigation: Navigation.fromJson(json['navigation']),
      books: BooksData.fromJson(json['books']),
      solutions: SolutionsData.fromJson(json['solutions']),
      objects: ObjectsData.fromJson(json['objects']),
      gridLayout: GridLayout.fromJson(json['gridLayout']),
      styles: AppStyles.fromJson(json['styles']),
    );
  }

  static Future<AppData> loadFromJsonFile(String jsonString) async {
    final Map<String, dynamic> jsonData = jsonDecode(jsonString);
    return AppData.fromJson(jsonData);
  }
}

// App metrics (counters shown in the top bar)
class AppMetrics {
  final int booksCount;
  final int solutionsCount;
  final int objectsCount;

  AppMetrics({
    required this.booksCount,
    required this.solutionsCount,
    required this.objectsCount,
  });

  factory AppMetrics.fromJson(Map<String, dynamic> json) {
    return AppMetrics(
      booksCount: json['booksCount'],
      solutionsCount: json['solutionsCount'],
      objectsCount: json['objectsCount'],
    );
  }
}

// Navigation data
class Navigation {
  final List<NavItem> items;

  Navigation({required this.items});

  factory Navigation.fromJson(Map<String, dynamic> json) {
    final List<dynamic> itemsJson = json['items'];
    final List<NavItem> navItems = itemsJson
        .map((item) => NavItem.fromJson(item))
        .toList();

    return Navigation(items: navItems);
  }
}

// Navigation item model
class NavItem {
  final String id;
  final String icon;
  final String route;
  final bool isSelected;

  NavItem({
    required this.id,
    required this.icon,
    required this.route,
    this.isSelected = false,
  });

  factory NavItem.fromJson(Map<String, dynamic> json) {
    return NavItem(
      id: json['id'],
      icon: json['icon'],
      route: json['route'],
      isSelected: json['isSelected'] ?? false,
    );
  }

  // Convert string icon name to IconData
  IconData getIcon() {
    switch (icon) {
      case 'chat_bubble_outline':
        return Icons.chat_bubble_outline;
      case 'apps':
        return Icons.apps;
      case 'work_outline':
        return Icons.work_outline;
      case 'description_outlined':
        return Icons.description_outlined;
      case 'calendar_today':
        return Icons.calendar_today;
      case 'notifications_none':
        return Icons.notifications_none;
      case 'person_outline':
        return Icons.person_outline;
      default:
        return Icons.circle;
    }
  }
}

// Books page data
class BooksData {
  final String pageTitle;
  final String createButtonText;
  final List<Book> items;

  BooksData({
    required this.pageTitle,
    required this.createButtonText,
    required this.items,
  });

  factory BooksData.fromJson(Map<String, dynamic> json) {
    final List<dynamic> itemsJson = json['items'];
    final List<Book> books = itemsJson
        .map((item) => Book.fromJson(item))
        .toList();

    return BooksData(
      pageTitle: json['pageTitle'],
      createButtonText: json['createButtonText'],
      items: books,
    );
  }
}

// Book model
class Book {
  final String id;
  final String title;
  final String categoryType;
  final String imageUrl;
  final bool isDraft;

  Book({
    required this.id,
    required this.title,
    required this.categoryType,
    required this.imageUrl,
    this.isDraft = false,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'],
      title: json['title'],
      categoryType: json['categoryType'],
      imageUrl: json['imageUrl'],
      isDraft: json['isDraft'] ?? false,
    );
  }
}

// Solutions page data
class SolutionsData {
  final String pageTitle;
  final String createButtonText;
  final List<Solution> items;

  SolutionsData({
    required this.pageTitle,
    required this.createButtonText,
    required this.items,
  });

  factory SolutionsData.fromJson(Map<String, dynamic> json) {
    final List<dynamic> itemsJson = json['items'];
    final List<Solution> solutions = itemsJson
        .map((item) => Solution.fromJson(item))
        .toList();

    return SolutionsData(
      pageTitle: json['pageTitle'],
      createButtonText: json['createButtonText'],
      items: solutions,
    );
  }
}

// Solution model
class Solution {
  final String id;
  final String title;
  final String categoryType;
  final String version;

  Solution({
    required this.id,
    required this.title,
    required this.categoryType,
    required this.version,
  });

  factory Solution.fromJson(Map<String, dynamic> json) {
    return Solution(
      id: json['id'],
      title: json['title'],
      categoryType: json['categoryType'],
      version: json['version'],
    );
  }
}

// Objects page data
class ObjectsData {
  final String pageTitle;
  final String createButtonText;
  final List<ObjectItem> items;

  ObjectsData({
    required this.pageTitle,
    required this.createButtonText,
    required this.items,
  });

  factory ObjectsData.fromJson(Map<String, dynamic> json) {
    final List<dynamic> itemsJson = json['items'];
    final List<ObjectItem> objects = itemsJson
        .map((item) => ObjectItem.fromJson(item))
        .toList();

    return ObjectsData(
      pageTitle: json['pageTitle'],
      createButtonText: json['createButtonText'],
      items: objects,
    );
  }
}

// Object model
class ObjectItem {
  final String id;
  final String title;
  final String version;
  final String icon;

  ObjectItem({
    required this.id,
    required this.title,
    required this.version,
    required this.icon,
  });

  factory ObjectItem.fromJson(Map<String, dynamic> json) {
    return ObjectItem(
      id: json['id'],
      title: json['title'],
      version: json['version'],
      icon: json['icon'],
    );
  }

  // Convert string icon name to IconData
  IconData getIcon() {
    switch (icon) {
      case 'person':
        return Icons.person;
      case 'shopping_bag':
        return Icons.shopping_bag;
      case 'location_on':
        return Icons.location_on;
      case 'badge':
        return Icons.badge;
      case 'rocket_launch':
        return Icons.rocket_launch;
      default:
        return Icons.category;
    }
  }
}

// Grid layout configuration
class GridLayout {
  final int columns;
  final double gutterWidth;
  final double columnWidth;
  final int maxItemsPerRow;
  final double cardPadding;
  final double sectionPadding;

  GridLayout({
    required this.columns,
    required this.gutterWidth,
    required this.columnWidth,
    required this.maxItemsPerRow,
    required this.cardPadding,
    required this.sectionPadding,
  });

  factory GridLayout.fromJson(Map<String, dynamic> json) {
    return GridLayout(
      columns: json['columns'],
      gutterWidth: json['gutterWidth'].toDouble(),
      columnWidth: json['columnWidth'].toDouble(),
      maxItemsPerRow: json['maxItemsPerRow'],
      cardPadding: json['cardPadding'].toDouble(),
      sectionPadding: json['sectionPadding'].toDouble(),
    );
  }
}

// App styling information
class AppStyles {
  final ColorStyles colors;
  final RadiusStyles radius;

  AppStyles({
    required this.colors,
    required this.radius,
  });

  factory AppStyles.fromJson(Map<String, dynamic> json) {
    return AppStyles(
      colors: ColorStyles.fromJson(json['colors']),
      radius: RadiusStyles.fromJson(json['radius']),
    );
  }
}

// Color styles
class ColorStyles {
  final Color primary;
  final Color secondary;
  final Color background;
  final Color card;
  final Color border;
  final TextColors text;
  final BadgeColors badge;

  ColorStyles({
    required this.primary,
    required this.secondary,
    required this.background,
    required this.card,
    required this.border,
    required this.text,
    required this.badge,
  });

  factory ColorStyles.fromJson(Map<String, dynamic> json) {
    return ColorStyles(
      primary: _hexToColor(json['primary']),
      secondary: _hexToColor(json['secondary']),
      background: _hexToColor(json['background']),
      card: _hexToColor(json['card']),
      border: _hexToColor(json['border']),
      text: TextColors.fromJson(json['text']),
      badge: BadgeColors.fromJson(json['badge']),
    );
  }

  static Color _hexToColor(String hexString) {
    hexString = hexString.replaceFirst('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    return Color(int.parse(hexString, radix: 16));
  }
}

// Text colors
class TextColors {
  final Color primary;
  final Color secondary;
  final Color muted;

  TextColors({
    required this.primary,
    required this.secondary,
    required this.muted,
  });

  factory TextColors.fromJson(Map<String, dynamic> json) {
    return TextColors(
      primary: ColorStyles._hexToColor(json['primary']),
      secondary: ColorStyles._hexToColor(json['secondary']),
      muted: ColorStyles._hexToColor(json['muted']),
    );
  }
}

// Badge colors
class BadgeColors {
  final Color draft;

  BadgeColors({
    required this.draft,
  });

  factory BadgeColors.fromJson(Map<String, dynamic> json) {
    return BadgeColors(
      draft: ColorStyles._hexToColor(json['draft']),
    );
  }
}

// Border radius styles
class RadiusStyles {
  final double small;
  final double medium;
  final double large;

  RadiusStyles({
    required this.small,
    required this.medium,
    required this.large,
  });

  factory RadiusStyles.fromJson(Map<String, dynamic> json) {
    return RadiusStyles(
      small: json['small'].toDouble(),
      medium: json['medium'].toDouble(),
      large: json['large'].toDouble(),
    );
  }
}
