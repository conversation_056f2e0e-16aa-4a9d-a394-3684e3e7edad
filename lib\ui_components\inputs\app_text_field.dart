import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Types of text fields available in the app
enum AppTextFieldType {
  text,
  email,
  password,
  number,
  multiline,
  search,
}

/// A customizable text field component that follows the app's design system
class AppTextField extends StatefulWidget {
  final String? label;
  final String? placeholder;
  final String? helperText;
  final String? errorText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final AppTextFieldType type;
  final bool autofocus;
  final bool enabled;
  final int? maxLength;
  final int? maxLines;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;
  final Widget? prefix;
  final Widget? suffix;
  final EdgeInsets? contentPadding;
  final double? borderRadius;
  final bool? obscureText;
  final bool noBorder;
  final ValueChanged<bool>? onTogglePasswordVisibility;

  const AppTextField({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.controller,
    this.focusNode,
    this.type = AppTextFieldType.text,
    this.autofocus = false,
    this.enabled = true,
    this.maxLength,
    this.maxLines,
    this.textInputAction,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.prefix,
    this.suffix,
    this.contentPadding,
    this.borderRadius,
    this.obscureText,
    this.noBorder = false,
    this.onTogglePasswordVisibility,
  });

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  late FocusNode _focusNode;
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _controller = widget.controller ?? TextEditingController();
  }

  // Get the current obscureText value
  bool get _obscureText =>
      widget.obscureText ?? widget.type == AppTextFieldType.password;

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      // mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              fontFamily: "Inter",
              color: widget.errorText != null
                  ? AppTheme.errorColor
                  : AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(width: AppTheme.spacingXl),
        ],
        TextField(
          controller: _controller,
          focusNode: _focusNode,
          obscureText: _obscureText,
          autofocus: widget.autofocus,
          enabled: widget.enabled,
          maxLength: widget.maxLength,
          maxLines: _getMaxLines(),
          keyboardType: _getKeyboardType(),
          textInputAction: widget.textInputAction,
          style: AppTheme.bodyMedium,
          onChanged: widget.onChanged,
          onEditingComplete: widget.onEditingComplete,
          onSubmitted: widget.onSubmitted,
          decoration: InputDecoration(
            hintText: widget.placeholder,
            hintStyle: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondaryColor.withAlpha(128),
            ),
            errorText: widget.errorText,
            helperText: widget.helperText,
            filled: true,
            fillColor: widget.enabled
                ? AppTheme.surfaceColor
                : AppTheme.surfaceColor.withAlpha(128),
            contentPadding: widget.contentPadding ??
                const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingM,
                  vertical: AppTheme.spacingM,
                ),
            prefixIcon: widget.prefix != null
                ? Padding(
                    padding: const EdgeInsets.only(
                      left: AppTheme.spacingM,
                      right: AppTheme.spacingXs,
                    ),
                    child: widget.prefix,
                  )
                : _buildPrefixIcon(),
            suffixIcon: widget.suffix != null
                ? Padding(
                    padding: const EdgeInsets.only(
                      left: AppTheme.spacingXs,
                      right: AppTheme.spacingM,
                    ),
                    child: widget.suffix,
                  )
                : (widget.type == AppTextFieldType.password &&
                        widget.suffix == null
                    ? _buildSuffixIcon()
                    : null),
            border: widget.noBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                    borderSide: const BorderSide(
                        color: AppTheme.loginColor, width: 0.5))
                : OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                        widget.borderRadius ?? AppTheme.borderRadiusM),
                    borderSide: const BorderSide(color: AppTheme.loginColor),
                  ),
            enabledBorder: widget.noBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                    borderSide: const BorderSide(
                        color: AppTheme.loginColor, width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                        widget.borderRadius ?? AppTheme.borderRadiusM),
                    borderSide: const BorderSide(color: AppTheme.primaryColor),
                  ),
            focusedBorder: widget.noBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                    borderSide: const BorderSide(
                        color: AppTheme.loginColor, width: 0.5))
                : OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                        widget.borderRadius ?? AppTheme.borderRadiusM),
                    borderSide: const BorderSide(color: AppTheme.primaryColor),
                  ),
            errorBorder: widget.noBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                    borderSide: BorderSide.none,
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                        widget.borderRadius ?? AppTheme.borderRadiusM),
                    borderSide: const BorderSide(color: AppTheme.errorColor),
                  ),
            focusedErrorBorder: widget.noBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                    borderSide: BorderSide.none,
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                        widget.borderRadius ?? AppTheme.borderRadiusM),
                    borderSide: const BorderSide(color: AppTheme.errorColor),
                  ),
            disabledBorder: widget.noBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                     borderSide: const BorderSide(
                        color: AppTheme.loginColor, width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                        widget.borderRadius ?? AppTheme.borderRadiusM),
                      
                  ),
          ),
        )
      ],
    );
  }

  Widget? _buildPrefixIcon() {
    if (widget.type == AppTextFieldType.search) {
      return Icon(
        Icons.search,
        color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
        size: 20,
      );
    }
    return null;
  }

  Widget? _buildSuffixIcon() {
    if (widget.type == AppTextFieldType.password) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off : Icons.visibility,
          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
          size: 20,
        ),
        onPressed: widget.onTogglePasswordVisibility != null
            ? () {
                // Call the callback with the new desired state (opposite of current)
                widget.onTogglePasswordVisibility!(!_obscureText);
              }
            : null,
      );
    } else if (widget.type == AppTextFieldType.search &&
        _controller.text.isNotEmpty) {
      return IconButton(
        icon: Icon(
          Icons.clear,
          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
          size: 20,
        ),
        onPressed: () {
          _controller.clear();
          if (widget.onChanged != null) {
            widget.onChanged!('');
          }
        },
      );
    }
    return null;
  }

  TextInputType _getKeyboardType() {
    switch (widget.type) {
      case AppTextFieldType.email:
        return TextInputType.emailAddress;
      case AppTextFieldType.password:
        return TextInputType.visiblePassword;
      case AppTextFieldType.number:
        return TextInputType.number;
      case AppTextFieldType.multiline:
        return TextInputType.multiline;
      case AppTextFieldType.search:
        return TextInputType.text;
      case AppTextFieldType.text:
        return TextInputType.text;
    }
  }

  int? _getMaxLines() {
    if (widget.maxLines != null) {
      return widget.maxLines;
    }

    switch (widget.type) {
      case AppTextFieldType.multiline:
        return 5;
      case AppTextFieldType.password:
        return 1;
      default:
        return 1;
    }
  }
}
