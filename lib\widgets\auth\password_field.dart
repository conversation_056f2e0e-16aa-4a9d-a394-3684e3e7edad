import 'package:flutter/material.dart';
import '../../ui_components/theme/app_theme.dart';
import '../../utils/logger.dart';

class PasswordField extends StatefulWidget {
  final String? label;
  final String? placeholder;
  final TextEditingController controller;
  final bool enabled;
  final TextInputAction? textInputAction;
  final String? Function(String?)? validator;
  final ValueChanged<String>? onChanged;
  final bool isRequired;
  final bool noBorder;
  final Widget? prefix;

  const PasswordField(
      {super.key,
      this.label,
      this.placeholder,
      required this.controller,
      this.enabled = true,
      this.textInputAction,
      this.validator,
      this.onChanged,
      this.isRequired = false,
      this.noBorder = false,
      this.prefix});

  @override
  State<PasswordField> createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> {
  bool _obscurePassword = true;

  void _togglePasswordVisibility() {
    Logger.info('Before toggle - Password visibility: $_obscurePassword');
    setState(() {
      _obscurePassword = !_obscurePassword;
      Logger.info('After toggle - Password visibility: $_obscurePassword');
    });
  }

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      validator: widget.validator,
      initialValue: widget.controller.text,
      builder: (FormFieldState<String> state) {
        return Column(
           crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.label != null) ...[
              Text(
                widget.isRequired ? '${widget.label!} *' : widget.label!,
                style: AppTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                   fontFamily: "Inter",
                  fontSize: 14,
                  color: state.hasError
                      ? AppTheme.errorColor
                      : AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM ),
            ],
            Container(
              child: TextField(
                controller: widget.controller,
                obscureText: _obscurePassword,
                enabled: widget.enabled,
                textInputAction: widget.textInputAction,
                style: AppTheme.bodyMedium,
                onChanged: (value) {
                  state.didChange(value);
                  if (widget.onChanged != null) {
                    widget.onChanged!(value);
                  }
                },
                decoration: InputDecoration(
                  hintText: widget.placeholder,
                  hintStyle: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondaryColor.withAlpha(128),
                  ),
                  errorText: state.hasError ? state.errorText : null,
                  filled: true,
                  fillColor: widget.enabled
                      ? AppTheme.surfaceColor
                      : AppTheme.surfaceColor.withAlpha(128),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingM,
                    vertical: AppTheme.spacingM,
                  ),
                  prefix: widget.prefix,
                  // prefixIcon: widget.prefix ??
                  //     Padding(
                  //       padding: const EdgeInsets.only(
                  //         left: AppTheme.spacingM,
                  //         right: AppTheme.spacingXs,
                  //       ),
                  //       child: const Icon(
                  //         Icons.lock_outlined,
                  //         color: Colors.black,
                  //       ),
                  //     ),
                  suffixIcon: Padding(
                    padding: const EdgeInsets.only(
                      left: AppTheme.spacingXs,
                     // right: AppTheme.spacingM,
                    ),
                    child: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                          size: 18,
                        // color: Theme.of(context)
                        //     .colorScheme
                        //     .onSurface
                        //     .withAlpha(178),
                        color: Colors.black,
                      ),
                      onPressed: _togglePasswordVisibility,
                    ),
                  ),
                  border: widget.noBorder
                      ? OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide: const BorderSide(color: AppTheme.loginColor,width:0.5)
                        )
                      : OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide:
                              const BorderSide(color: AppTheme.primaryColor),
                        ),
                  enabledBorder: widget.noBorder
                      ? OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide: const BorderSide(color: AppTheme.loginColor,width:0.5)
                        )
                      : OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide:
                              const BorderSide(color: AppTheme.primaryColor),
                        ),
                  focusedBorder: widget.noBorder
                      ? OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide: const BorderSide(color: AppTheme.loginColor,width:0.5)
                        )
                      : OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide:
                              const BorderSide(color: AppTheme.primaryColor),
                        ),
                  errorBorder: widget.noBorder
                      ? OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide: BorderSide.none,
                        )
                      : OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide:
                              const BorderSide(color: AppTheme.errorColor),
                        ),
                  focusedErrorBorder: widget.noBorder
                      ? OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide: const BorderSide(color: AppTheme.loginColor),
                        )
                      : OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide:
                              const BorderSide(color: AppTheme.errorColor),
                        ),
                  disabledBorder: widget.noBorder
                      ? OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide: BorderSide.none,
                        )
                      : OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          borderSide: BorderSide.none,
                        ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
