import 'package:flutter/material.dart';

class BooksPages extends StatefulWidget {
  const BooksPages({super.key});

  @override
  State<BooksPages> createState() => _BooksPagesState();
}

class _BooksPagesState extends State<BooksPages> {
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Side Navigation
          // SideNavigation(selectedIndex: 1),
          
          // Main Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top Bar with stats and back button
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    border: Border(bottom: BorderSide(color: Colors.grey.shade300))
                  ),
                  child: Row(
                    children: [
                      const Spacer(),
                      _buildStatItem(Icons.book, '12 Books'),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.article_outlined, '35 Solution'),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.category, '102 Object'),
                      const SizedBox(width: 32),
                      IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.blue),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                
                // Page Header with search and create
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Text(
                        'My Books',
                        style: TextStyle(
                          fontSize: 24, 
                          fontWeight: FontWeight.bold
                        ),
                      ),
                      const Spacer(),
                      // Search box
                      Container(
                        width: 300,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search',
                            border: InputBorder.none,
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.tune),
                              onPressed: () {},
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Create Book Button
                      ElevatedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.add),
                        label: const Text('Create Book'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.black,
                          elevation: 1,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Grid of Books - matching 12-column grid from design
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // Calculate columns based on available width
                        // Default to 5 columns for larger screens
                        int crossAxisCount = 5;
                        
                        // Desktop layout with 5 columns
                        if (constraints.maxWidth >= 1400) {
                          crossAxisCount = 5;
                        } 
                        // Medium width with 4 columns
                        else if (constraints.maxWidth >= 1100) {
                          crossAxisCount = 4;
                        }
                        // Smaller screens with 3 columns 
                        else if (constraints.maxWidth >= 800) {
                          crossAxisCount = 3;
                        }
                        // Mobile layout with 2 columns
                        else if (constraints.maxWidth >= 600) {
                          crossAxisCount = 2;
                        }
                        // Very small screens with 1 column
                        else {
                          crossAxisCount = 1;
                        }
                        
                        return GridView.builder(
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: crossAxisCount,
                            childAspectRatio: 0.85,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: booksList.length,
                          itemBuilder: (context, index) {
                            final book = booksList[index];
                            return BookCard(book: book);
                          },
                        );
                      }
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStatItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey.shade700),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(color: Colors.grey.shade700, fontSize: 14),
        ),
      ],
    );
  }
}

class BookCard extends StatelessWidget {
  final Book book;
  
  const BookCard({super.key, required this.book});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        // clipBehavior: Drag.hardEdge,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Book Image
            Expanded(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Image 
                  Image.network(
                    book.imageUrl,
                    fit: BoxFit.cover,
                  ),
                  
                  // Draft Label if needed
                  if (book.isDraft)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'Draft',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            // Book Info
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    book.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    book.categoryType,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SideNavigation extends StatelessWidget {
  final int selectedIndex;
  
  const SideNavigation({super.key, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 16),
          // Logo
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                'nb',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Chat Icon
          _buildNavItem(context, Icons.chat_bubble_outline, 0),
          
          // App Icon (Selected)
          _buildNavItem(context, Icons.apps, 1, isSelected: selectedIndex == 1),
          
          // Briefcase Icon
          _buildNavItem(context, Icons.work_outline, 2),
          
          // Document Icon
          _buildNavItem(context, Icons.description_outlined, 3),
          
          // Calendar Icon
          _buildNavItem(context, Icons.calendar_today, 4),
          
          // Notification Icon
          _buildNavItem(context, Icons.notifications_none, 5),
          
          const Spacer(),
          
          // Profile Icon
          _buildNavItem(context, Icons.person_outline, 6),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
  
  Widget _buildNavItem(BuildContext context, IconData icon, int index, {bool isSelected = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade100 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: () {},
        icon: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey.shade600,
        ),
      ),
    );
  }
}

// Model class for Book
class Book {
  final String id;
  final String title;
  final String categoryType;
  final String imageUrl;
  final bool isDraft;
  
  Book({
    required this.id,
    required this.title,
    required this.categoryType,
    required this.imageUrl,
    this.isDraft = false,
  });
}

// Sample data for books
final List<Book> booksList = [
  Book(
    id: '1',
    title: 'Ecommerce',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/3498db/FFFFFF/png?text=Ecommerce',
    isDraft: false,
  ),
  Book(
    id: '2',
    title: 'Fashion & Apparel',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Fashion',
    isDraft: false,
  ),
  Book(
    id: '3',
    title: 'Financial Advisory',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/3498db/FFFFFF/png?text=Finance',
    isDraft: false,
  ),
  Book(
    id: '4',
    title: 'Home Rentals',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Rentals',
    isDraft: true,
  ),
  Book(
    id: '5',
    title: 'Online Grocery',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/3498db/FFFFFF/png?text=Grocery',
    isDraft: false,
  ),
  Book(
    id: '6',
    title: 'Courier & Logistics',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Logistics',
    isDraft: false,
  ),
  Book(
    id: '7',
    title: 'Automotive',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/3498db/FFFFFF/png?text=Automotive',
    isDraft: true,
  ),
  Book(
    id: '8',
    title: 'Fitness & Wellness',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Fitness',
    isDraft: false,
  ),
  Book(
    id: '9',
    title: 'Real Estate',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/3498db/FFFFFF/png?text=Real+Estate',
    isDraft: false,
  ),
  Book(
    id: '10',
    title: 'Restaurant & Cafe',
    categoryType: 'B2C',
    imageUrl: 'https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Restaurant',
    isDraft: false,
  ),
];
