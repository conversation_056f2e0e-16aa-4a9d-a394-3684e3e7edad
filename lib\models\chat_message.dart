import 'package:flutter/material.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/models/solution/solution_session_model.dart';

/// A simple message model for chat
// Define a simple message model for our chat
class ChatMessage {
  String content;
  final bool isUser;
  final DateTime timestamp;
  final Widget? customContent;
  List<String>? reasoningData; // Added reasoningData property
  FileUploadOcrResponse?
      fileData; // Added fileData property for file uploads (non-final to allow modification)
  Widget? customText;
  SolutionSessionModel? brdDocument;
  bool? isReasoningDataExpanded;
  bool? hasAnimatedThinking;
  bool? hasNSLTypingCompleted;
  bool isTypingComplete;
  List<String>? followUpSuggestions; // Added follow-up suggestions property

  ChatMessage({
    required this.content,
    required this.isUser,
    DateTime? timestamp,
    this.customContent,
    this.reasoningData,
    this.fileData,
    this.customText,
    this.brdDocument,
    this.isReasoningDataExpanded,
    this.hasAnimatedThinking = false,
    this.hasNSLTypingCompleted = false,
    this.isTypingComplete = false,
    this.followUpSuggestions,
  }) : timestamp = timestamp ?? DateTime.now();
}
