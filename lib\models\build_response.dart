class BuildResponse {
  final String conversationId;
  final String prescriptiveText;
  final String yamlOutput;
  final String javaCode;
  final String validationStatus;
  final String? validationError;

  BuildResponse({
    required this.conversationId,
    required this.prescriptiveText,
    required this.yamlOutput,
    required this.javaCode,
    required this.validationStatus,
    this.validationError,
  });

  factory BuildResponse.fromJson(Map<String, dynamic> json) {
    return BuildResponse(
      conversationId: json['conversation_id'] ?? '',
      prescriptiveText: json['prescriptive_text'] ?? '',
      yamlOutput: json['yaml_output'] ?? '',
      javaCode: json['java_code'] ?? '',
      validationStatus: json['validation_status'] ?? '',
      validationError: json['validation_error'],
    );
  }
}
