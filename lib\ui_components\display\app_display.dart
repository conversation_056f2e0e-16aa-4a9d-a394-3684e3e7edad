import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable text component that follows the app's design system
class AppText extends StatelessWidget {
  /// The text to display
  final String text;

  /// The style of the text
  final TextStyle? style;

  /// The color of the text
  final Color? color;

  /// The font weight of the text
  final FontWeight? fontWeight;

  /// The font size of the text
  final double? fontSize;

  /// The text alignment
  final TextAlign? textAlign;

  /// The text overflow behavior
  final TextOverflow? overflow;

  /// The maximum number of lines
  final int? maxLines;

  /// Whether to show an ellipsis when overflowing
  final bool? softWrap;

  /// The text variant
  final AppTextVariant variant;

  const AppText(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
    this.variant = AppTextVariant.body,
  });

  /// Creates a heading large text
  const AppText.headingLarge(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
  }) : variant = AppTextVariant.headingLarge;

  /// Creates a heading medium text
  const AppText.headingMedium(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
  }) : variant = AppTextVariant.headingMedium;

  /// Creates a heading small text
  const AppText.headingSmall(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
  }) : variant = AppTextVariant.headingSmall;

  /// Creates a body large text
  const AppText.bodyLarge(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
  }) : variant = AppTextVariant.bodyLarge;

  /// Creates a body medium text
  const AppText.bodyMedium(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
  }) : variant = AppTextVariant.bodyMedium;

  /// Creates a body small text
  const AppText.bodySmall(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
  }) : variant = AppTextVariant.bodySmall;

  /// Creates a caption text
  const AppText.caption(
    this.text, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.softWrap,
  }) : variant = AppTextVariant.caption;

  @override
  Widget build(BuildContext context) {
    TextStyle baseStyle;

    // Get the base style based on the variant
    switch (variant) {
      case AppTextVariant.headingLarge:
        baseStyle = AppTheme.headingLarge;
        break;
      case AppTextVariant.headingMedium:
        baseStyle = AppTheme.headingMedium;
        break;
      case AppTextVariant.headingSmall:
        baseStyle = AppTheme.headingSmall;
        break;
      case AppTextVariant.bodyLarge:
        baseStyle = AppTheme.bodyLarge;
        break;
      case AppTextVariant.bodyMedium:
        baseStyle = AppTheme.bodyMedium;
        break;
      case AppTextVariant.bodySmall:
        baseStyle = AppTheme.bodySmall;
        break;
      case AppTextVariant.caption:
        baseStyle = AppTheme.bodySmall.copyWith(
          fontSize: 10.0,
          height: 1.2,
        );
        break;
      case AppTextVariant.body:
        baseStyle = AppTheme.bodyMedium;
        break;
    }

    // Apply custom style properties
    TextStyle finalStyle = style ?? baseStyle;

    if (color != null) {
      finalStyle = finalStyle.copyWith(color: color);
    }

    if (fontWeight != null) {
      finalStyle = finalStyle.copyWith(fontWeight: fontWeight);
    }

    if (fontSize != null) {
      finalStyle = finalStyle.copyWith(fontSize: fontSize);
    }

    return Text(
      text,
      style: finalStyle,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      softWrap: softWrap,
    );
  }
}

/// The text variants available in the app
enum AppTextVariant {
  headingLarge,
  headingMedium,
  headingSmall,
  bodyLarge,
  bodyMedium,
  bodySmall,
  caption,
  body,
}

/// A customizable image component that follows the app's design system
class AppImage extends StatelessWidget {
  /// The image source
  final ImageProvider image;

  /// The width of the image
  final double? width;

  /// The height of the image
  final double? height;

  /// The fit of the image
  final BoxFit fit;

  /// The border radius of the image
  final BorderRadius? borderRadius;

  /// The border of the image
  final Border? border;

  /// The background color of the image
  final Color? backgroundColor;

  /// The foreground color of the image
  final Color? foregroundColor;

  /// The elevation of the image
  final double elevation;

  /// The placeholder widget to show while loading
  final Widget? placeholder;

  /// The error widget to show on error
  final Widget? errorWidget;

  /// Callback when the image is tapped
  final VoidCallback? onTap;

  const AppImage({
    super.key,
    required this.image,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.border,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.placeholder,
    this.errorWidget,
    this.onTap,
  });

  /// Creates an image from a network URL
  factory AppImage.network(
    String url, {
    Key? key,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Border? border,
    Color? backgroundColor,
    Color? foregroundColor,
    double elevation = 0,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onTap,
    Map<String, String>? headers,
    double scale = 1.0,
  }) {
    return AppImage(
      key: key,
      image: NetworkImage(url, headers: headers, scale: scale),
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      border: border,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      placeholder: placeholder,
      errorWidget: errorWidget,
      onTap: onTap,
    );
  }

  /// Creates an image from an asset
  factory AppImage.asset(
    String assetName, {
    Key? key,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Border? border,
    Color? backgroundColor,
    Color? foregroundColor,
    double elevation = 0,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onTap,
    AssetBundle? bundle,
    String? package,
  }) {
    return AppImage(
      key: key,
      image: AssetImage(assetName, bundle: bundle, package: package),
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      border: border,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      placeholder: placeholder,
      errorWidget: errorWidget,
      onTap: onTap,
    );
  }

  /// Creates a circular image
  factory AppImage.circle({
    Key? key,
    required ImageProvider image,
    double size = 48.0,
    BoxFit fit = BoxFit.cover,
    Border? border,
    Color? backgroundColor,
    Color? foregroundColor,
    double elevation = 0,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onTap,
  }) {
    return AppImage(
      key: key,
      image: image,
      width: size,
      height: size,
      fit: fit,
      borderRadius: BorderRadius.circular(size / 2),
      border: border,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      placeholder: placeholder,
      errorWidget: errorWidget,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = Image(
      image: image,
      width: width,
      height: height,
      fit: fit,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        } else {
          return placeholder ??
              const Center(
                child: CircularProgressIndicator(),
              );
        }
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            Container(
              width: width,
              height: height,
              color: Colors.grey.shade200,
              child: Icon(
                Icons.broken_image,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
              ),
            );
      },
    );

    // Apply container styling if needed
    if (borderRadius != null ||
        border != null ||
        backgroundColor != null ||
        elevation > 0) {
      imageWidget = Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: borderRadius,
          border: border,
          color: backgroundColor,
          boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withAlpha(25),
                    blurRadius: elevation * 2,
                    offset: Offset(0, elevation),
                  ),
                ]
              : null,
        ),
        clipBehavior: Clip.antiAlias,
        child: imageWidget,
      );
    }

    // Apply foreground color if needed
    if (foregroundColor != null) {
      imageWidget = ColorFiltered(
        colorFilter: ColorFilter.mode(
          foregroundColor!.withAlpha(128),
          BlendMode.srcATop,
        ),
        child: imageWidget,
      );
    }

    // Apply tap handler if needed
    if (onTap != null) {
      imageWidget = InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

/// A customizable icon component that follows the app's design system
class AppIcon extends StatelessWidget {
  /// The icon data
  final IconData icon;

  /// The size of the icon
  final double size;

  /// The color of the icon
  final Color? color;

  /// The background color of the icon
  final Color? backgroundColor;

  /// The border radius of the icon
  final BorderRadius? borderRadius;

  /// The border of the icon
  final Border? border;

  /// The padding around the icon
  final EdgeInsetsGeometry padding;

  /// The elevation of the icon
  final double elevation;

  /// Callback when the icon is tapped
  final VoidCallback? onTap;

  const AppIcon({
    super.key,
    required this.icon,
    this.size = 24.0,
    this.color,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.padding = EdgeInsets.zero,
    this.elevation = 0,
    this.onTap,
  });

  /// Creates a circular icon
  factory AppIcon.circle({
    Key? key,
    required IconData icon,
    double size = 24.0,
    double containerSize = 40.0,
    Color? color,
    Color? backgroundColor,
    Border? border,
    double elevation = 0,
    VoidCallback? onTap,
  }) {
    return AppIcon(
      key: key,
      icon: icon,
      size: size,
      color: color,
      backgroundColor: backgroundColor,
      borderRadius: BorderRadius.circular(containerSize / 2),
      border: border,
      padding: EdgeInsets.all((containerSize - size) / 2),
      elevation: elevation,
      onTap: onTap,
    );
  }

  /// Creates a rounded icon
  factory AppIcon.rounded({
    Key? key,
    required IconData icon,
    double size = 24.0,
    double containerSize = 40.0,
    Color? color,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    Border? border,
    double elevation = 0,
    VoidCallback? onTap,
  }) {
    return AppIcon(
      key: key,
      icon: icon,
      size: size,
      color: color,
      backgroundColor: backgroundColor,
      borderRadius:
          borderRadius ?? BorderRadius.circular(AppTheme.borderRadiusM),
      border: border,
      padding: EdgeInsets.all((containerSize - size) / 2),
      elevation: elevation,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget iconWidget = Icon(
      icon,
      size: size,
      color: color ?? Theme.of(context).colorScheme.onSurface,
    );

    // Apply container styling if needed
    if (backgroundColor != null ||
        borderRadius != null ||
        border != null ||
        padding != EdgeInsets.zero ||
        elevation > 0) {
      iconWidget = Container(
        padding: padding,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: borderRadius,
          border: border,
          boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withAlpha(25),
                    blurRadius: elevation * 2,
                    offset: Offset(0, elevation),
                  ),
                ]
              : null,
        ),
        child: iconWidget,
      );
    }

    // Apply tap handler if needed
    if (onTap != null) {
      iconWidget = InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: iconWidget,
      );
    }

    return iconWidget;
  }
}

/// A customizable circular progress indicator component
class AppCircularProgressIndicator extends StatelessWidget {
  /// The value of the progress indicator (0.0 to 1.0)
  final double? value;

  /// The color of the progress indicator
  final Color? color;

  /// The color of the background
  final Color? backgroundColor;

  /// The stroke width of the progress indicator
  final double strokeWidth;

  /// The size of the progress indicator
  final double size;

  /// Whether to show a label in the center
  final bool showLabel;

  /// The style of the label
  final TextStyle? labelStyle;

  const AppCircularProgressIndicator({
    super.key,
    this.value,
    this.color,
    this.backgroundColor,
    this.strokeWidth = 4.0,
    this.size = 48.0,
    this.showLabel = false,
    this.labelStyle,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularProgressIndicator(
            value: value,
            strokeWidth: strokeWidth,
            valueColor:
                color != null ? AlwaysStoppedAnimation<Color>(color!) : null,
            backgroundColor: backgroundColor,
          ),
          if (showLabel && value != null)
            Text(
              '${(value! * 100).toInt()}%',
              style: labelStyle ??
                  AppTheme.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
        ],
      ),
    );
  }
}

/// A customizable linear progress indicator component
class AppLinearProgressIndicator extends StatelessWidget {
  /// The value of the progress indicator (0.0 to 1.0)
  final double? value;

  /// The color of the progress indicator
  final Color? color;

  /// The color of the background
  final Color? backgroundColor;

  /// The height of the progress indicator
  final double height;

  /// The border radius of the progress indicator
  final BorderRadius? borderRadius;

  /// Whether to show a label
  final bool showLabel;

  /// The position of the label
  final AppLinearProgressLabelPosition labelPosition;

  /// The style of the label
  final TextStyle? labelStyle;

  /// The padding around the progress indicator
  final EdgeInsetsGeometry padding;

  const AppLinearProgressIndicator({
    super.key,
    this.value,
    this.color,
    this.backgroundColor,
    this.height = 4.0,
    this.borderRadius,
    this.showLabel = false,
    this.labelPosition = AppLinearProgressLabelPosition.right,
    this.labelStyle,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final Widget progressBar = ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
      child: LinearProgressIndicator(
        value: value,
        minHeight: height,
        valueColor:
            color != null ? AlwaysStoppedAnimation<Color>(color!) : null,
        backgroundColor: backgroundColor,
      ),
    );

    if (!showLabel || value == null) {
      return Padding(
        padding: padding,
        child: progressBar,
      );
    }

    final Widget label = Text(
      '${(value! * 100).toInt()}%',
      style: labelStyle ??
          AppTheme.bodySmall.copyWith(
            fontWeight: FontWeight.w500,
          ),
    );

    switch (labelPosition) {
      case AppLinearProgressLabelPosition.left:
        return Padding(
          padding: padding,
          child: Row(
            children: [
              label,
              const SizedBox(width: AppTheme.spacingS),
              Expanded(child: progressBar),
            ],
          ),
        );
      case AppLinearProgressLabelPosition.right:
        return Padding(
          padding: padding,
          child: Row(
            children: [
              Expanded(child: progressBar),
              const SizedBox(width: AppTheme.spacingS),
              label,
            ],
          ),
        );
      case AppLinearProgressLabelPosition.top:
        return Padding(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              label,
              const SizedBox(height: AppTheme.spacingXs),
              progressBar,
            ],
          ),
        );
      case AppLinearProgressLabelPosition.bottom:
        return Padding(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              progressBar,
              const SizedBox(height: AppTheme.spacingXs),
              label,
            ],
          ),
        );
    }
  }
}

/// The position of the label in a linear progress indicator
enum AppLinearProgressLabelPosition {
  left,
  right,
  top,
  bottom,
}
