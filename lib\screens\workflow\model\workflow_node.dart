import 'dart:ui' show Offset;

enum NodeType { start, approval, email, end }

class WorkflowNode {
  final String id;
  final String title;
  final NodeType type;
  final Offset position;
  final String actionLabel;

  WorkflowNode({
    required this.id,
    required this.title,
    required this.type,
    required this.position,
    required this.actionLabel,
  });

  WorkflowNode copyWith({
    String? id,
    String? title,
    NodeType? type,
    Offset? position,
    String? actionLabel,
  }) {
    return WorkflowNode(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      position: position ?? this.position,
      actionLabel: actionLabel ?? this.actionLabel,
    );
  }
}