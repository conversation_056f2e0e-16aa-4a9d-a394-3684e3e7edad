import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';

/// AppTheme provides consistent theming for the entire application
class AppTheme {
  // Primary colors from app_colors.dart
  static const Color primaryColor = AppColors.primaryIndigo;
  static const Color secondaryColor = AppColors.primaryIndigoLight;
  static const Color primaryDarkColor = AppColors.primaryIndigoDark;

  // Light theme colors
  static const Color backgroundLight = AppColors.backgroundLight;
  static const Color surfaceLight = AppColors.surfaceLight;
  static const Color textPrimaryLight = AppColors.textPrimaryLight;
  static const Color textSecondaryLight = AppColors.textSecondaryLight;

  // Dark theme colors
  static const Color backgroundDark = AppColors.backgroundDark;
  static const Color surfaceDark = AppColors.surfaceDark;
  static const Color textPrimaryDark = AppColors.textPrimaryDark;
  static const Color textSecondaryDark = AppColors.textSecondaryDark;
  static const Color subtleIndigoDark = AppColors.subtleIndigoDark;
  static const Color subtleIndigoSurface = AppColors.subtleIndigoSurface;

  // Default colors (for backward compatibility)
  static const Color backgroundColor = AppColors.backgroundLight;
  static const Color surfaceColor = AppColors.surfaceLight;
  static const Color textPrimaryColor = AppColors.textPrimaryLight;
  static const Color textSecondaryColor = AppColors.textSecondaryLight;
  static const Color textLightColor = Colors.white;

  // Status colors
  static const Color successColor = AppColors.success;
  static const Color errorColor = AppColors.error;
  static const Color warningColor = AppColors.warning;
  static const Color infoColor = AppColors.info;

  static const Color dividerColor = Color(0xFFE5E5E5);
  static const Color loginColor = Color(0xFFA2A2A2);

  // Spacing
  static const double spacingXs = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;

  // Border radius
  static const double borderRadiusS = 4.0;
  static const double borderRadiusM = 8.0;
  static const double borderRadiusL = 16.0;
  static const double borderRadiusXl = 24.0;
  static const double borderRadiusCircular = 100.0;
  static const double radiusM = 10;

  // Elevation
  static const double elevationS = 1.0;
  static const double elevationM = 2.0;
  static const double elevationL = 4.0;
  static const double elevationXl = 8.0;

  // Animation durations
  static const Duration durationShort = Duration(milliseconds: 150);
  static const Duration durationMedium = Duration(milliseconds: 300);
  static const Duration durationLong = Duration(milliseconds: 500);

  // Text styles
  // SF Pro Text for headlines and UI elements
  static const TextStyle headingLarge = TextStyle(
    fontFamily: 'SFProText',
    fontSize: 28.0,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
    height: 1.3,
  );

  static const TextStyle headingMedium = TextStyle(
    fontFamily: 'SFProText',
    fontSize: 24.0,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
    height: 1.3,
  );

  static const TextStyle headingSmall = TextStyle(
    fontFamily: 'SFProText',
    fontSize: 20.0,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
    height: 1.3,
  );

  // Tiempos Text for body content
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'TiemposText',
    fontSize: 16.0,
    fontWeight: FontWeight.normal,
    color: textPrimaryColor,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'TiemposText',
    fontSize: 15.0,
    fontWeight: FontWeight.normal,
    color: textPrimaryColor,
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'TiemposText',
    fontSize: 14.0,
    fontWeight: FontWeight.normal,
    color: textSecondaryColor,
    height: 1.5,
  );

  // SF Pro Text for buttons and labels
  static const TextStyle buttonText = TextStyle(
    fontFamily: 'SFProText',
    fontSize: 16.0,
    fontWeight: FontWeight.w500,
    color: textLightColor,
    height: 1.5,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: 'SFProText',
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
    color: textPrimaryColor,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: 'SFProText',
    fontSize: 12.0,
    fontWeight: FontWeight.w500,
    color: textSecondaryColor,
  );

  // Get ThemeData for MaterialApp (Light Theme)
  static ThemeData getThemeData() {
    return lightTheme();
  }

  // Build text theme
  static TextTheme _buildTextTheme(TextTheme base, bool isDark) {
    Color primaryTextColor = isDark ? textPrimaryDark : textPrimaryLight;
    Color secondaryTextColor = isDark ? textSecondaryDark : textSecondaryLight;

    return base.copyWith(
      // SF Pro Text for headlines and UI elements
      displayLarge: base.displayLarge!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: primaryTextColor,
      ),
      displayMedium: base.displayMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: primaryTextColor,
      ),
      displaySmall: base.displaySmall!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      headlineMedium: base.headlineMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      titleLarge: base.titleLarge!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      titleMedium: base.titleMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      titleSmall: base.titleSmall!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: secondaryTextColor,
      ),

      // Tiempos Text for body content
      bodyLarge: base.bodyLarge!.copyWith(
        fontFamily: 'TiemposText',
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: primaryTextColor,
        height: 1.5,
      ),
      bodyMedium: base.bodyMedium!.copyWith(
        fontFamily: 'TiemposText',
        fontSize: 15,
        fontWeight: FontWeight.w400,
        color: primaryTextColor,
        height: 1.5,
      ),
      bodySmall: base.bodySmall!.copyWith(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: secondaryTextColor,
        height: 1.5,
      ),

      // SF Pro Text for buttons and labels
      labelLarge: base.labelLarge!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      labelMedium: base.labelMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      labelSmall: base.labelSmall!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: secondaryTextColor,
      ),
    );
  }

  // Light Theme
  static ThemeData lightTheme() {
    final ThemeData base = ThemeData.light();
    return base.copyWith(
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        onPrimary: Colors.white,
        secondary: secondaryColor,
        onSecondary: Colors.white,
        surface: surfaceLight,
        onSurface: textPrimaryLight,
        // Use surfaceContainer instead of background (deprecated)
        surfaceContainer: backgroundLight,
        error: errorColor,
        onError: Colors.white,
      ),
      textTheme: _buildTextTheme(base.textTheme, false),
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontFamily: 'SFProText',
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      scaffoldBackgroundColor: backgroundLight,
      cardTheme: CardTheme(
        color: surfaceLight,
        elevation: elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
        ),
      ),
      buttonTheme: ButtonThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
        ),
        buttonColor: primaryColor,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusM),
          ),
          padding: EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          textStyle: TextStyle(
            fontFamily: 'SFProText',
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor),
          padding: EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusM),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingS),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceLight,
        contentPadding: EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingM),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide(color: primaryColor),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide(color: errorColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide(color: errorColor),
        ),
      ),
      dividerTheme: DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: spacingM,
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: secondaryColor,
        contentTextStyle: bodyMedium.copyWith(color: Colors.white),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
        ),
      ),
    );
  }

  // Dark Theme
  static ThemeData darkTheme() {
    final ThemeData base = ThemeData.dark();
    return base.copyWith(
      colorScheme: ColorScheme.dark(
        primary: subtleIndigoDark,
        onPrimary: Colors.white,
        secondary: subtleIndigoSurface,
        onSecondary: Colors.white,
        surface: surfaceDark,
        onSurface: textPrimaryDark,
        // Use surfaceContainer instead of background (deprecated)
        surfaceContainer: backgroundDark,
        error: errorColor,
        onError: Colors.white,
      ),
      textTheme: _buildTextTheme(base.textTheme, true),
      appBarTheme: AppBarTheme(
        backgroundColor: subtleIndigoSurface,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontFamily: 'SFProText',
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      scaffoldBackgroundColor: backgroundDark,
      cardTheme: CardTheme(
        color: surfaceDark,
        elevation: elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
        ),
      ),
      buttonTheme: ButtonThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
        ),
        buttonColor: subtleIndigoDark,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: subtleIndigoDark,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusM),
          ),
          padding: EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          textStyle: TextStyle(
            fontFamily: 'SFProText',
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: subtleIndigoDark,
          side: BorderSide(color: subtleIndigoDark),
          padding: EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusM),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: subtleIndigoDark,
          padding: EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingS),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceDark,
        contentPadding: EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingM),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide(color: subtleIndigoDark),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide(color: errorColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
          borderSide: BorderSide(color: errorColor),
        ),
      ),
      dividerTheme: DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: spacingM,
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: subtleIndigoSurface,
        contentTextStyle: bodyMedium.copyWith(color: Colors.white),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusM),
        ),
      ),
    );
  }
}
