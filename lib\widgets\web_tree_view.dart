import 'dart:math';
import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import '../models/tree_node.dart';

/// Controller for programmatically controlling the tree view
class WebTreeViewController {
  final Function(String) expandNode;
  final Function(String) collapseNode;
  final Function(String) toggleNode;
  final Function(String) scrollToNode;
  final Function() expandAll;
  final Function() collapseAll;

  WebTreeViewController({
    required this.expandNode,
    required this.collapseNode,
    required this.toggleNode,
    required this.scrollToNode,
    required this.expandAll,
    required this.collapseAll,
  });
}

class WebTreeView extends StatefulWidget {
  final TreeStructure treeStructure;
  final Function(TreeNode)? onNodeTap;
  final Function(WebTreeViewController)? onControllerReady;
  final ScrollController? scrollController;
  final bool initiallyExpandAll;

  const WebTreeView({
    super.key,
    required this.treeStructure,
    this.onNodeTap,
    this.onControllerReady,
    this.scrollController,
    this.initiallyExpandAll = true,
  });

  @override
  State<WebTreeView> createState() => _WebTreeViewState();
}

class _WebTreeViewState extends State<WebTreeView>
    with SingleTickerProviderStateMixin {
  final Set<String> _expandedNodes = {};
  // Track which parallel components are expanded for each node
  final Map<String, Set<String>> _expandedParallelNodes = {};
  late AnimationController _animationController;
  late ScrollController _scrollController;
  final Map<String, GlobalKey> _nodeKeys = {};
  late WebTreeViewController _controller;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // Initialize scroll controller
    _scrollController = widget.scrollController ?? ScrollController();

    // Initialize controller
    _controller = WebTreeViewController(
      expandNode: _expandNode,
      collapseNode: _collapseNode,
      toggleNode: _toggleNode,
      scrollToNode: _scrollToNode,
      expandAll: _expandAll,
      collapseAll: _collapseAll,
    );

    // Notify parent when controller is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onControllerReady != null) {
        widget.onControllerReady!(_controller);
      }
    });

    // Initially expand all nodes if specified
    if (widget.initiallyExpandAll) {
      final rootNode = widget.treeStructure.getRootNode();
      if (rootNode != null) {
        _expandAllNodes(rootNode);
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  // Controller methods
  void _expandNode(String nodeId) {
    if (!_expandedNodes.contains(nodeId)) {
      setState(() {
        _expandedNodes.add(nodeId);
      });
      _animationController.forward();
    }
  }

  void _collapseNode(String nodeId) {
    if (_expandedNodes.contains(nodeId)) {
      _animationController.reverse().then((_) {
        setState(() {
          _expandedNodes.remove(nodeId);
        });
      });
    }
  }

  void _scrollToNode(String nodeId) {
    if (_nodeKeys.containsKey(nodeId)) {
      final context = _nodeKeys[nodeId]!.currentContext;
      if (context != null) {
        Scrollable.ensureVisible(
          context,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _expandAll() {
    final rootNode = widget.treeStructure.getRootNode();
    if (rootNode != null) {
      setState(() {
        _expandAllNodes(rootNode);
      });
    }
  }

  void _collapseAll() {
    setState(() {
      _expandedNodes.clear();
    });
  }

  // Calculate the vertical line position based on parent node having Alt tag
  double _getVerticalLinePosition(TreeNode node, int level) {
    // Get the parent node
    final parentNode = node.parent != null
        ? widget.treeStructure.getNodeById(node.parent!)
        : null;

    // Check if parent has Alt tag and adjust position accordingly
    if (parentNode != null &&
        parentNode.metadata != null &&
        parentNode.metadata!.containsKey('alt')) {
      // Position at the circle of parent with Alt tag
      return (parentNode.metadata!.containsKey('alt') ? 50.0 : 10.0) +
          ((level - 2) * 24.0);
    }

    // Default position
    return ((level - 1) * 24.0);
  }

  // Build rich text with highlighted parallel components
  Widget _buildRichTextWithParallel(
    String text,
    List<dynamic> parlNodes,
    String nodeId,
  ) {
    // If text is empty, return empty container
    if (text.isEmpty) {
      return const SizedBox.shrink();
    }

    // Create a map of parallel components by name for quick lookup
    final Map<String, Map<String, dynamic>> parlMap = {};
    for (final parlNode in parlNodes) {
      final parlName = parlNode['parlName'] as String;
      parlMap[parlName] = parlNode;
    }

    // Split text into lines for better handling
    final lines = text.split('\n');

    // Build a column of rich text spans for each line
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: lines.map((line) {
        // Create text spans for this line
        final List<InlineSpan> spans = [];

        // Current position in the line
        int currentPos = 0;

        // Process each parallel component in this line
        for (final parlNode in parlNodes) {
          final parlName = parlNode['parlName'] as String;
          final parlId = parlNode['parlId'] as String;
          final parlColor = parlNode['parlColor'] as String;

          // Find the position of this parallel component in the line
          final int startPos = line.indexOf(parlName, currentPos);
          if (startPos >= 0) {
            // Add text before this parallel component
            if (startPos > currentPos) {
              spans.add(TextSpan(
                text: line.substring(currentPos, startPos),
                style: const TextStyle(fontSize: 14.0),
              ));
            }

            // Convert hex color to Color
            Color bgColor;
            try {
              bgColor = Color(int.parse(parlColor.replaceFirst('#', '0xff')));
            } catch (e) {
              bgColor = Colors.grey.shade200;
            }

            // Check if this parallel component is expanded
            final bool isExpanded =
                _expandedParallelNodes[nodeId]?.contains(parlId) ?? false;

            // Add the parallel component with highlighting
            spans.add(WidgetSpan(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    if (isExpanded) {
                      _expandedParallelNodes[nodeId]?.remove(parlId);
                    } else {
                      _expandedParallelNodes[nodeId]?.add(parlId);
                    }
                  });
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Parl.",
                      style: const TextStyle(
                        fontSize: 10.0,
                      ),
                    ),
                    SizedBox(
                      width: AppSpacing.xxs,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4.0, vertical: 2.0),
                      decoration: BoxDecoration(
                        color: bgColor,
                        borderRadius: BorderRadius.circular(4.0),
                        border: Border.all(
                          color: bgColor.withAlpha(100),
                          width: 1.0,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            parlName,
                            style: const TextStyle(
                              fontSize: 14.0,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4.0),
                          Icon(
                            isExpanded
                                ? Icons.keyboard_arrow_up
                                : Icons.keyboard_arrow_down,
                            size: 14.0,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ));

            // Update current position
            currentPos = startPos + parlName.length;
          }
        }

        // Add remaining text after all parallel components
        if (currentPos < line.length) {
          spans.add(TextSpan(
            text: line.substring(currentPos),
            style: const TextStyle(fontSize: 14.0),
          ));
        }

        // If no parallel components were found, just add the whole line
        if (spans.isEmpty) {
          spans.add(TextSpan(
            text: line,
            style: const TextStyle(fontSize: 14.0),
          ));
        }

        // Return rich text for this line
        return Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 14.0,
              ),
              children: spans,
            ),
          ),
        );
      }).toList(),
    );
  }

  void _expandAllNodes(TreeNode node) {
    _expandedNodes.add(node.id);
    final children = widget.treeStructure.getChildrenOf(node.id);
    for (final child in children) {
      _expandAllNodes(child);
    }
  }

  void _toggleNode(String nodeId) {
    if (_expandedNodes.contains(nodeId)) {
      _collapseNode(nodeId);
    } else {
      _expandNode(nodeId);
    }
  }

  // Calculate the appropriate width based on tree depth and complexity
  double _calculateTreeWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final rootNode = widget.treeStructure.getRootNode();

    if (rootNode == null) {
      return screenWidth;
    }

    // Calculate the maximum depth of the tree
    int maxDepth = _calculateMaxDepth(rootNode);

    // Base width calculation on depth and screen size
    // Each level adds more width requirements
    double baseWidth = screenWidth;

    // For deeper trees, we need more width
    if (maxDepth > 3) {
      // Add more width for each level beyond 3
      baseWidth += (maxDepth - 3) * 300.0;
    }

    // Ensure minimum width is at least the screen width
    return max(baseWidth, screenWidth);
  }

  // Calculate the maximum depth of the tree
  int _calculateMaxDepth(TreeNode node, [int currentDepth = 0]) {
    final children = widget.treeStructure.getChildrenOf(node.id);
    if (children.isEmpty) {
      return currentDepth;
    }

    int maxChildDepth = currentDepth;
    for (final child in children) {
      final childDepth = _calculateMaxDepth(child, currentDepth + 1);
      maxChildDepth = max(maxChildDepth, childDepth);
    }

    return maxChildDepth;
  }

  @override
  Widget build(BuildContext context) {
    final rootNode = widget.treeStructure.getRootNode();
    if (rootNode == null) {
      return const Center(child: Text('No tree data available'));
    }

    // Calculate the appropriate width based on tree structure
    final treeWidth = _calculateTreeWidth(context);

    // Use a container with width constraints to ensure the tree takes full width
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Container(
            // Set width based on calculated tree width
            constraints: BoxConstraints(
              minWidth: treeWidth,
            ),
            width: treeWidth,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: _buildTreeNode(rootNode, 0, true),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTreeNode(TreeNode node, int level, bool isFirstChild) {
    final children = widget.treeStructure.getChildrenOf(node.id);
    final hasChildren = children.isNotEmpty;
    final isExpanded = _expandedNodes.contains(node.id);

    // For the root node, just render the title and its children
    if (node.type == 'root') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              node.label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18.0,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          ...children.asMap().entries.map((entry) {
            final index = entry.key;
            final child = entry.value;
            return _buildTreeNode(child, level, index == 0);
          }),
        ],
      );
    }

    // Create a key for this node for scrolling
    if (!_nodeKeys.containsKey(node.id)) {
      _nodeKeys[node.id] = GlobalKey();
    }

    // For all other nodes
    return Stack(
      key: _nodeKeys[node.id],
      children: [
        // Vertical line from parent to this node and extending to next level
        if (level > 0)
          Positioned(
            left: _getVerticalLinePosition(node, level),
            top: isFirstChild ? 16.0 : 0, // Adjusted to align with circle
            bottom: 0,
            width: 1,
            child: CustomPaint(
              painter: DashedLinePainter(
                color: Colors.grey.shade700,
                dashLength: 3,
                dashGap: 3,
              ),
            ),
          ),

        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Horizontal connector line and node indicator
                SizedBox(
                  width: ((node.metadata != null &&
                                  node.metadata!.containsKey('alt')
                              ? 60.0
                              : 35) *
                          level)
                      .toDouble(),
                  height: 30.0,
                  child: level > 0
                      ? Stack(
                          children: [
                            // Horizontal line to node
                            Positioned(
                              left: _getVerticalLinePosition(node, level),
                              top: 20.0,
                              width: 10.0,
                              height: 1,
                              child: CustomPaint(
                                painter: DashedLinePainter(
                                  color: Colors.grey.shade700,
                                  dashLength: 3,
                                  dashGap: 3,
                                  isHorizontal: true,
                                ),
                              ),
                            ),
                            // Alt label if present
                            if (node.metadata != null &&
                                node.metadata!.containsKey('alt'))
                              Positioned(
                                left: _getVerticalLinePosition(node, level) +
                                    15.0,
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      right: 8.0, top: 8.0),
                                  child: Text(
                                    'Alt. ${node.metadata!['alt'].split('.')[0]}',
                                    style: TextStyle(
                                      fontSize: 12.0,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ),
                              ),
                            // Circle node indicator at the end of the horizontal line
                            Positioned(
                              left: _getVerticalLinePosition(node, level) +
                                  (node.metadata != null &&
                                          node.metadata!.containsKey('alt')
                                      ? 50.0
                                      : 10.0),
                              top: (node.metadata != null &&
                                      node.metadata!.containsKey('alt')
                                  ? 14.0
                                  : 16.0),
                              child: Container(
                                width: 8.0,
                                height: 8.0,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.grey.shade700,
                                    width: 1.0,
                                  ),
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        )
                      : null,
                ),

                // Node content
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (hasChildren) {
                        _toggleNode(node.id);
                      }
                      if (widget.onNodeTap != null) {
                        widget.onNodeTap!(node);
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Text(
                        node.label,
                        style: TextStyle(
                          fontSize: node.type == 'milestone' ? 16.0 : 14.0,
                          fontWeight: node.type == 'milestone'
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Child nodes with animation
            if (hasChildren)
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                height: isExpanded ? null : 0,
                curve: Curves.easeInOut,
                clipBehavior: Clip.antiAlias,
                decoration: const BoxDecoration(),
                child: AnimatedOpacity(
                  opacity: isExpanded ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: children.asMap().entries.map((entry) {
                        final index = entry.key;
                        final child = entry.value;

                        // Display nodes with parallel components
                        if (child.metadata != null &&
                            child.metadata!.containsKey('parl')) {
                          final parlNodes =
                              child.metadata!['parl'] as List<dynamic>;

                          // Track expanded state for each parallel component
                          final String nodeId = child.id;
                          if (!_expandedParallelNodes.containsKey(nodeId)) {
                            _expandedParallelNodes[nodeId] = <String>{};
                          }

                          // Create a stack to include vertical line for parallel nodes
                          return Stack(
                            children: [
                              // Vertical line for parallel nodes
                              Positioned(
                                left:
                                    _getVerticalLinePosition(child, level + 1),
                                top: 16.0, // Start at the circle
                                bottom: 0,
                                width: 1,
                                child: CustomPaint(
                                  painter: DashedLinePainter(
                                    color: Colors.grey.shade700,
                                    dashLength: 3,
                                    dashGap: 3,
                                  ),
                                ),
                              ),
                              // Content column
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Row for horizontal line, circle indicator, and node content
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Horizontal connector line and node indicator
                                        SizedBox(
                                          width: ((child.metadata != null &&
                                                          child.metadata!
                                                              .containsKey(
                                                                  'alt')
                                                      ? 100.0 // For Alt tag
                                                      : 35.0) *
                                                  (level + 1))
                                              .toDouble(),
                                          height: 30.0,
                                          child: Stack(
                                            children: [
                                              // Horizontal line to node
                                              Positioned(
                                                left: _getVerticalLinePosition(
                                                        child, level + 1) -
                                                    0, // Start further left
                                                top: 20.0,
                                                width:
                                                    12.0, // Standard line length
                                                height: 1,
                                                child: CustomPaint(
                                                  painter: DashedLinePainter(
                                                    color: Colors.grey.shade700,
                                                    dashLength: 3,
                                                    dashGap: 3,
                                                    isHorizontal: true,
                                                  ),
                                                ),
                                              ),
                                              // Circle node indicator
                                              Positioned(
                                                left: _getVerticalLinePosition(
                                                        child, level + 1) +
                                                    12,
                                                top: 16.0,
                                                child: Container(
                                                  width: 8.0,
                                                  height: 8.0,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color:
                                                          Colors.grey.shade700,
                                                      width: 1.0,
                                                    ),
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        // Node content
                                        Expanded(
                                          child: Padding(
                                            padding:
                                                const EdgeInsets.only(top: 8.0),
                                            child: _buildRichTextWithParallel(
                                              child.label,
                                              parlNodes,
                                              nodeId,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                    // Display children of expanded parallel components
                                    for (final parlNode in parlNodes)
                                      Builder(builder: (context) {
                                        final parlId =
                                            parlNode['parlId'] as String;
                                        final parlChildren =
                                            parlNode['parlChildren']
                                                    as List<dynamic>? ??
                                                [];

                                        // Only show children if this parallel component is expanded
                                        if (_expandedParallelNodes[nodeId]!
                                                .contains(parlId) &&
                                            parlChildren.isNotEmpty) {
                                          return Padding(
                                            padding: EdgeInsets.only(
                                                left: _getVerticalLinePosition(
                                                        child, level + 1) +
                                                    24.0, // Align with the vertical line
                                                right: 16.0,
                                                bottom: 16.0,
                                                top: 8.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                // Children list
                                                ...parlChildren
                                                    .map((parlChild) {
                                                  final parlSubName =
                                                      parlChild['parlSubName']
                                                          as String;
                                                  final parlSubId =
                                                      parlChild['parlSubId']
                                                          as String;
                                                  final parlSubColor =
                                                      parlChild['parlColor']
                                                          as String;

                                                  // Convert hex color to Color
                                                  Color subBgColor;
                                                  try {
                                                    subBgColor = Color(
                                                        int.parse(parlSubColor
                                                            .replaceFirst(
                                                                '#', '0xff')));
                                                  } catch (e) {
                                                    subBgColor =
                                                        Colors.grey.shade200;
                                                  }

                                                  return Container(
                                                    margin:
                                                        const EdgeInsets.only(
                                                            bottom: 8.0),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 12.0,
                                                        vertical: 8.0),
                                                    decoration: BoxDecoration(
                                                      color: subBgColor,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4.0),
                                                      border: Border.all(
                                                        color: subBgColor
                                                            .withAlpha(100),
                                                        width: 1.0,
                                                      ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          parlSubId,
                                                          style:
                                                              const TextStyle(
                                                            fontSize: 12.0,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color:
                                                                Colors.black54,
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            width: 8.0),
                                                        Expanded(
                                                          child: Text(
                                                            parlSubName,
                                                            style:
                                                                const TextStyle(
                                                                    fontSize:
                                                                        14.0),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                }),
                                              ],
                                            ),
                                          );
                                        } else {
                                          return const SizedBox.shrink();
                                        }
                                      }),
                                  ],
                                ),
                              ),
                            ],
                          );
                        }

                        return _buildTreeNode(child, level + 1, index == 0);
                      }).toList(),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }
}

// Custom painter for dashed lines
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;
  final bool isHorizontal;

  DashedLinePainter({
    required this.color,
    required this.dashLength,
    required this.dashGap,
    this.isHorizontal = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final path = Path();

    if (isHorizontal) {
      double startX = 0;
      while (startX < size.width) {
        path.moveTo(startX, 0);
        path.lineTo(startX + dashLength, 0);
        startX += dashLength + dashGap;
      }
    } else {
      double startY = 0;
      while (startY < size.height) {
        path.moveTo(0, startY);
        path.lineTo(0, startY + dashLength);
        startY += dashLength + dashGap;
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
