import 'package:flutter/foundation.dart';

/// A simple logger utility class with enhanced functionality
class Logger {
  // Private constructor to prevent instantiation
  Logger._();

  // Logger configuration
  static bool _isInitialized = false;
  static bool _isDebug = true; // Set to false in production
  static bool _includeTimestamp = true;
  static bool _includeStackTrace = false;

  /// Initialize the logger with configuration options
  static void init({
    bool isDebug = true,
    bool includeTimestamp = true,
    bool includeStackTrace = false,
  }) {
    _isDebug = isDebug;
    _includeTimestamp = includeTimestamp;
    _includeStackTrace = includeStackTrace;
    _isInitialized = true;

    info(
        'Logger initialized with isDebug=$isDebug, includeTimestamp=$includeTimestamp, includeStackTrace=$includeStackTrace');
  }

  /// Format a log message with optional timestamp
  static String _formatMessage(String level, String message) {
    final timestamp =
        _includeTimestamp ? '[${DateTime.now().toIso8601String()}] ' : '';
    return '$timestamp$level: $message';
  }

  /// Log an info message
  static void info(String message) {
    if (!_isInitialized) {
      // Auto-initialize with defaults if not initialized
      init();
    }

    if (_isDebug) {
      debugPrint(_formatMessage('INFO', message));
    }
  }

  /// Log an error message with optional stack trace
  static void error(String message, {StackTrace? stackTrace}) {
    if (!_isInitialized) {
      init();
    }

    if (_isDebug) {
      debugPrint(_formatMessage('ERROR', message));

      if (stackTrace != null && _includeStackTrace) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }

  /// Log a warning message
  static void warning(String message) {
    if (!_isInitialized) {
      init();
    }

    if (_isDebug) {
      debugPrint(_formatMessage('WARNING', message));
    }
  }

  /// Log a debug message
  static void debug(String message) {
    if (!_isInitialized) {
      init();
    }

    if (_isDebug) {
      debugPrint(_formatMessage('DEBUG', message));
    }
  }

  /// Log a message at a specific level
  static void log(String level, String message) {
    if (!_isInitialized) {
      init();
    }

    if (_isDebug) {
      debugPrint(_formatMessage(level, message));
    }
  }
}
