// To parse this JSON data, do
//
//     final entitiesData = entitiesDataFromJson(jsonString);

import 'dart:convert';

import 'package:nsl/models/workflow/entity_manual_response_model.dart';
import 'package:nsl/models/workflow/relation_ship_properties.dart';

EntitiesData entitiesDataFromJson(String str) =>
    EntitiesData.fromJson(json.decode(str));

String entitiesDataToJson(EntitiesData data) => json.encode(data.toJson());

class EntitiesData {
  List<EntityGroup>? entityGroups;
  final SystemInfo? systemInfo;

  // Maps to track selected entity versions
  Map<String, String> selectedVersions = {};
  Map<String, String> selectedVersionsForChat = {};

  EntitiesData({
    this.entityGroups,
    this.systemInfo,
    Map<String, String>? selectedVersions,
    Map<String, String>? selectedVersionsForChat,
  })  : selectedVersions = selectedVersions ?? {},
        selectedVersionsForChat = selectedVersionsForChat ?? {};

  EntitiesData copyWith({
    List<EntityGroup>? entityGroups,
    SystemInfo? systemInfo,
    Map<String, String>? selectedVersions,
    Map<String, String>? selectedVersionsForChat,
  }) =>
      EntitiesData(
        entityGroups: entityGroups ?? this.entityGroups,
        systemInfo: systemInfo ?? this.systemInfo,
        selectedVersions: selectedVersions ?? this.selectedVersions,
        selectedVersionsForChat:
            selectedVersionsForChat ?? this.selectedVersionsForChat,
      );

  // Method to set selected version for an entity group
  void setSelectedVersion(String groupId, String version) {
    selectedVersions[groupId] = version;
  }

  // Method to get selected version for an entity group
  String getSelectedVersion(String groupId) {
    // If no version is selected, try to get the first version from the group
    if (!selectedVersions.containsKey(groupId) && entityGroups != null) {
      for (final group in entityGroups!) {
        if (group.id == groupId &&
            group.enitiyVersions != null &&
            group.enitiyVersions!.isNotEmpty) {
          selectedVersions[groupId] = group.enitiyVersions!.first;
          return group.enitiyVersions!.first;
        }
      }
    }

    return selectedVersions[groupId] ?? '';
  }

  // Method to set selected version for chat
  void setSelectedVersionForChat(String groupId, String version) {
    selectedVersionsForChat[groupId] = version;
  }

  // Method to get selected version for chat
  String getSelectedVersionForChat(String groupId) {
    return selectedVersionsForChat[groupId] ?? getSelectedVersion(groupId);
  }

  // Method to initialize selected versions
  void initializeSelectedVersions() {
    if (entityGroups == null) return;

    for (final group in entityGroups!) {
      if (group.enitiyVersions != null && group.enitiyVersions!.isNotEmpty) {
        // Only set if not already set
        if (!selectedVersions.containsKey(group.id)) {
          selectedVersions[group.id ?? ''] = group.enitiyVersions!.first;
        }
      }
    }
  }

  // Method to update entity checked state
  void updateEntityCheckedState(String entityId, bool checked) {
    if (entityGroups == null) return;

    for (int i = 0; i < entityGroups!.length; i++) {
      final group = entityGroups![i];
      if (group.entities == null) continue;

      for (int j = 0; j < group.entities!.length; j++) {
        final entity = group.entities![j];
        if (entity.id == entityId) {
          // Create a new entity with updated checked state
          final updatedEntity = entity.copyWith(checked: checked);

          // Create a new list of entities with the updated entity
          final updatedEntities = List<Entity>.from(group.entities!);
          updatedEntities[j] = updatedEntity;

          // Create a new group with the updated entities
          final updatedGroup = group.copyWith(entities: updatedEntities);

          // Update the group in the entityGroups list
          entityGroups![i] = updatedGroup;

          // If unchecking an entity, make sure the group is also unchecked
          if (!checked && group.checked == true) {
            updateGroupCheckedState(group.id ?? '', false);
          }

          return;
        }
      }
    }
  }

  // Method to update entity expanded state
  void updateEntityExpandedState(String entityId, bool expanded) {
    if (entityGroups == null) return;

    for (int i = 0; i < entityGroups!.length; i++) {
      final group = entityGroups![i];
      if (group.entities == null) continue;

      for (int j = 0; j < group.entities!.length; j++) {
        final entity = group.entities![j];
        if (entity.id == entityId) {
          // Create a new entity with updated expanded state
          final updatedEntity = entity.copyWith(expanded: expanded);

          // Create a new list of entities with the updated entity
          final updatedEntities = List<Entity>.from(group.entities!);
          updatedEntities[j] = updatedEntity;

          // Create a new group with the updated entities
          final updatedGroup = group.copyWith(entities: updatedEntities);

          // Update the group in the entityGroups list
          entityGroups![i] = updatedGroup;

          return;
        }
      }
    }
  }

  // Method to update group checked state
  void updateGroupCheckedState(String groupId, bool checked) {
    if (entityGroups == null) return;

    for (int i = 0; i < entityGroups!.length; i++) {
      final group = entityGroups![i];
      if (group.id == groupId) {
        // Create a new group with updated checked state
        final updatedGroup = group.copyWith(checked: checked);

        // Update the group in the entityGroups list
        entityGroups![i] = updatedGroup;

        // Update all entities in the group
        if (group.entities != null) {
          final updatedEntities = <Entity>[];

          for (final entity in group.entities!) {
            // Skip related entities
            if (entity.relationType != null) {
              updatedEntities.add(entity);
              continue;
            }

            // Create a new entity with updated checked state
            final updatedEntity = entity.copyWith(checked: checked);
            updatedEntities.add(updatedEntity);
          }

          // Create a new group with the updated entities
          final finalUpdatedGroup =
              updatedGroup.copyWith(entities: updatedEntities);

          // Update the group in the entityGroups list
          entityGroups![i] = finalUpdatedGroup;
        }

        return;
      }
    }
  }

  // Method to get entity checked state
  bool getEntityCheckedState(String entityId) {
    if (entityGroups == null) return false;

    for (final group in entityGroups!) {
      if (group.entities == null) continue;

      for (final entity in group.entities!) {
        if (entity.id == entityId) {
          return entity.checked ?? false;
        }
      }
    }

    return false;
  }

  // Method to get group checked state
  bool getGroupCheckedState(String groupId) {
    if (entityGroups == null) return false;

    for (final group in entityGroups!) {
      if (group.id == groupId) {
        return group.checked ?? false;
      }
    }

    return false;
  }

  factory EntitiesData.fromJson(Map<String, dynamic> json) => EntitiesData(
        entityGroups: json["entityGroups"] == null
            ? []
            : List<EntityGroup>.from(
                json["entityGroups"]!.map((x) => EntityGroup.fromJson(x))),
        systemInfo: json["systemInfo"] == null
            ? null
            : SystemInfo.fromJson(json["systemInfo"]),
        selectedVersions: json["selectedVersions"] == null
            ? {}
            : Map<String, String>.from(json["selectedVersions"]),
        selectedVersionsForChat: json["selectedVersionsForChat"] == null
            ? {}
            : Map<String, String>.from(json["selectedVersionsForChat"]),
      );

  Map<String, dynamic> toJson() => {
        "entityGroups": entityGroups == null
            ? []
            : List<dynamic>.from(entityGroups!.map((x) => x.toJson())),
        "systemInfo": systemInfo?.toJson(),
        "selectedVersions": selectedVersions,
        "selectedVersionsForChat": selectedVersionsForChat,
      };
}

class EntityGroup {
  final String? id;
  final String? title;
  final String? documentId;
  final String? coreCount;
  final List<String>? enitiyVersions;
  final bool? checked;
  final List<Entity>? entities;

  EntityGroup({
    this.id,
    this.title,
    this.documentId,
    this.coreCount,
    this.enitiyVersions,
    this.checked,
    this.entities,
  });

  EntityGroup copyWith({
    String? id,
    String? title,
    String? documentId,
    String? coreCount,
    List<String>? enitiyVersions,
    bool? checked,
    List<Entity>? entities,
  }) =>
      EntityGroup(
        id: id ?? this.id,
        title: title ?? this.title,
        documentId: documentId ?? this.documentId,
        coreCount: coreCount ?? this.coreCount,
        enitiyVersions: enitiyVersions ?? this.enitiyVersions,
        checked: checked ?? this.checked,
        entities: entities ?? this.entities,
      );

  factory EntityGroup.fromJson(Map<String, dynamic> json) => EntityGroup(
        id: json["id"],
        title: json["title"],
        documentId: json["documentId"],
        coreCount: json["coreCount"],
        enitiyVersions: json["enitiyVersions"] == null
            ? []
            : List<String>.from(json["enitiyVersions"]!.map((x) => x)),
        checked: json["checked"],
        entities: json["entities"] == null
            ? []
            : List<Entity>.from(
                json["entities"]!.map((x) => Entity.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "documentId": documentId,
        "coreCount": coreCount,
        "enitiyVersions": enitiyVersions == null
            ? []
            : List<dynamic>.from(enitiyVersions!.map((x) => x)),
        "checked": checked,
        "entities": entities == null
            ? []
            : List<dynamic>.from(entities!.map((x) => x.toJson())),
      };
}

class Entity {
  final String? id;
  final String? title;
  final String? description;
  final String? version;
  bool? expanded;
  final bool? checked;
  final String? attributeString;
  final String? createdBy;
  final DateTime? createdDate;
  final String? modifiedBy;
  final String? modifiedDate;
  final List<Attribute>? attributes;
  final List<BusinessRule>? businessRules;
  final String? relationType;
  final String? parentEntity;
  final List<AttributeMetadata>? attributeMetaDataList;
   List<Relationship>? relationships = [];
   List<Validation>? validationsList = [];
   List<RelationshipProperties>? relationshipProperties = [];
    List<EnumAttribute>? enumAttributes = [];



  Entity({
    this.id,
    this.title,
    this.description,
    this.version,
    this.expanded,
    this.checked,
    this.attributeString,
    this.createdBy,
    this.createdDate,
    this.modifiedBy,
    this.modifiedDate,
    this.attributes,
    this.businessRules,
    this.relationType,
    this.parentEntity,
    this.attributeMetaDataList,
    this.enumAttributes,
    this.relationships,
    this.validationsList,
    this.relationshipProperties
  });

  Entity copyWith({
    String? id,
    String? title,
    String? description,
    String? version,
    bool? expanded,
    bool? checked,
    String? attributeString,
    String? createdBy,
    DateTime? createdDate,
    String? modifiedBy,
    String? modifiedDate,
    List<Attribute>? attributes,
    List<BusinessRule>? businessRules,
    String? relationType,
    String? parentEntity,
List<AttributeMetadata>? attributeMetaDataList,
    List<Relationship>? relationships,
    List<Validation>? validationsList,
    List<RelationshipProperties>? relationshipProperties,
    List<EnumAttribute>? enumAttributes,


  }) =>
      Entity(
        id: id ?? this.id,
        title: title ?? this.title,
        description: description ?? this.description,
        version: version ?? this.version,
        expanded: expanded ?? this.expanded,
        checked: checked ?? this.checked,
        attributeString: attributeString ?? this.attributeString,
        createdBy: createdBy ?? this.createdBy,
        createdDate: createdDate ?? this.createdDate,
        modifiedBy: modifiedBy ?? this.modifiedBy,
        modifiedDate: modifiedDate ?? this.modifiedDate,
        attributes: attributes ?? this.attributes,
        businessRules: businessRules ?? this.businessRules,
        relationType: relationType ?? this.relationType,
        parentEntity: parentEntity ?? this.parentEntity,
          attributeMetaDataList :attributeMetaDataList??this.attributeMetaDataList,
          enumAttributes: enumAttributes??this.enumAttributes,
          relationshipProperties: relationshipProperties??this.relationshipProperties,
        relationships:  relationships??this.relationships,
        validationsList: validationsList??this.validationsList
      );

  factory Entity.fromJson(Map<String, dynamic> json) => Entity(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        version: json["version"],
        expanded: json["expanded"],
        checked: json["checked"],
        attributeString: json["attributeString"],
        createdBy: json["createdBy"],
        createdDate: json["createdDate"] == null
            ? null
            : DateTime.parse(json["createdDate"]),
        modifiedBy: json["modifiedBy"],
        modifiedDate: json["modifiedDate"],
        attributes: json["attributes"] == null
            ? []
            : List<Attribute>.from(
                json["attributes"]!.map((x) => Attribute.fromJson(x))),
        businessRules: json["businessRules"] == null
            ? []
            : List<BusinessRule>.from(
                json["businessRules"]!.map((x) => BusinessRule.fromJson(x))),
        relationType: json["relationType"],
        parentEntity: json["parentEntity"],
      attributeMetaDataList : json["attributeMetaDataList"] == null?[]:List<AttributeMetadata>.from(
          json["attributeMetaDataList"]!.map((x) => AttributeMetadata.fromJson(x)))
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "version": version,
        "expanded": expanded,
        "checked": checked,
        "attributeString": attributeString,
        "createdBy": createdBy,
        "createdDate":
            "${createdDate!.year.toString().padLeft(4, '0')}-${createdDate!.month.toString().padLeft(2, '0')}-${createdDate!.day.toString().padLeft(2, '0')}",
        "modifiedBy": modifiedBy,
        "modifiedDate": modifiedDate,
        "attributes": attributes == null
            ? []
            : List<dynamic>.from(attributes!.map((x) => x.toJson())),
        "businessRules": businessRules == null
            ? []
            : List<dynamic>.from(businessRules!.map((x) => x.toJson())),
        "relationType": relationType,
        "parentEntity": parentEntity,
    "attributeMetadata" :attributeMetaDataList

      };
}

class Attribute {
  final String? name;
  final String? type;
  final bool? required;
  final bool? isPk;
  final String? description;
  final bool? isFk;

  Attribute({
    this.name,
    this.type,
    this.required,
    this.isPk,
    this.description,
    this.isFk,
  });

  Attribute copyWith({
    String? name,
    String? type,
    bool? required,
    bool? isPk,
    String? description,
    bool? isFk,
  }) =>
      Attribute(
        name: name ?? this.name,
        type: type ?? this.type,
        required: required ?? this.required,
        isPk: isPk ?? this.isPk,
        description: description ?? this.description,
        isFk: isFk ?? this.isFk,
      );

  factory Attribute.fromJson(Map<String, dynamic> json) => Attribute(
        name: json["name"],
        type: json["type"],
        required: json["required"],
        isPk: json["isPK"],
        description: json["description"],
        isFk: json["isFK"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "type": type,
        "required": required,
        "isPK": isPk,
        "description": description,
        "isFK": isFk,
      };
}

class BusinessRule {
  final String? name;
  final String? description;

  BusinessRule({
    this.name,
    this.description,
  });

  factory BusinessRule.fromJson(Map<String, dynamic> json) => BusinessRule(
        name: json["name"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "description": description,
      };
}

class SystemInfo {
  final List<String>? enitiyVersions;
  final String? entityName;
  final int? entityCount;
  final int? coreEntities;
  final String? headerText;
  final List<String>? bulletPoints;

  SystemInfo({
    this.enitiyVersions,
    this.entityName,
    this.entityCount,
    this.coreEntities,
    this.headerText,
    this.bulletPoints,
  });

  SystemInfo copyWith({
    List<String>? enitiyVersions,
    String? entityName,
    int? entityCount,
    int? coreEntities,
    String? headerText,
    List<String>? bulletPoints,
  }) =>
      SystemInfo(
        enitiyVersions: enitiyVersions ?? this.enitiyVersions,
        entityName: entityName ?? this.entityName,
        entityCount: entityCount ?? this.entityCount,
        coreEntities: coreEntities ?? this.coreEntities,
        headerText: headerText ?? this.headerText,
        bulletPoints: bulletPoints ?? this.bulletPoints,
      );

  factory SystemInfo.fromJson(Map<String, dynamic> json) => SystemInfo(
        enitiyVersions: json["enitiyVersions"] == null
            ? []
            : List<String>.from(json["enitiyVersions"]!.map((x) => x)),
        entityName: json["entityName"],
        entityCount: json["entityCount"],
        coreEntities: json["coreEntities"],
        headerText: json["headerText"],
        bulletPoints: json["bulletPoints"] == null
            ? []
            : List<String>.from(json["bulletPoints"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "enitiyVersions": enitiyVersions == null
            ? []
            : List<dynamic>.from(enitiyVersions!.map((x) => x)),
        "entityName": entityName,
        "entityCount": entityCount,
        "coreEntities": coreEntities,
        "headerText": headerText,
        "bulletPoints": bulletPoints == null
            ? []
            : List<dynamic>.from(bulletPoints!.map((x) => x)),
      };
}
