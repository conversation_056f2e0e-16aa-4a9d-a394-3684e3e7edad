// To parse this JSON data, do
//
//     final fileUploadOcrResponse = fileUploadOcrResponseFromJson(jsonString);

import 'dart:convert';

FileUploadOcrResponse fileUploadOcrResponseFromJson(String str) =>
    FileUploadOcrResponse.fromJson(json.decode(str));

String fileUploadOcrResponseToJson(FileUploadOcrResponse data) =>
    json.encode(data.toJson());

class FileUploadOcrResponse {
  bool? success;
  AdapterConfig? adapterConfig;
  Data? data;
  String? message;
  dynamic error;
  DateTime? timestamp;
  int? processingTimeMs;
  OptimizationInfo? optimizationInfo;

  FileUploadOcrResponse({
    this.success,
    this.adapterConfig,
    this.data,
    this.message,
    this.error,
    this.timestamp,
    this.processingTimeMs,
    this.optimizationInfo,
  });

  FileUploadOcrResponse copyWith({
    bool? success,
    AdapterConfig? adapterConfig,
    Data? data,
    String? message,
    dynamic error,
    DateTime? timestamp,
    int? processingTimeMs,
    OptimizationInfo? optimizationInfo,
  }) =>
      FileUploadOcrResponse(
        success: success ?? this.success,
        adapterConfig: adapterConfig ?? this.adapterConfig,
        data: data ?? this.data,
        message: message ?? this.message,
        error: error ?? this.error,
        timestamp: timestamp ?? this.timestamp,
        processingTimeMs: processingTimeMs ?? this.processingTimeMs,
        optimizationInfo: optimizationInfo ?? this.optimizationInfo,
      );

  factory FileUploadOcrResponse.fromJson(Map<String, dynamic> json) =>
      FileUploadOcrResponse(
        success: json["success"],
        adapterConfig: json["adapter_config"] == null
            ? null
            : AdapterConfig.fromJson(json["adapter_config"]),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        message: json["message"],
        error: json["error"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        processingTimeMs: json["processing_time_ms"],
        optimizationInfo: json["optimization_info"] == null
            ? null
            : OptimizationInfo.fromJson(json["optimization_info"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "adapter_config": adapterConfig?.toJson(),
        "data": data?.toJson(),
        "message": message,
        "error": error,
        "timestamp": timestamp?.toIso8601String(),
        "processing_time_ms": processingTimeMs,
        "optimization_info": optimizationInfo?.toJson(),
      };
}

class AdapterConfig {
  String? service;
  String? operation;
  String? version;

  AdapterConfig({
    this.service,
    this.operation,
    this.version,
  });

  AdapterConfig copyWith({
    String? service,
    String? operation,
    String? version,
  }) =>
      AdapterConfig(
        service: service ?? this.service,
        operation: operation ?? this.operation,
        version: version ?? this.version,
      );

  factory AdapterConfig.fromJson(Map<String, dynamic> json) => AdapterConfig(
        service: json["service"],
        operation: json["operation"],
        version: json["version"],
      );

  Map<String, dynamic> toJson() => {
        "service": service,
        "operation": operation,
        "version": version,
      };
}

class Data {
  String? originalText;
  String? correctedText;
  String? contextHint;
  String? correctionType;
  int? characterCountOriginal;
  int? characterCountCorrected;
  bool? correctionApplied;
  bool? llmCorrectionApplied;
  bool? fileProcessed;
  bool? extractedFromFile;
  double? confidenceScore;
  String? fileName;

  Data({
    this.originalText,
    this.correctedText,
    this.contextHint,
    this.correctionType,
    this.characterCountOriginal,
    this.characterCountCorrected,
    this.correctionApplied,
    this.llmCorrectionApplied,
    this.fileProcessed,
    this.extractedFromFile,
    this.confidenceScore,
    this.fileName,
  });

  Data copyWith({
    String? originalText,
    String? correctedText,
    String? contextHint,
    String? correctionType,
    int? characterCountOriginal,
    int? characterCountCorrected,
    bool? correctionApplied,
    bool? llmCorrectionApplied,
    bool? fileProcessed,
    bool? extractedFromFile,
    double? confidenceScore,
    String? fileName,
  }) =>
      Data(
        originalText: originalText ?? this.originalText,
        correctedText: correctedText ?? this.correctedText,
        contextHint: contextHint ?? this.contextHint,
        correctionType: correctionType ?? this.correctionType,
        characterCountOriginal:
            characterCountOriginal ?? this.characterCountOriginal,
        characterCountCorrected:
            characterCountCorrected ?? this.characterCountCorrected,
        correctionApplied: correctionApplied ?? this.correctionApplied,
        llmCorrectionApplied: llmCorrectionApplied ?? this.llmCorrectionApplied,
        fileProcessed: fileProcessed ?? this.fileProcessed,
        extractedFromFile: extractedFromFile ?? this.extractedFromFile,
        confidenceScore: confidenceScore ?? this.confidenceScore,
        fileName: fileName ?? this.fileName,
      );

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        originalText: json["original_text"],
        correctedText: json["corrected_text"],
        contextHint: json["context_hint"],
        correctionType: json["correction_type"],
        characterCountOriginal: json["character_count_original"],
        characterCountCorrected: json["character_count_corrected"],
        correctionApplied: json["correction_applied"],
        llmCorrectionApplied: json["llm_correction_applied"],
        fileProcessed: json["file_processed"],
        extractedFromFile: json["extracted_from_file"],
        confidenceScore: json["confidence_score"]?.toDouble(),
        fileName: json["file_name"],
      );

  Map<String, dynamic> toJson() => {
        "original_text": originalText,
        "corrected_text": correctedText,
        "context_hint": contextHint,
        "correction_type": correctionType,
        "character_count_original": characterCountOriginal,
        "character_count_corrected": characterCountCorrected,
        "correction_applied": correctionApplied,
        "llm_correction_applied": llmCorrectionApplied,
        "file_processed": fileProcessed,
        "extracted_from_file": extractedFromFile,
        "confidence_score": confidenceScore,
        "file_name": fileName,
      };
}

class OptimizationInfo {
  bool? cached;
  bool? gpuUsed;
  int? processingTimeMs;
  int? modelCacheHits;
  int? resultCacheHits;

  OptimizationInfo({
    this.cached,
    this.gpuUsed,
    this.processingTimeMs,
    this.modelCacheHits,
    this.resultCacheHits,
  });

  OptimizationInfo copyWith({
    bool? cached,
    bool? gpuUsed,
    int? processingTimeMs,
    int? modelCacheHits,
    int? resultCacheHits,
  }) =>
      OptimizationInfo(
        cached: cached ?? this.cached,
        gpuUsed: gpuUsed ?? this.gpuUsed,
        processingTimeMs: processingTimeMs ?? this.processingTimeMs,
        modelCacheHits: modelCacheHits ?? this.modelCacheHits,
        resultCacheHits: resultCacheHits ?? this.resultCacheHits,
      );

  factory OptimizationInfo.fromJson(Map<String, dynamic> json) =>
      OptimizationInfo(
        cached: json["cached"],
        gpuUsed: json["gpu_used"],
        processingTimeMs: json["processing_time_ms"],
        modelCacheHits: json["model_cache_hits"],
        resultCacheHits: json["result_cache_hits"],
      );

  Map<String, dynamic> toJson() => {
        "cached": cached,
        "gpu_used": gpuUsed,
        "processing_time_ms": processingTimeMs,
        "model_cache_hits": modelCacheHits,
        "result_cache_hits": resultCacheHits,
      };
}
