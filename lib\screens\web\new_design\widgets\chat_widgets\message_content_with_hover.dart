// Widget that wraps the entire message content with hover functionality

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/build_question_formatted_text_spans.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/file_upload_response_preview.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/message_action_buttons.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/selectable_type_writer_text_widget.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_home_screen_chat.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:provider/provider.dart';

class MessageContentWithHover extends StatefulWidget {
  final ChatMessage message;
  final int index;
  final AudioPlayer audioPlayer;
  final String? currentPlayingMessageId;
  final bool isPlaying;
  final bool isPaused;
  final Duration currentPosition;
  final Duration totalDuration;
  final Function(String, {String? messageId}) onTextToSpeech;
  final VoidCallback onStopAudio;
  final Function(BuildContext) showCopyOverlay;
  final bool isLastItem;
  final dynamic parentState;
  final VoidCallback? onComplete;
  final VoidCallback? onNewLine;

  const MessageContentWithHover({
    super.key,
    required this.message,
    required this.index,
    required this.audioPlayer,
    required this.currentPlayingMessageId,
    required this.isPlaying,
    required this.isPaused,
    required this.currentPosition,
    required this.totalDuration,
    required this.onTextToSpeech,
    required this.onStopAudio,
    required this.showCopyOverlay,
    required this.isLastItem,
    required this.parentState,
    this.onNewLine,
    this.onComplete,
  });

  @override
  State<MessageContentWithHover> createState() =>
      _MessageContentWithHoverState();
}

class _MessageContentWithHoverState extends State<MessageContentWithHover> {
  bool isHovered = false;

  // final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    // Always show buttons if audio is playing for this message
    final String messageId = 'msg_${widget.index}';
    final bool alwaysShow =
        widget.currentPlayingMessageId == messageId && widget.isPlaying ||
            (widget.isLastItem);
    final baseTextStyle = TextStyle(
      height: MediaQuery.of(context).size.width > 1600 ? 1.5 : 2,
      fontFamily: 'TiemposText',
      fontSize: MediaQuery.of(context).size.width > 1600 ? 17 : 15,
      fontWeight: FontWeight.w400,
      color: Colors.black,
    );

    final spans =
        buildQuestionFormattedTextSpans(widget.message.content, baseTextStyle);
    Widget textWidget = Container();
    if (widget.message.content.isNotEmpty &&
        widget.isLastItem &&
        !widget.message.isTypingComplete) {
      textWidget = SelectableTypewriterText(
        textSpans: spans,
        speed: Duration(
          milliseconds: widget.message.content.length > 500
              ? (widget.message.content.length / 200).toInt()
              : (widget.message.content.length / 50).toInt(),
        ),
        onNewline: () => widget.onNewLine?.call(),
        onComplete: () {
          if (!(widget.message.isTypingComplete)) {
            setState(() => widget.message.isTypingComplete = true);
            widget.onComplete?.call();
          }
        },
      );
    } else {
      textWidget = SelectableText.rich(TextSpan(children: spans));
    }

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display file preview if this is a response to a file upload
          if (!widget.message.isUser && widget.message.fileData != null)
            FileUploadResponsePreview(
              fileName:
                  widget.message.fileData!.data?.fileName ?? 'Uploaded File',
              onTap: () {
                if (widget.parentState.runtimeType == WebHomeScreenChatState) {
                  widget.parentState.ocrText =
                      widget.message.fileData!.data?.originalText ?? '';
                  widget.parentState.ocrFileName =
                      widget.message.fileData!.data?.fileName ?? '';
                  widget.parentState.fileUploadOcrResponse =
                      widget.message.fileData;
                  widget.parentState.toggleOcrPanel();
                }
              },
            ),

          textWidget,

          const SizedBox(
            height: AppSpacing.md,
          ),
          if (widget.message.brdDocument != null &&
              ((widget.message.isTypingComplete) && !widget.isLastItem))
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  final provider =
                      Provider.of<WebHomeProvider>(context, listen: false);
                  if (provider.showStatusArtifacts) {
                    provider.toggleStatusArtifacts(null);
                  } else {
                    provider.toggleStatusArtifacts(widget.message.brdDocument);
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                      color: widget.message.brdDocument ==
                              Provider.of<WebHomeProvider>(context,
                                      listen: false)
                                  .selectedSolutionSessionModel
                          ? AppColors.grey
                          : AppColors.white,
                      borderRadius: BorderRadius.circular(AppSpacing.xs),
                      border: Border.all(
                          color: widget.message.brdDocument ==
                                  Provider.of<WebHomeProvider>(context,
                                          listen: false)
                                      .selectedSolutionSessionModel
                              ? AppColors.primaryBlue
                              : AppColors.darkGreyBorder,
                          width: 0.5)),
                  padding: EdgeInsets.symmetric(
                      vertical: AppSpacing.sm, horizontal: AppSpacing.lg),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text("Business Requirement Document",
                          style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontWeight: widget.message.brdDocument ==
                                      Provider.of<WebHomeProvider>(context,
                                              listen: false)
                                          .selectedSolutionSessionModel
                                  ? FontWeight.w600
                                  : FontWeight.w400)),
                      SvgPicture.asset("assets/images/artifact_file.svg"),
                    ],
                  ),
                ),
              ),
            ),
          // Add spacing if both content and customContent are present
          if (widget.message.content.isNotEmpty &&
              widget.message.customContent != null)
            SizedBox(height: 16),

          // Display custom content if available
          if (widget.message.customContent != null)
            widget.message.customContent!,

          SizedBox(height: 8),

          // Message action buttons with hover functionality
          AnimatedOpacity(
            opacity: isHovered || alwaysShow ? 1.0 : 0.0,
            duration: Duration(milliseconds: 200),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                MessageActionButtons(
                  // Use content for TTS and copy operations
                  // If content is empty but we have customContent, use a default message
                  messageContent: widget.message.content.isNotEmpty
                      ? widget.message.content
                      : "I've processed your request. Please see the information above.",
                  messageId: messageId,
                  audioPlayer: widget.audioPlayer,
                  currentPlayingMessageId: widget.currentPlayingMessageId,
                  isPlaying: widget.isPlaying,
                  isPaused: widget.isPaused,
                  currentPosition: widget.currentPosition,
                  totalDuration: widget.totalDuration,
                  onTextToSpeech: widget.onTextToSpeech,
                  onStopAudio: widget.onStopAudio,
                  showCopyOverlay: widget.showCopyOverlay,
                ),
              ],
            ),
          ),
          SizedBox(height: AppSpacing.xs)
        ],
      ),
    );
  }
}
