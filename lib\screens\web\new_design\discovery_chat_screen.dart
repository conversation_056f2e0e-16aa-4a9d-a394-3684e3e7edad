// import 'package:flutter/material.dart';
// import 'package:nsl/providers/discovery_provider.dart';
// import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
// import 'package:nsl/services/discovery_service.dart';
// import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
// import 'package:provider/provider.dart';

// class DiscoveryChatScreen extends StatefulWidget {
//   const DiscoveryChatScreen({super.key});

//   @override
//   State<DiscoveryChatScreen> createState() => _DiscoveryChatScreenState();
// }

// class _DiscoveryChatScreenState extends State<DiscoveryChatScreen> {
//   DiscoveryService _apoService = DiscoveryService();
//   Stream<List>? _responseStream;
//   final List<String> _messages = [];
//   final TextEditingController _controller =
//       TextEditingController(text: "Hi, who are you?");

//   void _startStreaming() {
//     setState(() {
//       _messages.clear();
//       _responseStream = _apoService.getUsersStream(_controller.text);
//     });
//   }

//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final discoveryProvider = Provider.of<DiscoveryProvider>(context);

//     return NSLKnowledgeLoaderWrapper(
//       isLoading: discoveryProvider.isLoading ||
//           discoveryProvider.isFileLoading ||
//           discoveryProvider.isSpeechLoading,
//       child: Column(
//         children: [
//           Expanded(
//             child: StreamBuilder<List>(
//               stream: _responseStream,
//               builder: (context, snapshot) {
//                 if (snapshot.hasData) {
//                   _messages.add(snapshot.data!.toString());
//                 }
//                 return ListView.builder(
//                   itemCount: _messages.length,
//                   itemBuilder: (context, index) => Text(_messages[index]),
//                 );
//               },
//             ),
//           ),
//           Padding(
//               padding: const EdgeInsets.all(8.0),
//               child: ChatField(
//                 controller: _controller,
//                 isGeneralLoading: discoveryProvider.isLoading,
//                 isFileLoading: discoveryProvider.isFileLoading,
//                 isSpeechLoading: discoveryProvider.isSpeechLoading,
//                 onSendMessage: () => _startStreaming(),
//                 onFileSelected: (p0, p1) {},
//                 onToggleRecording: () {},
//               )),
//         ],
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/providers/discovery_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_message_bubble.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/nsl_thinking_message_loader.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:provider/provider.dart';

class DiscoveryChatScreen extends StatefulWidget {
  const DiscoveryChatScreen({super.key});

  @override
  State<DiscoveryChatScreen> createState() => _DiscoveryChatScreenState();
}

class _DiscoveryChatScreenState extends State<DiscoveryChatScreen> {
  @override
  void initState() {
    super.initState();
  }

  final ScrollController _chatScrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    final discoveryProvider = Provider.of<DiscoveryProvider>(context);

    return Consumer<DiscoveryProvider>(
      builder: (context, value, child) {
        return NSLKnowledgeLoaderWrapper(
          isLoading: discoveryProvider.isFileLoading ||
              discoveryProvider.isSpeechLoading,
          child: Row(
            children: [
              Expanded(child: Container()),
              Expanded(
                flex: 7,
                child: Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        key: ValueKey<int>(discoveryProvider
                            .history.length), // Add key to force rebuild
                        controller: _chatScrollController,
                        shrinkWrap: true,
                        padding: EdgeInsets.all(AppSpacing.md),
                        itemCount: discoveryProvider.history.length +
                            (discoveryProvider.isLoading ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == discoveryProvider.history.length) {
                            // Show loading indicator
                            return NSLThinkingMessageLoader();
                          }
                          ChatMessage message = ChatMessage(
                            content:
                                discoveryProvider.history[index].content ?? "",
                            isUser:
                                discoveryProvider.history[index].role == "user"
                                    ? true
                                    : false,
                          );
                          return ChatMessageBubbleNew(
                              message: message,
                              // nslThinkingExpanded: {},
                              index: index,
                              isLastItem: index ==
                                  (discoveryProvider.history.length) - 1,
                              multimediaService: MultimediaService(),
                              parentState: this,
                              onComplete: () => _scrollToBottom());
                        },
                      ),
                    ),
                    Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: ChatField(
                          controller: discoveryProvider.chatController,
                          isGeneralLoading: discoveryProvider.isLoading,
                          isFileLoading: discoveryProvider.isFileLoading,
                          isSpeechLoading: discoveryProvider.isSpeechLoading,
                          onSendMessage: () {
                            discoveryProvider.sendMessage(
                                discoveryProvider.chatController.text);
                            discoveryProvider.chatController.text = "";
                            //  _scrollToTop();
                          },
                          onFileSelected: (p0, p1) {},
                          onToggleRecording: () {},
                        )),
                  ],
                ),
              ),
              Expanded(child: Container()),
            ],
          ),
        );
      },
    );
  }

  // Scroll to the bottom of the chat
  void _scrollToBottom() {
    if (_chatScrollController.hasClients) {
      final current = _chatScrollController.offset;
      final max = _chatScrollController.position.maxScrollExtent;

      // Only scroll if we're not already near the bottom
      const threshold = 150.0; // you can adjust this
      if ((max - current).abs() > threshold) {
        _chatScrollController.animateTo(
          max,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    }
  }
}
