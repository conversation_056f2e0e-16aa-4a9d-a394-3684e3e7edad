import 'package:flutter/material.dart';
import '../screens/auth/register_screen.dart';
import '../screens/auth/web/web_register_screen.dart';

class ResponsiveRegisterBuilder extends StatelessWidget {
  const ResponsiveRegisterBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use web layout for larger screens
        if (constraints.maxWidth >= 860) {
          return const WebRegisterScreen();
        }
        // Use mobile layout for smaller screens
        return const RegisterScreen();
      },
    );
  }
}
