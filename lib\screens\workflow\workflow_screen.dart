import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

class WorkflowScreen extends StatelessWidget {
  const WorkflowScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('workflow.title')),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(context.tr('workflow.createNewComingSoon'))),
              );
            },
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildWorkflowCard(
            context,
            title: context.tr('workflow.types.approvalWorkflow'),
            description: context.tr('workflow.descriptions.approvalWorkflow'),
            status: context.tr('workflow.status.active'),
          ),
          const SizedBox(height: 8),
          _buildWorkflowCard(
            context,
            title: context.tr('workflow.types.documentProcessing'),
            description: context.tr('workflow.descriptions.documentProcessing'),
            status: context.tr('workflow.status.draft'),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowCard(
    BuildContext context, {
    required String title,
    required String description,
    required String status,
  }) {
    return Card(
      child: ListTile(
        title: Text(title),
        subtitle: Text(description),
        trailing: Chip(
          label: Text(status),
          backgroundColor: status == context.tr('workflow.status.active')
              ? Theme.of(context).colorScheme.primary.withAlpha(51)
              : Theme.of(context).colorScheme.onSurface.withAlpha(51),
          labelStyle: TextStyle(
            color: status == context.tr('workflow.status.active')
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface.withAlpha(153),
          ),
        ),
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content:
                    Text(context.tr('workflow.workflowDetailsComingSoon'))),
          );
        },
      ),
    );
  }
}
