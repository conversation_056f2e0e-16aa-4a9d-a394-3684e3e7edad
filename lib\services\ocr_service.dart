import 'package:dio/dio.dart';
import 'package:nsl/models/multimedia/ocr_response_model.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/utils/logger.dart';

class OcrService {
  final Dio _dio = Dio();
  final AuthService _authService = AuthService();

  // Base URL for the API
  final String _baseUrl = 'http://10.26.1.52:8008/api/v1';

  // Method to process OCR from file path
  Future<Map<String, dynamic>> processOcrFromPath(String filePath,
      {String targetLang = 'en'}) async {
    try {
      // Get the token
      final token = await _authService.getValidToken();

      // Set up headers
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      // Extract just the file path without any base URL
      // The API expects just the relative path like 'uploads/filename.jpg'
      final cleanFilePath = _extractFilePath(filePath);

      // Create form data
      final formData = {
        'file_path': cleanFilePath,
        'target_lang': targetLang,
      };

      Logger.info('Sending OCR request with file path: $cleanFilePath');

      // Make the API call
      final response = await _dio.post(
        '$_baseUrl/ocr-from-path',
        data: formData,
        options: Options(
          headers: headers,
          contentType: 'application/x-www-form-urlencoded',
        ),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        Logger.info('OCR processed successfully: ${response.data}');

        // For the new API, the response is mostly just text
        // We'll create a simplified OcrResponseModel
        final String extractedText = response.data is String
            ? response.data
            : response.data['text'] ?? '';

        Logger.info('Extracted text: $extractedText');

        // Create a response model with the extracted text
        final ocrResponseModel = OcrResponseModel(
          success: true,
          text: extractedText,
          details: [],
          outputFile: '',
          targetLang: targetLang,
          output: OcrOutputModel(
            fullText: extractedText,
            details: [],
            filePath: cleanFilePath,
            timestamp: DateTime.now().toIso8601String(),
            targetLang: targetLang,
            fileType: 'image',
          ),
        );

        return {
          'success': true,
          'data': response.data,
          'text': extractedText,
          'ocr_response_model': ocrResponseModel,
          'file_path': cleanFilePath,
        };
      } else {
        Logger.error('Error processing OCR: ${response.statusCode}');

        // Create an error response
        final ocrResponseModel = OcrResponseModel(
          success: false,
          text: '',
          details: [],
          outputFile: '',
          error: 'Error processing OCR: ${response.statusCode}',
          targetLang: targetLang,
          output: OcrOutputModel(
            fullText: '',
            details: [],
            filePath: '',
            timestamp: DateTime.now().toIso8601String(),
            targetLang: targetLang,
            fileType: 'image',
          ),
        );

        return {
          'success': false,
          'message': 'Error processing OCR: ${response.statusCode}',
          'ocr_response_model': ocrResponseModel,
        };
      }
    } catch (e) {
      Logger.error('Exception processing OCR: $e');

      // Create an error response
      final ocrResponseModel = OcrResponseModel(
        success: false,
        text: '',
        details: [],
        outputFile: '',
        error: 'Exception processing OCR: $e',
        targetLang: targetLang,
        output: OcrOutputModel(
          fullText: '',
          details: [],
          filePath: '',
          timestamp: DateTime.now().toIso8601String(),
          targetLang: targetLang,
          fileType: 'image',
        ),
      );

      return {
        'success': false,
        'message': 'Exception processing OCR: $e',
        'ocr_response_model': ocrResponseModel,
      };
    }
  }

  // Helper method to extract just the file path from a full URL or path
  String _extractFilePath(String filePath) {
    // If the path contains 'uploads/', extract everything from that point
    if (filePath.contains('uploads/')) {
      final index = filePath.indexOf('uploads/');
      return filePath.substring(index);
    }

    // If it's just a filename, return it as is
    return filePath;
  }
}
