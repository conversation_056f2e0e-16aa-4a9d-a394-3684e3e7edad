import 'package:flutter/material.dart';
import 'package:nsl/widgets/responsive_builder.dart';
import '../theme/app_colors.dart';
import '../theme/spacing.dart';

/// Base class for inline interactive components
abstract class InlineComponent extends StatelessWidget {
  final String label;
  final VoidCallback onTap;
  final DeviceType deviceType;

  const InlineComponent({
    super.key,
    required this.label,
    required this.onTap,
    this.deviceType = DeviceType.desktop,
  });
}

/// Inline button component
class InlineButton extends InlineComponent {
  final IconData? icon;
  final bool isPrimary;

  const InlineButton({
    super.key,
    required super.label,
    required super.onTap,
    this.icon,
    this.isPrimary = false,
    super.deviceType,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSpacing.unit * 2),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal:
              AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
          vertical: AppSpacing.getResponsiveSpacing(AppSpacing.xs, deviceType),
        ),
        decoration: BoxDecoration(
          color: isPrimary
              ? (isDarkMode
                  ? AppColors.subtleIndigoDark
                  : AppColors.primaryIndigo)
              : (isDarkMode ? AppColors.surfaceDark : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(AppSpacing.unit * 2),
          border: isPrimary
              ? null
              : Border.all(
                  color:
                      isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                  width: 1,
                ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: deviceType == DeviceType.mobile ? 16 : 18,
                color: isPrimary
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: AppSpacing.xxs),
            ],
            Text(
              label,
              style: TextStyle(
                fontFamily: 'SFProText',
                fontSize: deviceType == DeviceType.mobile ? 13 : 14,
                fontWeight: FontWeight.w500,
                color: isPrimary
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Suggestion chip component
class SuggestionChip extends InlineComponent {
  const SuggestionChip({
    super.key,
    required super.label,
    required super.onTap,
    super.deviceType,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSpacing.unit * 4),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal:
              AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
          vertical: AppSpacing.getResponsiveSpacing(AppSpacing.xs, deviceType),
        ),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(AppSpacing.unit * 4),
          border: Border.all(
            color: isDarkMode
                ? AppColors.subtleIndigoDark
                : AppColors.primaryIndigo,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontFamily: 'SFProText',
            fontSize: deviceType == DeviceType.mobile ? 13 : 14,
            fontWeight: FontWeight.w500,
            color: isDarkMode
                ? AppColors.subtleIndigoDark
                : AppColors.primaryIndigo,
          ),
        ),
      ),
    );
  }
}

/// Interactive code block component
class InteractiveCodeBlock extends StatelessWidget {
  final String code;
  final String? language;
  final VoidCallback onCopy;
  final DeviceType deviceType;

  const InteractiveCodeBlock({
    super.key,
    required this.code,
    this.language,
    required this.onCopy,
    this.deviceType = DeviceType.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(
        vertical: AppSpacing.getResponsiveSpacing(AppSpacing.xs, deviceType),
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF1E293B) : Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        border: Border.all(
          color: isDarkMode ? Color(0xFF334155) : Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Code header
          Container(
            padding: EdgeInsets.symmetric(
              horizontal:
                  AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
              vertical:
                  AppSpacing.getResponsiveSpacing(AppSpacing.xxs, deviceType),
            ),
            decoration: BoxDecoration(
              color: isDarkMode ? Color(0xFF0F172A) : Color(0xFFF1F5F9),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppSpacing.xs),
                topRight: Radius.circular(AppSpacing.xs),
              ),
            ),
            child: Row(
              children: [
                if (language != null)
                  Text(
                    language!,
                    style: TextStyle(
                      fontFamily: 'SFProText',
                      fontSize: deviceType == DeviceType.mobile ? 12 : 13,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Color(0xFF94A3B8) : Color(0xFF64748B),
                    ),
                  ),
                Spacer(),
                InkWell(
                  onTap: onCopy,
                  borderRadius: BorderRadius.circular(AppSpacing.unit),
                  child: Padding(
                    padding: EdgeInsets.all(AppSpacing.xxs),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.content_copy,
                          size: deviceType == DeviceType.mobile ? 14 : 16,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withAlpha(153),
                        ),
                        SizedBox(width: AppSpacing.xxs),
                        Text(
                          'Copy',
                          style: TextStyle(
                            fontFamily: 'SFProText',
                            fontSize: deviceType == DeviceType.mobile ? 12 : 13,
                            fontWeight: FontWeight.w500,
                            color: isDarkMode
                                ? Color(0xFF94A3B8)
                                : Color(0xFF64748B),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Code content
          Padding(
            padding: EdgeInsets.all(
                AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType)),
            child: SelectableText(
              code,
              style: TextStyle(
                fontFamily: 'SFProText', // Ideally use a monospace font here
                fontSize: deviceType == DeviceType.mobile ? 13 : 14,
                color: isDarkMode ? Color(0xFFE2E8F0) : Color(0xFF0F172A),
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// References or citation component
class ReferenceBadge extends StatelessWidget {
  final String label;
  final String source;
  final VoidCallback onTap;
  final DeviceType deviceType;

  const ReferenceBadge({
    super.key,
    required this.label,
    required this.source,
    required this.onTap,
    this.deviceType = DeviceType.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSpacing.unit * 2),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal:
              AppSpacing.getResponsiveSpacing(AppSpacing.xs, deviceType),
          vertical: AppSpacing.getResponsiveSpacing(AppSpacing.xxs, deviceType),
        ),
        decoration: BoxDecoration(
          color: isDarkMode ? Color(0xFF1E293B) : Color(0xFFF1F5F9),
          borderRadius: BorderRadius.circular(AppSpacing.unit * 2),
          border: Border.all(
            color: isDarkMode ? Color(0xFF334155) : Color(0xFFCBD5E1),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.info_outline,
              size: deviceType == DeviceType.mobile ? 14 : 16,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
            ),
            SizedBox(width: AppSpacing.xxs),
            Text(
              label,
              style: TextStyle(
                fontFamily: 'SFProText',
                fontSize: deviceType == DeviceType.mobile ? 12 : 13,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? Color(0xFFE2E8F0) : Color(0xFF334155),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
