import 'package:flutter/material.dart';
import '../screens/web/web_components_screen.dart';
import '../screens/components_screen.dart';

/// A responsive builder that shows the appropriate components screen based on the device size.
/// 
/// This widget determines whether to show the mobile or web version of the components screen
/// based on the width of the screen.
class ResponsiveComponentsBuilder extends StatelessWidget {
  const ResponsiveComponentsBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use web layout for larger screens
        if (constraints.maxWidth >= 860) {
          return const WebComponentsScreen();
        }
        // Use mobile layout for smaller screens
        return const ComponentsScreen();
      },
    );
  }
}
