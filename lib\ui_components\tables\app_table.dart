import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable table component that follows the app's design system
class AppTable<T> extends StatelessWidget {
  /// The list of column definitions
  final List<AppTableColumn<T>> columns;

  /// The list of data items
  final List<T> data;

  /// The builder function for the empty state
  final Widget Function(BuildContext)? emptyBuilder;

  /// The background color of the table
  final Color? backgroundColor;

  /// The background color of the header row
  final Color? headerBackgroundColor;

  /// The background color of the rows
  final Color? rowBackgroundColor;

  /// The background color of the alternate rows
  final Color? alternateRowBackgroundColor;

  /// The background color of the selected row
  final Color? selectedRowBackgroundColor;

  /// The border radius of the table
  final BorderRadius? borderRadius;

  /// The elevation of the table
  final double elevation;

  /// The border of the table
  final Border? border;

  /// Whether to show the header row
  final bool showHeader;

  /// Whether to show horizontal dividers
  final bool showHorizontalDividers;

  /// Whether to show vertical dividers
  final bool showVerticalDividers;

  /// The color of the dividers
  final Color? dividerColor;

  /// The thickness of the dividers
  final double dividerThickness;

  /// The height of the header row
  final double headerHeight;

  /// The height of the data rows
  final double rowHeight;

  /// The currently selected row index
  final int? selectedRowIndex;

  /// Callback when a row is tapped
  final Function(T, int)? onRowTap;

  /// Whether the table is scrollable horizontally
  final bool horizontalScrollable;

  /// Whether the table is scrollable vertically
  final bool verticalScrollable;

  /// The horizontal scroll controller
  final ScrollController? horizontalController;

  /// The vertical scroll controller
  final ScrollController? verticalController;

  /// The maximum height of the table
  final double? maxHeight;

  const AppTable({
    super.key,
    required this.columns,
    required this.data,
    this.emptyBuilder,
    this.backgroundColor,
    this.headerBackgroundColor,
    this.rowBackgroundColor,
    this.alternateRowBackgroundColor,
    this.selectedRowBackgroundColor,
    this.borderRadius,
    this.elevation = 1,
    this.border,
    this.showHeader = true,
    this.showHorizontalDividers = true,
    this.showVerticalDividers = false,
    this.dividerColor,
    this.dividerThickness = 1.0,
    this.headerHeight = 48.0,
    this.rowHeight = 48.0,
    this.selectedRowIndex,
    this.onRowTap,
    this.horizontalScrollable = true,
    this.verticalScrollable = true,
    this.horizontalController,
    this.verticalController,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    // Show empty state if the data is empty
    if (data.isEmpty && emptyBuilder != null) {
      return emptyBuilder!(context);
    }

    final Color dividerColorValue = dividerColor ??
        Theme.of(context).dividerTheme.color ??
        AppTheme.textSecondaryColor.withAlpha(51);

    // Calculate column widths
    final List<double> columnWidths = _calculateColumnWidths();
    final double tableWidth =
        columnWidths.fold(0.0, (sum, width) => sum + width);

    // Build the table content
    Widget tableContent = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row
        if (showHeader)
          Container(
            height: headerHeight,
            color: headerBackgroundColor ?? AppTheme.surfaceColor,
            child: Row(
              children: List.generate(
                columns.length * 2 - 1,
                (index) {
                  if (index.isOdd) {
                    // Vertical divider
                    return showVerticalDividers
                        ? Container(
                            width: dividerThickness,
                            color: dividerColorValue,
                          )
                        : Container();
                  }

                  final columnIndex = index ~/ 2;
                  final column = columns[columnIndex];

                  return Container(
                    width: columnWidths[columnIndex],
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingM),
                    alignment: column.alignment,
                    child: column.headerBuilder != null
                        ? column.headerBuilder!(context)
                        : Text(
                            column.label,
                            style: AppTheme.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                  );
                },
              ),
            ),
          ),

        // Divider after header
        if (showHeader && showHorizontalDividers)
          Container(
            height: dividerThickness,
            color: dividerColorValue,
          ),

        // Data rows
        Flexible(
          child: ListView.separated(
            physics: verticalScrollable
                ? null
                : const NeverScrollableScrollPhysics(),
            controller: verticalController,
            shrinkWrap: !verticalScrollable,
            itemCount: data.length,
            separatorBuilder: (context, index) => showHorizontalDividers
                ? Container(
                    height: dividerThickness,
                    color: dividerColorValue,
                  )
                : Container(),
            itemBuilder: (context, index) {
              final item = data[index];
              final bool isSelected = selectedRowIndex == index;
              final bool isAlternate = index % 2 == 1;

              return InkWell(
                onTap: onRowTap != null ? () => onRowTap!(item, index) : null,
                child: Container(
                  height: rowHeight,
                  color: isSelected
                      ? selectedRowBackgroundColor ??
                          AppTheme.primaryColor.withAlpha(25)
                      : isAlternate
                          ? alternateRowBackgroundColor
                          : rowBackgroundColor,
                  child: Row(
                    children: List.generate(
                      columns.length * 2 - 1,
                      (colIndex) {
                        if (colIndex.isOdd) {
                          // Vertical divider
                          return showVerticalDividers
                              ? Container(
                                  width: dividerThickness,
                                  color: dividerColorValue,
                                )
                              : Container();
                        }

                        final columnIndex = colIndex ~/ 2;
                        final column = columns[columnIndex];

                        return Container(
                          width: columnWidths[columnIndex],
                          padding: const EdgeInsets.symmetric(
                              horizontal: AppTheme.spacingM),
                          alignment: column.alignment,
                          child: column.cellBuilder(context, item, index),
                        );
                      },
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );

    // Apply horizontal scrolling if needed
    if (horizontalScrollable) {
      tableContent = SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        controller: horizontalController,
        child: SizedBox(
          width: tableWidth,
          child: tableContent,
        ),
      );
    }

    // Apply max height if needed
    if (maxHeight != null) {
      tableContent = SizedBox(
        height: maxHeight,
        child: tableContent,
      );
    }

    // Apply container styling if needed
    if (backgroundColor != null ||
        borderRadius != null ||
        elevation > 0 ||
        border != null) {
      return Container(
        decoration: BoxDecoration(
          color: backgroundColor ?? AppTheme.backgroundColor,
          borderRadius: borderRadius,
          border: border,
          boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: elevation * 2,
                    offset: Offset(0, elevation),
                  ),
                ]
              : null,
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.zero,
          child: tableContent,
        ),
      );
    }

    return tableContent;
  }

  List<double> _calculateColumnWidths() {
    final List<double> widths = [];

    for (final column in columns) {
      if (column.width != null) {
        widths.add(column.width!);
      } else if (column.flexGrow != null) {
        // Placeholder for flex columns, will be calculated later
        widths.add(0);
      } else {
        // Default width
        widths.add(150);
      }
    }

    return widths;
  }
}

/// A column definition for the table
class AppTableColumn<T> {
  /// The title of the column
  final String label;

  /// The builder function for the header cell
  final Widget Function(BuildContext)? headerBuilder;

  /// The builder function for the data cells
  final Widget Function(BuildContext, T, int) cellBuilder;

  /// The width of the column
  final double? width;

  /// The flex grow factor of the column
  final int? flexGrow;

  /// The alignment of the column content
  final Alignment alignment;

  /// Whether the column is sortable
  final bool sortable;

  /// The sort direction of the column
  final AppTableSortDirection? sortDirection;

  /// Callback when the column is sorted
  final Function(AppTableSortDirection)? onSort;

  const AppTableColumn({
    required this.label,
    this.headerBuilder,
    required this.cellBuilder,
    this.width,
    this.flexGrow,
    this.alignment = Alignment.centerLeft,
    this.sortable = false,
    this.sortDirection,
    this.onSort,
  }) : assert(width == null || flexGrow == null,
            'Cannot specify both width and flexGrow');
}

/// The sort direction of a column
enum AppTableSortDirection {
  ascending,
  descending,
}

/// A customizable data table component with pagination
class AppPaginatedTable<T> extends StatelessWidget {
  /// The list of column definitions
  final List<AppTableColumn<T>> columns;

  /// The list of data items
  final List<T> data;

  /// The builder function for the empty state
  final Widget Function(BuildContext)? emptyBuilder;

  /// The background color of the table
  final Color? backgroundColor;

  /// The background color of the header row
  final Color? headerBackgroundColor;

  /// The background color of the rows
  final Color? rowBackgroundColor;

  /// The background color of the alternate rows
  final Color? alternateRowBackgroundColor;

  /// The background color of the selected row
  final Color? selectedRowBackgroundColor;

  /// The border radius of the table
  final BorderRadius? borderRadius;

  /// The elevation of the table
  final double elevation;

  /// The border of the table
  final Border? border;

  /// Whether to show the header row
  final bool showHeader;

  /// Whether to show horizontal dividers
  final bool showHorizontalDividers;

  /// Whether to show vertical dividers
  final bool showVerticalDividers;

  /// The color of the dividers
  final Color? dividerColor;

  /// The thickness of the dividers
  final double dividerThickness;

  /// The height of the header row
  final double headerHeight;

  /// The height of the data rows
  final double rowHeight;

  /// The currently selected row index
  final int? selectedRowIndex;

  /// Callback when a row is tapped
  final Function(T, int)? onRowTap;

  /// Whether the table is scrollable horizontally
  final bool horizontalScrollable;

  /// The horizontal scroll controller
  final ScrollController? horizontalController;

  /// The current page
  final int currentPage;

  /// The number of items per page
  final int itemsPerPage;

  /// The total number of items
  final int totalItems;

  /// Callback when the page is changed
  final Function(int) onPageChanged;

  /// The available page sizes
  final List<int>? pageSizes;

  /// Callback when the page size is changed
  final Function(int)? onPageSizeChanged;

  /// The height of the table
  final double? height;

  const AppPaginatedTable({
    super.key,
    required this.columns,
    required this.data,
    this.emptyBuilder,
    this.backgroundColor,
    this.headerBackgroundColor,
    this.rowBackgroundColor,
    this.alternateRowBackgroundColor,
    this.selectedRowBackgroundColor,
    this.borderRadius,
    this.elevation = 1,
    this.border,
    this.showHeader = true,
    this.showHorizontalDividers = true,
    this.showVerticalDividers = false,
    this.dividerColor,
    this.dividerThickness = 1.0,
    this.headerHeight = 48.0,
    this.rowHeight = 48.0,
    this.selectedRowIndex,
    this.onRowTap,
    this.horizontalScrollable = true,
    this.horizontalController,
    required this.currentPage,
    required this.itemsPerPage,
    required this.totalItems,
    required this.onPageChanged,
    this.pageSizes,
    this.onPageSizeChanged,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final int totalPages = (totalItems / itemsPerPage).ceil();
    final bool showPagination = totalPages > 1;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Table
        SizedBox(
          height: height,
          child: AppTable<T>(
            columns: columns,
            data: data,
            emptyBuilder: emptyBuilder,
            backgroundColor: backgroundColor,
            headerBackgroundColor: headerBackgroundColor,
            rowBackgroundColor: rowBackgroundColor,
            alternateRowBackgroundColor: alternateRowBackgroundColor,
            selectedRowBackgroundColor: selectedRowBackgroundColor,
            borderRadius: showPagination
                ? BorderRadius.only(
                    topLeft: borderRadius?.topLeft ??
                        Radius.circular(AppTheme.borderRadiusM),
                    topRight: borderRadius?.topRight ??
                        Radius.circular(AppTheme.borderRadiusM),
                  )
                : borderRadius,
            elevation: elevation,
            border: border,
            showHeader: showHeader,
            showHorizontalDividers: showHorizontalDividers,
            showVerticalDividers: showVerticalDividers,
            dividerColor: dividerColor,
            dividerThickness: dividerThickness,
            headerHeight: headerHeight,
            rowHeight: rowHeight,
            selectedRowIndex: selectedRowIndex,
            onRowTap: onRowTap,
            horizontalScrollable: horizontalScrollable,
            verticalScrollable: true,
            horizontalController: horizontalController,
          ),
        ),

        // Pagination
        if (showPagination)
          Container(
            decoration: BoxDecoration(
              color: backgroundColor ?? AppTheme.backgroundColor,
              borderRadius: BorderRadius.only(
                bottomLeft: borderRadius?.bottomLeft ??
                    Radius.circular(AppTheme.borderRadiusM),
                bottomRight: borderRadius?.bottomRight ??
                    Radius.circular(AppTheme.borderRadiusM),
              ),
              border: border != null
                  ? Border(
                      left: border!.left,
                      right: border!.right,
                      bottom: border!.bottom,
                    )
                  : null,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingM,
              vertical: AppTheme.spacingS,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Page size selector
                if (pageSizes != null && onPageSizeChanged != null)
                  Row(
                    children: [
                      Text(
                        'Rows per page:',
                        style: AppTheme.bodySmall,
                      ),
                      const SizedBox(width: AppTheme.spacingS),
                      DropdownButton<int>(
                        value: itemsPerPage,
                        items: pageSizes!
                            .map(
                              (size) => DropdownMenuItem<int>(
                                value: size,
                                child: Text(
                                  size.toString(),
                                  style: AppTheme.bodySmall,
                                ),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            onPageSizeChanged!(value);
                          }
                        },
                        style: AppTheme.bodySmall,
                        underline: Container(),
                      ),
                    ],
                  )
                else
                  Text(
                    'Showing ${(currentPage - 1) * itemsPerPage + 1} to ${currentPage * itemsPerPage > totalItems ? totalItems : currentPage * itemsPerPage} of $totalItems entries',
                    style: AppTheme.bodySmall,
                  ),

                // Pagination controls
                Row(
                  children: [
                    // First page
                    IconButton(
                      icon: const Icon(Icons.first_page),
                      onPressed:
                          currentPage > 1 ? () => onPageChanged(1) : null,
                      iconSize: 20,
                      color: AppTheme.textSecondaryColor,
                      splashRadius: 20,
                    ),

                    // Previous page
                    IconButton(
                      icon: const Icon(Icons.chevron_left),
                      onPressed: currentPage > 1
                          ? () => onPageChanged(currentPage - 1)
                          : null,
                      iconSize: 20,
                      color: AppTheme.textSecondaryColor,
                      splashRadius: 20,
                    ),

                    // Page numbers
                    _buildPageNumbers(context, currentPage, totalPages),

                    // Next page
                    IconButton(
                      icon: const Icon(Icons.chevron_right),
                      onPressed: currentPage < totalPages
                          ? () => onPageChanged(currentPage + 1)
                          : null,
                      iconSize: 20,
                      color: AppTheme.textSecondaryColor,
                      splashRadius: 20,
                    ),

                    // Last page
                    IconButton(
                      icon: const Icon(Icons.last_page),
                      onPressed: currentPage < totalPages
                          ? () => onPageChanged(totalPages)
                          : null,
                      iconSize: 20,
                      color: AppTheme.textSecondaryColor,
                      splashRadius: 20,
                    ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildPageNumbers(
      BuildContext context, int currentPage, int totalPages) {
    const int maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages
      return Row(
        children: List.generate(
          totalPages,
          (index) {
            final page = index + 1;
            return _buildPageButton(context, page, currentPage);
          },
        ),
      );
    } else {
      // Show a subset of pages with ellipsis
      final List<Widget> pageButtons = [];

      // Always show first page
      pageButtons.add(_buildPageButton(context, 1, currentPage));

      // Calculate range of visible pages
      int startPage = currentPage - 1;
      int endPage = currentPage + 1;

      if (startPage <= 1) {
        startPage = 2;
        endPage = Math.min(totalPages - 1, startPage + 2);
      } else if (endPage >= totalPages) {
        endPage = totalPages - 1;
        startPage = Math.max(2, endPage - 2);
      }

      // Add ellipsis before middle pages if needed
      if (startPage > 2) {
        pageButtons.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              '...',
              style: AppTheme.bodySmall,
            ),
          ),
        );
      }

      // Add middle pages
      for (int i = startPage; i <= endPage; i++) {
        pageButtons.add(_buildPageButton(context, i, currentPage));
      }

      // Add ellipsis after middle pages if needed
      if (endPage < totalPages - 1) {
        pageButtons.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              '...',
              style: AppTheme.bodySmall,
            ),
          ),
        );
      }

      // Always show last page
      if (totalPages > 1) {
        pageButtons.add(_buildPageButton(context, totalPages, currentPage));
      }

      return Row(children: pageButtons);
    }
  }

  Widget _buildPageButton(BuildContext context, int page, int currentPage) {
    final bool isSelected = page == currentPage;

    return InkWell(
      onTap: isSelected ? null : () => onPageChanged(page),
      borderRadius: BorderRadius.circular(4),
      child: Container(
        width: 32,
        height: 32,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          page.toString(),
          style: AppTheme.bodySmall.copyWith(
            color: isSelected
                ? AppTheme.textLightColor
                : AppTheme.textPrimaryColor,
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}

/// Math utility class
class Math {
  static int min(int a, int b) => a < b ? a : b;
  static int max(int a, int b) => a > b ? a : b;
}
