{"system_info": {"title": "NSL Complete Organization Chart Data - Comprehensive Extract", "framework": "GO-LO-NP-Stack-SubNSL BET Structure with Financial Integration", "version": "2.0 - Complete Hierarchy", "total_organizational_levels": 4, "bet_composition": "GOs + LOs + NP Functions + Input/Output Stacks + Subordinate NSL", "default_time_range": {"from": "2024-11-01T00:00", "to": "2024-11-30T23:59"}}, "organizational_structure": {"M4": {"level_name": "Executive", "node": {"id": "1", "title": "CEO Operations", "type": "Executive Node", "total_bets": 1356, "bet_breakdown": {"gos": 45, "los": 234, "np_functions": 567, "input_output_stacks": 345, "subordinate_nsl": 165}, "children": ["1.1", "1.2", "1.3"], "selected": true, "metrics": {"m3_nodes": 6, "total_gos": 45, "total_los": 234, "transactions": "15.2K", "bet_efficiency": "88.5%"}, "financial_summary": {"revenue": "$7,000,000", "cost": "$4,256,725", "margin": "39.2%"}}}, "M3": {"level_name": "Departments", "nodes": [{"id": "1.1", "title": "Technology", "type": "Department Node", "parent": "1", "total_bets": 892, "bet_breakdown": {"gos": 28, "los": 145, "np_functions": 372, "input_output_stacks": 235, "subordinate_nsl": 112}, "children": ["1.1.1", "1.1.2", "1.1.3", "1.1.4"], "metrics": {"m2_nodes": 4, "gos": 28, "los": 145, "transactions": "8.9K", "bet_efficiency": "91.2%"}, "financial_summary": {"revenue": "$4,200,000", "cost": "$2,400,000", "margin": "42.8%"}}, {"id": "1.2", "title": "Operations", "type": "Department Node", "parent": "1", "total_bets": 234, "bet_breakdown": {"gos": 12, "los": 56, "np_functions": 98, "input_output_stacks": 52, "subordinate_nsl": 16}, "children": ["1.2.1", "1.2.2", "1.2.3"], "metrics": {"m2_nodes": 3, "gos": 12, "los": 56, "transactions": "3.2K", "bet_efficiency": "84.3%"}, "financial_summary": {"revenue": "$1,800,000", "cost": "$1,100,000", "margin": "38.9%"}}, {"id": "1.3", "title": "Finance", "type": "Department Node", "parent": "1", "total_bets": 89, "bet_breakdown": {"gos": 5, "los": 33, "np_functions": 35, "input_output_stacks": 12, "subordinate_nsl": 4}, "children": ["1.3.1", "1.3.2"], "metrics": {"m2_nodes": 2, "gos": 5, "los": 33, "transactions": "1.8K", "bet_efficiency": "76.8%"}, "financial_summary": {"revenue": "Internal", "cost": "$168,000", "margin": "Support Function"}}]}, "M2": {"level_name": "Teams", "nodes": [{"id": "1.1.1", "title": "Engineering", "type": "Team Node", "parent": "1.1", "total_bets": 456, "bet_breakdown": {"gos": 18, "los": 89, "np_functions": 189, "input_output_stacks": 115, "subordinate_nsl": 45}, "children": ["1.1.1.1", "*******", "*******", "*******"], "metrics": {"m1_employees": 8, "gos": 18, "los": 89, "team_efficiency": "93.5%"}, "financial_summary": {"revenue": "$2,800,000", "cost": "$1,600,000", "margin": "42.9%"}}, {"id": "1.1.2", "title": "QA & Testing", "type": "Team Node", "parent": "1.1", "total_bets": 145, "bet_breakdown": {"gos": 6, "los": 34, "np_functions": 68, "input_output_stacks": 28, "subordinate_nsl": 9}, "children": ["1.1.2.1", "1.1.2.2", "1.1.2.3", "1.1.2.4"], "metrics": {"m1_employees": 4, "gos": 6, "los": 34, "team_efficiency": "87.2%"}, "financial_summary": {"revenue": "$850,000", "cost": "$520,000", "margin": "38.8%"}}, {"id": "1.1.3", "title": "Infrastructure", "type": "Team Node", "parent": "1.1", "total_bets": 89, "bet_breakdown": {"gos": 3, "los": 18, "np_functions": 45, "input_output_stacks": 18, "subordinate_nsl": 5}, "children": ["1.1.3.1", "1.1.3.2", "1.1.3.3"], "metrics": {"m1_employees": 3, "gos": 3, "los": 18, "team_efficiency": "89.6%"}, "financial_summary": {"revenue": "$380,000", "cost": "$210,000", "margin": "44.7%"}}, {"id": "1.1.4", "title": "Analytics", "type": "Team Node", "parent": "1.1", "total_bets": 23, "bet_breakdown": {"gos": 1, "los": 4, "np_functions": 12, "input_output_stacks": 5, "subordinate_nsl": 1}, "children": ["1.1.4.1", "1.1.4.2"], "metrics": {"m1_employees": 2, "gos": 1, "los": 4, "team_efficiency": "92.1%"}, "financial_summary": {"revenue": "$170,000", "cost": "$70,000", "margin": "58.8%"}}, {"id": "1.2.1", "title": "Customer Success", "type": "Team Node", "parent": "1.2", "total_bets": 112, "bet_breakdown": {"gos": 6, "los": 28, "np_functions": 48, "input_output_stacks": 24, "subordinate_nsl": 6}, "metrics": {"m1_employees": 5, "gos": 6, "los": 28, "team_efficiency": "85.4%"}, "financial_summary": {"revenue": "$850,000", "cost": "$520,000", "margin": "38.8%"}}, {"id": "1.2.2", "title": "Sales Operations", "type": "Team Node", "parent": "1.2", "total_bets": 78, "bet_breakdown": {"gos": 4, "los": 18, "np_functions": 32, "input_output_stacks": 18, "subordinate_nsl": 6}, "metrics": {"m1_employees": 4, "gos": 4, "los": 18, "team_efficiency": "82.7%"}, "financial_summary": {"revenue": "$680,000", "cost": "$380,000", "margin": "44.1%"}}, {"id": "1.2.3", "title": "Business Development", "type": "Team Node", "parent": "1.2", "total_bets": 44, "bet_breakdown": {"gos": 2, "los": 10, "np_functions": 18, "input_output_stacks": 10, "subordinate_nsl": 4}, "metrics": {"m1_employees": 3, "gos": 2, "los": 10, "team_efficiency": "78.9%"}, "financial_summary": {"revenue": "$270,000", "cost": "$200,000", "margin": "25.9%"}}, {"id": "1.3.1", "title": "Financial Planning", "type": "Team Node", "parent": "1.3", "total_bets": 55, "bet_breakdown": {"gos": 3, "los": 20, "np_functions": 22, "input_output_stacks": 8, "subordinate_nsl": 2}, "metrics": {"m1_employees": 3, "gos": 3, "los": 20, "team_efficiency": "79.2%"}, "financial_summary": {"revenue": "Internal", "cost": "$95,000", "margin": "Support Function"}}, {"id": "1.3.2", "title": "Accounting & Control", "type": "Team Node", "parent": "1.3", "total_bets": 34, "bet_breakdown": {"gos": 2, "los": 13, "np_functions": 13, "input_output_stacks": 4, "subordinate_nsl": 2}, "metrics": {"m1_employees": 2, "gos": 2, "los": 13, "team_efficiency": "74.1%"}, "financial_summary": {"revenue": "Internal", "cost": "$73,000", "margin": "Support Function"}}]}, "M1": {"level_name": "Individual Employees", "nodes": [{"id": "1.1.1.1", "title": "<PERSON> - Senior Developer", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-001", "total_bets": 67, "bet_breakdown": {"gos": 5, "los": 15, "np_functions": 25, "input_output_stacks": 15, "subordinate_nsl": 7}, "metrics": {"local_objectives": 5, "personal_bets": 15, "lo_efficiency": "90.2%"}, "financial_summary": {"annual_salary": "$89,000", "value_output": "$425,000", "efficiency": "90.2%"}, "personal_workflows": [{"go_id": "GO-001-001-001-001", "workflow_name": "Frontend Component Development", "local_objectives": [{"lo_id": "LO-001-001-001-001-001", "name": "React Component Architecture", "solution_space": "Individual component development and optimization", "outcome_focus": "High-quality, reusable React components", "efficiency": 96.7, "np_functions": [{"np_id": "NP-001-001-001-001-001", "name": "Component Development", "function_type": "create", "agent": "<PERSON>", "agent_cost": 45.0, "cycle_time": 4.5, "input_stacks": [{"stack_id": "INPUT-001-001-001-001-001", "entities": [{"entity_name": "Development Requirements", "attributes": [{"name": "component_spec", "ui_type": "textarea", "source": "manual", "cost": 2.0}, {"name": "design_mockup", "ui_type": "file", "source": "digital", "cost": 1.5}]}]}], "output_stacks": [{"stack_id": "OUTPUT-001-001-001-001-001", "entities": [{"entity_name": "React Component", "attributes": [{"name": "component_code", "ui_type": "textarea", "cost": 5.0}, {"name": "prop_types", "ui_type": "text", "cost": 1.0}, {"name": "test_coverage", "ui_type": "number", "cost": 2.0}]}]}], "subordinate_nsl": [{"sub_id": "SUB-001-001-001-001-001-001", "function_type": "validation", "trigger_condition": "On code completion", "description": "ESLint validation and type checking"}]}]}]}]}, {"id": "*******", "title": "<PERSON> - Lead Architect", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-002", "total_bets": 89, "bet_breakdown": {"gos": 4, "los": 18, "np_functions": 35, "input_output_stacks": 22, "subordinate_nsl": 10}, "metrics": {"local_objectives": 4, "personal_bets": 18, "lo_efficiency": "87.5%"}, "financial_summary": {"annual_salary": "$115,000", "value_output": "$380,000", "efficiency": "87.5%"}}, {"id": "*******", "title": "<PERSON> - Research Engineer", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-003", "total_bets": 35, "bet_breakdown": {"gos": 3, "los": 8, "np_functions": 15, "input_output_stacks": 7, "subordinate_nsl": 2}, "metrics": {"local_objectives": 3, "personal_bets": 8, "lo_efficiency": "45.0%"}, "financial_summary": {"annual_salary": "$95,000", "value_output": "$85,000", "efficiency": "45.0%"}}, {"id": "*******", "title": "<PERSON> - Platform Engineer", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-004", "total_bets": 112, "bet_breakdown": {"gos": 4, "los": 22, "np_functions": 45, "input_output_stacks": 28, "subordinate_nsl": 13}, "metrics": {"local_objectives": 4, "personal_bets": 22, "lo_efficiency": "94.1%"}, "financial_summary": {"annual_salary": "$105,000", "value_output": "$520,000", "efficiency": "94.1%"}}]}}, "financial_data": {"selected_node": {"id": "1", "name": "CEO Operations", "period": "For the Month Ended November 30, 2024"}, "summary_metrics": {"total_revenue": {"value": "$7,000,000", "change": "+12.5% vs last month"}, "net_margin": {"value": "39.2%", "change": "+2.1% vs last month"}, "total_transactions": {"value": "15,234", "change": "+8.7% vs last month"}, "bet_efficiency": {"value": "88.5%", "change": "+3.2% vs last month"}}, "income_statement": {"revenue": {"service_revenue_application_development": {"amount": "$3,150,000", "percentage": "45.0%", "bet_contribution": "245 Active BETs"}, "service_revenue_qa_testing": {"amount": "$1,400,000", "percentage": "20.0%", "bet_contribution": "89 Active BETs"}, "service_revenue_infrastructure": {"amount": "$1,400,000", "percentage": "20.0%", "bet_contribution": "67 Active BETs"}, "service_revenue_analytics": {"amount": "$1,050,000", "percentage": "15.0%", "bet_contribution": "34 Active BETs"}, "other_revenue": {"amount": "$0", "percentage": "0.0%"}, "total_revenue": {"amount": "$7,000,000", "percentage": "100.0%", "total_bet_contribution": "435 Total BETs"}}, "cost_of_revenue": {"employee_benefit_expenses_direct": {"amount": "$2,123,456", "percentage": "30.3%", "bet_type": "Human Agent BETs"}, "employee_benefit_expenses_support": {"amount": "$345,678", "percentage": "4.9%", "bet_type": "Support BETs"}, "technology_infrastructure": {"amount": "$102,457", "percentage": "1.5%", "bet_type": "Digital Agent BETs"}, "total_cost_of_revenue": {"amount": "$2,571,591", "percentage": "36.7%"}}, "gross_profit": {"amount": "$4,428,409", "percentage": "63.3%"}, "operating_expenses": {"sales_marketing": {"amount": "$234,567", "percentage": "3.4%"}, "general_administrative": {"amount": "$875,162", "percentage": "12.5%", "breakdown": {"finance_operations": {"amount": "$167,890", "percentage": "2.4%"}, "hr_operations": {"amount": "$134,568", "percentage": "1.9%"}, "it_infrastructure": {"amount": "$123,457", "percentage": "1.8%"}, "legal_compliance": {"amount": "$35,679", "percentage": "0.5%"}, "other_administrative": {"amount": "$413,568", "percentage": "5.9%"}}}, "research_development": {"amount": "$123,456", "percentage": "1.8%"}, "idle_capacity_costs": {"amount": "$487,760", "percentage": "7.0%", "breakdown": {"human_idle_costs": {"amount": "$417,392", "percentage": "6.0%", "bet_inefficiency": "11.7% of Human BETs idle"}, "digital_idle_costs": {"amount": "$70,368", "percentage": "1.0%", "bet_inefficiency": "25% of Digital BETs idle"}}}, "total_operating_expenses": {"amount": "$1,720,945", "percentage": "24.6%"}}, "operating_income": {"amount": "$2,707,464", "percentage": "38.7%"}, "other_income": {"interest_income": {"amount": "$23,456", "percentage": "0.3%"}, "other_income": {"amount": "$12,355", "percentage": "0.2%"}, "total_other_income": {"amount": "$35,811", "percentage": "0.5%"}}, "income_before_tax": {"amount": "$2,743,275", "percentage": "39.2%"}, "income_tax_expense": {"amount": "$0", "percentage": "0.0%"}, "net_income": {"amount": "$2,743,275", "percentage": "39.2%"}}, "balance_sheet": {"assets": {"current_assets": {"cash_equivalents": {"amount": "$4,567,890", "percentage": "26.3%", "breakdown": {"operating_cash": {"amount": "$3,200,000", "percentage": "18.4%"}, "payroll_reserve": {"amount": "$1,200,000", "percentage": "6.9%"}, "emergency_fund": {"amount": "$167,890", "percentage": "1.0%"}}}, "accounts_receivable": {"amount": "$10,500,000", "percentage": "60.4%", "breakdown": {"trade_receivables_americas": {"amount": "$4,410,000", "percentage": "25.4%"}, "trade_receivables_europe": {"amount": "$2,940,000", "percentage": "16.9%"}, "trade_receivables_apac": {"amount": "$2,100,000", "percentage": "12.1%"}, "trade_receivables_row": {"amount": "$425,000", "percentage": "2.4%"}, "unbilled_revenue_accrued": {"amount": "$625,000", "percentage": "3.6%"}}}, "work_in_progress": {"amount": "$1,160,490", "percentage": "6.7%", "projects": {"project_alpha": {"amount": "$234,567", "percentage": "1.3%", "gos": "GO-001 to GO-015", "los_breakdown": {"lo_001_completed": {"amount": "$89,234", "percentage": "0.5%", "range": "LO-001-1 to LO-001-45"}, "lo_002_in_progress": {"amount": "$145,333", "percentage": "0.8%", "range": "LO-002-1 to LO-002-67"}}}, "project_beta": {"amount": "$345,678", "percentage": "2.0%", "gos": "GO-016 to GO-035", "phases": {"development_phase": {"amount": "$189,445", "percentage": "1.1%", "los_completed": 234}, "testing_phase": {"amount": "$156,233", "percentage": "0.9%", "los_completed": 156}}}, "project_gamma": {"amount": "$123,456", "percentage": "0.7%", "gos": "GO-036 to GO-045"}, "other_active_projects": {"amount": "$456,789", "percentage": "2.6%", "gos_count": 89}}}, "total_current_assets": {"amount": "$16,487,870", "percentage": "94.8%"}}, "total_assets": {"amount": "$17,386,526", "percentage": "100.0%"}}}}, "bet_framework": {"description": "Binary Entities (BETs) - The Foundation of Structured Execution", "concept": {"origin": "Derived from dual-state nature, mirroring BiTs (Binary Digits) discovered by <PERSON>", "significance": "Just as BiTs quantify and process digital information, BETs quantify and process solutions", "binary_framework": "Like transistors/switches (on/off, open/closed), BETs operate in binary states for precise execution control", "state_transitions": "Potentiality → Reality"}, "entity_classification": {"bets": {"definition": "Binary Entities that actively participate in solution execution and undergo state transitions", "characteristics": ["Absorb information", "Process information", "Generate information", "Participate in functions"], "requirement": "All transformational elements must be BETs", "composition": "GOs + LOs + NP Functions + Input/Output Stacks + Subordinate NSL"}, "unary_entities": {"definition": "Entities serving only as informational references within enterprise", "characteristics": ["Do not actively participate in functions", "Purely informational elements"], "role": "Reference data without transformation capability"}}, "bet_breakdown": [{"name": "Employee_Salary", "bet_type": "Human Agent BET", "entity_attribute": "Entity.Attribute | Human Agent Cost", "cost": "$2,123,456", "state_transitions": {"potentiality": "Available human resources", "reality": "Active solution execution", "transformation_metrics": {"agent_count": 892, "avg_cost_per_agent": "$2,381", "go_integration": "88.3%", "lo_participation": "234 LOs", "active_execution_rate": "88.3%"}}, "information_processing": {"absorbs": "Project requirements, task specifications", "processes": "Solution development, code creation, testing", "generates": "Deliverables, completed features, solutions"}}, {"name": "Cloud_Infrastructure", "bet_type": "Digital Agent BET", "entity_attribute": "Entity.Attribute | Digital Agent Cost", "cost": "$102,457", "state_transitions": {"potentiality": "Available compute/storage resources", "reality": "Active processing/hosting solutions", "transformation_metrics": {"vcpu_hours": "129,600", "memory_gb": "259,200", "go_integration": "75.0%", "lo_distribution": "45 LOs", "active_execution_rate": "75.0%"}}, "information_processing": {"absorbs": "Application code, data requests, user interactions", "processes": "Computation, data storage, service delivery", "generates": "Application responses, processed data, service outputs"}}, {"name": "Solution_Revenue", "bet_type": "Value Generation BET", "entity_attribute": "Entity.Attribute | Revenue Stream", "cost": "$7,000,000", "state_transitions": {"potentiality": "Market opportunities, client needs", "reality": "Delivered solutions, realized value", "transformation_metrics": {"active_gos": 45, "solution_delivery": "98.2%", "lo_integration": "97.8%", "bet_coordination": "1,356 BETs", "value_realization_rate": "97.8%"}}, "information_processing": {"absorbs": "Client requirements, market demands, solution specifications", "processes": "Solution design, development execution, delivery coordination", "generates": "Completed solutions, client value, revenue streams"}}]}, "consolidated_data": {"consolidated_gos": {"value": "45", "description": "Including all sub-level integrations"}, "inter_go_eliminations": {"value": "-12", "description": "Overlapping solution spaces"}, "net_effective_gos": {"value": "33", "description": "After consolidation"}, "consolidated_efficiency": {"value": "91.2%", "description": "Integrated solution delivery"}}, "trends": {"go_evolution": {"value": "+15.2%", "description": "Solution space expansion"}, "lo_optimization": {"value": "+8.7%", "description": "Local solution efficiency"}, "bet_integration": {"value": "+5.3%", "description": "Cross-LO coordination"}, "solution_delivery": {"value": "+3.1%", "description": "Macro-objective achievement"}}, "bet_aggregation_rules": {"total_bet_calculation": "GOs + LOs + NP_Functions + Input_Output_Stacks + Subordinate_NSL", "rollup_logic": {"M1_to_M2": "Sum all M1 employee BETs within team", "M2_to_M3": "Sum all M2 team BETs within department", "M3_to_M4": "Sum all M3 department BETs within organization"}, "validation": {"M4_total": 1356, "M3_sum": 1215, "calculation_note": "M4 total includes additional executive-level coordination BETs"}}, "performance_metrics": {"M4": {"average_bet_efficiency": "88.5%", "go_completion_rate": "89.2%", "lo_integration_rate": "91.5%"}, "M3": {"technology_efficiency": "91.2%", "operations_efficiency": "84.3%", "finance_efficiency": "76.8%"}, "M2": {"engineering_efficiency": "93.5%", "qa_efficiency": "87.2%", "infrastructure_efficiency": "89.6%", "analytics_efficiency": "92.1%"}, "M1": {"john_efficiency": "90.2%", "sarah_efficiency": "87.5%", "mike_efficiency": "45.0%", "emily_efficiency": "94.1%"}}, "navigation": {"breadcrumb": [{"id": "1", "name": "M4: CEO Operations", "active": true}, {"id": "1.1", "name": "M3: Technology", "active": false}, {"id": "1.1.1", "name": "M2: Engineering", "active": false}, {"id": "1.1.1.1", "name": "M1: <PERSON>", "active": false}]}, "ui_configuration": {"tabs": [{"id": "standalone", "name": "Standalone", "active": true}, {"id": "consolidated", "name": "Consolidated", "active": false}, {"id": "breakdown", "name": "BET Breakdown", "active": false}, {"id": "trends", "name": "Trends", "active": false}], "time_controls": {"global": {"from": "2024-11-01T00:00", "to": "2024-11-30T23:59"}, "local": {"from": "2024-11-01T00:00", "to": "2024-11-30T23:59"}}}}