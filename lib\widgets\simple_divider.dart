import 'package:flutter/material.dart';

/// A simple divider widget that can be vertical or horizontal
class SimpleDivider extends StatelessWidget {
  /// Whether the divider is vertical or horizontal
  final bool isVertical;
  
  /// The thickness of the divider
  final double thickness;
  
  /// The color of the divider
  final Color? color;
  
  const SimpleDivider({
    super.key,
    this.isVertical = false,
    this.thickness = 1.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final dividerColor = color ?? Theme.of(context).dividerColor;
    
    return Container(
      width: isVertical ? thickness : double.infinity,
      height: isVertical ? double.infinity : thickness,
      color: dividerColor,
    );
  }
}
