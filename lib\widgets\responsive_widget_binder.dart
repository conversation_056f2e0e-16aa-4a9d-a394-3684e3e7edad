import 'package:flutter/material.dart';
import 'package:nsl/screens/web/web_widget_binder_screen.dart';
import 'package:nsl/screens/widget_binder_screen.dart';

class ResponsiveWidgetBinderBuilder extends StatelessWidget {
  const ResponsiveWidgetBinderBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use web layout for larger screens
        if (constraints.maxWidth >= 860) {
          return const WebWidgetBinderScreen();
        }
        // Use mobile layout for smaller screens
        return const WidgetBinderScreen();
      },
    );
  }
}
