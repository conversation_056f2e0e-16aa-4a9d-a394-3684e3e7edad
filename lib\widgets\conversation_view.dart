import 'package:flutter/material.dart';
import 'package:nsl/widgets/chat_bubble.dart';
import 'package:nsl/widgets/responsive_builder.dart';
import '../theme/spacing.dart';
import 'message_group.dart';

class ConversationView extends StatelessWidget {
  final List<Map<String, dynamic>> messages;
  final DeviceType deviceType;

  const ConversationView({
    super.key,
    required this.messages,
    this.deviceType = DeviceType.desktop,
  });

  @override
  Widget build(BuildContext context) {
    // Group messages by sender with no timestamp gap
    List<Map<String, dynamic>> messageGroups = [];

    MessageType? currentType;
    List<Map<String, dynamic>> currentGroup = [];

    for (final message in messages) {
      final type = message['type'] as MessageType;

      // If sender changed or there's a significant time gap, start a new group
      if (currentType != type || currentGroup.isEmpty) {
        if (currentGroup.isNotEmpty) {
          messageGroups.add({
            'type': currentType,
            'messages': List.from(currentGroup),
          });
          currentGroup.clear();
        }
        currentType = type;
      }

      currentGroup.add(message);
    }

    // Add the last group
    if (currentGroup.isNotEmpty && currentType != null) {
      messageGroups.add({
        'type': currentType,
        'messages': List.from(currentGroup),
      });
    }

    // Apply proper gutters and spacing
    return ListView.builder(
      padding: EdgeInsets.symmetric(
        vertical: AppSpacing.getResponsiveSpacing(AppSpacing.lg, deviceType),
        horizontal: AppSpacing.getResponsiveSpacing(
            deviceType == DeviceType.mobile ? AppSpacing.xs : AppSpacing.md,
            deviceType),
      ),
      itemCount: messageGroups.length,
      itemBuilder: (context, index) {
        final group = messageGroups[index];
        return MessageGroup(
          messages: group['messages'] as List<Map<String, dynamic>>,
          type: group['type'] as MessageType,
          deviceType: deviceType,
        );
      },
    );
  }
}
