import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/widget_render_models.dart';

/// Service for loading and parsing widget render configurations
class WidgetRendererService {
  /// Loads widget render configuration from a JSON file in the assets
  static Future<WidgetRenderModel> loadWidgetRenderConfig(String assetPath) async {
    try {
      // Load the JSON data from the assets file
      final jsonData = await rootBundle.loadString(assetPath);
      final Map<String, dynamic> configData = json.decode(jsonData);
      
      return WidgetRenderModel.fromJson(configData);
    } catch (e) {
      throw Exception('Failed to load widget render configuration: ${e.toString()}');
    }
  }

  /// Loads mock data for the widgets
  /// In a real application, this would fetch data from an API
  static Map<String, dynamic> getMockData() {
    return {
      'summary_metrics': {
        'total_revenue': 1250000,
        'total_units': 5280,
        'revenue_pct_change': 12.5,
        'units_pct_change': 8.3,
      },
      'quarterly_trend': [
        {'quarter_year': 'Q1 2023', 'revenue': 280000, 'units_sold': 1200},
        {'quarter_year': 'Q2 2023', 'revenue': 320000, 'units_sold': 1350},
        {'quarter_year': 'Q3 2023', 'revenue': 310000, 'units_sold': 1280},
        {'quarter_year': 'Q4 2023', 'revenue': 340000, 'units_sold': 1450},
      ],
      'region_breakdown': [
        {'region': 'North America', 'revenue': 520000},
        {'region': 'Europe', 'revenue': 380000},
        {'region': 'Asia Pacific', 'revenue': 250000},
        {'region': 'Latin America', 'revenue': 100000},
      ],
      'detailed_sales': [
        {
          'region': 'North America',
          'product_category': 'Electronics',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 150000,
          'units_sold': 750
        },
        {
          'region': 'North America',
          'product_category': 'Appliances',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 120000,
          'units_sold': 450
        },
        {
          'region': 'Europe',
          'product_category': 'Electronics',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 100000,
          'units_sold': 500
        },
        {
          'region': 'Europe',
          'product_category': 'Appliances',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 80000,
          'units_sold': 300
        },
        {
          'region': 'Asia Pacific',
          'product_category': 'Electronics',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 70000,
          'units_sold': 350
        },
        {
          'region': 'Asia Pacific',
          'product_category': 'Appliances',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 50000,
          'units_sold': 200
        },
        {
          'region': 'Latin America',
          'product_category': 'Electronics',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 30000,
          'units_sold': 150
        },
        {
          'region': 'Latin America',
          'product_category': 'Appliances',
          'quarter': 'Q1',
          'year': '2023',
          'revenue': 20000,
          'units_sold': 80
        },
      ],
    };
  }

  /// Gets data for a specific data source
  static dynamic getDataForSource(String sourceId) {
    final mockData = getMockData();
    return mockData[sourceId];
  }
}
