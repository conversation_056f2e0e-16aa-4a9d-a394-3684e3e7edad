import 'package:flutter/material.dart';

List<InlineSpan> buildQuestionFormattedTextSpans(
    String text, TextStyle baseStyle,
    {bool hasQuestion = true}) {
  final boldStyle = baseStyle.copyWith(fontWeight: FontWeight.w600);
  final spans = <InlineSpan>[];

  if (hasQuestion) {
    int currentIndex = 0;

    while (true) {
      int questionTagIndex = text.indexOf("**Question:**", currentIndex);

      if (questionTagIndex == -1) {
        if (currentIndex < text.length) {
          spans.add(
              TextSpan(text: text.substring(currentIndex), style: baseStyle));
        }
        break;
      }

      if (questionTagIndex > currentIndex) {
        spans.add(TextSpan(
          text: text.substring(currentIndex, questionTagIndex),
          style: baseStyle,
        ));
      }

      int searchStart = questionTagIndex + "**Question:**".length;
      int lastQuestionMark = text.indexOf("?", searchStart);
      int tempIndex = lastQuestionMark;

      while (tempIndex != -1) {
        lastQuestionMark = tempIndex;
        tempIndex = text.indexOf("?", lastQuestionMark + 1);
      }

      if (lastQuestionMark == -1) {
        spans
            .add(TextSpan(text: text.substring(searchStart), style: baseStyle));
        break;
      }

      String boldText = text.substring(searchStart, lastQuestionMark + 1);
      spans.add(TextSpan(text: boldText, style: boldStyle));

      currentIndex = lastQuestionMark + 1;
    }
  } else {
    spans.add(TextSpan(text: text, style: baseStyle));
  }

  return spans;
}
