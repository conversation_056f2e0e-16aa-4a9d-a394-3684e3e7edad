// lib/widgets/message_group.dart

import 'package:flutter/material.dart';
import 'package:nsl/widgets/responsive_builder.dart';
import '../theme/spacing.dart';
import 'chat_bubble.dart';

class MessageGroup extends StatelessWidget {
  final List<Map<String, dynamic>> messages;
  final MessageType type;
  final DeviceType deviceType;

  const MessageGroup({
    super.key,
    required this.messages,
    required this.type,
    this.deviceType = DeviceType.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: AppSpacing.getResponsiveSpacing(
            AppSpacing.messageGroupGap, deviceType),
      ),
      child: Column(
        crossAxisAlignment: type == MessageType.user
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          // Optional avatar or name
          if (type == MessageType.assistant)
            Padding(
              padding: EdgeInsets.only(
                left:
                    AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType),
                bottom:
                    AppSpacing.getResponsiveSpacing(AppSpacing.xxs, deviceType),
              ),
              child: Text(
                'Assistant',
                style: TextStyle(
                  fontFamily: 'SFProText',
                  fontSize: deviceType == DeviceType.mobile ? 12 : 13,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Color(0xFFD1D5DB) // Gray 300
                      : Color(0xFF6B7280), // Gray 500
                ),
              ),
            ),

          // Message bubbles with proper spacing
          ...messages.asMap().entries.map((entry) {
            final index = entry.key;
            final message = entry.value;

            return Padding(
              padding: EdgeInsets.only(
                bottom: index < messages.length - 1
                    ? AppSpacing.getResponsiveSpacing(
                        AppSpacing.messageGap, deviceType)
                    : 0,
              ),
              child: ChatBubble(
                message: message['text'],
                type: type,
                timestamp:
                    index == messages.length - 1 ? message['timestamp'] : null,
                deviceType: deviceType,
                showTail: index == messages.length - 1,
              ),
            );
          }),
        ],
      ),
    );
  }
}
