import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';

class ImageService {
  final ImagePicker _picker = ImagePicker();

  // Request gallery permission
  Future<bool> _requestGalleryPermission() async {
    Logger.info('Requesting gallery permission');

    // For Android 13+ (API level 33+)
    if (Platform.isAndroid) {
      final status = await Permission.photos.request();
      if (status.isGranted) {
        return true;
      } else if (status.isPermanentlyDenied) {
        Logger.error('Gallery permission permanently denied');
        return false;
      } else {
        Logger.error('Gallery permission denied');
        return false;
      }
    }
    // For iOS
    else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }

    return false;
  }

  // Pick an image from the gallery
  Future<File?> pickImageFromGallery() async {
    try {
      // Request permission first
      final hasPermission = await _requestGalleryPermission();
      if (!hasPermission) {
        Logger.error('Gallery permission not granted');
        return null;
      }

      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      Logger.error('Error picking image from gallery: $e');
      return null;
    }
  }

  // Request camera permission
  Future<bool> _requestCameraPermission() async {
    Logger.info('Requesting camera permission');

    final status = await Permission.camera.request();
    if (status.isGranted) {
      return true;
    } else if (status.isPermanentlyDenied) {
      Logger.error('Camera permission permanently denied');
      return false;
    } else {
      Logger.error('Camera permission denied');
      return false;
    }
  }

  // Take a photo with the camera
  Future<File?> takePhoto() async {
    try {
      // Request permission first
      final hasPermission = await _requestCameraPermission();
      if (!hasPermission) {
        Logger.error('Camera permission not granted');
        return null;
      }

      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      Logger.error('Error taking photo: $e');
      return null;
    }
  }

  // Pick a file from device storage
  Future<File?> pickFile() async {
    try {
      Logger.info('Picking file');

      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          return File(path);
        }
      }
      return null;
    } catch (e) {
      Logger.error('Error picking file: $e');
      return null;
    }
  }

  // Convert image file to base64 string
  Future<String?> imageToBase64(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      return base64Encode(bytes);
    } catch (e) {
      Logger.error('Error converting image to base64: $e');
      return null;
    }
  }

  // Show permission settings dialog
  Future<void> _showPermissionSettingsDialog(
      BuildContext context, String permissionType) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('$permissionType Permission Required'),
          content: Text(
              'This app needs $permissionType access to select profile pictures. Please enable it in app settings.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Open Settings'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  // Show image picker dialog
  Future<File?> showImagePickerDialog(BuildContext context) async {
    return await showDialog<File?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Image'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                GestureDetector(
                  child: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Icon(Icons.photo_library),
                        SizedBox(width: 10),
                        Text('Photo Gallery'),
                      ],
                    ),
                  ),
                  onTap: () async {
                    final hasPermission = await _requestGalleryPermission();
                    if (hasPermission) {
                      final image = await pickImageFromGallery();
                      if (context.mounted) {
                        Navigator.of(context).pop(image);
                      }
                    } else {
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        await _showPermissionSettingsDialog(context, 'Gallery');
                      }
                    }
                  },
                ),
                const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Divider(),
                ),
                GestureDetector(
                  child: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Icon(Icons.camera_alt),
                        SizedBox(width: 10),
                        Text('Camera'),
                      ],
                    ),
                  ),
                  onTap: () async {
                    final hasPermission = await _requestCameraPermission();
                    if (hasPermission) {
                      final image = await takePhoto();
                      if (context.mounted) {
                        Navigator.of(context).pop(image);
                      }
                    } else {
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        await _showPermissionSettingsDialog(context, 'Camera');
                      }
                    }
                  },
                ),
                const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Divider(),
                ),
                GestureDetector(
                  child: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Icon(Icons.file_present),
                        SizedBox(width: 10),
                        Text('Files'),
                      ],
                    ),
                  ),
                  onTap: () async {
                    final image = await pickFile();
                    if (context.mounted) {
                      Navigator.of(context).pop(image);
                    }
                  },
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // Create a multipart request for uploading an image
  Future<http.MultipartFile> createImageMultipart(
      File imageFile, String fieldName) async {
    final fileExtension = imageFile.path.split('.').last.toLowerCase();
    final subtype = fileExtension == 'jpg' ? 'jpeg' : fileExtension;

    return http.MultipartFile(
      fieldName,
      imageFile.readAsBytes().asStream(),
      await imageFile.length(),
      filename: imageFile.path.split('/').last,
      contentType: MediaType('image', subtype),
    );
  }
}
