import 'package:flutter/material.dart';
import '../../models/widget_render_models.dart';
import '../widget_factory.dart';

/// A widget that renders a layout based on a layout model
class LayoutRenderer extends StatelessWidget {
  final LayoutModel layout;
  final List<ComponentModel> components;

  const LayoutRenderer({
    super.key,
    required this.layout,
    required this.components,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Adjust layout based on device type
        if (layout.deviceType == 'mobile' && constraints.maxWidth < 600) {
          return _buildMobileLayout(context, constraints);
        } else if (layout.deviceType == 'tablet' &&
            constraints.maxWidth < 860) {
          return _buildTabletLayout(context, constraints);
        } else {
          return _buildDesktopLayout(context, constraints);
        }
      },
    );
  }

  Widget _buildDesktopLayout(BuildContext context, BoxConstraints constraints) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: layout.containers.map((container) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: _buildContainer(context, container),
        );
      }).toList(),
    );
  }

  Widget _buildTabletLayout(BuildContext context, BoxConstraints constraints) {
    // For tablet, we'll use a similar layout to desktop but with adjusted sizes
    return _buildDesktopLayout(context, constraints);
  }

  Widget _buildMobileLayout(BuildContext context, BoxConstraints constraints) {
    // For mobile, we'll stack all components vertically
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: layout.containers.expand((container) {
        return container.components.map((componentId) {
          final component = components.firstWhere(
            (comp) => comp.id == componentId,
            orElse: () => ComponentModel(
              id: componentId,
              widget: 'Unknown',
              config: {},
            ),
          );

          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: WidgetFactory.createWidget(component),
          );
        }).toList();
      }).toList(),
    );
  }

  Widget _buildContainer(BuildContext context, ContainerModel container) {
    switch (container.layout) {
      case 'horizontal_split':
        return _buildHorizontalSplitContainer(context, container);
      case 'vertical_split':
        return _buildVerticalSplitContainer(context, container);
      case 'full_width':
        return _buildFullWidthContainer(context, container);
      default:
        return Center(
          child: Text('Unknown container layout: ${container.layout}'),
        );
    }
  }

  Widget _buildHorizontalSplitContainer(
      BuildContext context, ContainerModel container) {
    final componentWidgets = container.components.map((componentId) {
      final component = components.firstWhere(
        (comp) => comp.id == componentId,
        orElse: () => ComponentModel(
          id: componentId,
          widget: 'Unknown',
          config: {},
        ),
      );

      return Flexible(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: WidgetFactory.createWidget(component),
        ),
      );
    }).toList();

    return Center(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: componentWidgets,
        ),
      ),
    );
  }

  Widget _buildVerticalSplitContainer(
      BuildContext context, ContainerModel container) {
    final componentWidgets = container.components.map((componentId) {
      final component = components.firstWhere(
        (comp) => comp.id == componentId,
        orElse: () => ComponentModel(
          id: componentId,
          widget: 'Unknown',
          config: {},
        ),
      );

      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: WidgetFactory.createWidget(component),
      );
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: componentWidgets,
    );
  }

  Widget _buildFullWidthContainer(
      BuildContext context, ContainerModel container) {
    if (container.components.isEmpty) {
      return const SizedBox.shrink();
    }

    final componentId = container.components.first;
    final component = components.firstWhere(
      (comp) => comp.id == componentId,
      orElse: () => ComponentModel(
        id: componentId,
        widget: 'Unknown',
        config: {},
      ),
    );

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: WidgetFactory.createWidget(component),
      ),
    );
  }
}
