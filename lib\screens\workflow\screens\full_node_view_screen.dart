import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';

import 'package:nsl/screens/workflow/screens/workflow-implementation.dart';
import 'package:nsl/widgets/resizable_divider.dart';

class FullNodeViewScreen extends StatefulWidget {
  const FullNodeViewScreen({super.key});

  @override
  State<FullNodeViewScreen> createState() => _FullNodeViewScreenState();
}

class _FullNodeViewScreenState extends State<FullNodeViewScreen> {
  // Controller for the interactive viewer
  final TransformationController _transformationController =
      TransformationController();

  // State variables to track selected node and details visibility
  bool _showNodeDetails = false;
  WorkflowNode? _selectedNode;

  // Panel width for the details panel
  double _detailsPanelWidth = 350.0;

  // Minimum and maximum width constraints for the details panel
  final double _minDetailsPanelWidth = 250.0;
  final double _maxDetailsPanelWidth = 500.0;

  // Reference to the workflow builder
  final GlobalKey<AdvancedWorkflowBuilderState> _workflowBuilderKey =
      GlobalKey();

  @override
  void initState() {
    super.initState();
    // Set preferred orientation to landscape
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    // Reset orientation when leaving this screen
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('workflow.title')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Row(
        children: [
          // Main workflow area - takes less space when details panel is visible
          Expanded(
            flex: _showNodeDetails ? 7 : 10,
            child: Stack(
              children: <Widget>[
                // Background image
                // backgroundImage(),
                SvgPicture.asset(
                  'assets/images/dot_bg_1.svg',
                  fit: BoxFit.fill,
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.height,
                ),

                // Interactive viewer with workflow builder
                InteractiveViewer(
                  transformationController: _transformationController,
                  boundaryMargin: const EdgeInsets.all(double.infinity),
                  constrained: false,
                  minScale: 0.1,
                  maxScale: 4.0,
                  child: AdvancedWorkflowBuilder(
                    key: _workflowBuilderKey,
                    isFullScreen: true,
                    onNodeSelected: _handleNodeSelected,
                  ),
                ),
              ],
            ),
          ),

          // Resizable divider - shown when a node is selected
          if (_showNodeDetails && _selectedNode != null)
            ResizableDivider(
              onResize: (delta) {
                setState(() {
                  // Adjust panel width (increase when dragging left, decrease when dragging right)
                  // Negative delta means dragging left, positive means dragging right
                  _detailsPanelWidth = (_detailsPanelWidth - delta)
                      .clamp(_minDetailsPanelWidth, _maxDetailsPanelWidth);
                });
              },
            ),

          // Node details panel - shown when a node is selected
          if (_showNodeDetails && _selectedNode != null)
            Container(
              width: _detailsPanelWidth,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 8,
                    offset: const Offset(-2, 0),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 16),
                    child: Row(
                      children: [
                        _buildNodeIcon(_selectedNode!.type),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _selectedNode!.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            setState(() {
                              _showNodeDetails = false;
                              _selectedNode = null;
                            });
                          },
                        ),
                      ],
                    ),
                  ),

                  const Divider(),

                  // Node details
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic information section
                          Text(
                            context.tr('workflow.currentSolution'),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _buildInfoRow('Node ID', _selectedNode!.id),
                          _buildInfoRow('Type',
                              _selectedNode!.type.toString().split('.').last),
                          _buildInfoRow('Action', _selectedNode!.actionLabel),
                          _buildInfoRow(
                              'Count', _selectedNode!.count.toString()),

                          const SizedBox(height: 24),

                          // Node details section (if available)
                          if (_selectedNode!.lodetails != null) ...[
                            Text(
                              context.tr('workflow.components'),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 12),
                            ..._selectedNode!.lodetails!.entries
                                .where((entry) => entry.key != 'inputs')
                                .map((entry) => _buildInfoRow(
                                    entry.key, entry.value.toString())),

                            // Show inputs section if available
                            if (_selectedNode!.lodetails!
                                    .containsKey('inputs') &&
                                _selectedNode!.lodetails!['inputs']
                                    is List) ...[
                              const SizedBox(height: 16),
                              const Text(
                                'Inputs',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              (_selectedNode!.lodetails!['inputs'] as List)
                                      .isEmpty
                                  ? Text(context
                                      .tr('workflow.noComponentsDefined'))
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: (_selectedNode!
                                              .lodetails!['inputs'] as List)
                                          .map<Widget>((input) {
                                        if (input is Map) {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: Text(
                                                '${input['name'] ?? 'Unnamed input'}: ${input['type'] ?? 'No type'}'),
                                          );
                                        }
                                        return const SizedBox.shrink();
                                      }).toList(),
                                    ),
                            ],
                            const SizedBox(height: 24),
                          ],

                          // Connections section
                          Text(
                            context.tr('workflow.solutionTemplates'),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 12),

                          // Connections list would go here
                          // We'll need to implement this separately
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            height: 32.0,
            width: 32.0,
            child: FittedBox(
              child: FloatingActionButton(
                heroTag: 'zoom_in',
                onPressed: () {
                  _zoomIn();
                },
                child: const Icon(Icons.add),
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 32.0,
            width: 32.0,
            child: FittedBox(
              child: FloatingActionButton(
                heroTag: 'zoom_out',
                onPressed: () {
                  _zoomOut();
                },
                child: const Icon(Icons.remove),
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 32.0,
            width: 32.0,
            child: FittedBox(
              child: FloatingActionButton(
                heroTag: 'reset',
                onPressed: () {
                  _resetView();
                },
                child: const Icon(Icons.refresh),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _zoomIn() {
    final Matrix4 currentMatrix = _transformationController.value;
    final Matrix4 newMatrix = Matrix4.copy(currentMatrix)..scale(1.2);
    _transformationController.value = newMatrix;
  }

  void _zoomOut() {
    final Matrix4 currentMatrix = _transformationController.value;
    final Matrix4 newMatrix = Matrix4.copy(currentMatrix)..scale(0.8);
    _transformationController.value = newMatrix;
  }

  void _resetView() {
    _transformationController.value = Matrix4.identity();
  }

  // Handle node selection from the workflow builder
  void _handleNodeSelected(WorkflowNode node) {
    setState(() {
      _selectedNode = node;
      _showNodeDetails = true;
    });
  }

  // Build node icon based on type
  Widget _buildNodeIcon(NodeType type) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final secondaryColor = theme.colorScheme.secondary;
    final errorColor = theme.colorScheme.error;
    final successColor = theme.colorScheme.primary;
    final onPrimaryColor = theme.colorScheme.onPrimary;

    Color getNodeColor(NodeType type) {
      switch (type) {
        case NodeType.start:
          return successColor;
        case NodeType.approval:
          return primaryColor;
        case NodeType.email:
          return secondaryColor;
        case NodeType.end:
          return errorColor;
      }
    }

    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: getNodeColor(type),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getNodeIconData(type),
        color: onPrimaryColor,
        size: 16,
      ),
    );
  }

  IconData _getNodeIconData(NodeType type) {
    switch (type) {
      case NodeType.start:
        return Icons.play_arrow;
      case NodeType.approval:
        return Icons.check_circle_outline;
      case NodeType.email:
        return Icons.email;
      case NodeType.end:
        return Icons.stop;
    }
  }

  // Helper method to build info rows in the details panel
  Widget _buildInfoRow(String label, String value) {
    final theme = Theme.of(context);
    final labelColor =
        theme.colorScheme.onSurface.withAlpha(179); // 0.7 opacity
    final valueColor = theme.colorScheme.onSurface;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: labelColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget backgroundImage() {
    return SvgPicture.asset(
      'assets/images/dot_bg.svg',
      fit: BoxFit.fill,
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
    );

    // Column(
    //   children: [
    //     Row(
    //       children: [
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //       ],
    //     ),
    //     Row(
    //       children: [
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //       ],
    //     ),
    //     Row(
    //       children: [
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //       ],
    //     ),
    //     Row(
    //       children: [
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //       ],
    //     ),
    //     Row(
    //       children: [
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //       ],
    //     ),
    //     Row(
    //       children: [
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //         image(6),
    //       ],
    //     )
    //   ],
    // );
  }

  Widget image(number) {
    return Image.asset(
      'assets/images/dot_bg.jpg',
      fit: BoxFit.fill,
      height: MediaQuery.of(context).size.height / number,
      width: MediaQuery.of(context).size.width / number,
    );
  }
}

// Import the AdvancedWorkflowBuilder from the workflow-implementation.dart file
