import 'user.dart';

/// A factory for creating models from JSON
class ModelFactory {
  // Private constructor to prevent instantiation
  ModelFactory._();

  /// Create a User model from JSON
  static User createUser(Map<String, dynamic> json) {
    return User.fromJson(json);
  }

  /// Create a list of User models from JSON
  static List<User> createUsers(List<dynamic> jsonList) {
    return jsonList.map((json) => createUser(json)).toList();
  }

  /// Create a model of the specified type from JSON
  static T createModel<T>(Map<String, dynamic> json) {
    switch (T) {
      case User _:
        return createUser(json) as T;
      default:
        throw ArgumentError('Unknown model type: $T');
    }
  }

  /// Create a list of models of the specified type from JSON
  static List<T> createModels<T>(List<dynamic> jsonList) {
    return jsonList.map((json) => createModel<T>(json)).toList();
  }
}
