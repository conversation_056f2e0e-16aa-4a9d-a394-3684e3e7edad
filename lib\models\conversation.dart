class Conversation {
  final String id;
  final String title;
  final String lastMessagePreview;
  final DateTime timestamp;

  Conversation({
    required this.id,
    required this.title,
    required this.lastMessagePreview,
    required this.timestamp,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      lastMessagePreview: json['last_message_preview'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'last_message_preview': lastMessagePreview,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
