import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/message.dart';
import '../models/project_details.dart';
import '../services/build_service.dart';
import '../utils/logger.dart';
import 'package:dio/dio.dart';

class BuildProvider extends ChangeNotifier {
  final List<Message> _messages = [];
  final BuildService _buildService = BuildService();
  bool _isLoading = false;
  BuildResponse? _lastResponse;
  CancelToken? _cancelToken;
  ProjectDetails? _projectDetails;
  bool _showProjectDetailsForm = false;

  List<Message> get messages => _messages;
  bool get isLoading => _isLoading;
  BuildResponse? get lastResponse => _lastResponse;
  Message? get selectedMessage => _selectedMessage;
  ProjectDetails? get projectDetails => _projectDetails;
  bool get showProjectDetailsForm => _showProjectDetailsForm;

  bool showChatView = false;
  Message? _selectedMessage;

  // Add cancel method
  void cancelRequest() {
    if (_isLoading && _cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken?.cancel("User cancelled the request");
      _isLoading = false;
      notifyListeners();
    }
  }

  BuildProvider() {
    _loadMessages();
  }

  Future<void> _loadMessages() async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson = prefs.getStringList('build_messages') ?? [];

    _messages.clear();
    for (final messageJson in messagesJson) {
      final messageMap = jsonDecode(messageJson);
      _messages.add(Message.fromJson(messageMap));
    }

    notifyListeners();
  }

  Future<void> _saveMessages() async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson =
        _messages.map((message) => jsonEncode(message.toJson())).toList();

    await prefs.setStringList('build_messages', messagesJson);
  }

  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;
    showChatView = true;
    final userMessage = Message(
      content: content,
      role: MessageRole.user,
    );

    _messages.add(userMessage);
    _isLoading = true;
    _cancelToken = CancelToken();
    notifyListeners();
    await _saveMessages();

    try {
      final response = await _buildService.sendMessage(
          content, _projectDetails, _cancelToken);
      _lastResponse = response;

      // Check if prescriptive text already has a title (starts with #)
      String prescriptiveText = response.prescriptiveText.trim();
      String title = '';

      // If prescriptive text doesn't start with a title, add a default one
      if (!prescriptiveText.startsWith('#')) {
        title = '# Workflow Generated\n\n';
      }

      // Create a formatted response message
      final responseContent = '''
$title## Prescriptive Text
$prescriptiveText

## YAML Output
```yaml
${response.yamlOutput}
```

## Java Code
```java
${response.javaCode}
```

**Validation Status**: ${response.validationStatus}
${response.validationError != null ? '**Validation Error**: ${response.validationError}' : ''}
''';

      final assistantMessage = Message(
        content: responseContent,
        role: MessageRole.assistant,
      );

      _messages.add(assistantMessage);
      await _saveMessages();
    } catch (e) {
      // Update error handling to check for cancellation
      if (e is! DioException || (e.type != DioExceptionType.cancel)) {
        Logger.error('Error sending message: $e');
        final errorMessage = Message(
          content: 'Error: $e',
          role: MessageRole.assistant,
        );
        _messages.add(errorMessage);
        await _saveMessages();
      }
    } finally {
      _isLoading = false;
      _cancelToken = null;
      notifyListeners();
    }
  }

  void clearChat() {
    _messages.clear();
    _lastResponse = null;
    _projectDetails = null;
    _saveMessages();
    notifyListeners();
  }

  void loadSolution(Solution solution) {
    _messages.clear();

    // Make sure the project details form is hidden when loading an existing solution
    _showProjectDetailsForm = false;

    // Load project details if available
    _projectDetails = solution.projectDetails;

    // Filter out system messages and only include user messages
    final filteredMessages = solution.chatHistory
        .where((message) => message.role == MessageRole.user)
        .toList();

    // Add filtered user messages
    _messages.addAll(filteredMessages);

    // Create and add the NSL response with the YAML content
    final nslResponse = Message(
      role: MessageRole.assistant,
      content: '''# Solution YAML

```yaml
${solution.yamlContent}
``````#java
${solution.javaCode}
```
```## Prescriptive Text
${solution.prescriptiveText}
```
''',
      timestamp: DateTime.now(),
    );

    _messages.add(nslResponse);

    // Create a BuildResponse object from the solution
    _lastResponse = BuildResponse(
      conversationId:
          solution.conversationId, // No conversation ID for loaded solutions
      yamlOutput: solution.yamlContent,
      prescriptiveText: solution.prescriptiveText,
      javaCode: solution.javaCode, // No Java code in the loaded solution
      validationStatus:
          'Success', // Assume validation was successful for previous solutions
    );

    _saveMessages();
    notifyListeners();
  }

  void updateChatViewFlag(bool showChatViewFlag) {
    showChatView = showChatViewFlag;
    notifyListeners();
  }

  void selectMessage(Message message) {
    _selectedMessage = message;
    notifyListeners();
  }

  void clearSelectedMessage() {
    _selectedMessage = null;
    notifyListeners();
  }

  void setProjectDetails(ProjectDetails details) {
    _projectDetails = details;
    notifyListeners();
  }

  void clearProjectDetails() {
    _projectDetails = null;
    notifyListeners();
  }

  void showProjectDetailsFormView() {
    _showProjectDetailsForm = true;
    notifyListeners();
  }

  void hideProjectDetailsFormView() {
    _showProjectDetailsForm = false;
    notifyListeners();
  }
}
