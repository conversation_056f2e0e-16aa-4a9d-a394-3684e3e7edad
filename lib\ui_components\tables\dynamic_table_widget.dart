import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:collection/collection.dart';
import 'dart:math' as math;

/// A highly configurable and interactive data table widget with extensive user interaction features
class DynamicTableWidget extends StatefulWidget {
  /// Data to display in the table
  final List<Map<String, dynamic>> data;

  /// Initial column definitions
  final List<DynamicTableColumn> columns;

  /// Configuration options for the table
  final DynamicTableConfig config;

  /// Callback when data changes
  final Function(List<Map<String, dynamic>> data)? onDataChanged;

  /// Callback when row is expanded
  final Function(Map<String, dynamic> row)? onRowExpand;

  /// Callback when row is archived/deleted
  final Function(Map<String, dynamic> row)? onRowDelete;

  /// Widget builder for expanded row content
  final Widget Function(Map<String, dynamic> row)? expandedRowBuilder;

  const DynamicTableWidget({
    super.key,
    required this.data,
    required this.columns,
    this.config = const DynamicTableConfig(),
    this.onDataChanged,
    this.onRowExpand,
    this.onRowDelete,
    this.expandedRowBuilder,
  });

  @override
  _DynamicTableWidgetState createState() => _DynamicTableWidgetState();
}

class _DynamicTableWidgetState extends State<DynamicTableWidget> {
  late List<Map<String, dynamic>> _data;
  late List<DynamicTableColumn> _columns;
  late Map<String, bool> _expandedRows;
  late List<int> _frozenRows;
  late List<String> _frozenColumns;
  late List<String> _hiddenColumns;
  late List<String> _collapsedColumns;
  late Map<String, double> _columnWidths;
  late ViewType _currentViewType;
  late List<SortInfo> _sortInfo;
  late List<FilterInfo> _filterInfo;
  late List<String> _groupByColumns;
  late TextEditingController _searchController;
  late Set<int> _selectedRows;
  late Set<String> _selectedCells;
  late double _rowHeight;
  bool _isHeaderFrozen = false;

  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final GlobalKey _tableKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _initializeState();
  }

  @override
  void didUpdateWidget(DynamicTableWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!const DeepCollectionEquality().equals(widget.data, oldWidget.data) ||
        !const DeepCollectionEquality().equals(widget.columns, oldWidget.columns)) {
      _initializeState();
    }
  }

  void _initializeState() {
    _data = List.from(widget.data);
    _columns = List.from(widget.columns);
    _expandedRows = {};
    _frozenRows = [];
    _frozenColumns = [];
    _hiddenColumns = [];
    _collapsedColumns = [];
    _columnWidths = {};
    _currentViewType = widget.config.initialViewType;
    _sortInfo = [];
    _filterInfo = [];
    _groupByColumns = [];
    _searchController = TextEditingController();
    _selectedRows = {};
    _selectedCells = {};
    _rowHeight = widget.config.defaultRowHeight;

    // Initialize column widths
    for (var column in _columns) {
      _columnWidths[column.id] = column.initialWidth ?? 120.0;
    }
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> _getFilteredAndSortedData() {
    List<Map<String, dynamic>> result = List.from(_data);

    // Apply filters
    for (var filter in _filterInfo) {
      result = result.where((row) {
        final value = row[filter.columnId];
        return filter.predicate(value);
      }).toList();
    }

    // Apply search
    if (_searchController.text.isNotEmpty) {
      final searchTerm = _searchController.text.toLowerCase();
      result = result.where((row) {
        return row.values.any((value) {
          return value != null && value.toString().toLowerCase().contains(searchTerm);
        });
      }).toList();
    }

    // Apply sorting
    if (_sortInfo.isNotEmpty) {
      result.sort((a, b) {
        for (var sort in _sortInfo) {
          final aValue = a[sort.columnId];
          final bValue = b[sort.columnId];

          int comparison;
          if (aValue == null && bValue == null) {
            comparison = 0;
          } else if (aValue == null) {
            comparison = 1;
          } else if (bValue == null) {
            comparison = -1;
          } else if (aValue is Comparable && bValue is Comparable) {
            comparison = aValue.compareTo(bValue);
          } else {
            comparison = aValue.toString().compareTo(bValue.toString());
          }

          if (comparison != 0) {
            return sort.isAscending ? comparison : -comparison;
          }
        }
        return 0;
      });
    }

    // Apply grouping
    if (_groupByColumns.isNotEmpty) {
      // This is a simplified implementation of grouping
      // A real implementation would restructure the data to show grouped rows
      final groups = groupBy(result, (Map<String, dynamic> row) {
        return _groupByColumns.map((colId) => row[colId]?.toString() ?? "null").join("_");
      });

      result = [];
      groups.forEach((key, groupRows) {
        // Add a group header row (simplified)
        final headerRow = Map<String, dynamic>.from(groupRows.first);
        headerRow['_isGroupHeader'] = true;
        headerRow['_groupSize'] = groupRows.length;
        result.add(headerRow);

        // Add the group items
        result.addAll(groupRows);
      });
    }

    return result;
  }

  // Methods for user interactions
  void _toggleRowExpand(int index) {
    setState(() {
      final rowKey = index.toString();
      _expandedRows[rowKey] = !(_expandedRows[rowKey] ?? false);
    });

    if (_expandedRows[index.toString()] == true) {
      widget.onRowExpand?.call(_data[index]);
    }
  }

  void _addNewRow() {
    final newRow = Map<String, dynamic>.fromIterable(
        _columns.map((c) => c.id),
        value: (_) => null
    );

    setState(() {
      _data.add(newRow);
      if (widget.onDataChanged != null) {
        widget.onDataChanged!(_data);
      }
    });
  }

  void _duplicateRow(int index) {
    final duplicatedRow = Map<String, dynamic>.from(_data[index]);

    setState(() {
      _data.insert(index + 1, duplicatedRow);
      if (widget.onDataChanged != null) {
        widget.onDataChanged!(_data);
      }
    });
  }

  void _deleteRow(int index) {
    final row = _data[index];

    setState(() {
      _data.removeAt(index);
      if (widget.onDataChanged != null) {
        widget.onDataChanged!(_data);
      }
    });

    widget.onRowDelete?.call(row);
  }

  void _moveRow(int oldIndex, int newIndex) {
    if (newIndex < 0 || newIndex >= _data.length) return;

    setState(() {
      final row = _data.removeAt(oldIndex);
      _data.insert(newIndex, row);
      if (widget.onDataChanged != null) {
        widget.onDataChanged!(_data);
      }
    });
  }

  void _toggleFreezeRow(int index) {
    setState(() {
      if (_frozenRows.contains(index)) {
        _frozenRows.remove(index);
      } else {
        _frozenRows.add(index);
      }
    });
  }

  void _toggleFreezeHeader() {
    setState(() {
      _isHeaderFrozen = !_isHeaderFrozen;
    });
  }

  void _updateCellValue(int rowIndex, String columnId, dynamic value) {
    setState(() {
      _data[rowIndex][columnId] = value;
      if (widget.onDataChanged != null) {
        widget.onDataChanged!(_data);
      }
    });
  }

  void _addColumn() {
    final newColumnId = 'column_${_columns.length + 1}';

    setState(() {
      _columns.add(DynamicTableColumn(
        id: newColumnId,
        title: 'New Column',
        type: DynamicColumnType.text,
      ));

      _columnWidths[newColumnId] = 120.0;

      // Add empty values for this column to all rows
      for (var row in _data) {
        row[newColumnId] = null;
      }
    });
  }

  void _deleteColumn(String columnId) {
    setState(() {
      _columns.removeWhere((col) => col.id == columnId);
      _hiddenColumns.remove(columnId);
      _frozenColumns.remove(columnId);
      _collapsedColumns.remove(columnId);
      _columnWidths.remove(columnId);

      // Remove this column from all rows
      for (var row in _data) {
        row.remove(columnId);
      }
    });
  }

  void _duplicateColumn(String columnId) {
    final originalColumn = _columns.firstWhere((col) => col.id == columnId);
    final newColumnId = '${columnId}_copy';

    setState(() {
      _columns.add(DynamicTableColumn(
        id: newColumnId,
        title: '${originalColumn.title} (Copy)',
        type: originalColumn.type,
        editable: originalColumn.editable,
        visible: originalColumn.visible,
        initialWidth: originalColumn.initialWidth,
      ));

      _columnWidths[newColumnId] = _columnWidths[columnId] ?? 120.0;

      // Copy values from original column to the new one
      for (var row in _data) {
        row[newColumnId] = row[columnId];
      }
    });
  }

  void _toggleColumnVisibility(String columnId) {
    setState(() {
      if (_hiddenColumns.contains(columnId)) {
        _hiddenColumns.remove(columnId);
      } else {
        _hiddenColumns.add(columnId);
      }
    });
  }

  void _toggleColumnCollapse(String columnId) {
    setState(() {
      if (_collapsedColumns.contains(columnId)) {
        _collapsedColumns.remove(columnId);
      } else {
        _collapsedColumns.add(columnId);
      }
    });
  }

  void _toggleFreezeColumn(String columnId) {
    setState(() {
      if (_frozenColumns.contains(columnId)) {
        _frozenColumns.remove(columnId);
      } else {
        _frozenColumns.add(columnId);
      }
    });
  }

  void _renameColumn(String columnId, String newTitle) {
    final index = _columns.indexWhere((col) => col.id == columnId);
    if (index != -1) {
      setState(() {
        _columns[index] = _columns[index].copyWith(title: newTitle);
      });
    }
  }

  void _resizeColumn(String columnId, double delta) {
    setState(() {
      _columnWidths[columnId] = math.max(40, (_columnWidths[columnId] ?? 120.0) + delta);
    });
  }

  void _reorderColumns(int oldIndex, int newIndex) {
    if (newIndex < 0 || newIndex >= _columns.length) return;

    setState(() {
      final column = _columns.removeAt(oldIndex);
      _columns.insert(newIndex, column);
    });
  }

  void _toggleSort(String columnId) {
    setState(() {
      final existingSort = _sortInfo.firstWhereOrNull((s) => s.columnId == columnId);

      if (existingSort != null) {
        // If already sorting by this column, toggle direction or remove
        if (existingSort.isAscending) {
          _sortInfo.remove(existingSort);
          _sortInfo.add(SortInfo(columnId: columnId, isAscending: false));
        } else {
          _sortInfo.remove(existingSort);
        }
      } else {
        // Add new sort
        _sortInfo.add(SortInfo(columnId: columnId, isAscending: true));
      }
    });
  }

  void _addFilter(String columnId, bool Function(dynamic) predicate) {
    setState(() {
      _filterInfo.add(FilterInfo(columnId: columnId, predicate: predicate));
    });
  }

  void _clearFilters() {
    setState(() {
      _filterInfo.clear();
    });
  }

  void _toggleGroupBy(String columnId) {
    setState(() {
      if (_groupByColumns.contains(columnId)) {
        _groupByColumns.remove(columnId);
      } else {
        _groupByColumns.add(columnId);
      }
    });
  }

  void _toggleViewType() {
    setState(() {
      switch (_currentViewType) {
        case ViewType.table:
          _currentViewType = ViewType.gantt;
          break;
        case ViewType.gantt:
          _currentViewType = ViewType.cards;
          break;
        case ViewType.cards:
          _currentViewType = ViewType.table;
          break;
      }
    });
  }

  void _setRowHeight(double height) {
    setState(() {
      _rowHeight = math.max(30, height);
    });
  }

  void _copyRowPermalink(int rowIndex) {
    final row = _data[rowIndex];
    // Generate a permalink (actual implementation would use a real URL scheme)
    final permalink = 'app://table/${widget.config.tableId ?? 'default'}/${row['id'] ?? rowIndex}';
    Clipboard.setData(ClipboardData(text: permalink));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Permalink copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredAndSortedData();

    // Handle different view types
    switch (_currentViewType) {
      case ViewType.table:
        return _buildTableView(filteredData);
      case ViewType.gantt:
        return _buildGanttView(filteredData);
      case ViewType.cards:
        return _buildCardsView(filteredData);
    }
  }

  Widget _buildTableView(List<Map<String, dynamic>> filteredData) {
    final visibleColumns = _columns.where((col) =>
    !_hiddenColumns.contains(col.id) &&
        col.visible != false
    ).toList();

    // Split frozen columns from scrollable columns
    final frozenColumns = visibleColumns.where((col) =>
        _frozenColumns.contains(col.id)
    ).toList();

    final scrollableColumns = visibleColumns.where((col) =>
    !_frozenColumns.contains(col.id)
    ).toList();

    return Column(
      children: [
        if (widget.config.showToolbar)
          _buildToolbar(),

        Expanded(
          child: Row(
            children: [
              // Frozen columns section
              if (frozenColumns.isNotEmpty)
                SizedBox(
                  width: frozenColumns.fold<double>(
                      0,
                          (sum, col) => sum + (_columnWidths[col.id] ?? 120.0)
                  ),
                  child: Column(
                    children: [
                      // Header for frozen columns
                      if (_isHeaderFrozen)
                        _buildHeaderRow(frozenColumns),

                      // Frozen rows for frozen columns
                      if (_frozenRows.isNotEmpty)
                        SizedBox(
                          height: _frozenRows.length * _rowHeight,
                          child: ListView.builder(
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: _frozenRows.length,
                            itemBuilder: (context, index) {
                              final rowIndex = _frozenRows[index];
                              if (rowIndex >= filteredData.length) return SizedBox.shrink();
                              return _buildTableRow(frozenColumns, filteredData[rowIndex], rowIndex);
                            },
                          ),
                        ),

                      // Scrollable rows for frozen columns
                      Expanded(
                        child: ListView.builder(
                          controller: _verticalController,
                          itemCount: filteredData.length,
                          itemBuilder: (context, index) {
                            if (_frozenRows.contains(index)) {
                              return SizedBox.shrink(); // Skip frozen rows
                            }
                            return _buildTableRow(frozenColumns, filteredData[index], index);
                          },
                        ),
                      ),
                    ],
                  ),
                ),

              // Scrollable columns section
              Expanded(
                child: Column(
                  children: [
                    // Header for scrollable columns
                    if (_isHeaderFrozen)
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        controller: _horizontalController,
                        child: _buildHeaderRow(scrollableColumns),
                      ),

                    // Frozen rows for scrollable columns
                    if (_frozenRows.isNotEmpty)
                      SizedBox(
                        height: _frozenRows.length * _rowHeight,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          controller: _horizontalController,
                          child: Column(
                            children: _frozenRows.map((rowIndex) {
                              if (rowIndex >= filteredData.length) return SizedBox.shrink();
                              return _buildTableRow(scrollableColumns, filteredData[rowIndex], rowIndex);
                            }).toList(),
                          ),
                        ),
                      ),

                    // Scrollable rows for scrollable columns
                    Expanded(
                      child: NotificationListener<ScrollNotification>(
                        onNotification: (ScrollNotification notification) {
                          // Sync horizontal scrolling between header and data
                          if (notification is ScrollUpdateNotification &&
                              notification.depth == 0 &&
                              notification.metrics.axis == Axis.horizontal) {
                            _horizontalController.jumpTo(_horizontalController.offset);
                          }
                          return false;
                        },
                        child: ListView.builder(
                          controller: _verticalController,
                          itemCount: filteredData.length,
                          itemBuilder: (context, index) {
                            if (_frozenRows.contains(index)) {
                              return SizedBox.shrink(); // Skip frozen rows
                            }

                            final row = filteredData[index];
                            final isExpanded = _expandedRows[index.toString()] ?? false;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  controller: _horizontalController,
                                  child: _buildTableRow(scrollableColumns, row, index),
                                ),

                                // Expanded content for the row
                                if (isExpanded && widget.expandedRowBuilder != null)
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    width: double.infinity,
                                    child: widget.expandedRowBuilder!(row),
                                  ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Column summary section
        if (widget.config.showColumnSummary)
          _buildColumnSummary(visibleColumns, filteredData),
      ],
    );
  }

  Widget _buildHeaderRow(List<DynamicTableColumn> columns) {
    return Container(
      height: 56,
      color: Theme.of(context).colorScheme.surface,
      child: Row(
        children: columns.map((column) {
          final isCollapsed = _collapsedColumns.contains(column.id);
          final width = isCollapsed ? 40.0 : (_columnWidths[column.id] ?? 120.0);

          return GestureDetector(
            onHorizontalDragUpdate: (details) {
              if (widget.config.allowColumnResize) {
                _resizeColumn(column.id, details.delta.dx);
              }
            },
            child: Container(
              width: width,
              padding: EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(color: Colors.grey.shade300),
                  bottom: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      if (!isCollapsed)
                        Expanded(
                          child: Text(
                            column.title,
                            style: TextStyle(fontWeight: FontWeight.bold),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                      if (!isCollapsed)
                        _buildHeaderActions(column),
                    ],
                  ),

                  if (!isCollapsed && column.description != null && column.description!.isNotEmpty)
                    Text(
                      column.description!,
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildHeaderActions(DynamicTableColumn column) {
    // Find current sort status for this column
    final sortInfo = _sortInfo.firstWhereOrNull((s) => s.columnId == column.id);
    final isSorted = sortInfo != null;
    final isAscending = sortInfo?.isAscending ?? false;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Sort indicator/button
        InkWell(
          onTap: () => _toggleSort(column.id),
          child: Icon(
            isSorted
                ? (isAscending ? Icons.arrow_upward : Icons.arrow_downward)
                : Icons.unfold_more,
            size: 16,
            color: isSorted ? Theme.of(context).primaryColor : Colors.grey,
          ),
        ),

        // Column menu
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, size: 16),
          onSelected: (value) {
            switch (value) {
              case 'hide':
                _toggleColumnVisibility(column.id);
                break;
              case 'freeze':
                _toggleFreezeColumn(column.id);
                break;
              case 'collapse':
                _toggleColumnCollapse(column.id);
                break;
              case 'duplicate':
                _duplicateColumn(column.id);
                break;
              case 'delete':
                _deleteColumn(column.id);
                break;
              case 'rename':
              // Show dialog to rename column
                showDialog(
                  context: context,
                  builder: (context) {
                    final controller = TextEditingController(text: column.title);
                    return AlertDialog(
                      title: Text('Rename Column'),
                      content: TextField(controller: controller),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            _renameColumn(column.id, controller.text);
                            Navigator.pop(context);
                          },
                          child: Text('Rename'),
                        ),
                      ],
                    );
                  },
                );
                break;
              case 'group':
                _toggleGroupBy(column.id);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'hide',
              child: Text(_hiddenColumns.contains(column.id) ? 'Show Column' : 'Hide Column'),
            ),
            PopupMenuItem(
              value: 'freeze',
              child: Text(_frozenColumns.contains(column.id) ? 'Unfreeze Column' : 'Freeze Column'),
            ),
            PopupMenuItem(
              value: 'collapse',
              child: Text(_collapsedColumns.contains(column.id) ? 'Expand Column' : 'Collapse Column'),
            ),
            PopupMenuItem(
              value: 'group',
              child: Text(_groupByColumns.contains(column.id) ? 'Remove Grouping' : 'Group By This Column'),
            ),
            PopupMenuItem(
              value: 'rename',
              child: Text('Rename Column'),
            ),
            PopupMenuItem(
              value: 'duplicate',
              child: Text('Duplicate Column'),
            ),
            if (widget.config.allowColumnDelete)
              PopupMenuItem(
                value: 'delete',
                child: Text('Delete Column'),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildTableRow(
      List<DynamicTableColumn> columns,
      Map<String, dynamic> rowData,
      int rowIndex
      ) {
    final isGroupHeader = rowData['_isGroupHeader'] == true;
    final isSelected = _selectedRows.contains(rowIndex);
    final isExpanded = _expandedRows[rowIndex.toString()] ?? false;

    return Container(
      height: _rowHeight,
      color: isSelected
          ? Colors.blue.withOpacity(0.1)
          : (isGroupHeader
          ? Colors.grey.withOpacity(0.2)
          : (rowIndex % 2 == 0 ? Colors.white : Colors.grey.withOpacity(0.05))),
      child: Row(
        children: [
          // Row actions column (expand, select, etc.)
          SizedBox(
            width: 40,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                if (widget.expandedRowBuilder != null)
                  InkWell(
                    onTap: () => _toggleRowExpand(rowIndex),
                    child: Icon(
                      isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 18,
                    ),
                  ),

                if (widget.config.allowRowSelection)
                  InkWell(
                    onTap: () {
                      setState(() {
                        if (isSelected) {
                          _selectedRows.remove(rowIndex);
                        } else {
                          _selectedRows.add(rowIndex);
                        }
                      });
                    },
                    child: Icon(
                      isSelected ? Icons.check_box : Icons.check_box_outline_blank,
                      size: 18,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
                    ),
                  ),
              ],
            ),
          ),

          // Actual data cells
          ...columns.map((column) {
            final isCollapsed = _collapsedColumns.contains(column.id);
            final width = isCollapsed ? 40.0 : (_columnWidths[column.id] ?? 120.0);
            final cellKey = '$rowIndex:${column.id}';
            final isCellSelected = _selectedCells.contains(cellKey);
            final isEditable = column.editable != false && widget.config.allowInlineEdit;

            return Container(
              width: width,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(color: Colors.grey.shade200),
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
                color: isCellSelected ? Colors.blue.withOpacity(0.2) : null,
              ),
              child: isCollapsed
                  ? Icon(Icons.more_horiz, size: 18, color: Colors.grey)
                  : InkWell(
                onTap: () {
                  if (isEditable) {
                    _showCellEditDialog(rowIndex, column);
                  }

                  setState(() {
                    if (isCellSelected) {
                      _selectedCells.remove(cellKey);
                    } else {
                      _selectedCells.add(cellKey);
                    }
                  });
                },
                onDoubleTap: isEditable
                    ? () => _showCellEditDialog(rowIndex, column)
                    : null,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: _buildCellContent(column, rowData, rowIndex),
                ),
              ),
            );
          }),

          // Row actions menu
          if (widget.config.showRowActions)
            SizedBox(
              width: 40,
              child: PopupMenuButton<String>(
                icon: Icon(Icons.more_vert, size: 18),
                onSelected: (value) {
                  switch (value) {
                    case 'duplicate':
                      _duplicateRow(rowIndex);
                      break;
                    case 'delete':
                      _deleteRow(rowIndex);
                      break;
                    case 'freeze':
                      _toggleFreezeRow(rowIndex);
                      break;
                    case 'moveUp':
                      _moveRow(rowIndex, rowIndex - 1);
                      break;
                    case 'moveDown':
                      _moveRow(rowIndex, rowIndex + 1);
                      break;
                    case 'permalink':
                      _copyRowPermalink(rowIndex);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'duplicate',
                    child: Text('Duplicate Row'),
                  ),
                  if (widget.config.allowRowDelete)
                    PopupMenuItem(
                      value: 'delete',
                      child: Text('Archive/Delete Row'),
                    ),
                  PopupMenuItem(
                    value: 'freeze',
                    child: Text(_frozenRows.contains(rowIndex) ? 'Unfreeze Row' : 'Freeze Row'),
                  ),
                  if (widget.config.allowRowReordering)
                    PopupMenuItem(
                      value: 'moveUp',
                      child: Text('Move Row Up'),
                    ),
                  if (widget.config.allowRowReordering)
                    PopupMenuItem(
                      value: 'moveDown',
                      child: Text('Move Row Down'),
                    ),
                  if (widget.config.allowPermalink)
                    PopupMenuItem(
                      value: 'permalink',
                      child: Text('Copy Permalink'),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCellContent(
      DynamicTableColumn column,
      Map<String, dynamic> rowData,
      int rowIndex
      ) {
    final value = rowData[column.id];

    // Apply conditional formatting if defined for this column
    Color? textColor;
    Color? backgroundColor;

    if (column.conditionalFormatting != null) {
      final formatting = column.conditionalFormatting!.firstWhereOrNull((f) => f.predicate(value));
      if (formatting != null) {
        textColor = formatting.textColor;
        backgroundColor = formatting.backgroundColor;
      }
    }

    switch (column.type) {
      case DynamicColumnType.text:
        return Container(
          alignment: Alignment.centerLeft,
          child: Text(
            value?.toString() ?? '',
            style: TextStyle(color: textColor),
            overflow: TextOverflow.ellipsis,
          ),
        );

      case DynamicColumnType.number:
        return Container(
          alignment: Alignment.centerRight,
          child: Text(
            value?.toString() ?? '',
            style: TextStyle(color: textColor),
            overflow: TextOverflow.ellipsis,
          ),
        );

      case DynamicColumnType.boolean:
        return Container(
          alignment: Alignment.center,
          child: value == true
              ? Icon(Icons.check, color: Colors.green, size: 18)
              : (value == false
              ? Icon(Icons.close, color: Colors.red, size: 18)
              : SizedBox()),
        );

      case DynamicColumnType.date:
        return Container(
          alignment: Alignment.centerLeft,
          child: Text(
            value is DateTime
                ? value.toString().split(' ')[0]
                : (value?.toString() ?? ''),
            style: TextStyle(color: textColor),
            overflow: TextOverflow.ellipsis,
          ),
        );

      case DynamicColumnType.custom:
        if (column.customCellBuilder != null) {
          return column.customCellBuilder!(context, value, rowData, rowIndex);
        }
        return SizedBox();

      default:
        return Container(
          alignment: Alignment.centerLeft,
          child: Text(
            value?.toString() ?? '',
            style: TextStyle(color: textColor),
            overflow: TextOverflow.ellipsis,
          ),
        );
    }
  }

  void _showCellEditDialog(int rowIndex, DynamicTableColumn column) {
    final value = _data[rowIndex][column.id];

    switch (column.type) {
      case DynamicColumnType.text:
        showDialog(
          context: context,
          builder: (context) {
            final controller = TextEditingController(text: value?.toString() ?? '');
            return AlertDialog(
              title: Text('Edit ${column.title}'),
              content: TextField(controller: controller),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    _updateCellValue(rowIndex, column.id, controller.text);
                    Navigator.pop(context);
                  },
                  child: Text('Save'),
                ),
              ],
            );
          },
        );
        break;

      case DynamicColumnType.number:
        showDialog(
          context: context,
          builder: (context) {
            final controller = TextEditingController(text: value?.toString() ?? '');
            return AlertDialog(
              title: Text('Edit ${column.title}'),
              content: TextField(
                controller: controller,
                keyboardType: TextInputType.number,
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    final numValue = double.tryParse(controller.text);
                    _updateCellValue(rowIndex, column.id, numValue);
                    Navigator.pop(context);
                  },
                  child: Text('Save'),
                ),
              ],
            );
          },
        );
        break;

      case DynamicColumnType.boolean:
        _updateCellValue(rowIndex, column.id, !(value ?? false));
        break;

      case DynamicColumnType.date:
        showDatePicker(
          context: context,
          initialDate: value is DateTime ? value : DateTime.now(),
          firstDate: DateTime(1900),
          lastDate: DateTime(2100),
        ).then((selectedDate) {
          if (selectedDate != null) {
            _updateCellValue(rowIndex, column.id, selectedDate);
          }
        });
        break;

      default:
      // For custom types, we'll need a custom editor
        if (column.customEditor != null) {
          column.customEditor!(
            context,
            value,
                (newValue) {
              _updateCellValue(rowIndex, column.id, newValue);
            },
          );
        }
    }
  }

  Widget _buildToolbar() {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          // Search field
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _searchController.clear();
                    });
                  },
                )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 16),
              ),
              onChanged: (value) {
                setState(() {
                  // Search is applied in _getFilteredAndSortedData()
                });
              },
            ),
          ),

          SizedBox(width: 8),

          // Add new row button
          if (widget.config.allowAddRow)
            IconButton(
              icon: Icon(Icons.add),
              tooltip: 'Add New Row',
              onPressed: _addNewRow,
            ),

          // Add new column button
          if (widget.config.allowAddColumn)
            IconButton(
              icon: Icon(Icons.add_box_outlined),
              tooltip: 'Add New Column',
              onPressed: _addColumn,
            ),

          // Toggle view type
          if (widget.config.allowViewTypeToggle)
            IconButton(
              icon: Icon(_currentViewType == ViewType.table
                  ? Icons.view_module
                  : (_currentViewType == ViewType.gantt
                  ? Icons.view_comfy
                  : Icons.table_chart)),
              tooltip: 'Change View',
              onPressed: _toggleViewType,
            ),

          // Freeze header toggle
          IconButton(
            icon: Icon(_isHeaderFrozen ? Icons.lock : Icons.lock_open),
            tooltip: _isHeaderFrozen ? 'Unfreeze Header' : 'Freeze Header',
            onPressed: _toggleFreezeHeader,
          ),

          // Advanced filters button
          PopupMenuButton<String>(
            icon: Icon(Icons.filter_list),
            tooltip: 'Advanced Filters',
            onSelected: (value) {
              switch (value) {
                case 'clearFilters':
                  _clearFilters();
                  break;
              // Additional filter options would be here
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'clearFilters',
                child: Text('Clear All Filters'),
              ),
              // Additional filter menu items would be here
            ],
          ),

          // Actions menu
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert),
            onSelected: (value) {
              // Handle additional actions
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'export',
                child: Text('Export Data'),
              ),
              PopupMenuItem(
                value: 'settings',
                child: Text('Table Settings'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColumnSummary(
      List<DynamicTableColumn> columns,
      List<Map<String, dynamic>> filteredData
      ) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: columns.map((column) {
          // Skip if column is hidden or collapsed
          if (_hiddenColumns.contains(column.id) ||
              _collapsedColumns.contains(column.id) ||
              column.visible == false) {
            return SizedBox.shrink();
          }

          // Calculate summary based on column type and summary type
          String summaryText = '';

          if (column.summaryType != null && filteredData.isNotEmpty) {
            switch (column.summaryType) {
              case ColumnSummaryType.sum:
                if (column.type == DynamicColumnType.number) {
                  final sum = filteredData.fold<double>(
                      0,
                          (sum, row) => sum + (row[column.id] as num? ?? 0)
                  );
                  summaryText = 'Sum: ${sum.toStringAsFixed(2)}';
                }
                break;

              case ColumnSummaryType.average:
                if (column.type == DynamicColumnType.number) {
                  final sum = filteredData.fold<double>(
                      0,
                          (sum, row) => sum + (row[column.id] as num? ?? 0)
                  );
                  final avg = sum / filteredData.length;
                  summaryText = 'Avg: ${avg.toStringAsFixed(2)}';
                }
                break;

              case ColumnSummaryType.count:
                final count = filteredData.where((row) => row[column.id] != null).length;
                summaryText = 'Count: $count';
                break;

              case ColumnSummaryType.min:
                if (column.type == DynamicColumnType.number) {
                  final values = filteredData
                      .map((row) => row[column.id] as num?)
                      .where((value) => value != null)
                      .toList();

                  if (values.isNotEmpty) {
                    final min = values.reduce((a, b) => a! < b! ? a : b);
                    summaryText = 'Min: ${min!.toStringAsFixed(2)}';
                  }
                }
                break;

              case ColumnSummaryType.max:
                if (column.type == DynamicColumnType.number) {
                  final values = filteredData
                      .map((row) => row[column.id] as num?)
                      .where((value) => value != null)
                      .toList();

                  if (values.isNotEmpty) {
                    final max = values.reduce((a, b) => a! > b! ? a : b);
                    summaryText = 'Max: ${max!.toStringAsFixed(2)}';
                  }
                }
                break;

              case ColumnSummaryType.custom:
                if (column.customSummary != null) {
                  summaryText = column.customSummary!(filteredData, column.id);
                }
                break;
              case null:
                // TODO: Handle this case.
                throw UnimplementedError();
            }
          }

          return Container(
            width: _columnWidths[column.id] ?? 120.0,
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              summaryText,
              style: TextStyle(fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
            ),
          );
        }).toList(),
      ),
    );
  }

  // Gantt view implementation
  Widget _buildGanttView(List<Map<String, dynamic>> filteredData) {
    // A simplified Gantt view implementation
    return Center(
      child: Text('Gantt View Not Implemented'),
    );
  }

  // Cards view implementation
  Widget _buildCardsView(List<Map<String, dynamic>> filteredData) {
    // A simplified Cards view implementation
    return Center(
      child: Text('Cards View Not Implemented'),
    );
  }
}

/// Column definition for the dynamic table
class DynamicTableColumn {
  /// Unique identifier for the column
  final String id;

  /// Display name for the column
  final String title;

  /// Data type for the column
  final DynamicColumnType type;

  /// Whether the column is editable
  final bool? editable;

  /// Whether the column is visible
  final bool? visible;

  /// Initial width for the column
  final double? initialWidth;

  /// Optional description for the column
  final String? description;

  /// Type of summary to display for this column
  final ColumnSummaryType? summaryType;

  /// Custom cell builder for complex cell rendering
  final Widget Function(BuildContext, dynamic, Map<String, dynamic>, int)? customCellBuilder;

  /// Custom editor for complex cell editing
  final void Function(BuildContext, dynamic, Function(dynamic))? customEditor;

  /// Conditional formatting rules for this column
  final List<ConditionalFormat>? conditionalFormatting;

  /// Custom summary calculation function
  final String Function(List<Map<String, dynamic>>, String)? customSummary;

  const DynamicTableColumn({
    required this.id,
    required this.title,
    required this.type,
    this.editable,
    this.visible,
    this.initialWidth,
    this.description,
    this.summaryType,
    this.customCellBuilder,
    this.customEditor,
    this.conditionalFormatting,
    this.customSummary,
  });

  DynamicTableColumn copyWith({
    String? id,
    String? title,
    DynamicColumnType? type,
    bool? editable,
    bool? visible,
    double? initialWidth,
    String? description,
    ColumnSummaryType? summaryType,
    Widget Function(BuildContext, dynamic, Map<String, dynamic>, int)? customCellBuilder,
    void Function(BuildContext, dynamic, Function(dynamic))? customEditor,
    List<ConditionalFormat>? conditionalFormatting,
    String Function(List<Map<String, dynamic>>, String)? customSummary,
  }) {
    return DynamicTableColumn(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      editable: editable ?? this.editable,
      visible: visible ?? this.visible,
      initialWidth: initialWidth ?? this.initialWidth,
      description: description ?? this.description,
      summaryType: summaryType ?? this.summaryType,
      customCellBuilder: customCellBuilder ?? this.customCellBuilder,
      customEditor: customEditor ?? this.customEditor,
      conditionalFormatting: conditionalFormatting ?? this.conditionalFormatting,
      customSummary: customSummary ?? this.customSummary,
    );
  }
}

/// Data type for a column
enum DynamicColumnType {
  text,
  number,
  boolean,
  date,
  custom,
}

/// Summary type for column
enum ColumnSummaryType {
  sum,
  average,
  count,
  min,
  max,
  custom,
}

/// View type for the table
enum ViewType {
  table,
  gantt,
  cards,
}

/// Sorting information
class SortInfo {
  final String columnId;
  final bool isAscending;

  const SortInfo({
    required this.columnId,
    required this.isAscending,
  });
}

/// Filter information
class FilterInfo {
  final String columnId;
  final bool Function(dynamic) predicate;

  const FilterInfo({
    required this.columnId,
    required this.predicate,
  });
}

/// Conditional formatting for cells
class ConditionalFormat {
  final bool Function(dynamic) predicate;
  final Color? textColor;
  final Color? backgroundColor;

  const ConditionalFormat({
    required this.predicate,
    this.textColor,
    this.backgroundColor,
  });
}

/// Configuration options for the dynamic table
class DynamicTableConfig {
  final bool allowRowSelection;
  final bool allowRowDelete;
  final bool allowRowReordering;
  final bool allowAddRow;
  final bool allowColumnResize;
  final bool allowColumnDelete;
  final bool allowAddColumn;
  final bool allowViewTypeToggle;
  final bool allowInlineEdit;
  final bool allowPermalink;
  final bool showToolbar;
  final bool showRowActions;
  final bool showColumnSummary;
  final double defaultRowHeight;
  final ViewType initialViewType;
  final String? tableId;

  const DynamicTableConfig({
    this.allowRowSelection = true,
    this.allowRowDelete = true,
    this.allowRowReordering = true,
    this.allowAddRow = true,
    this.allowColumnResize = true,
    this.allowColumnDelete = true,
    this.allowAddColumn = true,
    this.allowViewTypeToggle = true,
    this.allowInlineEdit = true,
    this.allowPermalink = true,
    this.showToolbar = true,
    this.showRowActions = true,
    this.showColumnSummary = true,
    this.defaultRowHeight = 40,
    this.initialViewType = ViewType.table,
    this.tableId,
  });
}