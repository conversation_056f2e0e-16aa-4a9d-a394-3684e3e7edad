import 'package:flutter/foundation.dart';
import '../utils/logger.dart';

/// A class to store and retrieve input values across rebuilds
class InputValueStore extends ChangeNotifier {
  // Map to store values: sectionId:fieldId -> value
  final Map<String, dynamic> _values = {};

  // Store a value
  void setValue(String sectionId, String fieldId, dynamic value) {
    final key = '$sectionId:$fieldId';
    _values[key] = value;
    Logger.info('InputValueStore: Stored value for $key: $value');
    notifyListeners();
  }

  // Get a value
  dynamic getValue(String sectionId, String fieldId) {
    final key = '$sectionId:$fieldId';
    final value = _values[key];
    Logger.info('InputValueStore: Retrieved value for $key: $value');
    return value;
  }

  // Check if a value exists
  bool hasValue(String sectionId, String fieldId) {
    final key = '$sectionId:$fieldId';
    return _values.containsKey(key);
  }

  // Get all values for a section
  Map<String, dynamic> getSectionValues(String sectionId) {
    final result = <String, dynamic>{};
    for (final entry in _values.entries) {
      if (entry.key.startsWith('$sectionId:')) {
        final fieldId = entry.key.substring(sectionId.length + 1);
        result[fieldId] = entry.value;
      }
    }
    return result;
  }

  // Clear all values
  void clear() {
    _values.clear();
    notifyListeners();
  }

  // Debug: print all values
  void debugPrintValues() {
    Logger.info('InputValueStore: All values:');
    for (final entry in _values.entries) {
      Logger.info('  ${entry.key}: ${entry.value}');
    }
  }

  // Get all values as a map
  Map<String, dynamic> getAllValues() {
    // Return a copy of the values map to avoid modification
    return Map<String, dynamic>.from(_values);
  }
}
