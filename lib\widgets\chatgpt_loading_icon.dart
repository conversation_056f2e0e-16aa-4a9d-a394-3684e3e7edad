import 'package:flutter/material.dart';

/// A loading indicator that mimics the ChatGPT style with a square that pulses
class ChatGptLoadingIcon extends StatefulWidget {
  final Color color;
  final double size;
  final VoidCallback? onTap;

  const ChatGptLoadingIcon({
    super.key,
    required this.color,
    this.size = 24.0,
    this.onTap,
  });

  @override
  State<ChatGptLoadingIcon> createState() => _ChatGptLoadingIconState();
}

class _ChatGptLoadingIconState extends State<ChatGptLoadingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              color: widget.color
                  .withAlpha((_animation.value * 0.8 * 255).toInt()),
              borderRadius: BorderRadius.circular(4.0),
            ),
          );
        },
      ),
    );
  }
}
