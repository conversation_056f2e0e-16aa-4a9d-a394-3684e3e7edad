import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

/// A customizable widget for selecting a year and month.
///
/// This widget provides a rich set of customization options for year and month selection,
/// including various display formats, styling options, and interaction capabilities.
class YearMonthWidget extends StatefulWidget {
  /// The initial year value.
  final int? initialYear;

  /// The initial month value (1-12).
  final int? initialMonth;

  /// The minimum selectable year.
  final int? minYear;

  /// The maximum selectable year.
  final int? maxYear;

  /// The format for displaying the selected year and month.
  final String format;

  /// The locale for formatting (e.g., 'en_US', 'fr_FR').
  final String locale;

  /// Whether to show the year.
  final bool showYear;

  /// Whether to show the month.
  final bool showMonth;

  /// Whether to show month as text (e.g., "January") instead of number.
  final bool showMonthAsText;

  /// Whether to show full month name (e.g., "January" vs "Jan").
  final bool showFullMonthName;

  /// Whether to show the year first, then month.
  final bool yearFirst;

  /// The separator between year and month.
  final String separator;

  /// The text color of the widget.
  final Color textColor;

  /// The background color of the widget.
  final Color backgroundColor;

  /// The border color of the widget.
  final Color borderColor;

  /// The width of the border.
  final double borderWidth;

  /// The border radius of the widget.
  final double borderRadius;

  /// Whether the widget has a border.
  final bool hasBorder;

  /// Whether the widget is read-only.
  final bool isReadOnly;

  /// Whether the widget is disabled.
  final bool isDisabled;

  /// The label text for the widget.
  final String? label;

  /// The hint text for the widget.
  final String? hint;

  /// The helper text for the widget.
  final String? helperText;

  /// The error text for the widget.
  final String? errorText;

  /// Whether to show a prefix icon.
  final bool showPrefix;

  /// Whether to show a suffix icon.
  final bool showSuffix;

  /// The prefix icon to display.
  final IconData? prefixIcon;

  /// The suffix icon to display.
  final IconData? suffixIcon;

  /// The font size of the text.
  final double fontSize;

  /// The font weight of the text.
  final FontWeight fontWeight;

  /// Whether the widget is in compact mode.
  final bool isCompact;

  /// The text alignment of the widget.
  final TextAlign textAlign;

  /// Whether the widget has a shadow.
  final bool hasShadow;

  /// The elevation of the shadow.
  final double elevation;

  /// Whether to use dark theme colors.
  final bool isDarkTheme;

  /// Whether the widget has animation.
  final bool hasAnimation;

  /// The prefix text to display.
  final String? prefixText;

  /// The suffix text to display.
  final String? suffixText;

  /// Whether to show dropdown for year selection.
  final bool showYearDropdown;

  /// Whether to show dropdown for month selection.
  final bool showMonthDropdown;

  /// Whether to show arrows for year navigation.
  final bool showYearArrows;

  /// Whether to show arrows for month navigation.
  final bool showMonthArrows;

  /// Whether to show a calendar icon.
  final bool showCalendarIcon;

  /// Whether to show a clear button.
  final bool showClearButton;

  /// Whether to allow future dates.
  final bool allowFutureDates;

  /// Whether to allow past dates.
  final bool allowPastDates;

  /// Whether to default to current year and month if not specified.
  final bool defaultToCurrent;

  /// The display mode for the widget.
  final YearMonthDisplayMode displayMode;

  /// The callback when the year or month changes.
  final Function(int year, int month)? onChanged;

  /// The padding around the widget.
  final EdgeInsetsGeometry padding;

  /// The width of the widget.
  final double? width;

  /// The height of the widget.
  final double? height;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// More general gesture callback
  ///
  /// This function is called when the widget is tapped.
  final GestureTapCallback? gestureTapCallback;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Creates a year month widget.
  const YearMonthWidget({
    super.key,
    this.initialYear,
    this.initialMonth,
    this.minYear,
    this.maxYear,
    this.format = 'yMMMM',
    this.locale = 'en_US',
    this.showYear = true,
    this.showMonth = true,
    this.showMonthAsText = true,
    this.showFullMonthName = true,
    this.yearFirst = true,
    this.separator = ' ',
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = false,
    this.showSuffix = false,
    this.prefixIcon,
    this.suffixIcon,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.textAlign = TextAlign.start,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.hasAnimation = false,
    this.prefixText,
    this.suffixText,
    this.showYearDropdown = true,
    this.showMonthDropdown = true,
    this.showYearArrows = false,
    this.showMonthArrows = false,
    this.showCalendarIcon = true,
    this.showClearButton = false,
    this.allowFutureDates = true,
    this.allowPastDates = true,
    this.defaultToCurrent = true,
    this.displayMode = YearMonthDisplayMode.dropdown,
    this.onChanged,
    this.padding = const EdgeInsets.all(12.0),
    this.width,
    this.height,
    // Advanced Interaction Properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.gestureTapCallback,
    this.enableFeedback = true,
  });

  /// Creates a YearMonthWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the YearMonthWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialYear": 2023,
  ///   "initialMonth": 6,
  ///   "showYear": true,
  ///   "showMonth": true,
  ///   "yearFirst": true,
  ///   "textColor": "black",
  ///   "backgroundColor": "white"
  /// }
  /// ```
  factory YearMonthWidget.fromJson(Map<String, dynamic> json) {
    // Handle text align
    TextAlign textAlign = TextAlign.start;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'end':
        case 'right':
          textAlign = TextAlign.end;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
        default:
          textAlign = TextAlign.start;
      }
    }

    // Handle font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] is String) {
        switch (json['fontWeight'].toString().toLowerCase()) {
          case 'bold':
            fontWeight = FontWeight.bold;
            break;
          case 'normal':
            fontWeight = FontWeight.normal;
            break;
          case 'light':
            fontWeight = FontWeight.w300;
            break;
        }
      } else if (json['fontWeight'] is int) {
        final weight = json['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    // Handle display mode
    YearMonthDisplayMode displayMode = YearMonthDisplayMode.inline;
    if (json['displayMode'] != null) {
      switch (json['displayMode'].toString().toLowerCase()) {
        case 'dropdown':
          displayMode = YearMonthDisplayMode.dropdown;
          break;
        case 'dialog':
          displayMode = YearMonthDisplayMode.dialog;
          break;
        case 'bottomsheet':
        case 'bottom_sheet':
          displayMode = YearMonthDisplayMode.bottomSheet;
          break;
        case 'separatefields':
        case 'separate_fields':
          displayMode = YearMonthDisplayMode.separateFields;
          break;
        case 'chip':
          displayMode = YearMonthDisplayMode.chip;
          break;
        default:
          displayMode = YearMonthDisplayMode.inline;
      }
    }

    // Handle icons
    IconData? prefixIcon;
    if (json['prefixIcon'] != null) {
      switch (json['prefixIcon'].toString().toLowerCase()) {
        case 'calendar':
          prefixIcon = Icons.calendar_today;
          break;
        case 'date':
          prefixIcon = Icons.date_range;
          break;
        case 'time':
          prefixIcon = Icons.access_time;
          break;
        case 'event':
          prefixIcon = Icons.event;
          break;
        case 'schedule':
          prefixIcon = Icons.schedule;
          break;
      }
    }

    IconData? suffixIcon;
    if (json['suffixIcon'] != null) {
      switch (json['suffixIcon'].toString().toLowerCase()) {
        case 'calendar':
          suffixIcon = Icons.calendar_today;
          break;
        case 'date':
          suffixIcon = Icons.date_range;
          break;
        case 'time':
          suffixIcon = Icons.access_time;
          break;
        case 'event':
          suffixIcon = Icons.event;
          break;
        case 'schedule':
          suffixIcon = Icons.schedule;
          break;
        case 'arrow_down':
          suffixIcon = Icons.arrow_drop_down;
          break;
        case 'arrow_up':
          suffixIcon = Icons.arrow_drop_up;
          break;
        case 'clear':
          suffixIcon = Icons.clear;
          break;
      }
    }

    // Handle colors
    Color parseColor(dynamic colorValue) {
      if (colorValue == null) return Colors.black;

      if (colorValue is String) {
        switch (colorValue.toLowerCase()) {
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'red':
            return Colors.red;
          case 'green':
            return Colors.green;
          case 'blue':
            return Colors.blue;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'transparent':
            return Colors.transparent;
          default:
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse(
                  '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
                );
                return Color(hexValue);
              } catch (e) {
                return Colors.black;
              }
            }
            return Colors.black;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return Colors.black;
    }

    // Handle padding
    EdgeInsetsGeometry padding = const EdgeInsets.all(12.0);
    if (json['padding'] != null) {
      if (json['padding'] is double || json['padding'] is int) {
        final paddingValue = (json['padding'] as num).toDouble();
        padding = EdgeInsets.all(paddingValue);
      } else if (json['padding'] is Map) {
        final paddingMap = json['padding'] as Map<String, dynamic>;
        padding = EdgeInsets.fromLTRB(
          (paddingMap['left'] as num?)?.toDouble() ?? 0.0,
          (paddingMap['top'] as num?)?.toDouble() ?? 0.0,
          (paddingMap['right'] as num?)?.toDouble() ?? 0.0,
          (paddingMap['bottom'] as num?)?.toDouble() ?? 0.0,
        );
      }
    }

    return YearMonthWidget(
      initialYear: json['initialYear'] as int?,
      initialMonth: json['initialMonth'] as int?,
      minYear: json['minYear'] as int?,
      maxYear: json['maxYear'] as int?,
      format: json['format'] as String? ?? 'yMMMM',
      locale: json['locale'] as String? ?? 'en_US',
      showYear: json['showYear'] as bool? ?? true,
      showMonth: json['showMonth'] as bool? ?? true,
      showMonthAsText: json['showMonthAsText'] as bool? ?? true,
      showFullMonthName: json['showFullMonthName'] as bool? ?? true,
      yearFirst: json['yearFirst'] as bool? ?? true,
      separator: json['separator'] as String? ?? ' ',
      textColor: parseColor(json['textColor']),
      backgroundColor: parseColor(json['backgroundColor']),
      //borderColor: parseColor(json['borderColor']),
      borderColor: parseColor(json['borderColor']) ,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      showPrefix: json['showPrefix'] as bool? ?? false,
      showSuffix: json['showSuffix'] as bool? ?? false,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      isCompact: json['isCompact'] as bool? ?? false,
      textAlign: textAlign,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      prefixText: json['prefixText'] as String?,
      suffixText: json['suffixText'] as String?,
      showYearDropdown: json['showYearDropdown'] as bool? ?? true,
      showMonthDropdown: json['showMonthDropdown'] as bool? ?? true,
      showYearArrows: json['showYearArrows'] as bool? ?? false,
      showMonthArrows: json['showMonthArrows'] as bool? ?? false,
      showCalendarIcon: json['showCalendarIcon'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? false,
      allowFutureDates: json['allowFutureDates'] as bool? ?? true,
      allowPastDates: json['allowPastDates'] as bool? ?? true,
      defaultToCurrent: json['defaultToCurrent'] as bool? ?? true,
      displayMode: displayMode,
      padding: padding,
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      // Advanced Interaction Properties
      autofocus: json['autofocus'] as bool? ?? false,
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      hoverColor:
          json['hoverColor'] != null ? parseColor(json['hoverColor']) : null,
      focusColor:
          json['focusColor'] != null ? parseColor(json['focusColor']) : null,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onHover:
          json['onHover'] == true
              ? (isHovered) {
                debugPrint(
                  'YearMonth hover state changed: isHovered: $isHovered',
                );
              }
              : null,
      onFocus:
          json['onFocus'] == true
              ? (isFocused) {
                debugPrint(
                  'YearMonth focus state changed: isFocused: $isFocused',
                );
              }
              : null,
      onTap:
          json['onTap'] == true
              ? () {
                debugPrint('YearMonth tapped');
              }
              : null,
      onDoubleTap:
          json['onDoubleTap'] == true
              ? () {
                debugPrint('YearMonth double-tapped');
              }
              : null,
      onLongPress:
          json['onLongPress'] == true
              ? () {
                debugPrint('YearMonth long-pressed');
              }
              : null,
      gestureTapCallback:
          json['gestureTapCallback'] == true
              ? () {
                debugPrint('YearMonth tapped with gesture tap callback');
              }
              : null,
      onChanged:
          json['onChanged'] == true
              ? (year, month) {
                debugPrint('YearMonth changed: year=$year, month=$month');
              }
              : null,
    );
  }

  /// Converts the YearMonthWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'initialYear': initialYear,
      'initialMonth': initialMonth,
      'minYear': minYear,
      'maxYear': maxYear,
      'format': format,
      'locale': locale,
      'showYear': showYear,
      'showMonth': showMonth,
      'showMonthAsText': showMonthAsText,
      'showFullMonthName': showFullMonthName,
      'yearFirst': yearFirst,
      'separator': separator,
      'textColor': _colorToString(textColor),
      'backgroundColor': _colorToString(backgroundColor),
      'borderColor': _colorToString(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'showPrefix': showPrefix,
      'showSuffix': showSuffix,
      'fontSize': fontSize,
      'fontWeight': _fontWeightToString(fontWeight),
      'isCompact': isCompact,
      'textAlign': _textAlignToString(textAlign),
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'hasAnimation': hasAnimation,
      'showYearDropdown': showYearDropdown,
      'showMonthDropdown': showMonthDropdown,
      'showYearArrows': showYearArrows,
      'showMonthArrows': showMonthArrows,
      'showCalendarIcon': showCalendarIcon,
      'showClearButton': showClearButton,
      'allowFutureDates': allowFutureDates,
      'allowPastDates': allowPastDates,
      'defaultToCurrent': defaultToCurrent,
      'displayMode': _displayModeToString(displayMode),
      'enableFeedback': enableFeedback,
      'autofocus': autofocus,
    };

    // Add optional properties
    if (label != null) json['label'] = label;
    if (hint != null) json['hint'] = hint;
    if (helperText != null) json['helperText'] = helperText;
    if (errorText != null) json['errorText'] = errorText;
    if (prefixText != null) json['prefixText'] = prefixText;
    if (suffixText != null) json['suffixText'] = suffixText;
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;
    if (width != null) json['width'] = width;
    if (height != null) json['height'] = height;

    // Add icon properties
    if (prefixIcon != null) json['prefixIcon'] = _iconToString(prefixIcon);
    if (suffixIcon != null) json['suffixIcon'] = _iconToString(suffixIcon);

    // Add color properties
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);

    // Add callback flags
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;
    if (onTap != null) json['onTap'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;
    if (gestureTapCallback != null) json['gestureTapCallback'] = true;
    if (onChanged != null) json['onChanged'] = true;

    // Add padding
    if (padding is EdgeInsets) {
      final EdgeInsets p = padding as EdgeInsets;
      json['padding'] = {
        'left': p.left,
        'top': p.top,
        'right': p.right,
        'bottom': p.bottom,
      };
    } else {
      json['padding'] = 12.0;
    }

    return json;
  }

  /// Converts a Color to a string representation
  String _colorToString(Color color) {
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.red) return 'red';
    if (color == Colors.green) return 'green';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.transparent) return 'transparent';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF);
    final g = ((colorValue >> 8) & 0xFF);
    final b = (colorValue & 0xFF);
    final a = ((colorValue >> 24) & 0xFF);

    return '#${a.toRadixString(16).padLeft(2, '0')}${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';
  }

  /// Converts a FontWeight to a string representation
  String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.bold) return 'bold';
    if (weight == FontWeight.normal) return 'normal';
    if (weight == FontWeight.w100) return '100';
    if (weight == FontWeight.w200) return '200';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w400) return 'normal';
    if (weight == FontWeight.w500) return '500';
    if (weight == FontWeight.w600) return '600';
    if (weight == FontWeight.w700) return 'bold';
    if (weight == FontWeight.w800) return '800';
    if (weight == FontWeight.w900) return '900';

    return 'normal';
  }

  /// Converts a TextAlign to a string representation
  String _textAlignToString(TextAlign align) {
    if (align == TextAlign.center) return 'center';
    if (align == TextAlign.end) return 'end';
    if (align == TextAlign.justify) return 'justify';

    return 'start';
  }

  /// Converts a YearMonthDisplayMode to a string representation
  String _displayModeToString(YearMonthDisplayMode mode) {
    switch (mode) {
      case YearMonthDisplayMode.dropdown:
        return 'dropdown';
      case YearMonthDisplayMode.dialog:
        return 'dialog';
      case YearMonthDisplayMode.bottomSheet:
        return 'bottomSheet';
      case YearMonthDisplayMode.separateFields:
        return 'separateFields';
      case YearMonthDisplayMode.chip:
        return 'chip';
      case YearMonthDisplayMode.inline:
        return 'inline';
    }
  }

  /// Converts an IconData to a string representation
  String? _iconToString(IconData? icon) {
    if (icon == null) return null;

    if (icon == Icons.calendar_today) return 'calendar';
    if (icon == Icons.date_range) return 'date';
    if (icon == Icons.access_time) return 'time';
    if (icon == Icons.event) return 'event';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.arrow_drop_down) return 'arrow_down';
    if (icon == Icons.arrow_drop_up) return 'arrow_up';
    if (icon == Icons.clear) return 'clear';

    return null;
  }

  @override
  State<YearMonthWidget> createState() => _YearMonthWidgetState();
}

/// The display mode for the year month widget.
enum YearMonthDisplayMode {
  /// Show year and month inline.
  inline,

  /// Show as a dropdown.
  dropdown,

  /// Show as a dialog.
  dialog,

  /// Show as a bottom sheet.
  bottomSheet,

  /// Show as separate fields.
  separateFields,

  /// Show as a compact chip.
  chip,
}

class _YearMonthWidgetState extends State<YearMonthWidget>
    with SingleTickerProviderStateMixin {
  late int _selectedYear;
  late int _selectedMonth;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isOpen = false;
  bool _showingYearSelection = true; // Track which selection mode we're in
  bool _showingMonthSelection = false;
  final LayerLink _layerLink = LayerLink();
  // Advanced interaction state variables
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _isHovered = false;
  OverlayEntry? _overlayEntry;
  @override
  void initState() {
    super.initState();

    // Initialize with current date if defaultToCurrent is true
    final now = DateTime.now();

    if (widget.defaultToCurrent) {
      _selectedYear = widget.initialYear ?? now.year;
      _selectedMonth = widget.initialMonth ?? now.month;
    } else {
      _selectedYear = widget.initialYear ?? 2023;
      _selectedMonth = widget.initialMonth ?? 1;
    }

    // Apply min/max year constraints
    if (widget.minYear != null && _selectedYear < widget.minYear!) {
      _selectedYear = widget.minYear!;
    }
    if (widget.maxYear != null && _selectedYear > widget.maxYear!) {
      _selectedYear = widget.maxYear!;
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.hasAnimation) {
      _animationController.repeat(reverse: true);
    }

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();

    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    super.dispose();
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (isHovered != _isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  void _incrementYear() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _selectedYear++;
      if (widget.maxYear != null && _selectedYear > widget.maxYear!) {
        _selectedYear = widget.maxYear!;
      }
    });

    _notifyChange();
  }

  void _decrementYear() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _selectedYear--;
      if (widget.minYear != null && _selectedYear < widget.minYear!) {
        _selectedYear = widget.minYear!;
      }
    });

    _notifyChange();
  }

  void _incrementMonth() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _selectedMonth++;
      if (_selectedMonth > 12) {
        _selectedMonth = 1;
        if (widget.showYear) {
          _incrementYear();
        }
      }
    });

    _notifyChange();
  }

  void _decrementMonth() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _selectedMonth--;
      if (_selectedMonth < 1) {
        _selectedMonth = 12;
        if (widget.showYear) {
          _decrementYear();
        }
      }
    });

    _notifyChange();
  }

  void _notifyChange() {
    if (widget.onChanged != null) {
      widget.onChanged!(_selectedYear, _selectedMonth);
    }
  }

  void _clearSelection() {
    if (widget.isDisabled || widget.isReadOnly) return;

    final now = DateTime.now();

    setState(() {
      if (widget.defaultToCurrent) {
        _selectedYear = now.year;
        _selectedMonth = now.month;
      } else {
        _selectedYear = widget.initialYear ?? 2023;
        _selectedMonth = widget.initialMonth ?? 1;
      }
    });

    _notifyChange();
  }

  void _showYearMonthPicker() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _isOpen = true;
    });
    if (widget.displayMode == YearMonthDisplayMode.dropdown) {
      _showYearMonthPickerOverlay();
    } else if (widget.displayMode == YearMonthDisplayMode.dialog) {
      showDialog(
        context: context,
        builder: (context) => _buildYearMonthPickerDialog(),
      ).then((_) {
        setState(() {
          _isOpen = false;
        });
      });
    } else if (widget.displayMode == YearMonthDisplayMode.bottomSheet) {
      showModalBottomSheet(
        context: context,
        builder: (context) => _buildYearMonthPickerSheet(),
      ).then((_) {
        setState(() {
          _isOpen = false;
        });
      });
    }
  }

  void _removeYearPickerOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _showYearMonthPickerOverlay() {
    _removeYearPickerOverlay();
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final screenSize = MediaQuery.of(context).size;

    const double desiredHeight = 225.0;
    const double padding = 20.0;

    final double spaceBelow = screenSize.height - (offset.dy + size.height);
    final double spaceAbove = offset.dy;

    bool showAbove = false;
    double finalHeight = desiredHeight;

    if (spaceBelow < desiredHeight + padding) {
      if (spaceAbove > spaceBelow && spaceAbove > desiredHeight + padding) {
        showAbove = true;
      } else if (spaceAbove > spaceBelow) {
        showAbove = true;
        finalHeight = spaceAbove - padding;
      } else {
        finalHeight = spaceBelow - padding;
      }
    }

    final Offset popoverOffset =
        showAbove
            ? Offset(0.0, -(finalHeight + 4))
            : Offset(0.0, size.height + 4);

    _overlayEntry = OverlayEntry(
      builder:
          (context) => Stack(
            children: [
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    setState(() {
                      _isOpen = false;
                      _removeYearPickerOverlay();
                    });
                  },
                ),
              ),
              Positioned(
                left: popoverOffset.dx,
                top: popoverOffset.dy,
                width: widget.width ?? size.width,
                child: CompositedTransformFollower(
                  link: _layerLink,
                  showWhenUnlinked: false,
                  offset: popoverOffset,
                  child: Material(
                    elevation: 8.0,
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                    child: Container(
                      constraints: BoxConstraints(
                        maxHeight: finalHeight,
                        minHeight: 200,
                      ),
                      padding: const EdgeInsets.all(18.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          StatefulBuilder(
                            builder: (context, parentSetState) {
                              return _buildYearMonthPickerContent(
                                parentSetState: parentSetState,
                              );
                            },
                          ),

                          // const SizedBox(height: 12),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.end,
                          //   children: [
                          //     TextButton(
                          //       onPressed: () {
                          //         _removeYearPickerOverlay();
                          //       },
                          //       child: const Text('Cancel'),
                          //     ),
                          //     const SizedBox(width: 8),
                          //     TextButton(
                          //       onPressed: () {
                          //         _notifyChange();
                          //         _removeYearPickerOverlay();
                          //       },
                          //       child: const Text('OK'),
                          //     ),
                          //   ],
                          // ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  Widget _buildYearMonthPickerDialog() {
    return AlertDialog(
      title: Text('Select Year'),
      content: _buildYearMonthPickerContent(),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            _notifyChange();
            Navigator.of(context).pop();
          },
          child: Text('OK'),
        ),
      ],
    );
  }

  Widget _buildYearMonthPickerSheet() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Select Year and Month',
            style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16.0),
          _buildYearMonthPickerContent(),
          const SizedBox(height: 16.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  _notifyChange();
                  Navigator.of(context).pop();
                },
                child: Text('OK'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildYearMonthPickerContent({Function? parentSetState}) {
    if (_showingYearSelection && widget.showYear) {
      return _buildYearGrid(parentSetState: parentSetState);
    } else if (_showingMonthSelection && widget.showMonth) {
      return _buildMonthGrid(parentSetState: parentSetState);
    } else {
      // Fallback to original layout
      return Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showYear) _buildYearSelector(),
          if (widget.showYear && widget.showMonth) const SizedBox(width: 16.0),
          if (widget.showMonth) _buildMonthSelector(),
        ],
      );
    }
  }

  Widget _buildYearGrid({Function? parentSetState}) {
    final currentYear = DateTime.now().year;
    final minYear = widget.minYear ?? (currentYear - 15);
    final maxYear = widget.maxYear ?? (currentYear + 15);

    // Generate years around the selected year for better UX
    final startYear = (_selectedYear - 15).clamp(minYear, maxYear);
    final endYear = (_selectedYear + 15).clamp(minYear, maxYear);

    final years = <int>[];
    for (int year = startYear; year <= endYear; year++) {
      years.add(year);
    }

    return Container(
      width: 280,
      constraints: const BoxConstraints(maxHeight: 300),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with navigation arrows
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.keyboard_arrow_up),
                onPressed: () {
                  setState(() {
                    _selectedYear = (_selectedYear - 15).clamp(
                      minYear,
                      maxYear,
                    );
                  });
                  if (parentSetState != null) {
                    parentSetState(() {});
                  }
                },
              ),
              Text(
                _selectedYear.toString(),
                style: TextStyle(
                  fontSize: widget.fontSize + 2,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.keyboard_arrow_down),
                onPressed: () {
                  setState(() {
                    _selectedYear = (_selectedYear + 15).clamp(
                      minYear,
                      maxYear,
                    );
                  });
                  if (parentSetState != null) {
                    parentSetState(() {});
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Year grid
          Flexible(
            child: GridView.builder(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 2.5,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: years.length,
              itemBuilder: (context, index) {
                final year = years[index];
                final isSelected = year == _selectedYear;
                final isCurrentYear = year == currentYear;

                return GestureDetector(
                  onTap:
                      widget.isDisabled || widget.isReadOnly
                          ? null
                          : () {
                            setState(() {
                              _selectedYear = year;
                              if (widget.showMonth) {
                                // Move to month selection
                                _showingYearSelection = false;
                                _showingMonthSelection = true;
                              } else {
                                // Only year selection, close picker
                                _isOpen = false;
                              }
                            });
                            if (parentSetState != null) {
                              parentSetState(() {});
                            }
                            _notifyChange();
                          },
                  child: Container(
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Color(0xFF0058FF)
                              : isCurrentYear
                              ? Colors.grey.shade200
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color:
                            isSelected
                                ? Color(0xFF0058FF)
                                : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        year.toString(),
                        style: TextStyle(
                          fontSize: widget.fontSize - 2,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                          color:
                              isSelected
                                  ? Colors.white
                                  : isCurrentYear
                                  ? Colors.black
                                  : widget.textColor,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthGrid({Function? parentSetState}) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return Container(
      width: 280,
      constraints: const BoxConstraints(maxHeight: 250),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with back button and selected year
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    _showingYearSelection = true;
                    _showingMonthSelection = false;
                  });
                  if (parentSetState != null) {
                    parentSetState(() {});
                  }
                },
              ),
              Text(
                _selectedYear.toString(),
                style: TextStyle(
                  fontSize: widget.fontSize + 2,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
              ),
              const SizedBox(width: 48), // Balance the back button
            ],
          ),
          const SizedBox(height: 8),
          // Month grid
          Flexible(
            child: GridView.builder(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 2.5,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: 12,
              itemBuilder: (context, index) {
                final month = index + 1;
                final isSelected = month == _selectedMonth;
                final isCurrentMonth =
                    month == DateTime.now().month &&
                    _selectedYear == DateTime.now().year;

                return GestureDetector(
                  onTap:
                      widget.isDisabled || widget.isReadOnly
                          ? null
                          : () {
                            setState(() {
                              _selectedMonth = month;
                              _isOpen =
                                  false; // Close picker after month selection
                              _showingYearSelection =
                                  true; // Reset for next time
                              _showingMonthSelection = false;
                            });
                            if (parentSetState != null) {
                              parentSetState(() {});
                              _removeYearPickerOverlay();
                            }
                            _notifyChange();
                          },
                  child: Container(
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Color(0xFF0058FF)
                              : isCurrentMonth
                              ? Colors.grey.shade200
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color:
                            isSelected
                                ? Color(0xFF0058FF)
                                : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        months[index],
                        style: TextStyle(
                          fontSize: widget.fontSize - 2,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                          color:
                              isSelected
                                  ? Colors.white
                                  : isCurrentMonth
                                  ? Colors.black
                                  : widget.textColor,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYearSelector() {
    final minYear = widget.minYear ?? 1900;
    final maxYear = widget.maxYear ?? 2100;
    final years = List<int>.generate(maxYear - minYear + 1, (i) => minYear + i);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.showYearArrows)
          IconButton(
            icon: Icon(Icons.arrow_back_ios),
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _decrementYear,
          ),
        if (widget.showYearDropdown)
          DropdownButton<int>(
            value: _selectedYear,
            onChanged:
                widget.isDisabled || widget.isReadOnly
                    ? null
                    : (int? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedYear = newValue;
                        });
                        _notifyChange();
                      }
                    },
            items:
                years.map<DropdownMenuItem<int>>((int year) {
                  return DropdownMenuItem<int>(
                    value: year,
                    child: Text(year.toString()),
                  );
                }).toList(),
          )
        else
          Text(
            _selectedYear.toString(),
            style: TextStyle(
              fontSize: widget.fontSize,
              fontWeight: widget.fontWeight,
            ),
          ),
        if (widget.showYearArrows)
          IconButton(
            icon: Icon(Icons.arrow_forward_ios),
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _incrementYear,
          ),
      ],
    );
  }

  Widget _buildMonthSelector() {
    final months = List<int>.generate(12, (i) => i + 1);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.showMonthArrows)
          IconButton(
            icon: Icon(Icons.arrow_back_ios),
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _decrementMonth,
          ),
        if (widget.showMonthDropdown)
          DropdownButton<int>(
            value: _selectedMonth,
            onChanged:
                widget.isDisabled || widget.isReadOnly
                    ? null
                    : (int? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedMonth = newValue;
                        });
                        _notifyChange();
                      }
                    },
            items:
                months.map<DropdownMenuItem<int>>((int month) {
                  return DropdownMenuItem<int>(
                    value: month,
                    child: Text(
                      widget.showMonthAsText
                          ? DateFormat(
                            widget.showFullMonthName ? 'MMMM' : 'MMM',
                            widget.locale,
                          ).format(DateTime(2023, month))
                          : month.toString(),
                    ),
                  );
                }).toList(),
          )
        else
          Text(
            widget.showMonthAsText
                ? DateFormat(
                  widget.showFullMonthName ? 'MMMM' : 'MMM',
                  widget.locale,
                ).format(DateTime(2023, _selectedMonth))
                : _selectedMonth.toString(),
            style: TextStyle(
              fontSize: widget.fontSize,
              fontWeight: widget.fontWeight,
            ),
          ),
        if (widget.showMonthArrows)
          IconButton(
            icon: Icon(Icons.arrow_forward_ios),
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _incrementMonth,
          ),
      ],
    );
  }

  String _getFormattedYearMonth() {
    if (widget.format.isNotEmpty) {
      try {
        return DateFormat(
          widget.format,
          widget.locale,
        ).format(DateTime(_selectedYear, _selectedMonth));
      } catch (e) {
        return 'Invalid format';
      }
    }

    if (!widget.showYear && !widget.showMonth) {
      return '';
    }

    if (!widget.showYear) {
      return widget.showMonthAsText
          ? DateFormat(
            widget.showFullMonthName ? 'MMMM' : 'MMM',
            widget.locale,
          ).format(DateTime(2023, _selectedMonth))
          : _selectedMonth.toString();
    }

    if (!widget.showMonth) {
      return _selectedYear.toString();
    }

    final yearStr = _selectedYear.toString();
    final monthStr =
        widget.showMonthAsText
            ? DateFormat(
              widget.showFullMonthName ? 'MMMM' : 'MMM',
              widget.locale,
            ).format(DateTime(2023, _selectedMonth))
            : _selectedMonth.toString();

    return widget.yearFirst
        ? '$yearStr${widget.separator}$monthStr'
        : '$monthStr${widget.separator}$yearStr';
  }

  Widget _buildInlineWidget() {
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Get responsive font size based on screen width
    double getResponsiveTitleFontSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 16.0; // Large
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium
      } else {
        return 14.0; // Default for very small screens
      }
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double responsiveTitleFontSize = getResponsiveTitleFontSize(
      screenWidth,
    );

    // Get responsive icon size based on screen width
    double getResponsiveIconSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 22.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 20.0; // Large
      } else if (screenWidth >= 1280) {
        return 18.0; // Medium
      } else if (screenWidth >= 768) {
        return 16.0; // Small
      } else {
        return 12.0; // Extra Small (fallback for very small screens)
      }
    }

    final double responsiveIconSize = getResponsiveIconSize(screenWidth);

    Widget content = AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: _getResponsiveHeight(context),
      padding: _getResponsivePadding(context),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(4),
        border: widget.hasBorder
    ? Border.all(
        color: _isHovered
            ? (widget.hoverColor ?? const Color(0xFF0058FF))
            : _isFocused
                ? (widget.focusColor ?? const Color(0xFF0058FF))
                : const Color(0xFFCCCCCC) ,// Default #CCCCCC
        width: widget.borderWidth,
      )
    : null,
        // boxShadow: widget.hasShadow
        //     ? [
        //         BoxShadow(
        //           color: Colors.black.withOpacity(0.2),
        //           blurRadius: widget.elevation,
        //           offset: Offset(0, widget.elevation / 2),
        //         ),
        //       ]
        //     : null,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (widget.prefixText != null) ...[
            Text(
              widget.prefixText!,
              style: TextStyle(
                color: effectiveTextColor.withOpacity(0.7),
                fontSize: widget.fontSize,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              _getFormattedYearMonth(),
              style: TextStyle(
                color: effectiveTextColor.withOpacity(0.6),
                fontSize: responsiveTitleFontSize,
                fontWeight: widget.fontWeight,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          if (widget.suffixText != null) ...[
            const SizedBox(width: 8),
            Text(
              widget.suffixText!,
              style: TextStyle(
                color: effectiveTextColor.withOpacity(0.7),
                fontSize: widget.fontSize,
              ),
            ),
          ],
          if (widget.showCalendarIcon) ...[
            const SizedBox(width: 8),
            SvgPicture.asset(
              _isHovered
                  ? 'assets/images/icon-date-hover.svg'
                  : 'assets/images/icon-date.svg',
              package: 'ui_controls_library',
              width: responsiveIconSize,
            ),
          ],
        ],
      ),
    );

    if (widget.hasAnimation) {
      content = ScaleTransition(scale: _scaleAnimation, child: content);
    }

    if (widget.label != null) {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Text(
          //   widget.label!,
          //   style: TextStyle(
          //     color: effectiveTextColor.withAlpha((255 * 0.7).round()),
          //     fontSize: widget.fontSize * 0.8,
          //   ),
          // ),
          // const SizedBox(height: 4),
          content,
        ],
      );
    }

    Widget container = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      decoration: BoxDecoration(
        color:
            _isHovered && widget.hoverColor != null
                ? widget.hoverColor
                : (_isFocused && widget.focusColor != null
                    ? widget.focusColor
                    : effectiveBackgroundColor),
        borderRadius: BorderRadius.circular(4),
        // border: widget.hasBorder
        //     ? Border.all(
        //         color: widget.borderColor,
        //         width: widget.borderWidth,
        //       )
        //     : null,
        // boxShadow: widget.hasShadow
        //     ? [
        //         BoxShadow(
        //           color: Colors.black.withAlpha((255 * 0.2).round()),
        //           blurRadius: widget.elevation,
        //           offset: Offset(0, widget.elevation / 2),
        //         ),
        //       ]
        //     : null,
      ),
      child: content,
    );

    if (widget.isCompact) {
      container = Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: effectiveBackgroundColor,
          borderRadius: BorderRadius.circular(4),
          border:
              widget.hasBorder
                  ? Border.all(
                    color: widget.borderColor,
                    width: widget.borderWidth,
                  )
                  : null,
        ),
        child: content,
      );
    }

    if (widget.displayMode == YearMonthDisplayMode.chip) {
      container = Chip(
        label: Text(_getFormattedYearMonth()),
        backgroundColor: effectiveBackgroundColor,
        labelStyle: TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
        ),
        avatar:
            widget.showCalendarIcon
                ? Icon(Icons.calendar_today, size: widget.fontSize)
                : null,
        deleteIcon:
            widget.showClearButton
                ? Icon(Icons.clear, size: widget.fontSize)
                : null,
        onDeleted:
            widget.showClearButton && !widget.isDisabled && !widget.isReadOnly
                ? _clearSelection
                : null,
      );
    }

    if (widget.displayMode == YearMonthDisplayMode.separateFields) {
      container = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.label != null) ...[
            Text(
              widget.label!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha((255 * 0.7).round()),
                fontSize: widget.fontSize * 0.8,
              ),
            ),
            const SizedBox(height: 8),
          ],
          Row(
            children: [
              if (widget.showYear) ...[
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: effectiveBackgroundColor,
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      border:
                          widget.hasBorder
                              ? Border.all(
                                color: widget.borderColor,
                                width: widget.borderWidth,
                              )
                              : null,
                    ),
                    child: _buildYearSelector(),
                  ),
                ),
              ],
              if (widget.showYear && widget.showMonth) const SizedBox(width: 8),
              if (widget.showMonth) ...[
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: effectiveBackgroundColor,
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      border:
                          widget.hasBorder
                              ? Border.all(
                                color: widget.borderColor,
                                width: widget.borderWidth,
                              )
                              : null,
                    ),
                    child: _buildMonthSelector(),
                  ),
                ),
              ],
            ],
          ),
          if (widget.helperText != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha((255 * 0.5).round()),
                fontSize: widget.fontSize * 0.7,
              ),
            ),
          ],
          if (widget.errorText != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize * 0.7,
              ),
            ),
          ],
        ],
      );
    }

    // Create the base widget with gesture detection
    Widget result = CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: () {
          // Handle the display mode specific tap behavior
          if (widget.displayMode == YearMonthDisplayMode.dropdown) {
            setState(() {
              _isOpen = !_isOpen;
              if (_isOpen) {
                // Reset to year selection when opening
                _showingYearSelection = true;
                _showingMonthSelection = false;
                _showYearMonthPicker();
              }
            });
          } else if (widget.displayMode == YearMonthDisplayMode.dialog ||
              widget.displayMode == YearMonthDisplayMode.bottomSheet) {
            _showYearMonthPicker();
          }

          // Request focus when tapped
          _focusNode.requestFocus();

          // Call onTap callback if provided
          if (widget.onTap != null) {
            widget.onTap!();
          }

          // Call gestureTapCallback if provided
          if (widget.gestureTapCallback != null) {
            widget.gestureTapCallback!();
          }

          // Provide feedback if enabled
          if (widget.enableFeedback) {
            HapticFeedback.selectionClick();
          }
        },
        onDoubleTap: widget.onDoubleTap,
        onLongPress: widget.onLongPress,
        child: Focus(
          focusNode: _focusNode,
          child: MouseRegion(
            onEnter: (_) => _handleHoverChange(true),
            onExit: (_) => _handleHoverChange(false),
            child: container,
          ),
        ),
      ),
    );

    // Wrap with tooltip if provided
    // if (widget.tooltip != null) {
    //   result = Tooltip(
    //     message: widget.tooltip!,
    //     child: result,
    //   );
    // }

    // Wrap with Semantics for accessibility if semanticsLabel is provided
    if (widget.semanticsLabel != null) {
      result = Semantics(label: widget.semanticsLabel, child: result);
    }

    return result;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.displayMode == YearMonthDisplayMode.dropdown && _isOpen) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildInlineWidget(),
          // const SizedBox(height: 8),
          // Card(
          //   elevation: 4.0,
          //   child: Padding(
          //     padding: const EdgeInsets.all(16.0),
          //     child: _buildYearMonthPickerContent(),
          //   ),
          // ),
        ],
      );
    }

    return _buildInlineWidget();
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0); // Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0); // Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 6.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

double _getResponsiveInputFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large (>1920px) - Reduced for better fit
  } else if (screenWidth >= 1440) {
    return 15.0; // Large (1440-1920px) - Reduced for better fit
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium (1280-1366px) - Standard size
  } else if (screenWidth >= 768) {
    return 14.0; // Small (768-1024px) - Increased for readability
  } else {
    return 14.0; // Default for very small screens - Consistent
  }
}
