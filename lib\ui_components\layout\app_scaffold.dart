import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable scaffold component that follows the app's design system
class AppScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final Widget? bottomNavigationBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final bool extendBody;
  final bool extendBodyBehindAppBar;

  const AppScaffold({
    super.key,
    required this.body,
    this.appBar,
    this.drawer,
    this.endDrawer,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      drawer: drawer,
      endDrawer: endDrawer,
      body: body,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      backgroundColor: backgroundColor ?? AppTheme.backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      extendBody: extendBody,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
    );
  }
}

/// A customizable app bar component that follows the app's design system
class AppHeader extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final double? titleSpacing;
  final bool automaticallyImplyLeading;
  final double toolbarHeight;

  const AppHeader({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.titleSpacing,
    this.automaticallyImplyLeading = true,
    this.toolbarHeight = kToolbarHeight,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: AppTheme.headingSmall.copyWith(
          color: foregroundColor ?? AppTheme.textLightColor,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppTheme.primaryColor,
      foregroundColor: foregroundColor ?? AppTheme.textLightColor,
      elevation: elevation,
      titleSpacing: titleSpacing,
      automaticallyImplyLeading: automaticallyImplyLeading,
      toolbarHeight: toolbarHeight,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight);
}

/// A customizable bottom navigation bar component that follows the app's design system
class AppBottomNavigationBar extends StatelessWidget {
  final List<AppBottomNavigationItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double iconSize;
  final double elevation;
  final bool showLabels;
  final bool showSelectedLabels;
  final bool showUnselectedLabels;

  const AppBottomNavigationBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.iconSize = 24.0,
    this.elevation = 8.0,
    this.showLabels = true,
    this.showSelectedLabels = true,
    this.showUnselectedLabels = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: elevation,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: BottomNavigationBar(
        items: items
            .map(
              (item) => BottomNavigationBarItem(
                icon: Icon(item.icon),
                activeIcon:
                    item.activeIcon != null ? Icon(item.activeIcon) : null,
                label: item.label,
                tooltip: item.tooltip,
                backgroundColor: item.backgroundColor,
              ),
            )
            .toList(),
        currentIndex: currentIndex,
        onTap: onTap,
        backgroundColor: backgroundColor ?? AppTheme.backgroundColor,
        selectedItemColor: selectedItemColor ?? AppTheme.primaryColor,
        unselectedItemColor: unselectedItemColor ?? AppTheme.textSecondaryColor,
        iconSize: iconSize,
        elevation: 0,
        type: BottomNavigationBarType.fixed,
        showSelectedLabels: showLabels && showSelectedLabels,
        showUnselectedLabels: showLabels && showUnselectedLabels,
        selectedLabelStyle: AppTheme.bodySmall.copyWith(
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: AppTheme.bodySmall,
      ),
    );
  }
}

/// A model class for bottom navigation items
class AppBottomNavigationItem {
  final IconData icon;
  final IconData? activeIcon;
  final String label;
  final String? tooltip;
  final Color? backgroundColor;

  const AppBottomNavigationItem({
    required this.icon,
    this.activeIcon,
    required this.label,
    this.tooltip,
    this.backgroundColor,
  });
}

/// A customizable floating action button component that follows the app's design system
class AppFloatingActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget? child;
  final IconData? icon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool mini;
  final bool extended;
  final String? label;

  const AppFloatingActionButton({
    super.key,
    required this.onPressed,
    this.child,
    this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.mini = false,
    this.extended = false,
    this.label,
  }) : assert(
          (child != null && icon == null) ||
              (child == null && icon != null) ||
              (extended && icon != null && label != null),
          'Either provide a child or an icon, or provide both icon and label for extended FAB',
        );

  @override
  Widget build(BuildContext context) {
    if (extended && icon != null && label != null) {
      return FloatingActionButton.extended(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(
          label!,
          style: AppTheme.bodyMedium.copyWith(
            color: foregroundColor ?? AppTheme.textLightColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: backgroundColor ?? AppTheme.primaryColor,
        foregroundColor: foregroundColor ?? AppTheme.textLightColor,
        elevation: elevation,
        tooltip: tooltip,
      );
    }

    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: backgroundColor ?? AppTheme.primaryColor,
      foregroundColor: foregroundColor ?? AppTheme.textLightColor,
      elevation: elevation,
      mini: mini,
      tooltip: tooltip,
      child: child ?? Icon(icon),
    );
  }
}
