import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'constants.dart';
import 'logger.dart';
import 'navigation_service.dart';

/// A centralized error handling system for the application
class ErrorHandler {
  // Private constructor to prevent instantiation
  ErrorHandler._();
  
  /// Handle any type of error and return a user-friendly message
  static String handleError(dynamic error, {String? context}) {
    String errorMessage = AppConstants.genericErrorMessage;
    
    // Log the error
    Logger.error('Error in $context: $error', 
      stackTrace: error is Error ? error.stackTrace : null
    );
    
    if (error is DioException) {
      errorMessage = _handleDioError(error);
    } else if (error is TypeError) {
      errorMessage = 'Type error: ${error.toString()}';
    } else if (error is FormatException) {
      errorMessage = 'Format error: ${error.message}';
    } else if (error is Exception) {
      errorMessage = error.toString().replaceAll('Exception: ', '');
    }
    
    return errorMessage;
  }
  
  /// Handle Dio specific errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppConstants.connectionErrorMessage;
        
      case DioExceptionType.badResponse:
        return _handleBadResponse(error.response);
        
      case DioExceptionType.cancel:
        return 'Request was cancelled';
        
      case DioExceptionType.connectionError:
        return AppConstants.connectionErrorMessage;
        
      case DioExceptionType.unknown:
      default:
        if (error.message?.contains('SocketException') ?? false) {
          return AppConstants.connectionErrorMessage;
        }
        return error.message ?? AppConstants.genericErrorMessage;
    }
  }
  
  /// Handle bad response errors
  static String _handleBadResponse(Response? response) {
    if (response == null) {
      return AppConstants.genericErrorMessage;
    }
    
    final statusCode = response.statusCode;
    
    // Try to extract error message from response
    String? serverMessage;
    if (response.data is Map) {
      serverMessage = response.data['message'] ?? 
                     response.data['error'] ?? 
                     response.data['detail'];
    }
    
    switch (statusCode) {
      case 400:
        return serverMessage ?? 'Bad request';
      case 401:
        return AppConstants.authErrorMessage;
      case 403:
        return 'Access denied';
      case 404:
        return 'Resource not found';
      case 500:
      case 501:
      case 502:
      case 503:
        return serverMessage ?? 'Server error';
      default:
        return serverMessage ?? 'Error $statusCode';
    }
  }
  
  /// Show an error snackbar
  static void showErrorSnackBar(String message) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  /// Show an error dialog
  static Future<void> showErrorDialog(String message) async {
    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) return;
    
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
