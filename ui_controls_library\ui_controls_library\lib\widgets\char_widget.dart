import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A widget for displaying and inputting a single character.
///
/// This widget provides extensive customization options for displaying
/// and inputting a single character with various formats and styles.
class CharWidget extends StatefulWidget {
  /// The initial character value
  final String initialValue;

  /// Whether to allow only alphabetic characters (a-z, A-Z)
  final bool alphabeticOnly;

  /// Whether to allow only numeric characters (0-9)
  final bool numericOnly;

  /// Whether to allow only alphanumeric characters (a-z, A-Z, 0-9)
  final bool alphanumericOnly;

  /// Whether to allow special characters
  final bool allowSpecialChars;

  /// Custom allowed characters (if specified, overrides other character restrictions)
  final String? allowedChars;

  /// Whether to force uppercase
  final bool forceUppercase;

  /// Whether to force lowercase
  final bool forceLowercase;

  /// Whether the widget is read-only
  final bool readOnly;

  /// Whether the widget is disabled
  final bool disabled;

  /// The text color
  final Color textColor;

  /// The background color
  final Color backgroundColor;

  /// The border color
  final Color borderColor;

  /// The border width
  final double borderWidth;

  /// The border radius
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// The font size
  final double fontSize;

  /// The font weight
  final FontWeight fontWeight;

  /// Whether to use a monospace font
  final bool monospace;

  /// Whether to use a compact layout
  final bool isCompact;

  /// The text alignment
  final TextAlign textAlign;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// Whether to use dark theme colors
  final bool isDarkTheme;

  /// Whether to show a label
  final bool showLabel;

  /// The label text
  final String? label;

  /// Whether to show a prefix
  final bool showPrefix;

  /// The prefix text
  final String? prefix;

  /// Whether to show a suffix
  final bool showSuffix;

  /// The suffix text
  final String? suffix;

  /// The prefix icon
  final IconData? prefixIcon;

  /// The suffix icon
  final IconData? suffixIcon;

  /// The color of the prefix icon
  final Color? prefixIconColor;

  /// The color of the suffix icon
  final Color? suffixIconColor;

  /// Whether to show a clear button
  final bool showClearButton;

  /// Whether to show a tooltip
  final bool showTooltip;

  /// The tooltip text
  final String? tooltipText;

  /// Whether to show a helper text
  final bool showHelperText;

  /// The helper text
  final String? helperText;

  /// The color of the helper text
  final Color? helperTextColor;

  /// Whether to show an error text
  final bool showErrorText;

  /// The error text
  final String? errorText;

  /// The color of the error text
  final Color errorTextColor;

  /// Whether to animate value changes
  final bool animateChanges;

  /// The duration of the animation
  final Duration animationDuration;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// The padding inside the widget
  final EdgeInsetsGeometry padding;

  /// The margin around the widget
  final EdgeInsetsGeometry margin;

  /// Whether to auto-focus the widget
  final bool autoFocus;

  /// Whether to show the character as a password (masked)
  final bool isPassword;

  /// The character to use for masking the password
  final String passwordChar;

  /// Whether to show the character code (ASCII/Unicode)
  final bool showCharCode;

  /// The format for displaying the character code
  final CharCodeFormat charCodeFormat;

  /// Whether to show the character name (e.g., "LATIN CAPITAL LETTER A")
  final bool showCharName;

  /// Whether to show a visual representation of the character (e.g., emoji)
  final bool showVisualRepresentation;

  /// Whether to allow copying the character
  final bool allowCopy;

  /// Whether to show a copy button
  final bool showCopyButton;

  /// The icon for the copy button
  final IconData copyIcon;

  /// The color of the copy button
  final Color? copyButtonColor;

  /// Callback when the value changes
  final void Function(String)? onChanged;

  /// Callback when the value is submitted
  final void Function(String)? onSubmitted;

  /// Creates a character widget.
  const CharWidget({
    super.key,
    this.initialValue = '',
    this.alphabeticOnly = false,
    this.numericOnly = false,
    this.alphanumericOnly = false,
    this.allowSpecialChars = true,
    this.allowedChars,
    this.forceUppercase = false,
    this.forceLowercase = false,
    this.readOnly = false,
    this.disabled = false,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.normal,
    this.monospace = false,
    this.isCompact = false,
    this.textAlign = TextAlign.center,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.showLabel = false,
    this.label,
    this.showPrefix = false,
    this.prefix,
    this.showSuffix = false,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixIconColor,
    this.suffixIconColor,
    this.showClearButton = false,
    this.showTooltip = false,
    this.tooltipText,
    this.showHelperText = false,
    this.helperText,
    this.helperTextColor,
    this.showErrorText = false,
    this.errorText,
    this.errorTextColor = Colors.red,
    this.animateChanges = false,
    this.animationDuration = const Duration(milliseconds: 200),
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.autoFocus = false,
    this.isPassword = false,
    this.passwordChar = '•',
    this.showCharCode = false,
    this.charCodeFormat = CharCodeFormat.decimal,
    this.showCharName = false,
    this.showVisualRepresentation = false,
    this.allowCopy = true,
    this.showCopyButton = false,
    this.copyIcon = Icons.content_copy,
    this.copyButtonColor,
    this.onChanged,
    this.onSubmitted,
  });

  @override
  State<CharWidget> createState() => _CharWidgetState();
}

/// Format for displaying the character code
enum CharCodeFormat {
  /// Decimal format (e.g., "65")
  decimal,

  /// Hexadecimal format (e.g., "0x41")
  hexadecimal,

  /// Unicode format (e.g., "U+0041")
  unicode,

  /// HTML entity format (e.g., "&#65;")
  htmlEntity
}

class _CharWidgetState extends State<CharWidget> with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _animation;
  late FocusNode _focusNode;

  String? _errorText;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();

    // Initialize the text controller with the initial value
    _controller = TextEditingController(text: _formatValue(widget.initialValue));

    // Initialize the animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Initialize the animation
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Initialize the focus node
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(CharWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update the text controller if the initial value changed
    if (oldWidget.initialValue != widget.initialValue) {
      _setValue(widget.initialValue);
    }

    // Update the text controller if the formatting options changed
    if (oldWidget.forceUppercase != widget.forceUppercase ||
        oldWidget.forceLowercase != widget.forceLowercase ||
        oldWidget.isPassword != widget.isPassword ||
        oldWidget.passwordChar != widget.passwordChar) {
      _controller.text = _formatValue(_controller.text);
    }
  }

  /// Handles focus changes
  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
    });
  }

  /// Sets the value and updates the text controller
  void _setValue(String newValue) {
    // Only update if the value changed
    if (_controller.text != newValue) {
      // Apply character restrictions
      final validatedValue = _validateAndFormatChar(newValue);

      setState(() {
        _controller.text = _formatValue(validatedValue);
      });

      // Trigger the animation if enabled
      if (widget.animateChanges) {
        _animationController.reset();
        _animationController.forward();
      }

      // Call the onChanged callback
      if (widget.onChanged != null) {
        widget.onChanged!(validatedValue);
      }
    }
  }

  /// Validates and formats the input character based on restrictions
  String _validateAndFormatChar(String input) {
    // Take only the first character if more than one is provided
    final char = input.isNotEmpty ? input[0] : '';

    // If empty, return empty
    if (char.isEmpty) {
      return '';
    }

    // Apply character restrictions
    if (widget.allowedChars != null && widget.allowedChars!.isNotEmpty) {
      // Check if the character is in the allowed characters list
      if (!widget.allowedChars!.contains(char)) {
        return '';
      }
    } else {
      // Apply standard restrictions
      if (widget.alphabeticOnly && !RegExp(r'[a-zA-Z]').hasMatch(char)) {
        return '';
      }

      if (widget.numericOnly && !RegExp(r'[0-9]').hasMatch(char)) {
        return '';
      }

      if (widget.alphanumericOnly && !RegExp(r'[a-zA-Z0-9]').hasMatch(char)) {
        return '';
      }

      if (!widget.allowSpecialChars && RegExp(r'[^a-zA-Z0-9]').hasMatch(char)) {
        return '';
      }
    }

    // Apply case transformations
    if (widget.forceUppercase) {
      return char.toUpperCase();
    } else if (widget.forceLowercase) {
      return char.toLowerCase();
    }

    return char;
  }

  /// Formats the value for display
  String _formatValue(String value) {
    if (value.isEmpty) {
      return '';
    }

    // Apply password masking if enabled
    if (widget.isPassword) {
      return widget.passwordChar;
    }

    return value;
  }

  /// Clears the value
  void _clearValue() {
    if (widget.readOnly || widget.disabled) return;
    _setValue('');
  }

  /// Copies the character to the clipboard
  void _copyToClipboard() {
    if (widget.readOnly || widget.disabled || _controller.text.isEmpty) return;

    // Get the actual character (not the formatted display value)
    final char = _controller.text.isNotEmpty ? _controller.text[0] : '';

    if (char.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: char));

      // Show a snackbar to indicate that the character was copied
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Character copied to clipboard'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// Gets the character code based on the specified format
  String _getCharCode(String char) {
    if (char.isEmpty) return '';

    final codePoint = char.codeUnitAt(0);

    switch (widget.charCodeFormat) {
      case CharCodeFormat.decimal:
        return codePoint.toString();

      case CharCodeFormat.hexadecimal:
        return '0x${codePoint.toRadixString(16).toUpperCase().padLeft(2, '0')}';

      case CharCodeFormat.unicode:
        return 'U+${codePoint.toRadixString(16).toUpperCase().padLeft(4, '0')}';

      case CharCodeFormat.htmlEntity:
        return '&#$codePoint;';
    }
  }

  /// Gets the character name (simplified implementation)
  String _getCharName(String char) {
    if (char.isEmpty) return '';

    // This is a simplified implementation
    // A complete implementation would use a comprehensive character database
    final codePoint = char.codeUnitAt(0);

    if (codePoint >= 65 && codePoint <= 90) {
      return 'LATIN CAPITAL LETTER ${char.toUpperCase()}';
    } else if (codePoint >= 97 && codePoint <= 122) {
      return 'LATIN SMALL LETTER ${char.toUpperCase()}';
    } else if (codePoint >= 48 && codePoint <= 57) {
      return 'DIGIT $char';
    } else {
      return 'CHARACTER CODE $codePoint';
    }
  }

  /// Gets a visual representation of the character (simplified implementation)
  String _getVisualRepresentation(String char) {
    if (char.isEmpty) return '';

    // This is a simplified implementation
    // A complete implementation would use a comprehensive mapping
    final codePoint = char.codeUnitAt(0);

    // Some simple mappings for demonstration
    switch (char.toLowerCase()) {
      case 'a': return '🅰️';
      case 'b': return '🅱️';
      case 'c': return '©️';
      case 'd': return '🇩';
      case 'e': return '📧';
      case 'f': return '🏁';
      case 'g': return '🇬';
      case 'h': return '♓';
      case 'i': return 'ℹ️';
      case 'j': return '🇯';
      case 'k': return '🇰';
      case 'l': return '🇱';
      case 'm': return 'Ⓜ️';
      case 'n': return '🇳';
      case 'o': return '⭕';
      case 'p': return '🅿️';
      case 'q': return '🇶';
      case 'r': return '®️';
      case 's': return '💲';
      case 't': return '✝️';
      case 'u': return '⛎';
      case 'v': return '✅';
      case 'w': return '〰️';
      case 'x': return '❌';
      case 'y': return '⚠️';
      case 'z': return '💤';
      case '0': return '0️⃣';
      case '1': return '1️⃣';
      case '2': return '2️⃣';
      case '3': return '3️⃣';
      case '4': return '4️⃣';
      case '5': return '5️⃣';
      case '6': return '6️⃣';
      case '7': return '7️⃣';
      case '8': return '8️⃣';
      case '9': return '9️⃣';
      case '!': return '❗';
      case '?': return '❓';
      case '+': return '➕';
      case '-': return '➖';
      case '*': return '✳️';
      case '/': return '➗';
      case '=': return '🟰';
      default: return char;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor = widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;
    final effectiveBorderColor = widget.isDarkTheme ? Colors.grey.shade600 : widget.borderColor;

    // Build the main content
    Widget content = TextField(
      controller: _controller,
      focusNode: _focusNode,
      readOnly: widget.readOnly,
      enabled: !widget.disabled,
      autofocus: widget.autoFocus,
      style: TextStyle(
        color: widget.disabled ? Colors.grey : effectiveTextColor,
        fontSize: widget.fontSize,
        fontWeight: widget.fontWeight,
        fontFamily: widget.monospace ? 'monospace' : null,
      ),
      textAlign: widget.textAlign,
      maxLength: 1,
      decoration: InputDecoration(
        filled: true,
        fillColor: effectiveBackgroundColor,
        contentPadding: widget.isCompact
            ? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0)
            : widget.padding,
        counterText: '', // Hide the character counter
        border: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: effectiveBorderColor,
                  width: widget.borderWidth,
                ),
              )
            : InputBorder.none,
        enabledBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: effectiveBorderColor,
                  width: widget.borderWidth,
                ),
              )
            : null,
        disabledBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: Colors.grey,
                  width: widget.borderWidth,
                ),
              )
            : null,
        focusedBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: widget.isDarkTheme ? Colors.white : Colors.blue,
                  width: widget.borderWidth,
                ),
              )
            : null,
        labelText: widget.showLabel ? widget.label : null,
        prefixText: widget.showPrefix ? widget.prefix : null,
        suffixText: widget.showSuffix ? widget.suffix : null,
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                color: widget.prefixIconColor ?? (widget.disabled ? Colors.grey : null),
              )
            : null,
        suffixIcon: _buildSuffixIcon(),
        errorText: _errorText,
      ),
      keyboardType: widget.numericOnly ? TextInputType.number : TextInputType.text,
      inputFormatters: [
        LengthLimitingTextInputFormatter(1),
      ],
      onChanged: (text) {
        if (widget.readOnly || widget.disabled) return;

        // Validate and format the input
        final validatedChar = _validateAndFormatChar(text);

        // Update the controller if needed
        if (validatedChar != text) {
          _controller.text = _formatValue(validatedChar);
          _controller.selection = TextSelection.fromPosition(
            TextPosition(offset: _controller.text.length),
          );
        }

        // Call the onChanged callback
        if (widget.onChanged != null) {
          widget.onChanged!(validatedChar);
        }
      },
      onSubmitted: (text) {
        if (widget.readOnly || widget.disabled) return;

        // Call the onSubmitted callback
        if (widget.onSubmitted != null) {
          widget.onSubmitted!(text);
        }
      },
    );

    // Add additional information if needed
    final List<Widget> additionalInfo = [];

    if (_controller.text.isNotEmpty) {
      // Add character code if enabled
      if (widget.showCharCode) {
        additionalInfo.add(
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              'Code: ${_getCharCode(_controller.text)}',
              style: TextStyle(
                color: effectiveTextColor.withOpacity(0.7),
                fontSize: widget.fontSize * 0.6,
              ),
            ),
          ),
        );
      }

      // Add character name if enabled
      if (widget.showCharName) {
        additionalInfo.add(
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              _getCharName(_controller.text),
              style: TextStyle(
                color: effectiveTextColor.withOpacity(0.7),
                fontSize: widget.fontSize * 0.6,
              ),
            ),
          ),
        );
      }

      // Add visual representation if enabled
      if (widget.showVisualRepresentation) {
        additionalInfo.add(
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              _getVisualRepresentation(_controller.text),
              style: TextStyle(
                fontSize: widget.fontSize * 1.2,
              ),
            ),
          ),
        );
      }
    }

    if (additionalInfo.isNotEmpty) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          content,
          ...additionalInfo,
        ],
      );
    }

    // Apply animation if enabled
    if (widget.animateChanges) {
      content = FadeTransition(
        opacity: _animation,
        child: content,
      );
    }

    // Apply container styling
    content = Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: content,
    );

    // Apply tooltip if enabled
    if (widget.showTooltip) {
      content = Tooltip(
        message: widget.tooltipText ?? 'Character input',
        child: content,
      );
    }

    // Build the final widget with helper/error text
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        content,

        if (widget.showHelperText && widget.helperText != null && !widget.showErrorText) ...[
          const SizedBox(height: 4),
          Text(
            widget.helperText!,
            style: TextStyle(
              color: widget.helperTextColor ?? Colors.grey,
              fontSize: widget.fontSize * 0.6,
            ),
          ),
        ],

        if (widget.showErrorText && widget.errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.errorText!,
            style: TextStyle(
              color: widget.errorTextColor,
              fontSize: widget.fontSize * 0.6,
            ),
          ),
        ],
      ],
    );
  }

  /// Builds the suffix icon based on the widget configuration
  Widget? _buildSuffixIcon() {
    if (widget.disabled) {
      return widget.suffixIcon != null
          ? Icon(
              widget.suffixIcon,
              color: Colors.grey,
            )
          : null;
    }

    final List<Widget> icons = [];

    // Add the clear button if enabled and there's text
    if (widget.showClearButton && _controller.text.isNotEmpty && !widget.readOnly) {
      icons.add(
        InkWell(
          onTap: _clearValue,
          child: Icon(
            Icons.clear,
            size: widget.fontSize * 0.8,
            color: widget.isDarkTheme ? Colors.white70 : Colors.grey,
          ),
        ),
      );
    }

    // Add the copy button if enabled and there's text
    if (widget.showCopyButton && _controller.text.isNotEmpty && widget.allowCopy) {
      if (icons.isNotEmpty) {
        icons.add(const SizedBox(width: 8));
      }

      icons.add(
        InkWell(
          onTap: _copyToClipboard,
          child: Icon(
            widget.copyIcon,
            size: widget.fontSize * 0.8,
            color: widget.copyButtonColor ?? (widget.isDarkTheme ? Colors.white70 : Colors.grey),
          ),
        ),
      );
    }

    // Add the custom suffix icon if specified
    if (widget.suffixIcon != null) {
      if (icons.isNotEmpty) {
        icons.add(const SizedBox(width: 8));
      }

      icons.add(
        Icon(
          widget.suffixIcon,
          color: widget.suffixIconColor,
        ),
      );
    }

    if (icons.isEmpty) {
      return null;
    } else if (icons.length == 1) {
      return icons.first;
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: icons,
      );
    }
  }
}