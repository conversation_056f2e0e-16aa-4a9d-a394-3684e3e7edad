import 'package:flutter/material.dart';
import '../models/conversation_response.dart';

/// Provider for managing workflow data from API responses
class WorkflowDataProvider extends ChangeNotifier {
  // Workflow-related variables
  List<Map<String, dynamic>> _workflows = [];
  Map<String, dynamic>? _selectedWorkflow;
  Map<String, dynamic> _workflowSystemInfo = {};
  bool _isLoadingWorkflows = true;

  // Getters
  List<Map<String, dynamic>> get workflows => _workflows;
  Map<String, dynamic>? get selectedWorkflow => _selectedWorkflow;
  Map<String, dynamic> get workflowSystemInfo => _workflowSystemInfo;
  bool get isLoadingWorkflows => _isLoadingWorkflows;

  // Setters
  set selectedWorkflow(Map<String, dynamic>? workflow) {
    _selectedWorkflow = workflow;
    notifyListeners();
  }

  /// Extract workflow data from API response
  List<Map<String, dynamic>> extractWorkflowDataFromResponse(ConversationResponse response) {
    try {

      List<Map<String, dynamic>> extractedWorkflows = [];

      // Check if we have agent results with workflow data
      if (response.workflowResult?.agentResults != null) {
        for (var agent in response.workflowResult!.agentResults!) {
          if (agent.result?.workFlow?.workFlowDetails != null) {
            final workflowDetails = agent.result!.workFlow!.workFlowDetails!;

            // Create a workflow map similar to your JSON structure
            Map<String, dynamic> workflow = {
              'id': workflowDetails.id ?? 'workflow_${DateTime.now().millisecondsSinceEpoch}',
              'title': workflowDetails.title ?? 'Untitled Workflow',
              'description': workflowDetails.description ?? '',
              'version': workflowDetails.version ?? '1.0',
              'createdBy': workflowDetails.createdBy ?? '',
              'createdDate': workflowDetails.createdDate ?? '',
              'modifiedBy': workflowDetails.modifiedBy ?? '',
              'modifiedDate': workflowDetails.modifiedDate ?? '',
              'mainTitle': workflowDetails.mainTitle ?? workflowDetails.title ?? 'Untitled Workflow',
              'tree': _convertApiTreeToJsonFormat(workflowDetails.tree ?? []),
              // Store the original WorkFlowDetails object
              'workFlowDetails': workflowDetails,
            };

            // Store the original WorkFlowDetails object

            // Check for sections in workflowDetails
            if (workflowDetails.sections != null && workflowDetails.sections!.isNotEmpty) {
              // Convert API sections to a format that can be stored in the workflow map
              List<Map<String, dynamic>> apiSections = [];

              for (var section in workflowDetails.sections!) {
                Map<String, dynamic> sectionMap = {
                  'id': section.id ?? 'section_${DateTime.now().millisecondsSinceEpoch}',
                  'title': section.title ?? 'Untitled Section',
                };

                // Add tabs if available
                if (section.tabs != null && section.tabs!.isNotEmpty) {
                  List<Map<String, dynamic>> tabs = [];
                  for (var tab in section.tabs!) {
                    tabs.add({
                      'id': tab.id ?? '${section.id}_tab',
                      'title': tab.title ?? 'Tab',
                    });
                  }
                  sectionMap['tabs'] = tabs;
                }

                // Add content if available
                if (section.content != null) {
                  Map<String, dynamic> contentMap = {
                    'description': section.content!.description ?? 'No description available',
                  };

                  // Add items if available
                  if (section.content!.items != null && section.content!.items!.isNotEmpty) {
                    List<Map<String, dynamic>> items = [];
                    for (var item in section.content!.items!) {
                      items.add({
                        'title': item.title ?? '',
                        'description': item.description ?? '',
                      });
                    }
                    contentMap['items'] = items;
                  }

                  // Add rules if available
                  if (section.content!.rules != null && section.content!.rules!.isNotEmpty) {
                    List<Map<String, dynamic>> rules = [];
                    for (var rule in section.content!.rules!) {
                      rules.add({
                        'title': rule.title ?? '',
                        'action': rule.action ?? '',
                        'condition': rule.condition ?? '',
                        'entity': rule.entity ?? '',
                        'implementation': rule.implementation ?? '',
                        'enforcedBy': rule.enforcedBy ?? '',
                      });
                    }
                    contentMap['rules'] = rules;
                  }

                  // Add entity operations if available
                  if (section.content!.entityOperations != null && section.content!.entityOperations!.isNotEmpty) {
                    List<Map<String, dynamic>> entityOperations = [];
                    for (var op in section.content!.entityOperations!) {
                      entityOperations.add({
                        'entity': op.entity ?? '',
                        'operations': op.operations ?? [],
                      });
                    }
                    contentMap['entity_operations'] = entityOperations;
                  }

                  // Add systems if available
                  if (section.content!.systems != null && section.content!.systems!.isNotEmpty) {
                    List<Map<String, dynamic>> systems = [];
                    for (var system in section.content!.systems!) {
                      systems.add({
                        'name': system.name ?? '',
                        'description': system.description ?? '',
                      });
                    }
                    contentMap['systems'] = systems;
                  }

                  // Add targets if available
                  if (section.content!.targets != null && section.content!.targets!.isNotEmpty) {
                    List<Map<String, dynamic>> targets = [];
                    for (var target in section.content!.targets!) {
                      targets.add({
                        'name': target.name ?? '',
                        'code': target.code ?? '',
                        'target': target.target ?? '',
                      });
                    }
                    contentMap['targets'] = targets;
                  }

                  // Add monitoring if available
                  if (section.content!.monitoring != null && section.content!.monitoring!.isNotEmpty) {
                    List<Map<String, dynamic>> monitoring = [];
                    for (var monitor in section.content!.monitoring!) {
                      monitoring.add({
                        'name': monitor.name ?? '',
                        'value': monitor.value ?? '',
                      });
                    }
                    contentMap['monitoring'] = monitoring;
                  }

                  sectionMap['content'] = contentMap;
                }

                apiSections.add(sectionMap);
              }

              // Add the sections to the workflow map
              workflow['apiSections'] = apiSections;
            } else {
              workflow['apiSections'] = [];
            }

            extractedWorkflows.add(workflow);
          }
        }
      }

      // Also check if there's workflow data in agent artifacts
      if (response.agentArtifacts != null) {
        for (var agent in response.agentArtifacts!) {
          if (agent.result?.workFlow?.workFlowDetails != null) {
            final workflowDetails = agent.result!.workFlow!.workFlowDetails!;

            Map<String, dynamic> workflow = {
              'id': workflowDetails.id ?? 'workflow_${DateTime.now().millisecondsSinceEpoch}',
              'title': workflowDetails.title ?? 'Untitled Workflow',
              'description': workflowDetails.description ?? '',
              'version': workflowDetails.version ?? '1.0',
              'createdBy': workflowDetails.createdBy ?? '',
              'createdDate': workflowDetails.createdDate ?? '',
              'modifiedBy': workflowDetails.modifiedBy ?? '',
              'modifiedDate': workflowDetails.modifiedDate ?? '',
              'mainTitle': workflowDetails.mainTitle ?? workflowDetails.title ?? 'Untitled Workflow',
              'tree': _convertApiTreeToJsonFormat(workflowDetails.tree ?? []),
              // Store the original WorkFlowDetails object
              'workFlowDetails': workflowDetails,
            };

            // Extract sections data from the API response
            if (workflowDetails.sections != null && workflowDetails.sections!.isNotEmpty) {

              // Convert API sections to a format that can be stored in the workflow map
              List<Map<String, dynamic>> apiSections = [];

              for (var section in workflowDetails.sections!) {
                Map<String, dynamic> sectionMap = {
                  'id': section.id ?? 'section_${DateTime.now().millisecondsSinceEpoch}',
                  'title': section.title ?? 'Untitled Section',
                };

                // Add tabs if available
                if (section.tabs != null && section.tabs!.isNotEmpty) {
                  List<Map<String, dynamic>> tabs = [];
                  for (var tab in section.tabs!) {
                    tabs.add({
                      'id': tab.id ?? '${section.id}_tab',
                      'title': tab.title ?? 'Tab',
                    });
                  }
                  sectionMap['tabs'] = tabs;
                }

                // Add content if available
                if (section.content != null) {
                  Map<String, dynamic> contentMap = {
                    'description': section.content!.description ?? 'No description available',
                  };

                  // Add items if available
                  if (section.content!.items != null && section.content!.items!.isNotEmpty) {
                    List<Map<String, dynamic>> items = [];
                    for (var item in section.content!.items!) {
                      items.add({
                        'title': item.title ?? '',
                        'description': item.description ?? '',
                      });
                    }
                    contentMap['items'] = items;
                  }

                  // Add rules if available
                  if (section.content!.rules != null && section.content!.rules!.isNotEmpty) {
                    List<Map<String, dynamic>> rules = [];
                    for (var rule in section.content!.rules!) {
                      rules.add({
                        'title': rule.title ?? '',
                        'action': rule.action ?? '',
                        'condition': rule.condition ?? '',
                        'entity': rule.entity ?? '',
                        'implementation': rule.implementation ?? '',
                        'enforcedBy': rule.enforcedBy ?? '',
                      });
                    }
                    contentMap['rules'] = rules;
                  }

                  // Add entity operations if available
                  if (section.content!.entityOperations != null && section.content!.entityOperations!.isNotEmpty) {
                    List<Map<String, dynamic>> entityOperations = [];
                    for (var op in section.content!.entityOperations!) {
                      entityOperations.add({
                        'entity': op.entity ?? '',
                        'operations': op.operations ?? [],
                      });
                    }
                    contentMap['entity_operations'] = entityOperations;
                  }

                  // Add systems if available
                  if (section.content!.systems != null && section.content!.systems!.isNotEmpty) {
                    List<Map<String, dynamic>> systems = [];
                    for (var system in section.content!.systems!) {
                      systems.add({
                        'name': system.name ?? '',
                        'description': system.description ?? '',
                      });
                    }
                    contentMap['systems'] = systems;
                  }

                  // Add targets if available
                  if (section.content!.targets != null && section.content!.targets!.isNotEmpty) {
                    List<Map<String, dynamic>> targets = [];
                    for (var target in section.content!.targets!) {
                      targets.add({
                        'name': target.name ?? '',
                        'code': target.code ?? '',
                        'target': target.target ?? '',
                      });
                    }
                    contentMap['targets'] = targets;
                  }

                  // Add monitoring if available
                  if (section.content!.monitoring != null && section.content!.monitoring!.isNotEmpty) {
                    List<Map<String, dynamic>> monitoring = [];
                    for (var monitor in section.content!.monitoring!) {
                      monitoring.add({
                        'name': monitor.name ?? '',
                        'value': monitor.value ?? '',
                      });
                    }
                    contentMap['monitoring'] = monitoring;
                  }

                  sectionMap['content'] = contentMap;
                }

                apiSections.add(sectionMap);
              }

              // Add the sections to the workflow map
              workflow['apiSections'] = apiSections;
            } else {
              workflow['apiSections'] = [];
            }

            extractedWorkflows.add(workflow);
          }
        }
      }

      return extractedWorkflows;
    } catch (e) {
      return [];
    }
  }

  /// Update workflow data from API response
  Future<void> updateWorkflowsFromApiResponse(ConversationResponse response) async {
    try {
      // Set loading state to true
      _isLoadingWorkflows = true;

      // Use a microtask to avoid updating during build
      await Future.microtask(() {
        // Extract workflow data from the response
        List<Map<String, dynamic>> extractedWorkflows = extractWorkflowDataFromResponse(response);

        if (extractedWorkflows.isNotEmpty) {
          // Update the workflows list with the extracted data
          _workflows = extractedWorkflows;

          // Update workflow system info
          _workflowSystemInfo = {
            'workflowCount': extractedWorkflows.length,
            'activeWorkflows': extractedWorkflows.length,
            'bulletPoints': [
              'Workflows extracted from API response',
              '${extractedWorkflows.length} workflows available',
            ],
          };

          // Set the selected workflow to the first one if none is selected
          if (_selectedWorkflow == null && _workflows.isNotEmpty) {
            _selectedWorkflow = _workflows.first;
          }


        }
      });

      // Set loading state to false
      _isLoadingWorkflows = false;

      // Notify listeners after all updates are complete
      notifyListeners();
    } catch (e) {

      _isLoadingWorkflows = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Update workflows directly with provided data
  /// This is a safer method that doesn't rely on extracting data from the response
  void updateDirectly(List<Map<String, dynamic>> workflows) {
    try {


      // Check if workflows have apiSections
      for (var i = 0; i < workflows.length; i++) {
        var workflow = workflows[i];


        if (workflow.containsKey('apiSections')) {


          // Log the first section to verify its structure
          if ((workflow['apiSections'] as List).isNotEmpty) {
            var firstSection = (workflow['apiSections'] as List)[0];
             }
        }
      }

      // Update the workflows list with the provided data
      _workflows = workflows;

      // Update workflow system info
      _workflowSystemInfo = {
        'workflowCount': workflows.length,
        'activeWorkflows': workflows.length,
        'bulletPoints': [
          'Workflows updated directly',
          '${workflows.length} workflows available',
        ],
      };

      // Set the selected workflow to the first one if none is selected
      if (_selectedWorkflow == null && _workflows.isNotEmpty) {
        _selectedWorkflow = _workflows.first;
      }

      // Set loading state to false
      _isLoadingWorkflows = false;

      // Notify listeners
      notifyListeners();


    } catch (e) {

      _isLoadingWorkflows = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Get workflow details by ID
  /// This method returns workflow details in the format expected by the side panel
  Future<Map<String, dynamic>?> getWorkflowDetailsById(String workflowId) async {
    try {
      // First check if we have this workflow in our list
      Map<String, dynamic>? workflow;

      try {
        if (_workflows.isEmpty) {
          return null;
        }

        // Find the workflow with matching ID
        for (var w in _workflows) {
          if (w['id'] == workflowId) {
            workflow = Map<String, dynamic>.from(w);
            break;
          }
        }

        if (workflow == null) {
          return null;
        }
      } catch (e) {
        return null;
      }

      if (workflow.isEmpty) {
        return null;
      }

      try {
        // Check if the workflow has the original WorkFlowDetails object
        bool hasWorkFlowDetails = workflow.containsKey('workFlowDetails') &&
                                 workflow['workFlowDetails'] != null;

        // Create the base workflow details structure
        Map<String, dynamic> workflowDetailsMap = {
          'id': workflow['id'] ?? '',
          'title': workflow['title'] ?? 'Untitled Workflow',
          'mainTitle': workflow['mainTitle'] ?? workflow['title'] ?? 'Untitled Workflow',
          'description': workflow['description'] ?? 'No description available',
          'sections': [],
        };

        // If we have the original WorkFlowDetails object, use its sections
        if (hasWorkFlowDetails) {
          final workFlowDetails = workflow['workFlowDetails'];

          // Check if the WorkFlowDetails object has sections
          if (workFlowDetails.sections != null && workFlowDetails.sections!.isNotEmpty) {
            // Convert sections to the format expected by the side panel
            List<Map<String, dynamic>> convertedSections = [];

            for (var section in workFlowDetails.sections!) {
              Map<String, dynamic> sectionMap = {
                'id': section.id ?? 'section_${DateTime.now().millisecondsSinceEpoch}',
                'title': section.title ?? 'Untitled Section',
              };

              // Add tabs if available
              if (section.tabs != null && section.tabs!.isNotEmpty) {
                List<Map<String, dynamic>> tabs = [];
                for (var tab in section.tabs!) {
                  tabs.add({
                    'id': tab.id ?? '${section.id}_tab',
                    'title': tab.title ?? 'Tab',
                  });
                }
                sectionMap['tabs'] = tabs;
              }

              // Add content if available
              if (section.content != null) {
                Map<String, dynamic> contentMap = {
                  'description': section.content!.description ?? 'No description available',
                  'items': [],
                };

                // Add items if available
                if (section.content!.items != null && section.content!.items!.isNotEmpty) {
                  List<Map<String, dynamic>> items = [];
                  for (var item in section.content!.items!) {
                    items.add({
                      'title': item.title ?? '',
                      'description': item.description ?? '',
                    });
                  }
                  contentMap['items'] = items;
                }

                // Add rules if available
                if (section.content!.rules != null && section.content!.rules!.isNotEmpty) {
                  List<Map<String, dynamic>> rules = [];
                  for (var rule in section.content!.rules!) {
                    rules.add({
                      'title': rule.title ?? '',
                      'action': rule.action ?? '',
                      'condition': rule.condition ?? '',
                      'entity': rule.entity ?? '',
                      'implementation': rule.implementation ?? '',
                      'enforced_by': rule.enforcedBy ?? '',
                    });
                  }
                  contentMap['rules'] = rules;
                }

                // Add entity operations if available
                if (section.content!.entityOperations != null && section.content!.entityOperations!.isNotEmpty) {
                  List<Map<String, dynamic>> entityOperations = [];
                  for (var op in section.content!.entityOperations!) {
                    entityOperations.add({
                      'entity': op.entity ?? '',
                      'operations': op.operations ?? '',
                    });
                  }
                  contentMap['entity_operations'] = entityOperations;
                }

                // Add systems if available
                if (section.content!.systems != null && section.content!.systems!.isNotEmpty) {
                  List<Map<String, dynamic>> systems = [];
                  for (var system in section.content!.systems!) {
                    systems.add({
                      'name': system.name ?? '',
                      'description': system.description ?? '',
                    });
                  }
                  contentMap['systems'] = systems;
                }

                // Add targets if available
                if (section.content!.targets != null && section.content!.targets!.isNotEmpty) {
                  List<Map<String, dynamic>> targets = [];
                  for (var target in section.content!.targets!) {
                    targets.add({
                      'name': target.name ?? '',
                      'code': target.code ?? '',
                      'target': target.target ?? '',
                    });
                  }
                  contentMap['targets'] = targets;
                }

                // Add monitoring if available
                if (section.content!.monitoring != null && section.content!.monitoring!.isNotEmpty) {
                  List<Map<String, dynamic>> monitoring = [];
                  for (var monitor in section.content!.monitoring!) {
                    monitoring.add({
                      'name': monitor.name ?? '',
                      'value': monitor.value ?? '',
                    });
                  }
                  contentMap['monitoring'] = monitoring;
                }

                sectionMap['content'] = contentMap;
              } else {
                // Create default content if none is provided
                sectionMap['content'] = {
                  'description': 'No content available for this section.',
                  'items': []
                };
              }

              convertedSections.add(sectionMap);
            }

            workflowDetailsMap['sections'] = convertedSections;
          } else {
            // Return empty sections list as per requirement to only show API data
            workflowDetailsMap['sections'] = [];
          }
        } else {
          // Check if the workflow has sections data (previously apiSections)
          bool hasSections = workflow.containsKey('sections') &&
                             workflow['sections'] != null &&
                             workflow['sections'] is List &&
                             (workflow['sections'] as List).isNotEmpty;

          if (hasSections) {
            // Use the sections directly since they're already in the right format
            workflowDetailsMap['sections'] = workflow['sections'];
          } else {
            // Check for legacy apiSections format
            bool hasApiSections = workflow.containsKey('apiSections') &&
                                 workflow['apiSections'] != null &&
                                 workflow['apiSections'] is List &&
                                 (workflow['apiSections'] as List).isNotEmpty;

            if (hasApiSections) {
              // Convert API sections to the format expected by the side panel
              List<Map<String, dynamic>> convertedSections = _convertApiSectionsToSidePanelFormat(
                workflow['apiSections'] as List,
                workflow
              );

              workflowDetailsMap['sections'] = convertedSections;
            } else {
              // Return empty sections list as per requirement to only show API data
              workflowDetailsMap['sections'] = [];
            }
          }
        }

        return workflowDetailsMap;
      } catch (conversionError) {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// Convert API sections to the format expected by the side panel
  List<Map<String, dynamic>> _convertApiSectionsToSidePanelFormat(
    List<dynamic> apiSections,
    Map<String, dynamic> workflow
  ) {
    List<Map<String, dynamic>> result = [];

   
    // If no API sections are provided, return an empty list
    if (apiSections.isEmpty) {
  
      return result;
    }

    for (var section in apiSections) {
      // Skip if section doesn't have required fields
      if (section == null || !section.containsKey('id') || !section.containsKey('title')) {
   
        continue;
      }


      // Create the base section structure
      Map<String, dynamic> convertedSection = {
        'id': section['id'],
        'title': section['title'],
      };

      // Add tabs if available
      if (section.containsKey('tabs') && section['tabs'] != null && section['tabs'] is List) {
      
        convertedSection['tabs'] = (section['tabs'] as List).map((tab) {
          return {
            'id': tab['id'] ?? '${section['id']}_tab',
            'title': tab['title'] ?? 'Tab',
          };
        }).toList();
      }

      // Add content if available
      if (section.containsKey('content') && section['content'] != null) {
        var content = section['content'];
       

        Map<String, dynamic> convertedContent = {
          'description': content['description'] ?? 'No description available',
        };

        // Handle different content types based on section ID
        switch (section['id']) {
          case 'business_rules':
           
            if (content.containsKey('rules') && content['rules'] != null && content['rules'] is List) {
              convertedContent['rules'] = content['rules'];
             

              // Also create items from rules for backward compatibility
              List<Map<String, dynamic>> items = [];
              for (var rule in content['rules']) {
                items.add({
                  'title': rule['title'] ?? 'Rule',
                  'description': '${rule['condition'] ?? ''} ${rule['action'] ?? ''}'
                });
              }
              convertedContent['items'] = items;
            } else if (content.containsKey('items') && content['items'] != null && content['items'] is List) {
              convertedContent['items'] = content['items'];
             
            } else {
              convertedContent['items'] = [];
             
            }
            break;

          case 'data_management':
           
            // Handle entity operations
            if (content.containsKey('entity_operations') && content['entity_operations'] != null) {
              convertedContent['entity_operations'] = content['entity_operations'];
             
              // Create items from entity operations for backward compatibility
              List<Map<String, dynamic>> items = [];
              for (var op in content['entity_operations']) {
                items.add({
                  'title': op['entity'] ?? 'Entity',
                  'description': 'Operations: ${op['operations'] ?? 'None'}'
                });
              }
              convertedContent['items'] = items;
            }

            // Handle inputs data for data management section
            if (content.containsKey('inputs') && content['inputs'] != null && content['inputs'] is List) {
              convertedContent['inputs'] = content['inputs'];
           

              // Create or add to items from inputs
              List<Map<String, dynamic>> items = convertedContent.containsKey('items') ?
                  List<Map<String, dynamic>>.from(convertedContent['items']) : [];

              for (var input in content['inputs']) {
                items.add({
                  'title': input['process'] ?? 'Process',
                  'description': input['description'] ?? 'No description'
                });
              }
              convertedContent['items'] = items;
            }

            // If no special fields, use items if available
            if (!convertedContent.containsKey('items') &&
                content.containsKey('items') && content['items'] != null) {
              convertedContent['items'] = content['items'];
           
            } else if (!convertedContent.containsKey('items')) {
              convertedContent['items'] = [];
             
            }
            break;

          case 'integrations':
           
            // Handle systems data for integrations section
            if (content.containsKey('systems') && content['systems'] != null && content['systems'] is List) {
              convertedContent['systems'] = content['systems'];
            
              // Create items from systems for backward compatibility
              List<Map<String, dynamic>> items = [];
              for (var system in content['systems']) {
                items.add({
                  'title': system['name'] ?? 'System',
                  'description': system['description'] ?? 'No description'
                });
              }
              convertedContent['items'] = items;
            }

            // If no systems, use items if available
            if (!convertedContent.containsKey('items') &&
                content.containsKey('items') && content['items'] != null) {
              convertedContent['items'] = content['items'];
            
            } else if (!convertedContent.containsKey('items')) {
              convertedContent['items'] = [];
             
            }
            break;

          case 'performance_metrics':
          case 'performance_targets':
           
            // Handle monitoring data
            if (content.containsKey('monitoring') && content['monitoring'] != null && content['monitoring'] is List) {
              convertedContent['monitoring'] = content['monitoring'];
             
              // Create items from monitoring for backward compatibility
              List<Map<String, dynamic>> items = [];
              for (var monitor in content['monitoring']) {
                items.add({
                  'title': monitor['name'] ?? 'Metric',
                  'description': monitor['value'] ?? 'No value'
                });
              }
              convertedContent['items'] = items;
            }

            // Handle targets data
            if (content.containsKey('targets') && content['targets'] != null && content['targets'] is List) {
              convertedContent['targets'] = content['targets'];
             

              // If no items were created from monitoring, create them from targets
              if (!convertedContent.containsKey('items')) {
                List<Map<String, dynamic>> items = [];
                for (var target in content['targets']) {
                  items.add({
                    'title': target['name'] ?? 'Target',
                    'description': '${target['target'] ?? 'No target'} (Code: ${target['code'] ?? 'N/A'})'
                  });
                }
                convertedContent['items'] = items;
              } else {
                // Add targets as additional items
                List<Map<String, dynamic>> existingItems = List<Map<String, dynamic>>.from(convertedContent['items']);
                for (var target in content['targets']) {
                  existingItems.add({
                    'title': target['name'] ?? 'Target',
                    'description': '${target['target'] ?? 'No target'} (Code: ${target['code'] ?? 'N/A'})'
                  });
                }
                convertedContent['items'] = existingItems;
              }
            }

            // If no special fields, use items if available
            if (!convertedContent.containsKey('items') &&
                content.containsKey('items') && content['items'] != null) {
              convertedContent['items'] = content['items'];
             
            } else if (!convertedContent.containsKey('items')) {
              convertedContent['items'] = [];
            
            }
            break;

          case 'ownership':
          default:
           
            // For ownership and any other sections, use items
            if (content.containsKey('items') && content['items'] != null && content['items'] is List) {
              convertedContent['items'] = content['items'];
           
            } else {
              // If no items, create default ones using workflow metadata
              convertedContent['items'] = [
                {
                  'title': 'Primary Owner:',
                  'description': workflow['createdBy'] ?? 'System'
                },
                {
                  'title': 'Secondary Owner:',
                  'description': workflow['modifiedBy'] ?? 'N/A'
                }
              ];
             
            }
            break;
        }

        convertedSection['content'] = convertedContent;
      } else {
        // Create default content if none is provided
       
        convertedSection['content'] = {
          'description': 'No content available for this section.',
          'items': []
        };
      }

      result.add(convertedSection);
    }

   
    return result;
  }

  /// Helper method to convert API tree format to JSON format
  List<Map<String, dynamic>> _convertApiTreeToJsonFormat(List<Tree> apiTree) {
    List<Map<String, dynamic>> result = [];

    for (var node in apiTree) {
      Map<String, dynamic> treeNode = {
        'id': node.id ?? 'node_${DateTime.now().millisecondsSinceEpoch}',
        'text': node.text ?? '',
        'level': node.level ?? 0,
        'sequence': node.sequence ?? 0,
        'altText': node.altText,
        'isRejection': node.isRejection ?? false,
        'isApproval': node.isApproval ?? false,
        'isParallel': node.isParallel ?? false,
        'hasCheckmark': node.hasCheckmark,
        'hasX': node.hasX,
      };

      // Add children if they exist
      if (node.children != null && node.children!.isNotEmpty) {
        treeNode['children'] = _convertApiChildrenToJsonFormat(node.children!);
      }

      result.add(treeNode);
    }

    return result;
  }

  /// Helper method to convert API children to JSON format
  List<Map<String, dynamic>> _convertApiChildrenToJsonFormat(List<TreeChild> apiChildren) {
    List<Map<String, dynamic>> result = [];

    for (var child in apiChildren) {
      Map<String, dynamic> childNode = {
        'id': child.id ?? 'child_${DateTime.now().millisecondsSinceEpoch}',
        'text': child.text ?? '',
        'level': child.level ?? 0,
        'sequence': child.sequence ?? 0,
        'altText': child.altText,
        'isRejection': child.isRejection ?? false,
        'isApproval': child.isApproval ?? false,
        'isParallel': child.isParallel ?? false,
        'hasX': child.hasX,
      };

      // Add nested children if they exist
      if (child.children != null && child.children!.isNotEmpty) {
        childNode['children'] = _convertNestedChildrenToJsonFormat(child.children!);
      }

      result.add(childNode);
    }

    return result;
  }

  /// Helper method to convert nested children to JSON format
  List<Map<String, dynamic>> _convertNestedChildrenToJsonFormat(List<ChildChild> nestedChildren) {
    List<Map<String, dynamic>> result = [];

    for (var child in nestedChildren) {
      Map<String, dynamic> childNode = {
        'id': child.id ?? 'nested_${DateTime.now().millisecondsSinceEpoch}',
        'text': child.text ?? '',
        'level': child.level ?? 0,
        'sequence': child.sequence ?? 0,
        'altText': child.altText,
        'isRejection': child.isRejection ?? false,
        'isApproval': child.isApproval ?? false,
        'isParallel': child.isParallel ?? false,
        'hasCheckmark': child.hasCheckmark,
      };

      // Handle recursive children if they exist
      if (child.children != null && child.children!.isNotEmpty) {
        childNode['children'] = _convertNestedChildrenToJsonFormat(child.children!);
      }

      result.add(childNode);
    }

    return result;
  }


}
