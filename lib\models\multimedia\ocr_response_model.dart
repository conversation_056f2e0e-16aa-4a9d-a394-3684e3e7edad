/// Model class for OCR API response
class OcrResponseModel {
  /// Whether the OCR processing was successful
  final bool success;
  
  /// The extracted text from the image
  final String text;
  
  /// Details about the extracted text (e.g., bounding boxes)
  final List<dynamic> details;
  
  /// The path to the output file
  final String outputFile;
  
  /// Any error message if the OCR processing failed
  final String? error;
  
  /// The target language for OCR
  final String targetLang;
  
  /// Additional output information
  final OcrOutputModel output;

  /// Constructor
  OcrResponseModel({
    required this.success,
    required this.text,
    required this.details,
    required this.outputFile,
    this.error,
    required this.targetLang,
    required this.output,
  });

  /// Factory constructor to create an OcrResponseModel from a JSON map
  factory OcrResponseModel.fromJson(Map<String, dynamic> json) {
    return OcrResponseModel(
      success: json['success'] ?? false,
      text: json['text'] ?? '',
      details: json['details'] ?? [],
      outputFile: json['output_file'] ?? '',
      error: json['error'],
      targetLang: json['target_lang'] ?? 'en',
      output: OcrOutputModel.fromJson(json['output'] ?? {}),
    );
  }

  /// Convert this OcrResponseModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'text': text,
      'details': details,
      'output_file': outputFile,
      'error': error,
      'target_lang': targetLang,
      'output': output.toJson(),
    };
  }
  
  /// Get a string representation of this OcrResponseModel
  @override
  String toString() {
    return 'OcrResponseModel{success: $success, text: ${text.isEmpty ? "empty" : "${text.substring(0, text.length > 50 ? 50 : text.length)}..."}, outputFile: $outputFile, error: $error, targetLang: $targetLang}';
  }
}

/// Model class for OCR output information
class OcrOutputModel {
  /// The full text extracted from the image
  final String fullText;
  
  /// Details about the extracted text (e.g., bounding boxes)
  final List<dynamic> details;
  
  /// The path to the file
  final String filePath;
  
  /// The timestamp of the OCR processing
  final String timestamp;
  
  /// The target language for OCR
  final String targetLang;
  
  /// The type of the file
  final String fileType;

  /// Constructor
  OcrOutputModel({
    required this.fullText,
    required this.details,
    required this.filePath,
    required this.timestamp,
    required this.targetLang,
    required this.fileType,
  });

  /// Factory constructor to create an OcrOutputModel from a JSON map
  factory OcrOutputModel.fromJson(Map<String, dynamic> json) {
    return OcrOutputModel(
      fullText: json['full_text'] ?? '',
      details: json['details'] ?? [],
      filePath: json['file_path'] ?? '',
      timestamp: json['timestamp'] ?? '',
      targetLang: json['target_lang'] ?? 'en',
      fileType: json['file_type'] ?? 'image',
    );
  }

  /// Convert this OcrOutputModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'full_text': fullText,
      'details': details,
      'file_path': filePath,
      'timestamp': timestamp,
      'target_lang': targetLang,
      'file_type': fileType,
    };
  }
  
  /// Get a string representation of this OcrOutputModel
  @override
  String toString() {
    return 'OcrOutputModel{fullText: ${fullText.isEmpty ? "empty" : "${fullText.substring(0, fullText.length > 50 ? 50 : fullText.length)}..."}, filePath: $filePath, timestamp: $timestamp, targetLang: $targetLang, fileType: $fileType}';
  }
}
