import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_new.dart';

/// Example demonstrating accordion behavior with expansion panels
/// Only one panel can be expanded at a time
class AccordionExample extends StatefulWidget {
  const AccordionExample({super.key});

  @override
  State<AccordionExample> createState() => _AccordionExampleState();
}

class _AccordionExampleState extends State<AccordionExample> {
  late AccordionController _accordionController;

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
  }

  @override
  void dispose() {
    _accordionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Accordion Example'),
        backgroundColor: Colors.blue.shade100,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Accordion Behavior Demo',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Click on any expansion panel below. Only one can be open at a time.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            SizedBox(height: 24),
            Expanded(
              child: ListView(
                children: [
                  // Panel 1
                  CustomExpansionTileNew(
                    panelId: 'panel_1',
                    accordionController: _accordionController,
                    title: Container(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      child: Text(
                        'Panel 1 - User Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    onExpansionChanged: (expanded) {
                      print('Panel 1 expanded: $expanded');
                    },
                    onTitleTap: () {
                      print('Panel 1 title tapped');
                    },
                    children: [
                      Container(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Name: John Doe'),
                            SizedBox(height: 8),
                            Text('Email: <EMAIL>'),
                            SizedBox(height: 8),
                            Text('Phone: ****** 567 8900'),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  
                  // Panel 2
                  CustomExpansionTileNew(
                    panelId: 'panel_2',
                    accordionController: _accordionController,
                    title: Container(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      child: Text(
                        'Panel 2 - Address Details',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    onExpansionChanged: (expanded) {
                      print('Panel 2 expanded: $expanded');
                    },
                    onTitleTap: () {
                      print('Panel 2 title tapped');
                    },
                    children: [
                      Container(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Street: 123 Main Street'),
                            SizedBox(height: 8),
                            Text('City: New York'),
                            SizedBox(height: 8),
                            Text('State: NY'),
                            SizedBox(height: 8),
                            Text('ZIP: 10001'),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  
                  // Panel 3
                  CustomExpansionTileNew(
                    panelId: 'panel_3',
                    accordionController: _accordionController,
                    title: Container(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      child: Text(
                        'Panel 3 - Preferences',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    onExpansionChanged: (expanded) {
                      print('Panel 3 expanded: $expanded');
                    },
                    onTitleTap: () {
                      print('Panel 3 title tapped');
                    },
                    children: [
                      Container(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Theme: Dark Mode'),
                            SizedBox(height: 8),
                            Text('Language: English'),
                            SizedBox(height: 8),
                            Text('Notifications: Enabled'),
                            SizedBox(height: 8),
                            Text('Auto-save: Every 5 minutes'),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  
                  // Panel 4
                  CustomExpansionTileNew(
                    panelId: 'panel_4',
                    accordionController: _accordionController,
                    title: Container(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      child: Text(
                        'Panel 4 - Account Settings',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    onExpansionChanged: (expanded) {
                      print('Panel 4 expanded: $expanded');
                    },
                    onTitleTap: () {
                      print('Panel 4 title tapped');
                    },
                    children: [
                      Container(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Account Type: Premium'),
                            SizedBox(height: 8),
                            Text('Member Since: January 2023'),
                            SizedBox(height: 8),
                            Text('Two-Factor Auth: Enabled'),
                            SizedBox(height: 8),
                            Text('Backup: Cloud Sync'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
