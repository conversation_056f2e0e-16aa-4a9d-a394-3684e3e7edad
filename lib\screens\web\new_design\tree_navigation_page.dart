import 'package:flutter/material.dart';
import 'package:nsl/models/global_objective.dart';
import 'package:nsl/providers/workflow_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/tree_navigation.dart';
import 'package:nsl/screens/web_transaction/workflow_transaction_screen.dart';
import 'package:provider/provider.dart';

class TreeNavigationPage extends StatefulWidget {
  const TreeNavigationPage({super.key});

  @override
  State<TreeNavigationPage> createState() => _TreeNavigationPageState();
}

class _TreeNavigationPageState extends State<TreeNavigationPage> {
  // Sample JSON data for the tree structure
  final List<Map<String, dynamic>> treeData = [
    {
      'id': 'product_catalog',
      'title': 'Product Catalog',
      'icon': 'assets/images/icons/product.png',
      'children': [
        {
          'id': 'cart',
          'title': 'Cart',
          'icon': 'assets/images/icons/cart.png',
          'children': []
        },
      ]
    },
    {
      'id': 'crm',
      'title': 'CRM',
      'icon': 'assets/images/icons/crm.png',
      'children': [
        {
          'id': 'marketing_automation',
          'title': 'Marketing Automation',
          'icon': 'assets/images/icons/marketing.png',
          'children': []
        },
      ]
    },
    {
      'id': 'order_tracking',
      'title': 'Order Tracking',
      'icon': 'assets/images/icons/order.png',
      'children': []
    },
  ];

  // Widget to display in the content area
  Widget? _contentWidget;

  @override
  Widget build(BuildContext context) {
    // Check if we're displaying the WorkflowTransactionScreen
    bool isShowingWorkflowTransaction = _contentWidget != null &&
        _contentWidget.toString().contains('WorkflowTransactionScreen');

    return Scaffold(
      // Only show AppBar if we're not displaying the WorkflowTransactionScreen
      appBar: isShowingWorkflowTransaction ? null : AppBar(
        title: Text('Navigation'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Row(
        children: [
          // Tree Navigation
          TreeNavigation(
            items: treeData,
            width: 250,
            backgroundColor: Colors.white,
            borderColor: Color(0xffD0D0D0),
            onItemSelected: _handleItemSelected,
          ),

          // Main content area
          Expanded(
            child: Container(
              color: Color(0xffF0F8FF), // Light blue background
              child: Builder(
                builder: (context) {
                  if (_contentWidget != null) {
                    return _contentWidget!;
                  } else {
                    return Center(
                      child: Text(
                        'Select an item from the tree navigation',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Handle item selection
  void _handleItemSelected(Map<String, dynamic> item) {
    setState(() {
      // Check if the selected item is CRM
      if (item['title'] == 'CRM') {
        // Create a sample GlobalObjective for CRM
        final objective = GlobalObjective(
          objectiveId: 'crm_objective',
          name: 'CRM Workflow',
          tenantId: 'default',
          version: '1.0',
          status: 'active',
        );

        // Directly load the WorkflowTransactionScreen as requested
        _contentWidget = ChangeNotifierProvider(
          create: (_) => WorkflowProvider(),
          child: WorkflowTransactionScreen(
            objective: objective,
          ),
        );
      } else {
        // For other items, display a simple message
        _contentWidget = Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Selected: ${item['title']}',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'ID: ${item['id']}',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        );
      }
    });
  }
}
