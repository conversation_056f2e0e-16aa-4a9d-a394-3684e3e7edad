// import 'package:flutter/material.dart';
// import 'package:fl_chart/fl_chart.dart';

// /// A highly configurable pie chart widget using fl_chart package.
// ///
// /// This widget provides a comprehensive pie chart with customizable appearance,
// /// data, colors, labels, and behavior options. It can be configured via JSON
// /// parameters or direct properties.
// class PieChartWidget extends StatefulWidget {
//   // Data properties
//   final List<PieChartDataItem> data;
//   final String? title;
//   final String? subtitle;

//   // Appearance properties
//   final double radius;
//   final double centerSpaceRadius;
//   final double sectionsSpace;
//   final double startDegreeOffset;
//   final List<Color> colors;
//   final Color backgroundColor;
//   final bool showPercentages;
//   final bool showValues;
//   final bool showLabels;
//   final bool showLegend;
//   final LegendPosition legendPosition;

//   // Text styling properties
//   final TextStyle? titleStyle;
//   final TextStyle? subtitleStyle;
//   final TextStyle? labelStyle;
//   final TextStyle? percentageStyle;
//   final TextStyle? valueStyle;
//   final TextStyle? legendStyle;

//   // Legend properties
//   final Color legendBackgroundColor;
//   final double legendSpacing;
//   final double legendIconSize;
//   final bool legendShowPercentages;
//   final bool legendShowValues;

//   // Size and layout properties
//   final double? width;
//   final double? height;
//   final EdgeInsetsGeometry padding;
//   final EdgeInsetsGeometry margin;

//   // Interaction properties
//   final bool enableTooltip;
//   final bool enableAnimation;
//   final Duration animationDuration;
//   final Curve animationCurve;
//   final Function(int)? onSectionTap;

//   // Advanced properties
//   final bool showCenterWidget;
//   final Widget? centerWidget;
//   final String? centerText;
//   final TextStyle? centerTextStyle;
//   final double borderWidth;
//   final Color borderColor;

//   const PieChartWidget({
//     super.key,
//     this.data = const [],
//     this.title,
//     this.subtitle,
//     this.radius = 80.0,
//     this.centerSpaceRadius = 40.0,
//     this.sectionsSpace = 2.0,
//     this.startDegreeOffset = 0.0,
//     this.colors = const [
//       Color(0xFF0058FF),
//       Color(0xFF00C853),
//       Color(0xFFFF5722),
//       Color(0xFFFFC107),
//       Color(0xFF9C27B0),
//       Color(0xFF00BCD4),
//       Color(0xFFFF9800),
//       Color(0xFF4CAF50),
//     ],
//     this.backgroundColor = Colors.transparent,
//     this.showPercentages = true,
//     this.showValues = false,
//     this.showLabels = true,
//     this.showLegend = true,
//     this.legendPosition = LegendPosition.right,
//     this.titleStyle,
//     this.subtitleStyle,
//     this.labelStyle,
//     this.percentageStyle,
//     this.valueStyle,
//     this.legendStyle,
//     this.legendBackgroundColor = Colors.transparent,
//     this.legendSpacing = 8.0,
//     this.legendIconSize = 12.0,
//     this.legendShowPercentages = true,
//     this.legendShowValues = false,
//     this.width,
//     this.height,
//     this.padding = const EdgeInsets.all(16.0),
//     this.margin = EdgeInsets.zero,
//     this.enableTooltip = true,
//     this.enableAnimation = true,
//     this.animationDuration = const Duration(milliseconds: 1500),
//     this.animationCurve = Curves.easeInOut,
//     this.onSectionTap,
//     this.showCenterWidget = false,
//     this.centerWidget,
//     this.centerText,
//     this.centerTextStyle,
//     this.borderWidth = 0.0,
//     this.borderColor = Colors.transparent,
//   });

//   /// Creates a PieChartWidget from a JSON map
//   ///
//   /// This factory constructor allows creating a PieChartWidget from a JSON map,
//   /// making it easy to configure the widget from dynamic data.
//   ///
//   /// Example JSON:
//   /// ```json
//   /// {
//   ///   "title": "Sales Distribution",
//   ///   "data": [
//   ///     {"label": "Product A", "value": 30.0},
//   ///     {"label": "Product B", "value": 25.0},
//   ///     {"label": "Product C", "value": 20.0},
//   ///     {"label": "Product D", "value": 25.0}
//   ///   ],
//   ///   "radius": 100.0,
//   ///   "showPercentages": true,
//   ///   "showLegend": true,
//   ///   "colors": ["#0058FF", "#00C853", "#FF5722", "#FFC107"]
//   /// }
//   /// ```
//   factory PieChartWidget.fromJson(Map<String, dynamic> json) {
//     // Parse data
//     List<PieChartDataItem> data = [];
//     if (json['data'] is List) {
//       data = (json['data'] as List).map((item) {
//         if (item is Map<String, dynamic>) {
//           return PieChartDataItem(
//             label: item['label']?.toString() ?? '',
//             value: (item['value'] as num?)?.toDouble() ?? 0.0,
//             color: _colorFromJson(item['color']),
//           );
//         }
//         return PieChartDataItem(label: '', value: 0.0);
//       }).toList();
//     }

//     // Parse colors
//     List<Color> colors = [
//       const Color(0xFF0058FF),
//       const Color(0xFF00C853),
//       const Color(0xFFFF5722),
//       const Color(0xFFFFC107),
//       const Color(0xFF9C27B0),
//       const Color(0xFF00BCD4),
//       const Color(0xFFFF9800),
//       const Color(0xFF4CAF50),
//     ];
//     if (json['colors'] is List) {
//       final parsedColors = (json['colors'] as List)
//           .map((color) => _colorFromJson(color))
//           .where((color) => color != null)
//           .cast<Color>()
//           .toList();
//       if (parsedColors.isNotEmpty) {
//         colors = parsedColors;
//       }
//     }

//     // Parse legend position
//     LegendPosition legendPosition = LegendPosition.right;
//     if (json['legendPosition'] != null) {
//       switch (json['legendPosition'].toString().toLowerCase()) {
//         case 'top':
//           legendPosition = LegendPosition.top;
//           break;
//         case 'bottom':
//           legendPosition = LegendPosition.bottom;
//           break;
//         case 'left':
//           legendPosition = LegendPosition.left;
//           break;
//         case 'right':
//           legendPosition = LegendPosition.right;
//           break;
//       }
//     }

//     return PieChartWidget(
//       data: data,
//       title: json['title']?.toString(),
//       subtitle: json['subtitle']?.toString(),
//       radius: (json['radius'] as num?)?.toDouble() ?? 80.0,
//       centerSpaceRadius: (json['centerSpaceRadius'] as num?)?.toDouble() ?? 40.0,
//       sectionsSpace: (json['sectionsSpace'] as num?)?.toDouble() ?? 2.0,
//       startDegreeOffset: (json['startDegreeOffset'] as num?)?.toDouble() ?? 0.0,
//       colors: colors,
//       backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.transparent,
//       showPercentages: json['showPercentages'] ?? true,
//       showValues: json['showValues'] ?? false,
//       showLabels: json['showLabels'] ?? true,
//       showLegend: json['showLegend'] ?? true,
//       legendPosition: legendPosition,
//       legendBackgroundColor: _colorFromJson(json['legendBackgroundColor']) ?? Colors.transparent,
//       legendSpacing: (json['legendSpacing'] as num?)?.toDouble() ?? 8.0,
//       legendIconSize: (json['legendIconSize'] as num?)?.toDouble() ?? 12.0,
//       legendShowPercentages: json['legendShowPercentages'] ?? true,
//       legendShowValues: json['legendShowValues'] ?? false,
//       width: (json['width'] as num?)?.toDouble(),
//       height: (json['height'] as num?)?.toDouble(),
//       padding: json['padding'] != null ? EdgeInsets.fromLTRB(
//         (json['padding']['left'] as num?)?.toDouble() ?? 16.0,
//         (json['padding']['top'] as num?)?.toDouble() ?? 16.0,
//         (json['padding']['right'] as num?)?.toDouble() ?? 16.0,
//         (json['padding']['bottom'] as num?)?.toDouble() ?? 16.0,
//       ) : const EdgeInsets.all(16.0),
//       margin: json['margin'] != null ? EdgeInsets.fromLTRB(
//         (json['margin']['left'] as num?)?.toDouble() ?? 0.0,
//         (json['margin']['top'] as num?)?.toDouble() ?? 0.0,
//         (json['margin']['right'] as num?)?.toDouble() ?? 0.0,
//         (json['margin']['bottom'] as num?)?.toDouble() ?? 0.0,
//       ) : EdgeInsets.zero,
//       enableTooltip: json['enableTooltip'] ?? true,
//       enableAnimation: json['enableAnimation'] ?? true,
//       animationDuration: Duration(
//         milliseconds: json['animationDuration'] ?? 1500,
//       ),
//       showCenterWidget: json['showCenterWidget'] ?? false,
//       centerText: json['centerText']?.toString(),
//       borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 0.0,
//       borderColor: _colorFromJson(json['borderColor']) ?? Colors.transparent,
//     );
//   }

//   /// Converts the PieChartWidget to a JSON map
//   Map<String, dynamic> toJson() {
//     return {
//       'data': data.map((item) => item.toJson()).toList(),
//       if (title != null) 'title': title,
//       if (subtitle != null) 'subtitle': subtitle,
//       'radius': radius,
//       'centerSpaceRadius': centerSpaceRadius,
//       'sectionsSpace': sectionsSpace,
//       'startDegreeOffset': startDegreeOffset,
//       'colors': colors.map((color) => _colorToJson(color)).toList(),
//       'backgroundColor': _colorToJson(backgroundColor),
//       'showPercentages': showPercentages,
//       'showValues': showValues,
//       'showLabels': showLabels,
//       'showLegend': showLegend,
//       'legendPosition': legendPosition.name,
//       'legendBackgroundColor': _colorToJson(legendBackgroundColor),
//       'legendSpacing': legendSpacing,
//       'legendIconSize': legendIconSize,
//       'legendShowPercentages': legendShowPercentages,
//       'legendShowValues': legendShowValues,
//       if (width != null) 'width': width,
//       if (height != null) 'height': height,
//       'padding': {
//         'left': padding.horizontal / 2,
//         'top': padding.vertical / 2,
//         'right': padding.horizontal / 2,
//         'bottom': padding.vertical / 2,
//       },
//       'margin': {
//         'left': margin.horizontal / 2,
//         'top': margin.vertical / 2,
//         'right': margin.horizontal / 2,
//         'bottom': margin.vertical / 2,
//       },
//       'enableTooltip': enableTooltip,
//       'enableAnimation': enableAnimation,
//       'animationDuration': animationDuration.inMilliseconds,
//       'showCenterWidget': showCenterWidget,
//       if (centerText != null) 'centerText': centerText,
//       'borderWidth': borderWidth,
//       'borderColor': _colorToJson(borderColor),
//     };
//   }

//   /// Converts a JSON color value to a Flutter Color
//   static Color? _colorFromJson(dynamic colorValue) {
//     if (colorValue == null) return null;

//     if (colorValue is String) {
//       // Handle hex strings like "#FF0000"
//       if (colorValue.startsWith('#')) {
//         String hexColor = colorValue.substring(1);

//         // Handle shorthand hex like #RGB
//         if (hexColor.length == 3) {
//           hexColor = hexColor.split('').map((c) => '$c$c').join('');
//         }

//         // Add alpha channel if missing
//         if (hexColor.length == 6) {
//           hexColor = 'FF$hexColor';
//         }

//         // Parse the hex value
//         try {
//           return Color(int.parse('0x$hexColor'));
//         } catch (e) {
//           return null;
//         }
//       }

//       // Handle named colors
//       switch (colorValue.toLowerCase()) {
//         case 'red':
//           return Colors.red;
//         case 'blue':
//           return const Color(0xFF0058FF);
//         case 'green':
//           return Colors.green;
//         case 'yellow':
//           return Colors.yellow;
//         case 'orange':
//           return Colors.orange;
//         case 'purple':
//           return Colors.purple;
//         case 'pink':
//           return Colors.pink;
//         case 'brown':
//           return Colors.brown;
//         case 'grey':
//         case 'gray':
//           return Colors.grey;
//         case 'black':
//           return Colors.black;
//         case 'white':
//           return Colors.white;
//         case 'amber':
//           return Colors.amber;
//         case 'cyan':
//           return Colors.cyan;
//         case 'indigo':
//           return Colors.indigo;
//         case 'lime':
//           return Colors.lime;
//         case 'teal':
//           return Colors.teal;
//         case 'transparent':
//           return Colors.transparent;
//         default:
//           return null;
//       }
//     } else if (colorValue is int) {
//       return Color(colorValue);
//     }

//     return null;
//   }

//   /// Converts a Flutter Color to a JSON representation
//   static String _colorToJson(Color color) {
//     // Handle transparent
//     if (color == Colors.transparent) return 'transparent';
    
//     // Handle the custom color #0058FF
//     if (color.value == 0xFF0058FF) return '#0058FF';
    
//     // Handle standard colors by name
//     if (color == Colors.red) return 'red';
//     if (color == Colors.green) return 'green';
//     if (color == Colors.yellow) return 'yellow';
//     if (color == Colors.orange) return 'orange';
//     if (color == Colors.purple) return 'purple';
//     if (color == Colors.pink) return 'pink';
//     if (color == Colors.brown) return 'brown';
//     if (color == Colors.grey) return 'grey';
//     if (color == Colors.black) return 'black';
//     if (color == Colors.white) return 'white';
//     if (color == Colors.amber) return 'amber';
//     if (color == Colors.cyan) return 'cyan';
//     if (color == Colors.indigo) return 'indigo';
//     if (color == Colors.lime) return 'lime';
//     if (color == Colors.teal) return 'teal';

//     // Convert to hex string for other colors
//     final r = (color.r * 255).round().toRadixString(16).padLeft(2, '0');
//     final g = (color.g * 255).round().toRadixString(16).padLeft(2, '0');
//     final b = (color.b * 255).round().toRadixString(16).padLeft(2, '0');

//     return '#$r$g$b';
//   }

//   @override
//   State<PieChartWidget> createState() => _PieChartWidgetState();
// }

// class _PieChartWidgetState extends State<PieChartWidget>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _animationController;
//   late Animation<double> _animation;
//   int touchedIndex = -1;

//   @override
//   void initState() {
//     super.initState();
//     _animationController = AnimationController(
//       vsync: this,
//       duration: widget.animationDuration,
//     );
//     _animation = CurvedAnimation(
//       parent: _animationController,
//       curve: widget.animationCurve,
//     );

//     if (widget.enableAnimation) {
//       _animationController.forward();
//     } else {
//       _animationController.value = 1.0;
//     }
//   }

//   @override
//   void dispose() {
//     _animationController.dispose();
//     super.dispose();
//   }

//   double _getResponsiveFontSize(BuildContext context, double baseFontSize) {
//     final screenWidth = MediaQuery.of(context).size.width;

//     if (screenWidth > 1920) {
//       return baseFontSize * 1.2;
//     } else if (screenWidth >= 1440) {
//       return baseFontSize;
//     } else if (screenWidth >= 1280) {
//       return baseFontSize * 0.9;
//     } else if (screenWidth >= 768) {
//       return baseFontSize * 0.8;
//     } else {
//       return baseFontSize * 0.7;
//     }
//   }

//   List<PieChartSectionData> _buildSections() {
//     if (widget.data.isEmpty) {
//       return [
//         PieChartSectionData(
//           color: Colors.grey.shade300,
//           value: 100,
//           title: 'No Data',
//           radius: widget.radius,
//           titleStyle: TextStyle(
//             fontSize: _getResponsiveFontSize(context, 12),
//             fontWeight: FontWeight.w500,
//             color: Colors.grey.shade600,
//           ),
//         ),
//       ];
//     }

//     final total = widget.data.fold<double>(0, (sum, item) => sum + item.value);
    
//     return widget.data.asMap().entries.map((entry) {
//       final index = entry.key;
//       final data = entry.value;
//       final isTouched = index == touchedIndex;
//       final radius = isTouched ? widget.radius + 10 : widget.radius;
      
//       // Use custom color if provided, otherwise use color from palette
//       final color = data.color ?? 
//           widget.colors[index % widget.colors.length];

//       String title = '';
//       if (widget.showLabels && data.label.isNotEmpty) {
//         title = data.label;
//       }
//       if (widget.showPercentages) {
//         final percentage = ((data.value / total) * 100).toStringAsFixed(1);
//         title = title.isEmpty ? '$percentage%' : '$title\n$percentage%';
//       }
//       if (widget.showValues) {
//         final valueStr = data.value.toStringAsFixed(1);
//         title = title.isEmpty ? valueStr : '$title\n$valueStr';
//       }

//       return PieChartSectionData(
//         color: color,
//         value: data.value,
//         title: title,
//         radius: radius,
//         titleStyle: widget.labelStyle ?? TextStyle(
//           fontSize: _getResponsiveFontSize(context, 12),
//           fontWeight: FontWeight.w500,
//           color: Colors.white,
//         ),
//         borderSide: widget.borderWidth > 0 ? BorderSide(
//           color: widget.borderColor,
//           width: widget.borderWidth,
//         ) : BorderSide.none,
//       );
//     }).toList();
//   }

//   Widget _buildLegend() {
//     if (!widget.showLegend || widget.data.isEmpty) {
//       return const SizedBox.shrink();
//     }

//     final total = widget.data.fold<double>(0, (sum, item) => sum + item.value);

//     return Container(
//       padding: const EdgeInsets.all(8.0),
//       decoration: BoxDecoration(
//         color: widget.legendBackgroundColor,
//         borderRadius: BorderRadius.circular(8.0),
//       ),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: widget.data.asMap().entries.map((entry) {
//           final index = entry.key;
//           final data = entry.value;
//           final color = data.color ?? 
//               widget.colors[index % widget.colors.length];

//           String legendText = data.label;
//           if (widget.legendShowPercentages) {
//             final percentage = ((data.value / total) * 100).toStringAsFixed(1);
//             legendText = '$legendText ($percentage%)';
//           }
//           if (widget.legendShowValues) {
//             final valueStr = data.value.toStringAsFixed(1);
//             legendText = widget.legendShowPercentages 
//                 ? '$legendText - $valueStr'
//                 : '$legendText ($valueStr)';
//           }

//           return Padding(
//             padding: EdgeInsets.symmetric(vertical: widget.legendSpacing / 2),
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Container(
//                   width: widget.legendIconSize,
//                   height: widget.legendIconSize,
//                   decoration: BoxDecoration(
//                     color: color,
//                     shape: BoxShape.circle,
//                   ),
//                 ),
//                 SizedBox(width: widget.legendSpacing),
//                 Flexible(
//                   child: Text(
//                     legendText,
//                     style: widget.legendStyle ?? TextStyle(
//                       fontSize: _getResponsiveFontSize(context, 12),
//                       fontWeight: FontWeight.w400,
//                       color: Colors.black87,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           );
//         }).toList(),
//       ),
//     );
//   }

//   Widget _buildChart() {
//     return AnimatedBuilder(
//       animation: _animation,
//       builder: (context, child) {
//         return PieChart(
//           PieChartData(
//             pieTouchData: PieTouchData(
//               enabled: widget.enableTooltip,
//               touchCallback: (FlTouchEvent event, pieTouchResponse) {
//                 setState(() {
//                   if (!event.isInterestedForInteractions ||
//                       pieTouchResponse == null ||
//                       pieTouchResponse.touchedSection == null) {
//                     touchedIndex = -1;
//                     return;
//                   }
//                   touchedIndex = pieTouchResponse
//                       .touchedSection!.touchedSectionIndex;
                  
//                   if (widget.onSectionTap != null) {
//                     widget.onSectionTap!(touchedIndex);
//                   }
//                 });
//               },
//             ),
//             borderData: FlBorderData(show: false),
//             sectionsSpace: widget.sectionsSpace,
//             centerSpaceRadius: widget.centerSpaceRadius,
//             startDegreeOffset: widget.startDegreeOffset,
//             sections: _buildSections().map((section) {
//               return PieChartSectionData(
//                 color: section.color,
//                 value: section.value * _animation.value,
//                 title: _animation.value > 0.8 ? section.title : '',
//                 radius: section.radius,
//                 titleStyle: section.titleStyle,
//                 borderSide: section.borderSide,
//               );
//             }).toList(),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildCenterWidget() {
//     if (!widget.showCenterWidget) {
//       return const SizedBox.shrink();
//     }

//     if (widget.centerWidget != null) {
//       return Positioned.fill(
//         child: Center(child: widget.centerWidget!),
//       );
//     }

//     if (widget.centerText != null) {
//       return Positioned.fill(
//         child: Center(
//           child: Text(
//             widget.centerText!,
//             style: widget.centerTextStyle ?? TextStyle(
//               fontSize: _getResponsiveFontSize(context, 16),
//               fontWeight: FontWeight.bold,
//               color: Colors.black87,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       );
//     }

//     return const SizedBox.shrink();
//   }

//   @override
//   Widget build(BuildContext context) {
//     Widget chartWidget = Stack(
//       children: [
//         AspectRatio(
//           aspectRatio: 1,
//           child: _buildChart(),
//         ),
//         _buildCenterWidget(),
//       ],
//     );

//     // Add title and subtitle if provided
//     List<Widget> columnChildren = [];
    
//     if (widget.title != null) {
//       columnChildren.add(
//         Text(
//           widget.title!,
//           style: widget.titleStyle ?? TextStyle(
//             fontSize: _getResponsiveFontSize(context, 18),
//             fontWeight: FontWeight.bold,
//             color: Colors.black87,
//           ),
//           textAlign: TextAlign.center,
//         ),
//       );
//       columnChildren.add(const SizedBox(height: 8));
//     }

//     if (widget.subtitle != null) {
//       columnChildren.add(
//         Text(
//           widget.subtitle!,
//           style: widget.subtitleStyle ?? TextStyle(
//             fontSize: _getResponsiveFontSize(context, 14),
//             fontWeight: FontWeight.w400,
//             color: Colors.black54,
//           ),
//           textAlign: TextAlign.center,
//         ),
//       );
//       columnChildren.add(const SizedBox(height: 16));
//     }

//     // Arrange chart and legend based on legend position
//     Widget mainContent;
//     switch (widget.legendPosition) {
//       case LegendPosition.top:
//         mainContent = Column(
//           children: [
//             _buildLegend(),
//             const SizedBox(height: 16),
//             Expanded(child: chartWidget),
//           ],
//         );
//         break;
//       case LegendPosition.bottom:
//         mainContent = Column(
//           children: [
//             Expanded(child: chartWidget),
//             const SizedBox(height: 16),
//             _buildLegend(),
//           ],
//         );
//         break;
//       case LegendPosition.left:
//         mainContent = Row(
//           children: [
//             _buildLegend(),
//             const SizedBox(width: 16),
//             Expanded(child: chartWidget),
//           ],
//         );
//         break;
//       case LegendPosition.right:
//         mainContent = Row(
//           children: [
//             Expanded(child: chartWidget),
//             const SizedBox(width: 16),
//             _buildLegend(),
//           ],
//         );
//         break;
//     }

//     columnChildren.add(Expanded(child: mainContent));

//     Widget finalWidget = Column(
//       children: columnChildren,
//     );

//     // Apply size constraints if provided
//     if (widget.width != null || widget.height != null) {
//       finalWidget = SizedBox(
//         width: widget.width,
//         height: widget.height,
//         child: finalWidget,
//       );
//     }

//     // Apply padding and margin
//     finalWidget = Container(
//       padding: widget.padding,
//       margin: widget.margin,
//       decoration: BoxDecoration(
//         color: widget.backgroundColor,
//       ),
//       child: finalWidget,
//     );

//     return finalWidget;
//   }
// }

// /// Data class for pie chart sections
// class PieChartDataItem {
//   final String label;
//   final double value;
//   final Color? color;

//   const PieChartDataItem({
//     required this.label,
//     required this.value,
//     this.color,
//   });

//   Map<String, dynamic> toJson() {
//     return {
//       'label': label,
//       'value': value,
//       if (color != null) 'color': PieChartWidget._colorToJson(color!),
//     };
//   }

//   factory PieChartDataItem.fromJson(Map<String, dynamic> json) {
//     return PieChartDataItem(
//       label: json['label']?.toString() ?? '',
//       value: (json['value'] as num?)?.toDouble() ?? 0.0,
//       color: PieChartWidget._colorFromJson(json['color']),
//     );
//   }
// }

// /// Enum for legend position
// enum LegendPosition {
//   top,
//   bottom,
//   left,
//   right,
// }

// extension LegendPositionExtension on LegendPosition {
//   String get name {
//     switch (this) {
//       case LegendPosition.top:
//         return 'top';
//       case LegendPosition.bottom:
//         return 'bottom';
//       case LegendPosition.left:
//         return 'left';
//       case LegendPosition.right:
//         return 'right';
//     }
//   }
// }
