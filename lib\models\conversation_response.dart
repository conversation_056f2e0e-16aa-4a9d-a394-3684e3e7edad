// To parse this JSON data, do
//
//     final conversationResponse = conversationResponseFromJson(jsonString);

import 'dart:convert';

ConversationResponse conversationResponseFromJson(String str) =>
    ConversationResponse.fromJson(json.decode(str));

String conversationResponseToJson(ConversationResponse data) =>
    json.encode(data.toJson());

class ConversationResponse {
  final String? status;
  final String? sessionId;
  final dynamic question;
  final Classification? classification;
  final List<AgentMapping>? agentMappings;
  final WorkflowResult? workflowResult;
  final dynamic error;
  final ConversationArtifact? conversationArtifact;
  final List<Agent>? agentArtifacts;
  final SummaryArtifact? summaryArtifact;
  final dynamic draftContinuation;
  final dynamic isDraftContinuation;

  ConversationResponse({
    this.status,
    this.sessionId,
    this.question,
    this.classification,
    this.agentMappings,
    this.workflowResult,
    this.error,
    this.conversationArtifact,
    this.agentArtifacts,
    this.summaryArtifact,
    this.draftContinuation,
    this.isDraftContinuation,
  });

  ConversationResponse copyWith({
    String? status,
    String? sessionId,
    dynamic question,
    Classification? classification,
    List<AgentMapping>? agentMappings,
    WorkflowResult? workflowResult,
    dynamic error,
    ConversationArtifact? conversationArtifact,
    List<Agent>? agentArtifacts,
    SummaryArtifact? summaryArtifact,
    dynamic draftContinuation,
    dynamic isDraftContinuation,
  }) =>
      ConversationResponse(
        status: status ?? this.status,
        sessionId: sessionId ?? this.sessionId,
        question: question ?? this.question,
        classification: classification ?? this.classification,
        agentMappings: agentMappings ?? this.agentMappings,
        workflowResult: workflowResult ?? this.workflowResult,
        error: error ?? this.error,
        conversationArtifact: conversationArtifact ?? this.conversationArtifact,
        agentArtifacts: agentArtifacts ?? this.agentArtifacts,
        summaryArtifact: summaryArtifact ?? this.summaryArtifact,
        draftContinuation: draftContinuation ?? this.draftContinuation,
        isDraftContinuation: isDraftContinuation ?? this.isDraftContinuation,
      );

  factory ConversationResponse.fromJson(Map<String, dynamic> json) =>
      ConversationResponse(
        status: json["status"],
        sessionId: json["session_id"],
        question: json["question"],
        classification: json["classification"] == null
            ? null
            : Classification.fromJson(json["classification"]),
        agentMappings: json["agent_mappings"] == null
            ? []
            : List<AgentMapping>.from(
                json["agent_mappings"]!.map((x) => AgentMapping.fromJson(x))),
        workflowResult: json["workflow_result"] == null
            ? null
            : WorkflowResult.fromJson(json["workflow_result"]),
        error: json["error"],
        conversationArtifact: json["conversation_artifact"] == null
            ? null
            : ConversationArtifact.fromJson(json["conversation_artifact"]),
        agentArtifacts: json["agent_artifacts"] == null
            ? []
            : List<Agent>.from(
                json["agent_artifacts"]!.map((x) => Agent.fromJson(x))),
        summaryArtifact: json["summary_artifact"] == null
            ? null
            : SummaryArtifact.fromJson(json["summary_artifact"]),
        draftContinuation: json["draft_continuation"],
        isDraftContinuation: json["is_draft_continuation"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "session_id": sessionId,
        "question": question,
        "classification": classification?.toJson(),
        "agent_mappings": agentMappings == null
            ? []
            : List<dynamic>.from(agentMappings!.map((x) => x.toJson())),
        "workflow_result": workflowResult?.toJson(),
        "error": error,
        "conversation_artifact": conversationArtifact?.toJson(),
        "agent_artifacts": agentArtifacts == null
            ? []
            : List<dynamic>.from(agentArtifacts!.map((x) => x.toJson())),
        "summary_artifact": summaryArtifact?.toJson(),
        "draft_continuation": draftContinuation,
        "is_draft_continuation": isDraftContinuation,
      };
}

class Agent {
  final String? agentId;
  final String? agentName;
  final int? sequenceOrder;
  final String? status;
  final Result? result;
  final dynamic error;

  Agent({
    this.agentId,
    this.agentName,
    this.sequenceOrder,
    this.status,
    this.result,
    this.error,
  });

  Agent copyWith({
    String? agentId,
    String? agentName,
    int? sequenceOrder,
    String? status,
    Result? result,
    dynamic error,
  }) =>
      Agent(
        agentId: agentId ?? this.agentId,
        agentName: agentName ?? this.agentName,
        sequenceOrder: sequenceOrder ?? this.sequenceOrder,
        status: status ?? this.status,
        result: result ?? this.result,
        error: error ?? this.error,
      );

  factory Agent.fromJson(Map<String, dynamic> json) => Agent(
        agentId: json["agent_id"],
        agentName: json["agent_name"],
        sequenceOrder: json["sequence_order"],
        status: json["status"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
        error: json["error"],
      );

  Map<String, dynamic> toJson() => {
        "agent_id": agentId,
        "agent_name": agentName,
        "sequence_order": sequenceOrder,
        "status": status,
        "result": result?.toJson(),
        "error": error,
      };
}

class Result {
  final String? adapterType;
  final String? originalInput;
  final bool? staticResponse;
  final ResultEntity? entity;
  final List<String>? businessLogic;
  final List<String>? coreComponents;
  final List<String>? dataFlow;
  final List<String>? decisionPoints;
  final List<String>? errorHandling;
  final List<String>? interfaces;
  final DateTime? timestamp;
  final List<AgentElement>? agents;
  final WorkFlow? workFlow;

  Result({
    this.adapterType,
    this.originalInput,
    this.staticResponse,
    this.entity,
    this.businessLogic,
    this.coreComponents,
    this.dataFlow,
    this.decisionPoints,
    this.errorHandling,
    this.interfaces,
    this.timestamp,
    this.agents,
    this.workFlow,
  });

  Result copyWith({
    String? adapterType,
    String? originalInput,
    bool? staticResponse,
    ResultEntity? entity,
    List<String>? businessLogic,
    List<String>? coreComponents,
    List<String>? dataFlow,
    List<String>? decisionPoints,
    List<String>? errorHandling,
    List<String>? interfaces,
    DateTime? timestamp,
    List<AgentElement>? agents,
    WorkFlow? workFlow,
  }) =>
      Result(
        adapterType: adapterType ?? this.adapterType,
        originalInput: originalInput ?? this.originalInput,
        staticResponse: staticResponse ?? this.staticResponse,
        entity: entity ?? this.entity,
        businessLogic: businessLogic ?? this.businessLogic,
        coreComponents: coreComponents ?? this.coreComponents,
        dataFlow: dataFlow ?? this.dataFlow,
        decisionPoints: decisionPoints ?? this.decisionPoints,
        errorHandling: errorHandling ?? this.errorHandling,
        interfaces: interfaces ?? this.interfaces,
        timestamp: timestamp ?? this.timestamp,
        agents: agents ?? this.agents,
        workFlow: workFlow ?? this.workFlow,
      );

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        adapterType: json["_adapter_type"],
        originalInput: json["_original_input"],
        staticResponse: json["_static_response"],
        entity: json["entity"] == null
            ? null
            : ResultEntity.fromJson(json["entity"]),
        businessLogic: json["business_logic"] == null
            ? []
            : List<String>.from(json["business_logic"]!.map((x) => x)),
        coreComponents: json["core_components"] == null
            ? []
            : List<String>.from(json["core_components"]!.map((x) => x)),
        dataFlow: json["data_flow"] == null
            ? []
            : List<String>.from(json["data_flow"]!.map((x) => x)),
        decisionPoints: json["decision_points"] == null
            ? []
            : List<String>.from(json["decision_points"]!.map((x) => x)),
        errorHandling: json["error_handling"] == null
            ? []
            : List<String>.from(json["error_handling"]!.map((x) => x)),
        interfaces: json["interfaces"] == null
            ? []
            : List<String>.from(json["interfaces"]!.map((x) => x)),
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        agents: json["agents"] == null
            ? []
            : List<AgentElement>.from(
                json["agents"]!.map((x) => AgentElement.fromJson(x))),
        workFlow: json["work_flow"] == null
            ? null
            : WorkFlow.fromJson(json["work_flow"]),
      );

  Map<String, dynamic> toJson() => {
        "_adapter_type": adapterType,
        "_original_input": originalInput,
        "_static_response": staticResponse,
        "entity": entity?.toJson(),
        "business_logic": businessLogic == null
            ? []
            : List<dynamic>.from(businessLogic!.map((x) => x)),
        "core_components": coreComponents == null
            ? []
            : List<dynamic>.from(coreComponents!.map((x) => x)),
        "data_flow":
            dataFlow == null ? [] : List<dynamic>.from(dataFlow!.map((x) => x)),
        "decision_points": decisionPoints == null
            ? []
            : List<dynamic>.from(decisionPoints!.map((x) => x)),
        "error_handling": errorHandling == null
            ? []
            : List<dynamic>.from(errorHandling!.map((x) => x)),
        "interfaces": interfaces == null
            ? []
            : List<dynamic>.from(interfaces!.map((x) => x)),
        "timestamp": timestamp?.toIso8601String(),
        "agents": agents == null
            ? []
            : List<dynamic>.from(agents!.map((x) => x.toJson())),
        "work_flow": workFlow?.toJson(),
      };
}

class AgentElement {
  final String? createdBy;
  final String? createdDate;
  final String? description;
  final String? id;
  final String? modifiedBy;
  final String? modifiedDate;
  final List<AgentSection>? sections;
  final String? title;
  final String? version;

  AgentElement({
    this.createdBy,
    this.createdDate,
    this.description,
    this.id,
    this.modifiedBy,
    this.modifiedDate,
    this.sections,
    this.title,
    this.version,
  });

  AgentElement copyWith({
    String? createdBy,
    String? createdDate,
    String? description,
    String? id,
    String? modifiedBy,
    String? modifiedDate,
    List<AgentSection>? sections,
    String? title,
    String? version,
  }) =>
      AgentElement(
        createdBy: createdBy ?? this.createdBy,
        createdDate: createdDate ?? this.createdDate,
        description: description ?? this.description,
        id: id ?? this.id,
        modifiedBy: modifiedBy ?? this.modifiedBy,
        modifiedDate: modifiedDate ?? this.modifiedDate,
        sections: sections ?? this.sections,
        title: title ?? this.title,
        version: version ?? this.version,
      );

  factory AgentElement.fromJson(Map<String, dynamic> json) => AgentElement(
        createdBy: json["createdBy"],
        createdDate: json["createdDate"],
        description: json["description"],
        id: json["id"],
        modifiedBy: json["modifiedBy"],
        modifiedDate: json["modifiedDate"],
        sections: json["sections"] == null
            ? []
            : List<AgentSection>.from(
                json["sections"]!.map((x) => AgentSection.fromJson(x))),
        title: json["title"],
        version: json["version"],
      );

  Map<String, dynamic> toJson() => {
        "createdBy": createdBy,
        "createdDate": createdDate,
        "description": description,
        "id": id,
        "modifiedBy": modifiedBy,
        "modifiedDate": modifiedDate,
        "sections": sections == null
            ? []
            : List<dynamic>.from(sections!.map((x) => x.toJson())),
        "title": title,
        "version": version,
      };
}

class AgentSection {
  final String? abbreviation;
  final String? id;
  final List<String>? items;
  final String? title;
  final List<PurpleTab>? tabs;

  

  AgentSection({
    this.abbreviation,
    this.id,
    this.items,
    this.title,
    this.tabs,
  });

  AgentSection copyWith({
    String? abbreviation,
    String? id,
    List<String>? items,
    String? title,
    List<PurpleTab>? tabs,
  }) =>
      AgentSection(
        abbreviation: abbreviation ?? this.abbreviation,
        id: id ?? this.id,
        items: items ?? this.items,
        title: title ?? this.title,
        tabs: tabs ?? this.tabs,
      );

  factory AgentSection.fromJson(Map<String, dynamic> json) => AgentSection(
        abbreviation: json["abbreviation"],
        id: json["id"],
        items: json["items"] == null
            ? []
            : List<String>.from(json["items"]!.map((x) => x)),
        title: json["title"],
        tabs: json["tabs"] == null
            ? []
            : List<PurpleTab>.from(
                json["tabs"]!.map((x) => PurpleTab.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "abbreviation": abbreviation,
        "id": id,
        "items": items == null ? [] : List<dynamic>.from(items!.map((x) => x)),
        "title": title,
        "tabs": tabs == null
            ? []
            : List<dynamic>.from(tabs!.map((x) => x.toJson())),
      };
}

class PurpleTab {
  final List<String>? data;
  final bool? isSelected;
  final String? name;

  PurpleTab({
    this.data,
    this.isSelected,
    this.name,
  });

  PurpleTab copyWith({
    List<String>? data,
    bool? isSelected,
    String? name,
  }) =>
      PurpleTab(
        data: data ?? this.data,
        isSelected: isSelected ?? this.isSelected,
        name: name ?? this.name,
      );

  factory PurpleTab.fromJson(Map<String, dynamic> json) => PurpleTab(
        data: json["data"] == null
            ? []
            : List<String>.from(json["data"]!.map((x) => x)),
        isSelected: json["isSelected"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x)),
        "isSelected": isSelected,
        "name": name,
      };
}

class ResultEntity {
  final List<EntityVersion>? versions;

  ResultEntity({
    this.versions,
  });

  ResultEntity copyWith({
    List<EntityVersion>? versions,
  }) =>
      ResultEntity(
        versions: versions ?? this.versions,
      );

  factory ResultEntity.fromJson(Map<String, dynamic> json) => ResultEntity(
        versions: json["versions"] == null
            ? []
            : List<EntityVersion>.from(
                json["versions"]!.map((x) => EntityVersion.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "versions": versions == null
            ? []
            : List<dynamic>.from(versions!.map((x) => x.toJson())),
      };
}

class EntityVersion {
  final List<VersionDatum>? data;
  final VersionSummary? summary;
  final String? versionId;
  final String? versionName;

  EntityVersion({
    this.data,
    this.summary,
    this.versionId,
    this.versionName,
  });

  EntityVersion copyWith({
    List<VersionDatum>? data,
    VersionSummary? summary,
    String? versionId,
    String? versionName,
  }) =>
      EntityVersion(
        data: data ?? this.data,
        summary: summary ?? this.summary,
        versionId: versionId ?? this.versionId,
        versionName: versionName ?? this.versionName,
      );

  factory EntityVersion.fromJson(Map<String, dynamic> json) => EntityVersion(
        data: json["data"] == null
            ? []
            : List<VersionDatum>.from(
                json["data"]!.map((x) => VersionDatum.fromJson(x))),
        summary: json["summary"] == null
            ? null
            : VersionSummary.fromJson(json["summary"]),
        versionId: json["version_id"],
        versionName: json["version_name"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "summary": summary?.toJson(),
        "version_id": versionId,
        "version_name": versionName,
      };
}

class VersionDatum {
  final List<DatumDatum>? data;
  final String? type;

  VersionDatum({
    this.data,
    this.type,
  });

  VersionDatum copyWith({
    List<DatumDatum>? data,
    String? type,
  }) =>
      VersionDatum(
        data: data ?? this.data,
        type: type ?? this.type,
      );

  factory VersionDatum.fromJson(Map<String, dynamic> json) => VersionDatum(
        data: json["data"] == null
            ? []
            : List<DatumDatum>.from(
                json["data"]!.map((x) => DatumDatum.fromJson(x))),
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "type": type,
      };
}

class DatumDatum {
  final List<EntityElement>? entities;
  final String? id;
  final String? title;

  DatumDatum({
    this.entities,
    this.id,
    this.title,
  });

  DatumDatum copyWith({
    List<EntityElement>? entities,
    String? id,
    String? title,
  }) =>
      DatumDatum(
        entities: entities ?? this.entities,
        id: id ?? this.id,
        title: title ?? this.title,
      );

  factory DatumDatum.fromJson(Map<String, dynamic> json) => DatumDatum(
        entities: json["entities"] == null
            ? []
            : List<EntityElement>.from(
                json["entities"]!.map((x) => EntityElement.fromJson(x))),
        id: json["id"],
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "entities": entities == null
            ? []
            : List<dynamic>.from(entities!.map((x) => x.toJson())),
        "id": id,
        "title": title,
      };
}

class EntityElement {
  final String? attributeString;
  final List<Attributes>? attributes;
  final String? createdBy;
  final String? createdDate;
  final String? description;
  final bool? expanded;
  final String? id;
  final String? modifiedBy;
  final String? modifiedDate;
  final List<EntitySection>? sections;
  final String? title;
  final String? version;
  final String? parentEntity;
  final String? relationType;

  EntityElement({
    this.attributeString,
    this.attributes,
    this.createdBy,
    this.createdDate,
    this.description,
    this.expanded,
    this.id,
    this.modifiedBy,
    this.modifiedDate,
    this.sections,
    this.title,
    this.version,
    this.parentEntity,
    this.relationType,
  });

  EntityElement copyWith({
    String? attributeString,
    List<Attributes>? attributes,
    String? createdBy,
    String? createdDate,
    String? description,
    bool? expanded,
    String? id,
    String? modifiedBy,
    String? modifiedDate,
    List<EntitySection>? sections,
    String? title,
    String? version,
    String? parentEntity,
    String? relationType,
  }) =>
      EntityElement(
        attributeString: attributeString ?? this.attributeString,
        attributes: attributes ?? this.attributes,
        createdBy: createdBy ?? this.createdBy,
        createdDate: createdDate ?? this.createdDate,
        description: description ?? this.description,
        expanded: expanded ?? this.expanded,
        id: id ?? this.id,
        modifiedBy: modifiedBy ?? this.modifiedBy,
        modifiedDate: modifiedDate ?? this.modifiedDate,
        sections: sections ?? this.sections,
        title: title ?? this.title,
        version: version ?? this.version,
        parentEntity: parentEntity ?? this.parentEntity,
        relationType: relationType ?? this.relationType,
      );

  factory EntityElement.fromJson(Map<String, dynamic> json) => EntityElement(
        attributeString: json["attributeString"],
        attributes: json["attributes"] == null
            ? []
            : List<Attributes>.from(
                json["attributes"]!.map((x) => Attributes.fromJson(x))),
        createdBy: json["created_by"],
        createdDate: json["created_date"],
        description: json["description"],
        expanded: json["expanded"],
        id: json["id"],
        modifiedBy: json["modified_by"],
        modifiedDate: json["modified_date"],
        sections: json["sections"] == null
            ? []
            : List<EntitySection>.from(
                json["sections"]!.map((x) => EntitySection.fromJson(x))),
        title: json["title"],
        version: json["version"],
        parentEntity: json["parentEntity"],
        relationType: json["relationType"],
      );

  Map<String, dynamic> toJson() => {
        "attributeString": attributeString,
        "attributes": attributes == null
            ? []
            : List<dynamic>.from(attributes!.map((x) => x.toJson())),
        "created_by": createdBy,
        "created_date": createdDate,
        "description": description,
        "expanded": expanded,
        "id": id,
        "modified_by": modifiedBy,
        "modified_date": modifiedDate,
        "sections": sections == null
            ? []
            : List<dynamic>.from(sections!.map((x) => x.toJson())),
        "title": title,
        "version": version,
        "parentEntity": parentEntity,
        "relationType": relationType,
      };
}

class Attributes {
  final String? createdBy;
  final String? createdDate;
  final String? description;
  final List<String>? entityIds;
  final String? id;
  final bool? isPk;
  final String? modifiedBy;
  final String? modifiedDate;
  final String? name;
  final bool? required;
  final String? type;
  final String? uiControl;
  final List<Validation>? validation;
  final String? version;
  final bool? isFk;

  Attributes({
    this.createdBy,
    this.createdDate,
    this.description,
    this.entityIds,
    this.id,
    this.isPk,
    this.modifiedBy,
    this.modifiedDate,
    this.name,
    this.required,
    this.type,
    this.uiControl,
    this.validation,
    this.version,
    this.isFk,
  });

  Attributes copyWith({
    String? createdBy,
    String? createdDate,
    String? description,
    List<String>? entityIds,
    String? id,
    bool? isPk,
    String? modifiedBy,
    String? modifiedDate,
    String? name,
    bool? required,
    String? type,
    String? uiControl,
    List<Validation>? validation,
    String? version,
    bool? isFk,
  }) =>
      Attributes(
        createdBy: createdBy ?? this.createdBy,
        createdDate: createdDate ?? this.createdDate,
        description: description ?? this.description,
        entityIds: entityIds ?? this.entityIds,
        id: id ?? this.id,
        isPk: isPk ?? this.isPk,
        modifiedBy: modifiedBy ?? this.modifiedBy,
        modifiedDate: modifiedDate ?? this.modifiedDate,
        name: name ?? this.name,
        required: required ?? this.required,
        type: type ?? this.type,
        uiControl: uiControl ?? this.uiControl,
        validation: validation ?? this.validation,
        version: version ?? this.version,
        isFk: isFk ?? this.isFk,
      );

  factory Attributes.fromJson(Map<String, dynamic> json) => Attributes(
        createdBy: json["created_by"],
        createdDate: json["created_date"],
        description: json["description"],
        entityIds: json["entity_ids"] == null
            ? []
            : List<String>.from(json["entity_ids"]!.map((x) => x)),
        id: json["id"],
        isPk: json["isPK"],
        modifiedBy: json["modified_by"],
        modifiedDate: json["modified_date"],
        name: json["name"],
        required: json["required"],
        type: json["type"],
        uiControl: json["ui_control"],
        validation: json["validation"] == null
            ? []
            : List<Validation>.from(
                json["validation"]!.map((x) => Validation.fromJson(x))),
        version: json["version"],
        isFk: json["isFK"],
      );

  Map<String, dynamic> toJson() => {
        "created_by": createdBy,
        "created_date": createdDate,
        "description": description,
        "entity_ids": entityIds == null
            ? []
            : List<dynamic>.from(entityIds!.map((x) => x)),
        "id": id,
        "isPK": isPk,
        "modified_by": modifiedBy,
        "modified_date": modifiedDate,
        "name": name,
        "required": required,
        "type": type,
        "ui_control": uiControl,
        "validation": validation == null
            ? []
            : List<dynamic>.from(validation!.map((x) => x.toJson())),
        "version": version,
        "isFK": isFk,
      };
}

class Validation {
  final String? hint;
  final String? regExp;
  final String? validationMessage;

  Validation({
    this.hint,
    this.regExp,
    this.validationMessage,
  });

  Validation copyWith({
    String? hint,
    String? regExp,
    String? validationMessage,
  }) =>
      Validation(
        hint: hint ?? this.hint,
        regExp: regExp ?? this.regExp,
        validationMessage: validationMessage ?? this.validationMessage,
      );

  factory Validation.fromJson(Map<String, dynamic> json) => Validation(
        hint: json["hint"],
        regExp: json["reg_exp"],
        validationMessage: json["validation_message"],
      );

  Map<String, dynamic> toJson() => {
        "hint": hint,
        "reg_exp": regExp,
        "validation_message": validationMessage,
      };
}

class EntitySection {
  final String? abbreviation;
  final String? id;
  final List<dynamic>? items;
  final String? title;
  final String? content;
  final List<PurpleTab>? tabs;
  final List<List<EntityDatum>>? entityData;

  EntitySection({
    this.abbreviation,
    this.id,
    this.items,
    this.title,
    this.content,
    this.tabs,
    this.entityData,
  });

  EntitySection copyWith({
    String? abbreviation,
    String? id,
    List<dynamic>? items,
    String? title,
    String? content,
    List<PurpleTab>? tabs,
    List<List<EntityDatum>>? entityData,
  }) =>
      EntitySection(
        abbreviation: abbreviation ?? this.abbreviation,
        id: id ?? this.id,
        items: items ?? this.items,
        title: title ?? this.title,
        content: content ?? this.content,
        tabs: tabs ?? this.tabs,
        entityData: entityData ?? this.entityData,
      );

  factory EntitySection.fromJson(Map<String, dynamic> json) => EntitySection(
        abbreviation: json["abbreviation"],
        id: json["id"],
        items: json["items"] == null
            ? []
            : List<dynamic>.from(json["items"]!.map((x) => x)),
        title: json["title"],
        content: json["content"],
        tabs: json["tabs"] == null
            ? []
            : List<PurpleTab>.from(
                json["tabs"]!.map((x) => PurpleTab.fromJson(x))),
        entityData: json["entity_data"] == null
            ? []
            : List<List<EntityDatum>>.from(json["entity_data"]!.map((x) =>
                List<EntityDatum>.from(x.map((x) => EntityDatum.fromJson(x))))),
      );

  Map<String, dynamic> toJson() => {
        "abbreviation": abbreviation,
        "id": id,
        "items": items == null ? [] : List<dynamic>.from(items!.map((x) => x)),
        "title": title,
        "content": content,
        "tabs": tabs == null
            ? []
            : List<dynamic>.from(tabs!.map((x) => x.toJson())),
        "entity_data": entityData == null
            ? []
            : List<dynamic>.from(entityData!
                .map((x) => List<dynamic>.from(x.map((x) => x.toJson())))),
      };
}

class EntityDatum {
  final String? id;
  final String? name;
  final String? value;

  EntityDatum({
    this.id,
    this.name,
    this.value,
  });

  EntityDatum copyWith({
    String? id,
    String? name,
    String? value,
  }) =>
      EntityDatum(
        id: id ?? this.id,
        name: name ?? this.name,
        value: value ?? this.value,
      );

  factory EntityDatum.fromJson(Map<String, dynamic> json) => EntityDatum(
        id: json["id"],
        name: json["name"],
        value: json["value"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "value": value,
      };
}

class PurpleItem {
  final String? description;
  final String? name;
  final String? severity;
  final String? entity;
  final String? field;
  final String? type;
  final String? via;
  final String? rule;
  final List<String>? data;
  final String? title;

  PurpleItem({
    this.description,
    this.name,
    this.severity,
    this.entity,
    this.field,
    this.type,
    this.via,
    this.rule,
    this.data,
    this.title,
  });

  PurpleItem copyWith({
    String? description,
    String? name,
    String? severity,
    String? entity,
    String? field,
    String? type,
    String? via,
    String? rule,
    List<String>? data,
    String? title,
  }) =>
      PurpleItem(
        description: description ?? this.description,
        name: name ?? this.name,
        severity: severity ?? this.severity,
        entity: entity ?? this.entity,
        field: field ?? this.field,
        type: type ?? this.type,
        via: via ?? this.via,
        rule: rule ?? this.rule,
        data: data ?? this.data,
        title: title ?? this.title,
      );

  factory PurpleItem.fromJson(Map<String, dynamic> json) => PurpleItem(
        description: json["description"],
        name: json["name"],
        severity: json["severity"],
        entity: json["entity"],
        field: json["field"],
        type: json["type"],
        via: json["via"],
        rule: json["rule"],
        data: json["data"] == null
            ? []
            : List<String>.from(json["data"]!.map((x) => x)),
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "description": description,
        "name": name,
        "severity": severity,
        "entity": entity,
        "field": field,
        "type": type,
        "via": via,
        "rule": rule,
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x)),
        "title": title,
      };
}

class VersionSummary {
  final String? title;
  final List<VersionsListElement>? versions;

  VersionSummary({
    this.title,
    this.versions,
  });

  VersionSummary copyWith({
    String? title,
    List<VersionsListElement>? versions,
  }) =>
      VersionSummary(
        title: title ?? this.title,
        versions: versions ?? this.versions,
      );

  factory VersionSummary.fromJson(Map<String, dynamic> json) => VersionSummary(
        title: json["title"],
        versions: json["versions"] == null
            ? []
            : List<VersionsListElement>.from(
                json["versions"]!.map((x) => VersionsListElement.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "versions": versions == null
            ? []
            : List<dynamic>.from(versions!.map((x) => x.toJson())),
      };
}

class VersionsListElement {
  final String? versionId;
  final String? versionName;

  VersionsListElement({
    this.versionId,
    this.versionName,
  });

  VersionsListElement copyWith({
    String? versionId,
    String? versionName,
  }) =>
      VersionsListElement(
        versionId: versionId ?? this.versionId,
        versionName: versionName ?? this.versionName,
      );

  factory VersionsListElement.fromJson(Map<String, dynamic> json) =>
      VersionsListElement(
        versionId: json["version_id"],
        versionName: json["version_name"],
      );

  Map<String, dynamic> toJson() => {
        "version_id": versionId,
        "version_name": versionName,
      };
}

class WorkFlow {
  final List<VersionsListElement>? versionsList;
  final WorkFlowDetails? workFlowDetails;

  WorkFlow({
    this.versionsList,
    this.workFlowDetails,
  });

  WorkFlow copyWith({
    List<VersionsListElement>? versionsList,
    WorkFlowDetails? workFlowDetails,
  }) =>
      WorkFlow(
        versionsList: versionsList ?? this.versionsList,
        workFlowDetails: workFlowDetails ?? this.workFlowDetails,
      );

  factory WorkFlow.fromJson(Map<String, dynamic> json) => WorkFlow(
        versionsList: json["versions_list"] == null
            ? []
            : List<VersionsListElement>.from(json["versions_list"]!
                .map((x) => VersionsListElement.fromJson(x))),
        workFlowDetails: json["work_flow_details"] == null
            ? null
            : WorkFlowDetails.fromJson(json["work_flow_details"]),
      );

  Map<String, dynamic> toJson() => {
        "versions_list": versionsList == null
            ? []
            : List<dynamic>.from(versionsList!.map((x) => x.toJson())),
        "work_flow_details": workFlowDetails?.toJson(),
      };
}

class WorkFlowDetails {
  final String? createdBy;
  final String? createdDate;
  final String? description;
  final String? id;
  final String? mainTitle;
  final String? modifiedBy;
  final String? modifiedDate;
  final List<WorkFlowDetailsSection>? sections;
  final String? title;
  final List<Tree>? tree;
  final String? version;
  final String? versionId;
  final String? versionName;

  WorkFlowDetails({
    this.createdBy,
    this.createdDate,
    this.description,
    this.id,
    this.mainTitle,
    this.modifiedBy,
    this.modifiedDate,
    this.sections,
    this.title,
    this.tree,
    this.version,
    this.versionId,
    this.versionName,
  });

  WorkFlowDetails copyWith({
    String? createdBy,
    String? createdDate,
    String? description,
    String? id,
    String? mainTitle,
    String? modifiedBy,
    String? modifiedDate,
    List<WorkFlowDetailsSection>? sections,
    String? title,
    List<Tree>? tree,
    String? version,
    String? versionId,
    String? versionName,
  }) =>
      WorkFlowDetails(
        createdBy: createdBy ?? this.createdBy,
        createdDate: createdDate ?? this.createdDate,
        description: description ?? this.description,
        id: id ?? this.id,
        mainTitle: mainTitle ?? this.mainTitle,
        modifiedBy: modifiedBy ?? this.modifiedBy,
        modifiedDate: modifiedDate ?? this.modifiedDate,
        sections: sections ?? this.sections,
        title: title ?? this.title,
        tree: tree ?? this.tree,
        version: version ?? this.version,
        versionId: versionId ?? this.versionId,
        versionName: versionName ?? this.versionName,
      );

  factory WorkFlowDetails.fromJson(Map<String, dynamic> json) =>
      WorkFlowDetails(
        createdBy: json["createdBy"],
        createdDate: json["createdDate"],
        description: json["description"],
        id: json["id"],
        mainTitle: json["mainTitle"],
        modifiedBy: json["modifiedBy"],
        modifiedDate: json["modifiedDate"],
        sections: json["sections"] == null
            ? []
            : List<WorkFlowDetailsSection>.from(json["sections"]!
                .map((x) => WorkFlowDetailsSection.fromJson(x))),
        title: json["title"],
        tree: json["tree"] == null
            ? []
            : List<Tree>.from(json["tree"]!.map((x) => Tree.fromJson(x))),
        version: json["version"],
        versionId: json["version_id"],
        versionName: json["version_name"],
      );

  Map<String, dynamic> toJson() => {
        "createdBy": createdBy,
        "createdDate": createdDate,
        "description": description,
        "id": id,
        "mainTitle": mainTitle,
        "modifiedBy": modifiedBy,
        "modifiedDate": modifiedDate,
        "sections": sections == null
            ? []
            : List<dynamic>.from(sections!.map((x) => x.toJson())),
        "title": title,
        "tree": tree == null
            ? []
            : List<dynamic>.from(tree!.map((x) => x.toJson())),
        "version": version,
        "version_id": versionId,
        "version_name": versionName,
      };
}

class WorkFlowDetailsSection {
  final Content? content;
  final String? id;
  final List<FluffyTab>? tabs;
  final String? title;

  WorkFlowDetailsSection({
    this.content,
    this.id,
    this.tabs,
    this.title,
  });

  WorkFlowDetailsSection copyWith({
    Content? content,
    String? id,
    List<FluffyTab>? tabs,
    String? title,
  }) =>
      WorkFlowDetailsSection(
        content: content ?? this.content,
        id: id ?? this.id,
        tabs: tabs ?? this.tabs,
        title: title ?? this.title,
      );

  factory WorkFlowDetailsSection.fromJson(Map<String, dynamic> json) =>
      WorkFlowDetailsSection(
        content:
            json["content"] == null ? null : Content.fromJson(json["content"]),
        id: json["id"],
        tabs: json["tabs"] == null
            ? []
            : List<FluffyTab>.from(
                json["tabs"]!.map((x) => FluffyTab.fromJson(x))),
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "content": content?.toJson(),
        "id": id,
        "tabs": tabs == null
            ? []
            : List<dynamic>.from(tabs!.map((x) => x.toJson())),
        "title": title,
      };
}

class Content {
  final String? description;
  final List<ContentItem>? items;
  final List<Rule>? rules;
  final List<EntityOperation>? entityOperations;
  final List<Input>? inputs;
  final List<System>? systems;
  final List<Monitoring>? monitoring;
  final List<Target>? targets;

  Content({
    this.description,
    this.items,
    this.rules,
    this.entityOperations,
    this.inputs,
    this.systems,
    this.monitoring,
    this.targets,
  });

  Content copyWith({
    String? description,
    List<ContentItem>? items,
    List<Rule>? rules,
    List<EntityOperation>? entityOperations,
    List<Input>? inputs,
    List<System>? systems,
    List<Monitoring>? monitoring,
    List<Target>? targets,
  }) =>
      Content(
        description: description ?? this.description,
        items: items ?? this.items,
        rules: rules ?? this.rules,
        entityOperations: entityOperations ?? this.entityOperations,
        inputs: inputs ?? this.inputs,
        systems: systems ?? this.systems,
        monitoring: monitoring ?? this.monitoring,
        targets: targets ?? this.targets,
      );

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        description: json["description"],
        items: json["items"] == null
            ? []
            : List<ContentItem>.from(
                json["items"]!.map((x) => ContentItem.fromJson(x))),
        rules: json["rules"] == null
            ? []
            : List<Rule>.from(json["rules"]!.map((x) => Rule.fromJson(x))),
        entityOperations: json["entity_operations"] == null
            ? []
            : List<EntityOperation>.from(json["entity_operations"]!
                .map((x) => EntityOperation.fromJson(x))),
        inputs: json["inputs"] == null
            ? []
            : List<Input>.from(json["inputs"]!.map((x) => Input.fromJson(x))),
        systems: json["systems"] == null
            ? []
            : List<System>.from(
                json["systems"]!.map((x) => System.fromJson(x))),
        monitoring: json["monitoring"] == null
            ? []
            : List<Monitoring>.from(
                json["monitoring"]!.map((x) => Monitoring.fromJson(x))),
        targets: json["targets"] == null
            ? []
            : List<Target>.from(
                json["targets"]!.map((x) => Target.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "description": description,
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "rules": rules == null
            ? []
            : List<dynamic>.from(rules!.map((x) => x.toJson())),
        "entity_operations": entityOperations == null
            ? []
            : List<dynamic>.from(entityOperations!.map((x) => x.toJson())),
        "inputs": inputs == null
            ? []
            : List<dynamic>.from(inputs!.map((x) => x.toJson())),
        "systems": systems == null
            ? []
            : List<dynamic>.from(systems!.map((x) => x.toJson())),
        "monitoring": monitoring == null
            ? []
            : List<dynamic>.from(monitoring!.map((x) => x.toJson())),
        "targets": targets == null
            ? []
            : List<dynamic>.from(targets!.map((x) => x.toJson())),
      };
}

class EntityOperation {
  final String? entity;
  final String? operations;

  EntityOperation({
    this.entity,
    this.operations,
  });

  EntityOperation copyWith({
    String? entity,
    String? operations,
  }) =>
      EntityOperation(
        entity: entity ?? this.entity,
        operations: operations ?? this.operations,
      );

  factory EntityOperation.fromJson(Map<String, dynamic> json) =>
      EntityOperation(
        entity: json["entity"],
        operations: json["operations"],
      );

  Map<String, dynamic> toJson() => {
        "entity": entity,
        "operations": operations,
      };
}

class Input {
  final String? description;
  final String? from;
  final String? process;

  Input({
    this.description,
    this.from,
    this.process,
  });

  Input copyWith({
    String? description,
    String? from,
    String? process,
  }) =>
      Input(
        description: description ?? this.description,
        from: from ?? this.from,
        process: process ?? this.process,
      );

  factory Input.fromJson(Map<String, dynamic> json) => Input(
        description: json["description"],
        from: json["from"],
        process: json["process"],
      );

  Map<String, dynamic> toJson() => {
        "description": description,
        "from": from,
        "process": process,
      };
}

class ContentItem {
  final String? description;
  final String? title;

  ContentItem({
    this.description,
    this.title,
  });

  ContentItem copyWith({
    String? description,
    String? title,
  }) =>
      ContentItem(
        description: description ?? this.description,
        title: title ?? this.title,
      );

  factory ContentItem.fromJson(Map<String, dynamic> json) => ContentItem(
        description: json["description"],
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "description": description,
        "title": title,
      };
}

class Monitoring {
  final String? name;
  final String? value;

  Monitoring({
    this.name,
    this.value,
  });

  Monitoring copyWith({
    String? name,
    String? value,
  }) =>
      Monitoring(
        name: name ?? this.name,
        value: value ?? this.value,
      );

  factory Monitoring.fromJson(Map<String, dynamic> json) => Monitoring(
        name: json["name"],
        value: json["value"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "value": value,
      };
}

class Rule {
  final String? entity;
  final String? implementation;
  final String? title;
  final String? action;
  final String? condition;
  final String? enforcedBy;

  Rule({
    this.entity,
    this.implementation,
    this.title,
    this.action,
    this.condition,
    this.enforcedBy,
  });

  Rule copyWith({
    String? entity,
    String? implementation,
    String? title,
    String? action,
    String? condition,
    String? enforcedBy,
  }) =>
      Rule(
        entity: entity ?? this.entity,
        implementation: implementation ?? this.implementation,
        title: title ?? this.title,
        action: action ?? this.action,
        condition: condition ?? this.condition,
        enforcedBy: enforcedBy ?? this.enforcedBy,
      );

  factory Rule.fromJson(Map<String, dynamic> json) => Rule(
        entity: json["entity"],
        implementation: json["implementation"],
        title: json["title"],
        action: json["action"],
        condition: json["condition"],
        enforcedBy: json["enforced_by"],
      );

  Map<String, dynamic> toJson() => {
        "entity": entity,
        "implementation": implementation,
        "title": title,
        "action": action,
        "condition": condition,
        "enforced_by": enforcedBy,
      };
}

class System {
  final String? description;
  final String? name;

  System({
    this.description,
    this.name,
  });

  System copyWith({
    String? description,
    String? name,
  }) =>
      System(
        description: description ?? this.description,
        name: name ?? this.name,
      );

  factory System.fromJson(Map<String, dynamic> json) => System(
        description: json["description"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "description": description,
        "name": name,
      };
}

class Target {
  final String? code;
  final String? name;
  final String? target;

  Target({
    this.code,
    this.name,
    this.target,
  });

  Target copyWith({
    String? code,
    String? name,
    String? target,
  }) =>
      Target(
        code: code ?? this.code,
        name: name ?? this.name,
        target: target ?? this.target,
      );

  factory Target.fromJson(Map<String, dynamic> json) => Target(
        code: json["code"],
        name: json["name"],
        target: json["target"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "name": name,
        "target": target,
      };
}

class FluffyTab {
  final String? id;
  final String? title;

  FluffyTab({
    this.id,
    this.title,
  });

  FluffyTab copyWith({
    String? id,
    String? title,
  }) =>
      FluffyTab(
        id: id ?? this.id,
        title: title ?? this.title,
      );

  factory FluffyTab.fromJson(Map<String, dynamic> json) => FluffyTab(
        id: json["id"],
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
      };
}

class Tree {
  final String? functionType;
  final String? id;
  final bool? isApproval;
  final bool? isParallel;
  final bool? isRejection;
  final int? level;
  final List<TreeSection>? sections;
  final int? sequence;
  final String? status;
  final String? text;
  final String? version;
  final String? workflowSource;
  final List<TreeChild>? children;
  final String? altText;
  final bool? hasCheckmark;
  final bool? hasX;

  Tree({
    this.functionType,
    this.id,
    this.isApproval,
    this.isParallel,
    this.isRejection,
    this.level,
    this.sections,
    this.sequence,
    this.status,
    this.text,
    this.version,
    this.workflowSource,
    this.children,
    this.altText,
    this.hasCheckmark,
    this.hasX,
  });

  Tree copyWith({
    String? functionType,
    String? id,
    bool? isApproval,
    bool? isParallel,
    bool? isRejection,
    int? level,
    List<TreeSection>? sections,
    int? sequence,
    String? status,
    String? text,
    String? version,
    String? workflowSource,
    List<TreeChild>? children,
    String? altText,
    bool? hasCheckmark,
    bool? hasX,
  }) =>
      Tree(
        functionType: functionType ?? this.functionType,
        id: id ?? this.id,
        isApproval: isApproval ?? this.isApproval,
        isParallel: isParallel ?? this.isParallel,
        isRejection: isRejection ?? this.isRejection,
        level: level ?? this.level,
        sections: sections ?? this.sections,
        sequence: sequence ?? this.sequence,
        status: status ?? this.status,
        text: text ?? this.text,
        version: version ?? this.version,
        workflowSource: workflowSource ?? this.workflowSource,
        children: children ?? this.children,
        altText: altText ?? this.altText,
        hasCheckmark: hasCheckmark ?? this.hasCheckmark,
        hasX: hasX ?? this.hasX,
      );

  factory Tree.fromJson(Map<String, dynamic> json) => Tree(
        functionType: json["function_type"],
        id: json["id"],
        isApproval: json["isApproval"],
        isParallel: json["isParallel"],
        isRejection: json["isRejection"],
        level: json["level"],
        sections: json["sections"] == null
            ? []
            : List<TreeSection>.from(
                json["sections"]!.map((x) => TreeSection.fromJson(x))),
        sequence: json["sequence"],
        status: json["status"],
        text: json["text"],
        version: json["version"],
        workflowSource: json["workflow_source"],
        children: json["children"] == null
            ? []
            : List<TreeChild>.from(
                json["children"]!.map((x) => TreeChild.fromJson(x))),
        altText: json["altText"],
        hasCheckmark: json["hasCheckmark"],
        hasX: json["hasX"],
      );

  Map<String, dynamic> toJson() => {
        "function_type": functionType,
        "id": id,
        "isApproval": isApproval,
        "isParallel": isParallel,
        "isRejection": isRejection,
        "level": level,
        "sections": sections == null
            ? []
            : List<dynamic>.from(sections!.map((x) => x.toJson())),
        "sequence": sequence,
        "status": status,
        "text": text,
        "version": version,
        "workflow_source": workflowSource,
        "children": children == null
            ? []
            : List<dynamic>.from(children!.map((x) => x.toJson())),
        "altText": altText,
        "hasCheckmark": hasCheckmark,
        "hasX": hasX,
      };
}

class TreeChild {
  final String? altText;
  final List<ChildChild>? children;
  final String? functionType;
  final String? id;
  final bool? isApproval;
  final bool? isParallel;
  final bool? isRejection;
  final int? level;
  final int? sequence;
  final String? status;
  final String? text;
  final String? version;
  final String? workflowSource;
  final bool? hasX;

  TreeChild({
    this.altText,
    this.children,
    this.functionType,
    this.id,
    this.isApproval,
    this.isParallel,
    this.isRejection,
    this.level,
    this.sequence,
    this.status,
    this.text,
    this.version,
    this.workflowSource,
    this.hasX,
  });

  TreeChild copyWith({
    String? altText,
    List<ChildChild>? children,
    String? functionType,
    String? id,
    bool? isApproval,
    bool? isParallel,
    bool? isRejection,
    int? level,
    int? sequence,
    String? status,
    String? text,
    String? version,
    String? workflowSource,
    bool? hasX,
  }) =>
      TreeChild(
        altText: altText ?? this.altText,
        children: children ?? this.children,
        functionType: functionType ?? this.functionType,
        id: id ?? this.id,
        isApproval: isApproval ?? this.isApproval,
        isParallel: isParallel ?? this.isParallel,
        isRejection: isRejection ?? this.isRejection,
        level: level ?? this.level,
        sequence: sequence ?? this.sequence,
        status: status ?? this.status,
        text: text ?? this.text,
        version: version ?? this.version,
        workflowSource: workflowSource ?? this.workflowSource,
        hasX: hasX ?? this.hasX,
      );

  factory TreeChild.fromJson(Map<String, dynamic> json) => TreeChild(
        altText: json["altText"],
        children: json["children"] == null
            ? []
            : List<ChildChild>.from(
                json["children"]!.map((x) => ChildChild.fromJson(x))),
        functionType: json["function_type"],
        id: json["id"],
        isApproval: json["isApproval"],
        isParallel: json["isParallel"],
        isRejection: json["isRejection"],
        level: json["level"],
        sequence: json["sequence"],
        status: json["status"],
        text: json["text"],
        version: json["version"],
        workflowSource: json["workflow_source"],
        hasX: json["hasX"],
      );

  Map<String, dynamic> toJson() => {
        "altText": altText,
        "children": children == null
            ? []
            : List<dynamic>.from(children!.map((x) => x.toJson())),
        "function_type": functionType,
        "id": id,
        "isApproval": isApproval,
        "isParallel": isParallel,
        "isRejection": isRejection,
        "level": level,
        "sequence": sequence,
        "status": status,
        "text": text,
        "version": version,
        "workflow_source": workflowSource,
        "hasX": hasX,
      };
}

class ChildChild {
  final String? altText;
  final String? functionType;
  final bool? hasCheckmark;
  final String? id;
  final bool? isApproval;
  final bool? isParallel;
  final bool? isRejection;
  final int? level;
  final int? sequence;
  final String? status;
  final String? text;
  final String? version;
  final String? workflowSource;
  final List<ChildChild>? children;

  ChildChild({
    this.altText,
    this.functionType,
    this.hasCheckmark,
    this.id,
    this.isApproval,
    this.isParallel,
    this.isRejection,
    this.level,
    this.sequence,
    this.status,
    this.text,
    this.version,
    this.workflowSource,
    this.children,
  });

  ChildChild copyWith({
    String? altText,
    String? functionType,
    bool? hasCheckmark,
    String? id,
    bool? isApproval,
    bool? isParallel,
    bool? isRejection,
    int? level,
    int? sequence,
    String? status,
    String? text,
    String? version,
    String? workflowSource,
    List<ChildChild>? children,
  }) =>
      ChildChild(
        altText: altText ?? this.altText,
        functionType: functionType ?? this.functionType,
        hasCheckmark: hasCheckmark ?? this.hasCheckmark,
        id: id ?? this.id,
        isApproval: isApproval ?? this.isApproval,
        isParallel: isParallel ?? this.isParallel,
        isRejection: isRejection ?? this.isRejection,
        level: level ?? this.level,
        sequence: sequence ?? this.sequence,
        status: status ?? this.status,
        text: text ?? this.text,
        version: version ?? this.version,
        workflowSource: workflowSource ?? this.workflowSource,
        children: children ?? this.children,
      );

  factory ChildChild.fromJson(Map<String, dynamic> json) => ChildChild(
        altText: json["altText"],
        functionType: json["function_type"],
        hasCheckmark: json["hasCheckmark"],
        id: json["id"],
        isApproval: json["isApproval"],
        isParallel: json["isParallel"],
        isRejection: json["isRejection"],
        level: json["level"],
        sequence: json["sequence"],
        status: json["status"],
        text: json["text"],
        version: json["version"],
        workflowSource: json["workflow_source"],
        children: json["children"] == null
            ? []
            : List<ChildChild>.from(
                json["children"]!.map((x) => ChildChild.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "altText": altText,
        "function_type": functionType,
        "hasCheckmark": hasCheckmark,
        "id": id,
        "isApproval": isApproval,
        "isParallel": isParallel,
        "isRejection": isRejection,
        "level": level,
        "sequence": sequence,
        "status": status,
        "text": text,
        "version": version,
        "workflow_source": workflowSource,
        "children": children == null
            ? []
            : List<dynamic>.from(children!.map((x) => x.toJson())),
      };
}

class TreeSection {
  final String? abbreviation;
  final String? id;
  final List<SectionItemClass>? items;
  final String? title;

  TreeSection({
    this.abbreviation,
    this.id,
    this.items,
    this.title,
  });

  TreeSection copyWith({
    String? abbreviation,
    String? id,
    List<SectionItemClass>? items,
    String? title,
  }) =>
      TreeSection(
        abbreviation: abbreviation ?? this.abbreviation,
        id: id ?? this.id,
        items: items ?? this.items,
        title: title ?? this.title,
      );

  factory TreeSection.fromJson(Map<String, dynamic> json) => TreeSection(
        abbreviation: json["abbreviation"],
        id: json["id"],
        items: json["items"] == null
            ? []
            : List<SectionItemClass>.from(
                json["items"]!.map((x) => SectionItemClass.fromJson(x))),
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "abbreviation": abbreviation,
        "id": id,
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "title": title,
      };
}

class SectionItemClass {
  final HoverData? hoverData;
  final String? title;
  final List<FluffyItem>? items;

  SectionItemClass({
    this.hoverData,
    this.title,
    this.items,
  });

  SectionItemClass copyWith({
    HoverData? hoverData,
    String? title,
    List<FluffyItem>? items,
  }) =>
      SectionItemClass(
        hoverData: hoverData ?? this.hoverData,
        title: title ?? this.title,
        items: items ?? this.items,
      );

  factory SectionItemClass.fromJson(Map<String, dynamic> json) =>
      SectionItemClass(
        hoverData: json["hover_data"] == null
            ? null
            : HoverData.fromJson(json["hover_data"]),
        title: json["title"],
        items: json["items"] == null
            ? []
            : List<FluffyItem>.from(
                json["items"]!.map((x) => FluffyItem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "hover_data": hoverData?.toJson(),
        "title": title,
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
      };
}

class HoverData {
  final String? createdBy;
  final String? createdDate;
  final String? modifiedBy;
  final String? modifiedDate;
  final String? version;

  HoverData({
    this.createdBy,
    this.createdDate,
    this.modifiedBy,
    this.modifiedDate,
    this.version,
  });

  HoverData copyWith({
    String? createdBy,
    String? createdDate,
    String? modifiedBy,
    String? modifiedDate,
    String? version,
  }) =>
      HoverData(
        createdBy: createdBy ?? this.createdBy,
        createdDate: createdDate ?? this.createdDate,
        modifiedBy: modifiedBy ?? this.modifiedBy,
        modifiedDate: modifiedDate ?? this.modifiedDate,
        version: version ?? this.version,
      );

  factory HoverData.fromJson(Map<String, dynamic> json) => HoverData(
        createdBy: json["createdBy"],
        createdDate: json["createdDate"],
        modifiedBy: json["modifiedBy"],
        modifiedDate: json["modifiedDate"],
        version: json["version"],
      );

  Map<String, dynamic> toJson() => {
        "createdBy": createdBy,
        "createdDate": createdDate,
        "modifiedBy": modifiedBy,
        "modifiedDate": modifiedDate,
        "version": version,
      };
}

class FluffyItem {
  final String? title;

  FluffyItem({
    this.title,
  });

  FluffyItem copyWith({
    String? title,
  }) =>
      FluffyItem(
        title: title ?? this.title,
      );

  factory FluffyItem.fromJson(Map<String, dynamic> json) => FluffyItem(
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
      };
}

class AgentMapping {
  final String? agentId;
  final String? agentName;
  final String? agentType;
  final String? requiredFor;
  final int? sequenceOrder;

  AgentMapping({
    this.agentId,
    this.agentName,
    this.agentType,
    this.requiredFor,
    this.sequenceOrder,
  });

  AgentMapping copyWith({
    String? agentId,
    String? agentName,
    String? agentType,
    String? requiredFor,
    int? sequenceOrder,
  }) =>
      AgentMapping(
        agentId: agentId ?? this.agentId,
        agentName: agentName ?? this.agentName,
        agentType: agentType ?? this.agentType,
        requiredFor: requiredFor ?? this.requiredFor,
        sequenceOrder: sequenceOrder ?? this.sequenceOrder,
      );

  factory AgentMapping.fromJson(Map<String, dynamic> json) => AgentMapping(
        agentId: json["agent_id"],
        agentName: json["agent_name"],
        agentType: json["agent_type"],
        requiredFor: json["required_for"],
        sequenceOrder: json["sequence_order"],
      );

  Map<String, dynamic> toJson() => {
        "agent_id": agentId,
        "agent_name": agentName,
        "agent_type": agentType,
        "required_for": requiredFor,
        "sequence_order": sequenceOrder,
      };
}

class Classification {
  final String? intent;
  final String? domain;
  final String? complexity;
  final String? priority;
  final double? confidence;
  final String? explanation;
  final DateTime? timestamp;
  final bool? needsClarification;
  final List<dynamic>? clarificationQuestions;
  final String? originalText;

  Classification({
    this.intent,
    this.domain,
    this.complexity,
    this.priority,
    this.confidence,
    this.explanation,
    this.timestamp,
    this.needsClarification,
    this.clarificationQuestions,
    this.originalText,
  });

  Classification copyWith({
    String? intent,
    String? domain,
    String? complexity,
    String? priority,
    double? confidence,
    String? explanation,
    DateTime? timestamp,
    bool? needsClarification,
    List<dynamic>? clarificationQuestions,
    String? originalText,
  }) =>
      Classification(
        intent: intent ?? this.intent,
        domain: domain ?? this.domain,
        complexity: complexity ?? this.complexity,
        priority: priority ?? this.priority,
        confidence: confidence ?? this.confidence,
        explanation: explanation ?? this.explanation,
        timestamp: timestamp ?? this.timestamp,
        needsClarification: needsClarification ?? this.needsClarification,
        clarificationQuestions:
            clarificationQuestions ?? this.clarificationQuestions,
        originalText: originalText ?? this.originalText,
      );

  factory Classification.fromJson(Map<String, dynamic> json) => Classification(
        intent: json["intent"],
        domain: json["domain"],
        complexity: json["complexity"],
        priority: json["priority"],
        confidence: json["confidence"]?.toDouble(),
        explanation: json["explanation"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        needsClarification: json["needs_clarification"],
        clarificationQuestions: json["clarification_questions"] == null
            ? []
            : List<dynamic>.from(
                json["clarification_questions"]!.map((x) => x)),
        originalText: json["original_text"],
      );

  Map<String, dynamic> toJson() => {
        "intent": intent,
        "domain": domain,
        "complexity": complexity,
        "priority": priority,
        "confidence": confidence,
        "explanation": explanation,
        "timestamp": timestamp?.toIso8601String(),
        "needs_clarification": needsClarification,
        "clarification_questions": clarificationQuestions == null
            ? []
            : List<dynamic>.from(clarificationQuestions!.map((x) => x)),
        "original_text": originalText,
      };
}

class ConversationArtifact {
  final String? sessionId;
  final List<Message>? messages;
  final DateTime? timestamp;

  ConversationArtifact({
    this.sessionId,
    this.messages,
    this.timestamp,
  });

  ConversationArtifact copyWith({
    String? sessionId,
    List<Message>? messages,
    DateTime? timestamp,
  }) =>
      ConversationArtifact(
        sessionId: sessionId ?? this.sessionId,
        messages: messages ?? this.messages,
        timestamp: timestamp ?? this.timestamp,
      );

  factory ConversationArtifact.fromJson(Map<String, dynamic> json) =>
      ConversationArtifact(
        sessionId: json["session_id"],
        messages: json["messages"] == null
            ? []
            : List<Message>.from(
                json["messages"]!.map((x) => Message.fromJson(x))),
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
      );

  Map<String, dynamic> toJson() => {
        "session_id": sessionId,
        "messages": messages == null
            ? []
            : List<dynamic>.from(messages!.map((x) => x.toJson())),
        "timestamp": timestamp?.toIso8601String(),
      };
}

class Message {
  final String? role;
  final String? content;
  final DateTime? timestamp;

  Message({
    this.role,
    this.content,
    this.timestamp,
  });

  Message copyWith({
    String? role,
    String? content,
    DateTime? timestamp,
  }) =>
      Message(
        role: role ?? this.role,
        content: content ?? this.content,
        timestamp: timestamp ?? this.timestamp,
      );

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        role: json["role"],
        content: json["content"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
      );

  Map<String, dynamic> toJson() => {
        "role": role,
        "content": content,
        "timestamp": timestamp?.toIso8601String(),
      };
}

class SummaryArtifact {
  final Metadata? metadata;
  final Classification? classification;
  final List<AgentMapping>? agentMappings;
  final SummaryArtifactSummary? summary;

  SummaryArtifact({
    this.metadata,
    this.classification,
    this.agentMappings,
    this.summary,
  });

  SummaryArtifact copyWith({
    Metadata? metadata,
    Classification? classification,
    List<AgentMapping>? agentMappings,
    SummaryArtifactSummary? summary,
  }) =>
      SummaryArtifact(
        metadata: metadata ?? this.metadata,
        classification: classification ?? this.classification,
        agentMappings: agentMappings ?? this.agentMappings,
        summary: summary ?? this.summary,
      );

  factory SummaryArtifact.fromJson(Map<String, dynamic> json) =>
      SummaryArtifact(
        metadata: json["metadata"] == null
            ? null
            : Metadata.fromJson(json["metadata"]),
        classification: json["classification"] == null
            ? null
            : Classification.fromJson(json["classification"]),
        agentMappings: json["agent_mappings"] == null
            ? []
            : List<AgentMapping>.from(
                json["agent_mappings"]!.map((x) => AgentMapping.fromJson(x))),
        summary: json["summary"] == null
            ? null
            : SummaryArtifactSummary.fromJson(json["summary"]),
      );

  Map<String, dynamic> toJson() => {
        "metadata": metadata?.toJson(),
        "classification": classification?.toJson(),
        "agent_mappings": agentMappings == null
            ? []
            : List<dynamic>.from(agentMappings!.map((x) => x.toJson())),
        "summary": summary?.toJson(),
      };
}

class Metadata {
  final String? sessionId;
  final DateTime? timestamp;
  final String? status;

  Metadata({
    this.sessionId,
    this.timestamp,
    this.status,
  });

  Metadata copyWith({
    String? sessionId,
    DateTime? timestamp,
    String? status,
  }) =>
      Metadata(
        sessionId: sessionId ?? this.sessionId,
        timestamp: timestamp ?? this.timestamp,
        status: status ?? this.status,
      );

  factory Metadata.fromJson(Map<String, dynamic> json) => Metadata(
        sessionId: json["session_id"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "session_id": sessionId,
        "timestamp": timestamp?.toIso8601String(),
        "status": status,
      };
}

class SummaryArtifactSummary {
  final String? userRequest;
  final String? domain;
  final String? intent;
  final String? complexity;
  final int? totalAgents;
  final int? successfulAgents;
  final int? failedAgents;

  SummaryArtifactSummary({
    this.userRequest,
    this.domain,
    this.intent,
    this.complexity,
    this.totalAgents,
    this.successfulAgents,
    this.failedAgents,
  });

  SummaryArtifactSummary copyWith({
    String? userRequest,
    String? domain,
    String? intent,
    String? complexity,
    int? totalAgents,
    int? successfulAgents,
    int? failedAgents,
  }) =>
      SummaryArtifactSummary(
        userRequest: userRequest ?? this.userRequest,
        domain: domain ?? this.domain,
        intent: intent ?? this.intent,
        complexity: complexity ?? this.complexity,
        totalAgents: totalAgents ?? this.totalAgents,
        successfulAgents: successfulAgents ?? this.successfulAgents,
        failedAgents: failedAgents ?? this.failedAgents,
      );

  factory SummaryArtifactSummary.fromJson(Map<String, dynamic> json) =>
      SummaryArtifactSummary(
        userRequest: json["user_request"],
        domain: json["domain"],
        intent: json["intent"],
        complexity: json["complexity"],
        totalAgents: json["total_agents"],
        successfulAgents: json["successful_agents"],
        failedAgents: json["failed_agents"],
      );

  Map<String, dynamic> toJson() => {
        "user_request": userRequest,
        "domain": domain,
        "intent": intent,
        "complexity": complexity,
        "total_agents": totalAgents,
        "successful_agents": successfulAgents,
        "failed_agents": failedAgents,
      };
}

class WorkflowResult {
  final List<Agent>? agentResults;
  final String? input;
  final String? sessionId;
  final String? status;

  WorkflowResult({
    this.agentResults,
    this.input,
    this.sessionId,
    this.status,
  });

  WorkflowResult copyWith({
    List<Agent>? agentResults,
    String? input,
    String? sessionId,
    String? status,
  }) =>
      WorkflowResult(
        agentResults: agentResults ?? this.agentResults,
        input: input ?? this.input,
        sessionId: sessionId ?? this.sessionId,
        status: status ?? this.status,
      );

  factory WorkflowResult.fromJson(Map<String, dynamic> json) => WorkflowResult(
        agentResults: json["agent_results"] == null
            ? []
            : List<Agent>.from(
                json["agent_results"]!.map((x) => Agent.fromJson(x))),
        input: json["input"],
        sessionId: json["session_id"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "agent_results": agentResults == null
            ? []
            : List<dynamic>.from(agentResults!.map((x) => x.toJson())),
        "input": input,
        "session_id": sessionId,
        "status": status,
      };
}
