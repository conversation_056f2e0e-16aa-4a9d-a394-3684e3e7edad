import '../models/base_model.dart';
import '../services/base_api_service.dart';
import '../utils/logger.dart';

/// A base repository class that provides common functionality for all repositories
abstract class BaseRepository<T extends BaseModel> {
  final BaseApiService _apiService;
  
  BaseRepository(this._apiService);
  
  /// Get the API service
  BaseApiService get apiService => _apiService;
  
  /// Get all items
  Future<List<T>> getAll();
  
  /// Get an item by ID
  Future<T?> getById(String id);
  
  /// Create an item
  Future<T?> create(T item);
  
  /// Update an item
  Future<T?> update(T item);
  
  /// Delete an item
  Future<bool> delete(String id);
  
  /// Convert a JSON map to a model
  T fromJson(Map<String, dynamic> json);
  
  /// Convert a list of JSON maps to a list of models
  List<T> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => fromJson(json)).toList();
  }
  
  /// Log an error
  void logError(String method, dynamic error) {
    Logger.error('Error in ${runtimeType.toString()}.$method: $error');
  }
}
