import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart';

// Custom intents for keyboard shortcuts
class BoldTextIntent extends Intent {
  const BoldTextIntent();
}

class ItalicTextIntent extends Intent {
  const ItalicTextIntent();
}

class UnderlineTextIntent extends Intent {
  const UnderlineTextIntent();
}

class SearchIntent extends Intent {
  const SearchIntent();
}

/// A rich text editor component using Flutter Quill with advanced features
///
/// This implementation includes:
/// - Custom toolbar with all formatting options
/// - Keyboard shortcuts for common formatting actions
/// - Search functionality
/// - Custom embeds support
/// - Optimized for performance
class AppQuillEditor extends StatefulWidget {
  /// The initial text content for the editor
  final String? initialText;

  /// Whether the editor is read-only
  final bool readOnly;

  /// The minimum height of the editor
  final double minHeight;

  /// The maximum height of the editor
  final double? maxHeight;

  /// Callback when the text content changes
  final Function(String)? onTextChanged;

  /// Whether to show the toolbar
  final bool showToolbar;

  /// The placeholder text when the editor is empty
  final String? placeholder;

  const AppQuillEditor({
    super.key,
    this.initialText,
    this.readOnly = false,
    this.minHeight = 150,
    this.maxHeight,
    this.onTextChanged,
    this.showToolbar = true,
    this.placeholder,
  });

  @override
  State<AppQuillEditor> createState() => _AppQuillEditorState();
}

class _AppQuillEditorState extends State<AppQuillEditor> {
  late QuillController _controller;
  final FocusNode _focusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _showSearch = false;
  String _searchTerm = '';
  int _currentSearchMatch = 0;
  List<int> _searchMatches = [];

  // Typing state tracking
  bool _isTyping = false;

  // Emoji picker state
  bool _showEmojiPicker = false;

  @override
  void initState() {
    super.initState();

    // Create a default document
    Document document = Document();

    // Add initial text if provided
    if (widget.initialText != null && widget.initialText!.isNotEmpty) {
      document = Document.fromJson([
        {"insert": "${widget.initialText!}\n"}
      ]);
    }

    // Create the controller with configuration
    _controller = QuillController(
      document: document,
      selection: const TextSelection.collapsed(offset: 0),
    );

    // Listen for document changes, but don't update UI during typing
    _controller.document.changes.listen((event) {
      // Track typing state - set to true when document changes
      _isTyping = true;

      // Reset typing state after a delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _isTyping = false;
        }
      });

      // Only notify parent of changes, don't trigger UI updates
      if (widget.onTextChanged != null) {
        widget.onTextChanged!(_controller.document.toPlainText());
      }

      // Only update search if search is active and not during typing
      if (_showSearch && _searchTerm.isNotEmpty && !_isTyping) {
        _updateSearchResults();
      }
    });

    // Don't add a continuous listener to the controller
    // Instead, we'll update the toolbar state only when focus changes
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        // Update toolbar state when focus is lost
        setState(() {});
      }
    });

    // Add listener to search controller
    _searchController.addListener(() {
      setState(() {
        _searchTerm = _searchController.text;
        _updateSearchResults();
      });
    });
  }

  // No longer needed - typing state is tracked in the document changes listener

  // Update search results
  void _updateSearchResults() {
    if (_searchTerm.isEmpty) {
      setState(() {
        _searchMatches = [];
        _currentSearchMatch = 0;
      });
      return;
    }

    final text = _controller.document.toPlainText();
    final matches = <int>[];

    int index = 0;
    while (true) {
      index = text.indexOf(_searchTerm, index);
      if (index == -1) break;
      matches.add(index);
      index += _searchTerm.length;
    }

    setState(() {
      _searchMatches = matches;
      _currentSearchMatch = matches.isNotEmpty ? 1 : 0;
    });

    // Scroll to the first match
    if (matches.isNotEmpty) {
      _scrollToCurrentMatch();
    }
  }

  // Scroll to the current match
  void _scrollToCurrentMatch() {
    if (_searchMatches.isEmpty || _currentSearchMatch <= 0) return;

    final index = _searchMatches[_currentSearchMatch - 1];
    _controller.updateSelection(
      TextSelection(
        baseOffset: index,
        extentOffset: index + _searchTerm.length,
      ),
      ChangeSource.local,
    );
  }

  // Navigate to the next search match
  void _nextSearchMatch() {
    if (_searchMatches.isEmpty) return;

    setState(() {
      _currentSearchMatch = (_currentSearchMatch % _searchMatches.length) + 1;
    });

    _scrollToCurrentMatch();
  }

  // Navigate to the previous search match
  void _previousSearchMatch() {
    if (_searchMatches.isEmpty) return;

    setState(() {
      _currentSearchMatch = _currentSearchMatch > 1
          ? _currentSearchMatch - 1
          : _searchMatches.length;
    });

    _scrollToCurrentMatch();
  }

  // Toggle search visibility
  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      // Hide emoji picker when showing search
      if (_showSearch) {
        _showEmojiPicker = false;
        _searchFocusNode.requestFocus();
      } else {
        _searchTerm = '';
        _searchController.clear();
        _searchMatches = [];
      }
    });
  }

  // Toggle emoji picker visibility
  void _toggleEmojiPicker() {
    setState(() {
      _showEmojiPicker = !_showEmojiPicker;
      // Hide search when showing emoji picker
      if (_showEmojiPicker) {
        _showSearch = false;
      }
    });
  }

  // Insert emoji at current cursor position
  void _insertEmoji(String emoji) {
    if (_controller.selection.extentOffset >= 0) {
      _controller.document.insert(_controller.selection.extentOffset, emoji);
      // Hide emoji picker after selection
      setState(() {
        _showEmojiPicker = false;
      });
    }
  }

  // Build a button for each emoji
  Widget _buildEmojiButton(String emoji) {
    return InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: () => _insertEmoji(emoji),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 24),
          ),
        ),
      ),
    );
  }

  // Build the list of emoji buttons using map
  List<Widget> _buildEmojiList() {
    // Define emoji categories
    final Map<String, List<String>> emojiCategories = {
      'Smileys': [
        '😊',
        '😂',
        '🤣',
        '😍',
        '😒',
        '😘',
        '🥰',
        '😎',
        '🤔',
        '😢',
        '😭',
        '😤',
        '😠',
        '🥳',
        '🤩',
        '😴'
      ],
      'Gestures': ['👍', '👎', '👌', '✌️', '🤞', '👏', '🙏', '🤝'],
      'Hearts': ['❤️', '💙', '💚', '💛', '💜', '🖤', '💔'],
      'Animals': ['🐶', '🐱', '🐭', '🐰', '🦊', '🐻'],
      'Food': ['🍎', '🍕', '🍔', '🍦', '🍩', '🍺', '☕'],
      'Travel & Places': ['🏠', '🚗', '✈️', '🏖️', '⛰️', '🌇'],
      'Activities': ['⚽', '🏀', '🎮', '🎬', '🎵', '🎨'],
      'Objects': ['💻', '📱', '⌚', '💡', '🔑', '🎁'],
      'Symbols': ['✅', '❌', '⭐', '⚠️', '♻️', '🔄'],
    };

    // Create a list to hold all widgets
    List<Widget> emojiWidgets = [];

    // Add each category with a header
    emojiCategories.forEach((category, emojis) {
      // Add category header
      emojiWidgets.add(
        Padding(
          padding: const EdgeInsets.only(top: 8.0, bottom: 4.0, left: 4.0),
          child: Text(
            category,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ),
      );

      // Add emojis for this category
      emojiWidgets.addAll(
        emojis.map((emoji) => _buildEmojiButton(emoji)).toList(),
      );
    });

    return emojiWidgets;
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.showToolbar && !widget.readOnly)
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Column(
              children: [
                // Main toolbar
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Wrap(
                    spacing: 1.0,
                    runSpacing: 1.0,
                    children: [
                      // History buttons
                      QuillToolbarHistoryButton(
                        isUndo: true,
                        controller: _controller,
                      ),
                      QuillToolbarHistoryButton(
                        isUndo: false,
                        controller: _controller,
                      ),

                      // Text formatting
                      QuillToolbarToggleStyleButton(
                        options: const QuillToolbarToggleStyleButtonOptions(),
                        controller: _controller,
                        attribute: Attribute.bold,
                      ),
                      QuillToolbarToggleStyleButton(
                        options: const QuillToolbarToggleStyleButtonOptions(),
                        controller: _controller,
                        attribute: Attribute.italic,
                      ),
                      QuillToolbarToggleStyleButton(
                        controller: _controller,
                        attribute: Attribute.underline,
                      ),
                      QuillToolbarToggleStyleButton(
                        controller: _controller,
                        attribute: Attribute.strikeThrough,
                      ),

                      const VerticalDivider(),

                      // Color buttons
                      QuillToolbarColorButton(
                        controller: _controller,
                        isBackground: false,
                      ),
                      QuillToolbarColorButton(
                        controller: _controller,
                        isBackground: true,
                      ),

                      // Clear format
                      QuillToolbarClearFormatButton(
                        controller: _controller,
                      ),

                      const VerticalDivider(),

                      // Paragraph formatting
                      QuillToolbarSelectAlignmentButton(
                        controller: _controller,
                      ),

                      const VerticalDivider(),

                      // Lists
                      QuillToolbarToggleStyleButton(
                        controller: _controller,
                        attribute: Attribute.ol,
                      ),
                      QuillToolbarToggleStyleButton(
                        controller: _controller,
                        attribute: Attribute.ul,
                      ),
                      QuillToolbarToggleCheckListButton(
                        controller: _controller,
                      ),

                      const VerticalDivider(),

                      // Indentation
                      QuillToolbarIndentButton(
                        controller: _controller,
                        isIncrease: true,
                      ),
                      QuillToolbarIndentButton(
                        controller: _controller,
                        isIncrease: false,
                      ),

                      const VerticalDivider(),

                      // Headers
                      QuillToolbarSelectHeaderStyleDropdownButton(
                        controller: _controller,
                      ),

                      const VerticalDivider(),

                      // Block formatting
                      QuillToolbarToggleStyleButton(
                        controller: _controller,
                        attribute: Attribute.blockQuote,
                      ),
                      QuillToolbarToggleStyleButton(
                        controller: _controller,
                        attribute: Attribute.codeBlock,
                      ),

                      const VerticalDivider(),

                      // Link
                      QuillToolbarLinkStyleButton(
                        controller: _controller,
                      ),

                      // Custom buttons
                      const VerticalDivider(),

                      // Emoji button
                      IconButton(
                        icon: const Icon(Icons.emoji_emotions),
                        tooltip: 'Insert Emoji',
                        color: _showEmojiPicker
                            ? Colors.blue
                            : Colors.grey.shade700,
                        onPressed: _toggleEmojiPicker,
                      ),

                      // Table button
                      IconButton(
                        icon: const Icon(Icons.table_chart),
                        tooltip: 'Insert Table',
                        onPressed: () {
                          // Insert a simple table
                          final index = _controller.selection.extentOffset;
                          _controller.document.insert(index, '\n');
                          _controller.document.insert(index + 1,
                              '| Column 1 | Column 2 | Column 3 |\n');
                          _controller.document.insert(index + 2,
                              '| -------- | -------- | -------- |\n');
                          _controller.document.insert(index + 3,
                              '| Cell 1   | Cell 2   | Cell 3   |\n');
                        },
                      ),

                      // Search button
                      IconButton(
                        icon: const Icon(Icons.search),
                        tooltip: 'Search',
                        color: _showSearch ? Colors.blue : Colors.grey.shade700,
                        onPressed: _toggleSearch,
                      ),
                    ],
                  ),
                ),

                // Search bar
                if (_showSearch)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            focusNode: _searchFocusNode,
                            decoration: InputDecoration(
                              hintText: 'Search...',
                              isDense: true,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                                vertical: 8.0,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              suffixText: _searchMatches.isNotEmpty
                                  ? '$_currentSearchMatch/${_searchMatches.length}'
                                  : null,
                            ),
                            onSubmitted: (_) => _nextSearchMatch(),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.arrow_upward),
                          onPressed: _previousSearchMatch,
                          tooltip: 'Previous match',
                        ),
                        IconButton(
                          icon: const Icon(Icons.arrow_downward),
                          onPressed: _nextSearchMatch,
                          tooltip: 'Next match',
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: _toggleSearch,
                          tooltip: 'Close search',
                        ),
                      ],
                    ),
                  ),

                // Emoji picker
                if (_showEmojiPicker)
                  Container(
                    height: 200,
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Emojis',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: _toggleEmojiPicker,
                              tooltip: 'Close emoji picker',
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: _buildEmojiList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        const SizedBox(height: 8),
        Container(
          height: widget.minHeight,
          constraints: BoxConstraints(
            minHeight: widget.minHeight,
            maxHeight: widget.maxHeight ?? double.infinity,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            // Use a more advanced editor configuration
            child: Shortcuts(
              shortcuts: <LogicalKeySet, Intent>{
                LogicalKeySet(
                        LogicalKeyboardKey.control, LogicalKeyboardKey.keyB):
                    const BoldTextIntent(),
                LogicalKeySet(
                        LogicalKeyboardKey.control, LogicalKeyboardKey.keyI):
                    const ItalicTextIntent(),
                LogicalKeySet(
                        LogicalKeyboardKey.control, LogicalKeyboardKey.keyU):
                    const UnderlineTextIntent(),
                LogicalKeySet(
                        LogicalKeyboardKey.control, LogicalKeyboardKey.keyF):
                    const SearchIntent(),
              },
              child: Actions(
                actions: <Type, Action<Intent>>{
                  BoldTextIntent: CallbackAction<BoldTextIntent>(
                    onInvoke: (intent) {
                      _controller.formatSelection(Attribute.bold);
                      return null;
                    },
                  ),
                  ItalicTextIntent: CallbackAction<ItalicTextIntent>(
                    onInvoke: (intent) {
                      _controller.formatSelection(Attribute.italic);
                      return null;
                    },
                  ),
                  UnderlineTextIntent: CallbackAction<UnderlineTextIntent>(
                    onInvoke: (intent) {
                      _controller.formatSelection(Attribute.underline);
                      return null;
                    },
                  ),
                  SearchIntent: CallbackAction<SearchIntent>(
                    onInvoke: (intent) {
                      _toggleSearch();
                      return null;
                    },
                  ),
                },
                child: QuillEditor.basic(
                  controller: _controller,
                  focusNode: _focusNode,
                  config: QuillEditorConfig(
                    expands: false,
                  ),
                  // Use the most basic configuration to avoid typing interruptions
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
