import 'package:dio/dio.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';
import 'auth_service.dart';

/// A singleton Dio client service with default configuration
class DioClient {
  // Singleton instance
  static final DioClient _instance = DioClient._internal();

  // Dio instance
  late final Dio dio;

  /// Factory constructor to return the same instance
  factory DioClient() {
    return _instance;
  }

  /// Private constructor to initialize the Dio instance
  DioClient._internal() {
    Logger.info('Initializing DioClient');

    dio = Dio(
      BaseOptions(
        connectTimeout: AppConstants.connectionTimeout,
        receiveTimeout: AppConstants.receiveTimeout,
        sendTimeout: AppConstants.sendTimeout,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );

    // Add interceptors
    _addInterceptors(dio);

    Logger.info('DioClient initialized successfully');
  }

  /// Add interceptors to the Dio instance
  void _addInterceptors(Dio dioInstance) {
    // Add logging interceptor for debugging
    dioInstance.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      error: true,
      request: false,
      requestHeader: false,
      responseHeader: false,
      logPrint: (object) {
        Logger.debug('Dio: $object');
      },
    ));

    // Add auth refresh interceptor
    dioInstance.interceptors.add(InterceptorsWrapper(
      onResponse: (response, handler) async {
        // Check if response status code is 401 Unauthorized
        if (response.statusCode == 401) {
          Logger.info(
              'Received 401 Unauthorized in response, attempting to refresh token');

          try {
            // Get a new valid token
            final authService = AuthService();
            final newToken = await authService.getValidToken();

            if (newToken != null) {
              Logger.info('Token refreshed successfully, retrying request');

              // Clone the original request
              final options = response.requestOptions;

              // Update the Authorization header with the new token
              options.headers['Authorization'] = 'Bearer $newToken';

              // Create a new request with the updated token
              final newResponse = await dioInstance.fetch(options);

              // Return the new response to the original caller
              return handler.resolve(newResponse);
            } else {
              Logger.error('Failed to refresh token');
              // Continue with the original 401 response
              return handler.next(response);
            }
          } catch (refreshError) {
            Logger.error('Error refreshing token: $refreshError');
            // Continue with the original 401 response
            return handler.next(response);
          }
        }

        // For other responses, continue with the response
        return handler.next(response);
      },
      onError: (DioException e, handler) async {
        // Check if error is 401 Unauthorized
        if (e.response?.statusCode == 401) {
          Logger.info('Received 401 Unauthorized, attempting to refresh token');

          try {
            // Get a new valid token
            final authService = AuthService();
            final newToken = await authService.getValidToken();

            if (newToken != null) {
              Logger.info('Token refreshed successfully, retrying request');

              // Clone the original request
              final options = e.requestOptions;

              // Update the Authorization header with the new token
              options.headers['Authorization'] = 'Bearer $newToken';

              // Create a new request with the updated token
              final response = await dioInstance.fetch(options);

              // Return the response to the original caller
              return handler.resolve(response);
            } else {
              Logger.error('Failed to refresh token');
            }
          } catch (refreshError) {
            Logger.error('Error refreshing token: $refreshError');
          }
        }

        // For other errors or if token refresh failed, continue with the error
        Logger.error('Dio Error: ${e.message}', stackTrace: e.stackTrace);
        return handler.next(e);
      },
    ));
  }

  /// Get the Dio instance
  Dio get client => dio;

  /// Reset the Dio instance (useful for testing)
  void reset() {
    Logger.info('Resetting DioClient');
    dio.close(force: true);

    dio = Dio(
      BaseOptions(
        connectTimeout: AppConstants.connectionTimeout,
        receiveTimeout: AppConstants.receiveTimeout,
        sendTimeout: AppConstants.sendTimeout,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );

    // Re-add interceptors using the common method
    _addInterceptors(dio);

    Logger.info('DioClient reset successfully');
  }
}
