import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Provider for managing the app's language settings
class LanguageProvider extends ChangeNotifier {
  static const String _languageCodeKey = 'language_code';
  static const String _countryCodeKey = 'country_code';

  // Default locale
  Locale _locale = const Locale('en', 'US');

  // Available locales
  final List<Locale> _supportedLocales = const [
    Locale('en', 'US'), // English
    Locale('es', 'ES'), // Spanish
    Locale('fr', 'FR'), // French
    Locale('de', 'DE'), // German
    Locale('zh', 'CN'), // Chinese
    Locale('ja', 'JP'), // Japanese
    Locale('ko', 'KR'), // Korean
    Locale('ru', 'RU'), // Russian
    Locale('ar', 'SA'), // Arabic
    Locale('hi', 'IN'), // Hindi
    Locale('te', 'IN'), // Telugu
  ];

  // Getter for current locale
  Locale get locale => _locale;

  // Getter for supported locales
  List<Locale> get supportedLocales => _supportedLocales;

  // Constructor
  LanguageProvider() {
    _loadSavedLocale();
  }

  // Load saved locale from SharedPreferences
  Future<void> _loadSavedLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageCodeKey);
      final countryCode = prefs.getString(_countryCodeKey);

      if (languageCode != null && countryCode != null) {
        _locale = Locale(languageCode, countryCode);
        Logger.info('Loaded saved locale: $languageCode-$countryCode');
      } else {
        Logger.info('No saved locale found, using default: en-US');
      }

      notifyListeners();
    } catch (e) {
      Logger.error('Error loading saved locale: $e');
    }
  }

  // Set locale and save to SharedPreferences
  Future<void> setLocale(Locale locale) async {
    if (!_isSupportedLocale(locale)) {
      Logger.error(
          'Unsupported locale: ${locale.languageCode}-${locale.countryCode}');
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageCodeKey, locale.languageCode);
      await prefs.setString(_countryCodeKey, locale.countryCode ?? '');

      _locale = locale;
      Logger.info(
          'Set locale to: ${locale.languageCode}-${locale.countryCode}');

      notifyListeners();
    } catch (e) {
      Logger.error('Error saving locale: $e');
    }
  }

  // Check if locale is supported
  bool _isSupportedLocale(Locale locale) {
    return _supportedLocales.any((supportedLocale) =>
        supportedLocale.languageCode == locale.languageCode &&
        supportedLocale.countryCode == locale.countryCode);
  }

  // Get language name from locale
  String getLanguageName(Locale locale) {
    switch ('${locale.languageCode}-${locale.countryCode}') {
      case 'en-US':
        return 'English';
      case 'es-ES':
        return 'Español'; // Spanish in Spanish
      case 'fr-FR':
        return 'Français'; // French in French
      case 'de-DE':
        return 'Deutsch'; // German in German
      case 'zh-CN':
        return '中文'; // Chinese in Chinese
      case 'ja-JP':
        return '日本語'; // Japanese in Japanese
      case 'ko-KR':
        return '한국어'; // Korean in Korean
      case 'ru-RU':
        return 'Русский'; // Russian in Russian
      case 'ar-SA':
        return 'العربية'; // Arabic in Arabic
      case 'hi-IN':
        return 'हिन्दी'; // Hindi in Devanagari script
      case 'te-IN':
        return 'తెలుగు'; // Telugu in Telugu script
      default:
        return 'Unknown';
    }
  }
}
