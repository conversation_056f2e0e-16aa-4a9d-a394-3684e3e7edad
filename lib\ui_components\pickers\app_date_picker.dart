import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable date picker component that follows the app's design system
class AppDatePicker extends StatelessWidget {
  /// The selected date
  final DateTime? selectedDate;

  /// The first date that can be selected
  final DateTime firstDate;

  /// The last date that can be selected
  final DateTime lastDate;

  /// The initial date to display
  final DateTime? initialDate;

  /// Callback when a date is selected
  final Function(DateTime) onDateSelected;

  /// The label of the date picker
  final String? label;

  /// The placeholder text when no date is selected
  final String placeholder;

  /// The format of the displayed date
  final String Function(DateTime)? dateFormat;

  /// The icon to display
  final IconData icon;

  /// The color of the icon
  final Color? iconColor;

  /// The background color of the date picker
  final Color? backgroundColor;

  /// The border radius of the date picker
  final BorderRadius? borderRadius;

  /// The elevation of the date picker
  final double elevation;

  /// The border of the date picker
  final Border? border;

  /// The error text to display
  final String? errorText;

  /// The helper text to display
  final String? helperText;

  /// Whether the date picker is enabled
  final bool enabled;

  const AppDatePicker({
    super.key,
    this.selectedDate,
    required this.firstDate,
    required this.lastDate,
    this.initialDate,
    required this.onDateSelected,
    this.label,
    this.placeholder = 'Select date',
    this.dateFormat,
    this.icon = Icons.calendar_today,
    this.iconColor,
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 0,
    this.border,
    this.errorText,
    this.helperText,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
              color: errorText != null
                  ? AppTheme.errorColor
                  : AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXs),
        ],
        InkWell(
          onTap: enabled ? () => _showDatePicker(context) : null,
          borderRadius:
              borderRadius ?? BorderRadius.circular(AppTheme.borderRadiusM),
          child: Container(
            decoration: BoxDecoration(
              color: backgroundColor ?? AppTheme.surfaceColor,
              borderRadius:
                  borderRadius ?? BorderRadius.circular(AppTheme.borderRadiusM),
              border: border ??
                  (errorText != null
                      ? Border.all(color: AppTheme.errorColor)
                      : null),
              boxShadow: elevation > 0
                  ? [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: elevation * 2,
                        offset: Offset(0, elevation),
                      ),
                    ]
                  : null,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingM,
              vertical: AppTheme.spacingM,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    selectedDate != null
                        ? _formatDate(selectedDate!)
                        : placeholder,
                    style: AppTheme.bodyMedium.copyWith(
                      color: selectedDate != null
                          ? AppTheme.textPrimaryColor
                          : AppTheme.textSecondaryColor.withAlpha(128),
                    ),
                  ),
                ),
                Icon(
                  icon,
                  color: iconColor ?? AppTheme.textSecondaryColor,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (errorText != null) ...[
          const SizedBox(height: AppTheme.spacingXs),
          Text(
            errorText!,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.errorColor,
            ),
          ),
        ] else if (helperText != null) ...[
          const SizedBox(height: AppTheme.spacingXs),
          Text(
            helperText!,
            style: AppTheme.bodySmall,
          ),
        ],
      ],
    );
  }

  Future<void> _showDatePicker(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? selectedDate ?? DateTime.now(),
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: AppTheme.textLightColor,
              onSurface: AppTheme.textPrimaryColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  String _formatDate(DateTime date) {
    if (dateFormat != null) {
      return dateFormat!(date);
    }

    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

/// A customizable date and time picker component
class AppDateTimePicker extends StatefulWidget {
  /// The selected date and time
  final DateTime? selectedDateTime;

  /// The first date that can be selected
  final DateTime firstDate;

  /// The last date that can be selected
  final DateTime lastDate;

  /// The initial date and time to display
  final DateTime? initialDateTime;

  /// Callback when a date and time is selected
  final Function(DateTime) onDateTimeSelected;

  /// The label of the date and time picker
  final String? label;

  /// The placeholder text when no date and time is selected
  final String placeholder;

  /// The format of the displayed date and time
  final String Function(DateTime)? dateTimeFormat;

  /// The icon to display
  final IconData icon;

  /// The color of the icon
  final Color? iconColor;

  /// The background color of the date and time picker
  final Color? backgroundColor;

  /// The border radius of the date and time picker
  final BorderRadius? borderRadius;

  /// The elevation of the date and time picker
  final double elevation;

  /// The border of the date and time picker
  final Border? border;

  /// The error text to display
  final String? errorText;

  /// The helper text to display
  final String? helperText;

  /// Whether the date and time picker is enabled
  final bool enabled;

  /// Whether to use 24-hour format for time
  final bool use24HourFormat;

  const AppDateTimePicker({
    super.key,
    this.selectedDateTime,
    required this.firstDate,
    required this.lastDate,
    this.initialDateTime,
    required this.onDateTimeSelected,
    this.label,
    this.placeholder = 'Select date and time',
    this.dateTimeFormat,
    this.icon = Icons.event,
    this.iconColor,
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 0,
    this.border,
    this.errorText,
    this.helperText,
    this.enabled = true,
    this.use24HourFormat = false,
  });

  @override
  State<AppDateTimePicker> createState() => _AppDateTimePickerState();
}

class _AppDateTimePickerState extends State<AppDateTimePicker> {
  // Access widget properties through widget.property
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
              color: widget.errorText != null
                  ? AppTheme.errorColor
                  : AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXs),
        ],
        InkWell(
          onTap: widget.enabled ? () => _showDateTimePicker(context) : null,
          borderRadius: widget.borderRadius ??
              BorderRadius.circular(AppTheme.borderRadiusM),
          child: Container(
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? AppTheme.surfaceColor,
              borderRadius: widget.borderRadius ??
                  BorderRadius.circular(AppTheme.borderRadiusM),
              border: widget.border ??
                  (widget.errorText != null
                      ? Border.all(color: AppTheme.errorColor)
                      : null),
              boxShadow: widget.elevation > 0
                  ? [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: widget.elevation * 2,
                        offset: Offset(0, widget.elevation),
                      ),
                    ]
                  : null,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingM,
              vertical: AppTheme.spacingM,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.selectedDateTime != null
                        ? _formatDateTime(widget.selectedDateTime!)
                        : widget.placeholder,
                    style: AppTheme.bodyMedium.copyWith(
                      color: widget.selectedDateTime != null
                          ? AppTheme.textPrimaryColor
                          : AppTheme.textSecondaryColor.withAlpha(128),
                    ),
                  ),
                ),
                Icon(
                  widget.icon,
                  color: widget.iconColor ?? AppTheme.textSecondaryColor,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (widget.errorText != null) ...[
          const SizedBox(height: AppTheme.spacingXs),
          Text(
            widget.errorText!,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.errorColor,
            ),
          ),
        ] else if (widget.helperText != null) ...[
          const SizedBox(height: AppTheme.spacingXs),
          Text(
            widget.helperText!,
            style: AppTheme.bodySmall,
          ),
        ],
      ],
    );
  }

  Future<void> _showDateTimePicker(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime initialDate =
        widget.initialDateTime ?? widget.selectedDateTime ?? now;

    // Show date picker
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: widget.firstDate,
      lastDate: widget.lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: AppTheme.textLightColor,
              onSurface: AppTheme.textPrimaryColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate == null) {
      return;
    }

    // Show time picker
    final TimeOfDay initialTime = TimeOfDay.fromDateTime(initialDate);

    // Use a separate method to avoid BuildContext across async gaps
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: AppTheme.textLightColor,
              onSurface: AppTheme.textPrimaryColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    // Check if widget is still mounted after async operation
    if (!mounted) return;

    if (pickedTime == null) {
      return;
    }

    // Combine date and time
    final DateTime pickedDateTime = DateTime(
      pickedDate.year,
      pickedDate.month,
      pickedDate.day,
      pickedTime.hour,
      pickedTime.minute,
    );

    widget.onDateTimeSelected(pickedDateTime);
  }

  String _formatDateTime(DateTime dateTime) {
    if (widget.dateTimeFormat != null) {
      return widget.dateTimeFormat!(dateTime);
    }

    final String date =
        '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    final String time = widget.use24HourFormat
        ? '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}'
        : '${dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour == 0 ? 12 : dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')} ${dateTime.hour >= 12 ? 'PM' : 'AM'}';

    return '$date $time';
  }
}
