import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/api_response.dart';
import '../models/auth/auth_response_models.dart';
import '../repositories/user_repository.dart';
import '../services/service_locator.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';
import 'base_provider.dart';

class AuthProvider extends BaseProvider {
  final UserRepository _userRepository = ServiceLocator().userRepository;

  bool _isLoggedIn = false;
  final bool _isLoading = false;
  User? _user;
  String? _token;
  // String? _error;
  bool _rememberMe = false;
  String? _savedEmail;
  String? _savedPassword;
  String? _savedMobile;

  // Specific loading states for different operations
  bool _isLoadingProfile = false;
  bool _isRefreshingToken = false;
  bool _isLoadingAuthData = false;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isAuthenticated => _isLoggedIn && _token != null;

  @override
  bool get isLoading => _isLoading;

  User? get user => _user;
  String? get token => _token;

  // @override
  // String? get error => _error;

  bool get rememberMe => _rememberMe;
  String? get savedEmail => _savedEmail;
  String? get savedPassword => _savedPassword;
  String? get savedMobile => _savedMobile;

  // Specific loading state getters
  bool get isLoadingProfile => _isLoadingProfile;
  bool get isRefreshingToken => _isRefreshingToken;
  bool get isLoadingAuthData => _isLoadingAuthData;

  // Methods to set specific loading states
  void setLoadingProfile(bool value) {
    if (_isLoadingProfile != value) {
      _isLoadingProfile = value;
      notifyListeners();
    }
  }

  void setRefreshingToken(bool value) {
    if (_isRefreshingToken != value) {
      _isRefreshingToken = value;
      notifyListeners();
    }
  }

  void setLoadingAuthData(bool value) {
    if (_isLoadingAuthData != value) {
      _isLoadingAuthData = value;
      notifyListeners();
    }
  }

  // Constructor - check for saved auth data
  AuthProvider() {
    // Load saved auth data immediately
    _checkSavedAuthData();

    // Add debug log
    Logger.info('AuthProvider initialized');
  }

  // Check if user is already logged in
  Future<void> _checkSavedAuthData() async {
    Logger.info('Starting to check saved auth data');
    setLoadingAuthData(true);

    try {
      final authDataResponse = await _userRepository.getSavedAuthData();
      Logger.info('Auth data loaded: ${authDataResponse.toString()}');

      if (authDataResponse.success && authDataResponse.data != null) {
        final authData = authDataResponse.data!;
        _isLoggedIn = authData.isLoggedIn;
        _user = authData.user;
        _token = authData.token;
        _rememberMe = authData.rememberMe;
        _savedEmail = authData.email;
        _savedPassword = authData.password;
        _savedMobile = authData.mobile;
      } else {
        // Handle error case
        _isLoggedIn = false;
        _user = null;
        _token = null;
        _rememberMe = false;
        _savedEmail = null;
        _savedPassword = null;
        _savedMobile = null;
      }

      Logger.info('Remember me: $_rememberMe');
      Logger.info('Saved email: $_savedEmail');
      Logger.info(
          'Saved password: ${_savedPassword != null ? '******' : 'null'}');

      if (_isLoggedIn) {
        Logger.info('User already logged in: ${_user?.email}');
      } else if (_savedEmail != null) {
        Logger.info('Found saved credentials for: $_savedEmail');
      } else {
        Logger.info('No saved credentials found');
      }
    } catch (e) {
      Logger.error('Error checking saved auth data: $e');
      _isLoggedIn = false;
      _user = null;
      _token = null;
      _savedEmail = null;
      _savedPassword = null;
      _savedMobile = null;
    }

    setLoadingAuthData(false);
  }

  // Login
  Future<bool> login(String email, String password) async {
    final result = await runWithLoadingAndErrorHandling<ApiResponse<AuthData>>(
      () => _userRepository.login(email, password),
      context: 'AuthProvider.login',
    );

    if (result != null && result.success && result.data != null) {
      final authData = result.data!;
      _user = authData.user;
      _token = authData.token;
      _isLoggedIn = true;
      _savedEmail = email;
      _savedPassword = password;

      // Save auth data if remember me is checked
      await _userRepository.saveAuthData(
          _user!, _token!, _rememberMe, email, password,
          mobile: _savedMobile);

      // Fetch user profile to get additional user details including user_id
      Logger.info('Login successful, fetching user profile...');
      await getUserProfile();

      Logger.info('Login successful for user: ${_user?.email}');
    } else if (result != null) {
      _isLoggedIn = false;
      final errorMessage =
          result.message ?? 'Login failed. Please check your credentials.';
      setError(errorMessage);
      Logger.error('Login failed: $errorMessage');
    } else {
      _isLoggedIn = false;
      setError('Login failed. Please try again.');
      Logger.error('Login failed: No result returned');
    }

    return _isLoggedIn;
  }

  // Logout
  Future<bool> logout() async {
    final result =
        await runWithLoadingAndErrorHandling<ApiResponse<LogoutData>>(
      () async {
        // Store current values before logout
        final currentRememberMe = _rememberMe;
        final currentEmail = _savedEmail;
        final currentPassword = _savedPassword;
        final currentMobile = _savedMobile;

        Logger.info('Logging out user. Remember Me: $currentRememberMe');
        final logoutResult = await _userRepository.logout();

        _isLoggedIn = false;
        _user = null;
        _token = null;

        // Keep credentials if remember me is checked
        if (currentRememberMe) {
          Logger.info('Preserving credentials for next login');
          _savedEmail = currentEmail;
          _savedPassword = currentPassword;
          _savedMobile = currentMobile;
        } else {
          Logger.info('Clearing all credentials');
          _savedEmail = null;
          _savedPassword = null;
          _savedMobile = null;
        }
        // Keep remember me preference

        return logoutResult;
      },
      context: 'AuthProvider.logout',
    );

    if (result != null && !result.success) {
      setError(result.message ?? 'Logout failed');
      return false;
    }

    return true;
  }

  // Set remember me
  Future<void> setRememberMe(bool value) async {
    Logger.info('Setting Remember Me to: $value');
    _rememberMe = value;

    // Save the remember me preference to SharedPreferences
    await runWithLoadingAndErrorHandling<void>(
      () async {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(AppConstants.rememberMeKey, value);
        Logger.info(
            'Remember Me preference saved to SharedPreferences: $value');

        // If remember me is unchecked, clear saved credentials
        if (!value) {
          Logger.info('Remember Me unchecked, clearing saved credentials');
          await prefs.remove(AppConstants.emailKey);
          await prefs.remove(AppConstants.passwordKey);
          await prefs.remove(AppConstants.mobileKey);
          _savedEmail = null;
          _savedPassword = null;
          _savedMobile = null;
        }
      },
      context: 'AuthProvider.setRememberMe',
      showLoading: false, // Don't show loading indicator for this operation
    );

    notifyListeners();
  }

  // Clear error
  @override
  void clearError() {
    // _error = null;
    notifyListeners();
  }

  // Clear all cached data and force fresh login
  Future<void> clearCachedData() async {
    Logger.info('Clearing all cached data');
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Store remember me preference and credentials if needed
      final currentRememberMe = _rememberMe;
      final currentEmail = _savedEmail;
      final currentPassword = _savedPassword;
      final currentMobile = _savedMobile;
      
      // Clear all data
      await prefs.clear();
      
      // Reset all state variables
      _isLoggedIn = false;
      _user = null;
      _token = null;
      
      // Restore remember me data if it was set
      if (currentRememberMe) {
        _rememberMe = true;
        _savedEmail = currentEmail;
        _savedPassword = currentPassword;
        _savedMobile = currentMobile;
        
        // Save back the remember me data
        await prefs.setBool(AppConstants.rememberMeKey, true);
        if (currentEmail != null) {
          await prefs.setString(AppConstants.emailKey, currentEmail);
        }
        if (currentPassword != null) {
          await prefs.setString(AppConstants.passwordKey, currentPassword);
        }
        if (currentMobile != null) {
          await prefs.setString(AppConstants.mobileKey, currentMobile);
        }
      } else {
        _rememberMe = false;
        _savedEmail = null;
        _savedPassword = null;
        _savedMobile = null;
      }
      
      notifyListeners();
      Logger.info('Cached data cleared successfully');
    } catch (e) {
      Logger.error('Error clearing cached data: $e');
    }
  }

  // Register
  Future<bool> register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String username,
    required String role,
    required String organization,
    String? profilePicture,
  }) async {
    final result = await runWithLoadingAndErrorHandling<ApiResponse<AuthData>>(
      () => _userRepository.register(
        name: name,
        email: email,
        mobile: mobile,
        password: password,
        username: username,
        role: role,
        organization: organization,
        profilePicture: profilePicture,
      ),
      context: 'AuthProvider.register',
    );

    if (result != null && result.success && result.data != null) {
      final authData = result.data!;
      _user = authData.user;
      _token = authData.token;
      _isLoggedIn = true;
      _savedEmail = email;
      _savedPassword = password;
      _savedMobile = mobile;

      // Save auth data (don't auto-check remember me for new registrations)
      await _userRepository.saveAuthData(
          _user!, _token!, _rememberMe, email, password,
          mobile: mobile);

      Logger.info('Registration successful for user: ${_user?.email}');
    } else if (result != null) {
      setError(result.message);
      Logger.error('Registration failed: ${result.message}');
    }

    return _isLoggedIn;
  }

  // Get user profile
  Future<bool> getUserProfile() async {
    setLoadingProfile(true);

    final result =
        await runWithLoadingAndErrorHandling<ApiResponse<UserProfileData>>(
      () => _userRepository.getUserProfile(),
      context: 'AuthProvider.getUserProfile',
      showLoading: false, // We're managing loading state manually
    );

    if (result != null && result.success && result.data != null) {
      // Update the user object with the latest data
      _user = result.data!.user;

      Logger.info('User profile fetched successfully: ${_user?.email}');
      setLoadingProfile(false);
      return true;
    } else if (result != null) {
      final errorMessage = result.message ?? 'Failed to fetch user profile';
      setError(errorMessage);
      Logger.error('Failed to fetch user profile: $errorMessage');
    } else {
      setError('Failed to fetch user profile. Please try again.');
      Logger.error('Failed to fetch user profile: No result returned');
    }

    setLoadingProfile(false);
    return false;
  }

  // Refresh token
  Future<bool> refreshToken() async {
    setRefreshingToken(true);

    try {
      final prefs = await SharedPreferences.getInstance();
      final refreshToken = prefs.getString('refresh_token');

      if (refreshToken == null) {
        Logger.error('No refresh token available');
        setError('No refresh token available');
        return false;
      }

      final result =
          await runWithLoadingAndErrorHandling<ApiResponse<AuthData>>(
        () => _userRepository.refreshToken(refreshToken),
        context: 'AuthProvider.refreshToken',
        showLoading: false, // We're managing loading state manually
      );

      if (result != null && result.success && result.data != null) {
        final authData = result.data!;
        _user = authData.user;
        _token = authData.token;

        Logger.info('Token refreshed successfully');
        setRefreshingToken(false);
        return true;
      } else if (result != null) {
        final errorMessage = result.message ?? 'Failed to refresh token';
        setError(errorMessage);
        Logger.error('Failed to refresh token: $errorMessage');
      } else {
        setError('Failed to refresh token. Please try again.');
        Logger.error('Failed to refresh token: No result returned');
      }
    } catch (e) {
      Logger.error('Error during token refresh: $e');
      setError('An error occurred during token refresh');
    }

    setRefreshingToken(false);
    return false;
  }
}
