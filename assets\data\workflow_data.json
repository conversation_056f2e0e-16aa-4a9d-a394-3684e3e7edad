{"nodes": [{"id": "1", "title": "Start Task", "type": "start", "position": {"x": 50.0, "y": 50.0}, "actionLabel": "Start", "count": 1}, {"id": "2", "title": "Marketing Manager <PERSON><PERSON><PERSON><PERSON>", "type": "approval", "position": {"x": 50.0, "y": 200.0}, "actionLabel": "Manager <PERSON><PERSON><PERSON><PERSON>", "count": 10, "lodetails": {"status": "Active", "createdBy": "<PERSON>", "createdOn": "2025-04-17", "priority": "High", "approvers": "Marketing Team", "inputs": []}}, {"id": "3", "title": "Email Task", "type": "email", "position": {"x": 20.0, "y": 480.0}, "actionLabel": "Mail", "count": 3}, {"id": "4", "title": "EndTask", "type": "end", "position": {"x": 220.0, "y": 380.0}, "actionLabel": "End", "count": 1}], "connections": [{"sourceId": "1", "targetId": "2", "label": "Next", "color": "blue", "isDashed": false}, {"sourceId": "2", "targetId": "3", "label": "If Rejected", "color": "orange", "isDashed": true}, {"sourceId": "2", "targetId": "4", "label": "Then", "color": "blue", "isDashed": false}, {"sourceId": "3", "targetId": "4", "label": "Also", "color": "green", "isDashed": false}]}