import 'package:flutter/material.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import '../theme/app_colors.dart';
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';

class FullWidthTypingIndicator extends StatelessWidget {
  final String? message;
  const FullWidthTypingIndicator({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    // Get device type for responsive spacing
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        vertical: AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
      ),
      child: Center(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal:
                AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType),
          ),
          constraints: BoxConstraints(
            maxWidth: 800, // Limit max width for very large screens
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'NSL is ',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryIndigo,
                    ),
              ),
              AnimatedTextKit(
                animatedTexts: [
                  TypewriterAnimatedText(
                    message ?? 'building a solution...',
                    speed: const Duration(milliseconds: 100),
                    textStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: AppColors.textSecondaryLight,
                        ),
                  ),
                ],
                repeatForever: true,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
}
