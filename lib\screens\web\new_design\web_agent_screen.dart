import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/screens/web/new_design/widgets/create_object_popup.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class Agent {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String versionNumber;
  // final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  Agent({
    required this.title,
    this.subtitle = '',
    required this.imageUrl,
    required this.versionNumber,
    // this.isDraft = false,
    this.imageWidth = 170.0,
    this.imageHeight = 170.0,
  });

  factory Agent.fromJson(Map<String, dynamic> json) {
    return Agent(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String? ?? '',
      imageUrl: json['imageUrl'] as String,
      versionNumber: json['versionNumber'] as String? ?? '',
      // isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 170.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 170.0,
    );
  }
}

class WebAgentScreen extends StatefulWidget {
  const WebAgentScreen({super.key});

  @override
  State<WebAgentScreen> createState() => _WebAgentScreenState();
}

class _WebAgentScreenState extends State<WebAgentScreen>
    with TickerProviderStateMixin {
  late List<Agent> allAgents;
  late List<Agent> agents;
  List<Agent> _filteredAgents = []; // Store filtered agents for display
  bool isLoading = true;
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;

  // Search state
  final TextEditingController _searchController = TextEditingController();

  // Pagination variables
  int _currentPage = 1;
  int _itemsPerPage = 16; // Will be calculated dynamically
  int _totalPages = 1;

  // JSON string containing agent data
  static const String agentsJsonString = '''
{
   "agents": [
    {
      "title": "Brane Agent-1",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "NSL Agent-2",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Admin Agent-3",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "User Agent-4",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Admin2 Agent-5",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": " User 2Agent-6",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-7",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-8",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-9",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-10",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-11",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-12",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-13",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-14",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-15",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-16",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-17",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-18",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-19",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    },
    {
      "title": "Agent-20",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadAgents();
    _searchController.addListener(_onSearchChanged);
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      agents.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      ),
    );

    _slideAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 50.0, end: 0.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();

    _fadeAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // Start animations with staggered delays (bottom to top)
    for (int i = 0; i < _animationControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _animationControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadAgents() {
    try {
      // Parse the JSON string
      final data = json.decode(agentsJsonString);

      // Convert to Agent objects
      final List<Agent> loadedAgents = (data['agents'] as List)
          .map((agentJson) => Agent.fromJson(agentJson))
          .toList();

      setState(() {
        allAgents = loadedAgents;
        _filteredAgents = loadedAgents;
        // Use default items per page, will be updated in build method
        _updateCurrentPageAgents();
        isLoading = false;
      });

      // Debug print to check if agents are loaded
      debugPrint(
          'Loaded ${allAgents.length} agents, current page agents: ${agents.length}');

      // Initialize animations after agents are loaded
      _initializeAnimations();
    } catch (e) {
      setState(() {
        allAgents = [];
        agents = [];
        _filteredAgents = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading agents: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
    }
  }

  // Handle search text changes
  void _onSearchChanged() {
    _filterAgents();
  }

  // Filter agents based on search text
  void _filterAgents() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredAgents = List.from(allAgents);
      } else {
        _filteredAgents = allAgents
            .where((agent) =>
                agent.title.toLowerCase().contains(searchText) ||
                agent.versionNumber.toLowerCase().contains(searchText))
            .toList();
      }

      // Update allAgents to use filtered results for pagination
      allAgents = _filteredAgents;
      // Reset to first page when filtering
      _currentPage = 1;
      _updateCurrentPageAgents();
    });
  }

  // Calculate items per page based on screen width and side panel state
  int _calculateItemsPerPage(double availableWidth) {
    // For 1920px and above screens, force exactly 8 agents per row
    if (availableWidth >= 1520.0) {
      // Using 1520 to account for side panel
      return 8 * 2; // 8 agents per row, 2 rows = 16 total
    }

    const double cardWidth = 170.0; // Agent cards are 170px wide
    double minSpacing = 50.0;

    // Calculate agents per row for smaller screens
    int agentsPerRow =
        ((availableWidth + minSpacing) / (cardWidth + minSpacing)).floor();
    if (agentsPerRow == 0) agentsPerRow = 1;

    // Determine number of rows
    int rows = 2;

    return agentsPerRow * rows;
  }

  void _updateCurrentPageAgents() {
    debugPrint(
        '_updateCurrentPageAgents called: allAgents.length=${allAgents.length}, _itemsPerPage=$_itemsPerPage, _currentPage=$_currentPage');

    if (allAgents.isEmpty) {
      _totalPages = 1;
      agents = [];
      debugPrint('allAgents is empty, setting agents to empty list');
      return;
    }

    _totalPages = (allAgents.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentPage > _totalPages) {
      _currentPage = _totalPages;
    }
    if (_currentPage < 1) {
      _currentPage = 1;
    }

    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = (startIndex + _itemsPerPage).clamp(0, allAgents.length);
    agents = allAgents.sublist(startIndex, endIndex);

    debugPrint(
        'Updated agents: startIndex=$startIndex, endIndex=$endIndex, agents.length=${agents.length}, _totalPages=$_totalPages');
  }

  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
        _updateCurrentPageAgents();
      });
      _initializeAnimations();
    }
  }

  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
        _updateCurrentPageAgents();
      });
      _initializeAnimations();
    }
  }

  void _showCreateAgentPopup(BuildContext context) {
    // Find the button's position
    final RenderBox? buttonBox = context.findRenderObject() as RenderBox?;
    if (buttonBox == null) return;

    final buttonPosition = buttonBox.localToGlobal(Offset.zero);
    final buttonSize = buttonBox.size;

    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return Stack(
          children: [
            // Invisible barrier to close popup when clicking outside
            Positioned.fill(
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(color: Colors.transparent),
              ),
            ),
            // Positioned popup
            Positioned(
              left: buttonPosition.dx +
                  buttonSize.width -
                  200, // Align to right edge of button
              top: buttonPosition.dy +
                  buttonSize.height +
                  8, // Below the button with 8px gap
              child: CreateObjectPopup(
                onAIGeneratedTap: () {
                  Navigator.of(context).pop();
                  // Navigate to AI Generated screen
                  Provider.of<WebHomeProvider>(context, listen: false)
                      .currentScreenIndex = ScreenConstants.aiGeneratedObject;
                },
                onManualCreationTap: () {
                  Navigator.of(context).pop();
                  // Handle manual creation - you can add your logic here
                  // For now, just show a placeholder
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Manual Creation selected')),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff6f6f6),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top navigation bar with 4 tabs including Agents
          Padding(
            padding:
                const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
            child: Column(
              children: [
                const HoverNavItems(),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Agents text
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)
                            .translate('webagent.pageTitle'),
                            style: FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                    // Search bar with filter
                    Expanded(
                      child: Container(
                        width: MediaQuery.of(context).size.width / 3.722,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Row(
                          children: [
                            // Search text field
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(left: 16.0),
                                child: TextField(
                                  controller: _searchController,
                                   cursorHeight: 16,
                                  decoration: InputDecoration(
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    hintText: 'Search Agents...',
                                    border: InputBorder.none,
                                    hintStyle: TextStyle(
                                        fontSize: FontManager.s14, color: Colors.grey[500]),
                                    isDense: true,
                                    contentPadding: EdgeInsets.zero,
                                    filled: false,
                                    fillColor: Colors.transparent,
                                  ),
                                ),
                              ),
                            ),

                            // Search icon
                            _HoverSvgButton(
                              normalIconPath: 'assets/images/search.svg',
                              hoverIconPath: 'assets/images/search.svg',
                              onPressed: () {
                                _filterAgents();
                              },
                              imageWidth: 20,
                              imageHeight: 20,
                              showBorderOnHover: false,
                            ),

                            // Divider between search and filter
                            Container(
                              height: 24,
                              width: 1,
                              color: Colors.grey.shade200,
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                            ),

                            // Filter icon
                            _HoverSvgButton(
                              normalIconPath: 'assets/images/filter-icon.svg',
                              hoverIconPath: 'assets/images/filter-hover.svg',
                              onPressed: () {},
                              imageWidth: 32,
                              imageHeight: 32,
                              showBorderOnHover: false,
                            ),
                            const SizedBox(width: 8),
                          ],
                        ),
                      ),
                    ),

                    // Create agent button
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Builder(
                            builder: (buttonContext) => HoverCreateButton(
                              text: AppLocalizations.of(context)
                                  .translate('webagent.createButtonText'),
                              onPressed: () {
                                _showCreateAgentPopup(buttonContext);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
              ],
            ),
          ),
          // Main content
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.only(
                    left: 94, right: 94, bottom: 0.0, top: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),

                    // Agent grid
                    isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : agents.isEmpty
                            ? const Center(child: Text('No agents found'))
                            : Center(
                                child: LayoutBuilder(
                                  builder: (context, constraints) {
                                    final screenWidth =
                                        MediaQuery.of(context).size.width;

                                    debugPrint(
                                        'LayoutBuilder - Screen width: $screenWidth, Constraints width: ${constraints.maxWidth}');

                                    // Calculate available width
                                    double availableWidth = constraints.maxWidth;

                                    // Update items per page based on available width
                                    int newItemsPerPage =
                                        _calculateItemsPerPage(availableWidth);
                                    if (_itemsPerPage != newItemsPerPage) {
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        setState(() {
                                          _itemsPerPage = newItemsPerPage;
                                          _updateCurrentPageAgents();
                                        });
                                      });
                                    }

                                    // Calculate agents per row using the same logic as _calculateItemsPerPage
                                    int agentsPerRow;
                                    if (availableWidth >= 1520.0) {
                                      agentsPerRow =
                                          8; // Force 8 agents per row for large screens
                                    } else {
                                      // For smaller screens, calculate based on available space
                                      const double cardWidth = 170.0;
                                      double minSpacing = 50.0;
                                      agentsPerRow =
                                          ((availableWidth + minSpacing) /
                                                  (cardWidth + minSpacing))
                                              .floor();
                                      if (agentsPerRow == 0) agentsPerRow = 1;
                                    }

                                    // Calculate rows needed
                                    int rowCount =
                                        (agents.length / agentsPerRow).ceil();

                                    return Column(
                                      children:
                                          List.generate(rowCount, (rowIndex) {
                                        // Calculate start and end indices for this row
                                        int startIndex =
                                            rowIndex * agentsPerRow;
                                        int endIndex =
                                            (startIndex + agentsPerRow <=
                                                    agents.length)
                                                ? startIndex + agentsPerRow
                                                : agents.length;

                                        // Create a list of agents for this row
                                        List<Agent> rowAgents = agents.sublist(
                                            startIndex, endIndex);

                                        // For last row with fewer items, calculate positions
                                        bool isLastRowWithFewerItems =
                                            rowAgents.length < agentsPerRow &&
                                                rowIndex == rowCount - 1;

                                        return Padding(
                                          padding: const EdgeInsets.only(
                                              bottom: 42.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                isLastRowWithFewerItems
                                                    ? MainAxisAlignment.start
                                                    : MainAxisAlignment
                                                        .spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: rowAgents
                                                .asMap()
                                                .entries
                                                .map((entry) {
                                              int idx = entry.key;
                                              Agent agent = entry.value;

                                              // Calculate spacing for consistent layout
                                              double rightPadding = 0;
                                              if (idx < rowAgents.length - 1) {
                                                // For large screens (8 agents per row), use calculated spacing
                                                if (availableWidth >= 1520.0) {
                                                  rightPadding =
                                                      (availableWidth -
                                                              (8 * 170)) /
                                                          7; // Distribute remaining space evenly
                                                } else {
                                                  // For medium screens, calculate spacing based on actual agents per row
                                                  rightPadding =
                                                      (availableWidth -
                                                              (agentsPerRow *
                                                                  170)) /
                                                          (agentsPerRow - 1);
                                                }
                                              }

                                              return Padding(
                                                padding: EdgeInsets.only(
                                                    right: rightPadding),
                                                child: _HoverAgentCard(
                                                  onTap: () {
                                                    Provider.of<WebHomeProvider>(
                                                                context,
                                                                listen: false)
                                                            .currentScreenIndex =
                                                        ScreenConstants
                                                            .webBookSolution;
                                                             Provider.of<ManualCreationProvider>(
                                                            context,
                                                            listen: false)
                                                        .handleAgentValidationForBook();

                                                  },
                                                  child: _buildAgentCard(
                                                    title: agent.title,
                                                    versionNumber:
                                                        agent.versionNumber,
                                                    subtitle: agent.subtitle,
                                                    imageUrl: agent.imageUrl,
                                                    // isDraft: agent.isDraft,
                                                    imageWidth:
                                                        agent.imageWidth,
                                                    imageHeight:
                                                        agent.imageHeight,
                                                    index:
                                                        agents.indexOf(agent),
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                        );
                                      }),
                                    );
                                  },
                                ),
                              ),
                    // Pagination controls
                    if (allAgents.isNotEmpty && _totalPages > 1)
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Page info
                            Text(
                              'Page $_currentPage of $_totalPages',
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey,
                              ),
                            ),
                            // Navigation buttons
                            Row(
                              children: [
                                // Previous button
                                _HoverPaginationButton(
                                  icon:
                                      const Icon(Icons.chevron_left, size: 20),
                                  onPressed: _currentPage > 1
                                      ? _goToPreviousPage
                                      : null,
                                ),
                                const SizedBox(width: 8),
                                // Next button
                                _HoverPaginationButton(
                                  icon:
                                      const Icon(Icons.chevron_right, size: 20),
                                  onPressed: _currentPage < _totalPages
                                      ? _goToNextPage
                                      : null,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAgentCard({
    required String title,
    String subtitle = '',
    required String versionNumber,
    required String imageUrl,
    // bool isDraft = false,
    double imageWidth = 170.0,
    double imageHeight = 170.0,
    int index = 0,
  }) {
    // Check if animations are initialized and index is valid
    if (index >= _animationControllers.length) {
      // Fallback to non-animated version if index is out of bounds
      return _buildStaticAgentCard(
        title: title,
        subtitle: subtitle,
        versionNumber: versionNumber,
        imageUrl: imageUrl,
        // isDraft: isDraft,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
      );
    }

    return AnimatedBuilder(
      animation: _animationControllers[index],
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimations[index].value),
          child: Opacity(
            opacity: _fadeAnimations[index].value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Agent circular image without background color
                AnimatedBuilder(
                  animation: _animationControllers[index],
                  builder: (context, child) {
                    double scale =
                        0.9 + (0.1 * _animationControllers[index].value);
                    return Transform.scale(
                      scale: scale,
                      child: Container(
                        width: imageWidth,
                        height: imageHeight,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          imageUrl, // Use the SVG image from JSON
                          width: imageWidth,
                          height: imageHeight,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: imageWidth,
                  child: Text(
                    title,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s14,
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Fallback static version for cases where animations aren't ready
  Widget _buildStaticAgentCard({
    required String title,
    String subtitle = '',
    required String versionNumber,
    required String imageUrl,
    // bool isDraft = false,
    double imageWidth = 170.0,
    double imageHeight = 170.0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: imageWidth,
          height: imageHeight,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: ClipOval(
            child: SvgPicture.asset(
              imageUrl, // Use the SVG image from JSON
              width: imageWidth,
              height: imageHeight,
              fit: BoxFit.cover,
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;

  const _HoverNavItem({
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<_HoverNavItem> createState() => _HoverNavItemState();
}

class _HoverNavItemState extends State<_HoverNavItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isHovered ? Colors.white : Colors.transparent,
            border: Border.all(
              color: isHovered ? Color(0xff0058FF) : Colors.transparent,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.iconPath,
                width: 12,
                height: 12,
                colorFilter: ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                widget.label,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontWeight:
                      widget.isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _HoverAgentCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _HoverAgentCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_HoverAgentCard> createState() => _HoverAgentCardState();
}

class _HoverAgentCardState extends State<_HoverAgentCard> {
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: widget.child,
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          border: Border.all(
            color: isDisabled
                ? Colors.grey.shade200
                : isHovered
                    ? Color(0xff0058FF)
                    : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered && !isDisabled ? BorderRadius.zero : null,
          color: isDisabled ? Colors.grey.shade50 : Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isDisabled
              ? Colors.grey.shade400
              : isHovered
                  ? Color(0xff0058FF)
                  : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
