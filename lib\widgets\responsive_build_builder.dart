import 'package:flutter/material.dart';
import '../screens/build_screen.dart';
import '../screens/web/web_build_screen.dart';
import 'base_responsive_builder.dart';

class ResponsiveBuildBuilder extends BaseResponsiveBuilder {
  const ResponsiveBuildBuilder({super.key}) : super(builderKey: 'build');

  @override
  BaseResponsiveBuilderState<BaseResponsiveBuilder> createState() => _ResponsiveBuildBuilderState();

  @override
  Widget buildWebLayout(BuildContext context, String? currentRoute) {
    return const WebBuildScreen();
  }

  @override
  Widget buildMobileLayout(BuildContext context, String? currentRoute) {
    return const BuildScreen();
  }
}

class _ResponsiveBuildBuilderState extends BaseResponsiveBuilderState<ResponsiveBuildBuilder> {}
