import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:nsl/theme/app_colors.dart';

/// A reusable text link for authentication screens
class AuthLink extends StatelessWidget {
  final String text;
  final String linkText;
  final VoidCallback? onPressed;
  final bool isDisabled;

  const AuthLink({
    super.key,
    required this.text,
    required this.linkText,
    required this.onPressed,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(
            text: text,
            style: TextStyle(
              color: AppColors.black,
              fontWeight: FontWeight.normal,
            ),
          ),
          TextSpan(
            text: linkText,
            style: TextStyle(
              decoration: TextDecoration.underline,
              // color: Theme.of(context).colorScheme.primary,
              color: AppColors.textBlue2,
              fontWeight: FontWeight.bold,
            ),
             recognizer: TapGestureRecognizer()
              ..onTap = isDisabled ? null : onPressed,
          ),
        ],
      ),
    );
  }
}
