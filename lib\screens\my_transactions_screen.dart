import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/transaction_provider.dart';
import '../models/transaction_detail.dart';
import '../models/global_objective.dart';
import '../widgets/navigation_drawer.dart';
import '../ui_components/theme/app_theme.dart';
import '../utils/input_value_store.dart';
import 'workflow_detail_screen_fixed.dart';

// Model class for grouped transactions
class GroupedTransaction {
  final String workflowInstanceId;
  final List<TransactionDetail> transactions;
  final String goName;
  final String goId;
  final String tenantId;
  final String userId;
  final String createdAt;
  final String updatedAt;

  GroupedTransaction({
    required this.workflowInstanceId,
    required this.transactions,
    required this.goName,
    required this.goId,
    required this.tenantId,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Get the status based on the last transaction in the group
  String get status {
    if (transactions.isEmpty) return 'Unknown';

    // Sort transactions by updatedAt to get the most recent one
    final sortedTransactions = List<TransactionDetail>.from(transactions);
    sortedTransactions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return sortedTransactions.first.status;
  }

  // Get a display name for the group
  String get displayName => goName;

  // Get a description for the group
  String get description => 'Workflow ID: $workflowInstanceId';

  // Get the number of LOs in this group
  int get loCount => transactions.length;

  // Get the most recent transaction in the group
  TransactionDetail get latestTransaction {
    if (transactions.isEmpty) {
      throw Exception('No transactions in group');
    }

    final sortedTransactions = List<TransactionDetail>.from(transactions);
    sortedTransactions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return sortedTransactions.first;
  }
}

// Sort options
enum SortField { date, name, id, status }

enum SortDirection { ascending, descending }

// Sort configuration
class SortConfig {
  final SortField field;
  final SortDirection direction;

  const SortConfig(this.field, this.direction);

  String get displayName {
    String fieldName = '';
    switch (field) {
      case SortField.date:
        fieldName = 'Date';
        break;
      case SortField.name:
        fieldName = 'Name';
        break;
      case SortField.id:
        fieldName = 'ID';
        break;
      case SortField.status:
        fieldName = 'Status';
        break;
    }

    return '$fieldName (${direction == SortDirection.ascending ? 'A-Z' : 'Z-A'})';
  }
}

class MyTransactionsScreen extends StatefulWidget {
  const MyTransactionsScreen({super.key});

  @override
  State<MyTransactionsScreen> createState() => _MyTransactionsScreenState();
}

class _MyTransactionsScreenState extends State<MyTransactionsScreen>
    with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  late TabController _tabController;
  final List<String> _statusFilters = ['All', 'Pending', 'Completed'];
  String _currentFilter = 'All';
  // Default sort configuration: date descending (newest first)
  SortConfig _currentSort =
      const SortConfig(SortField.date, SortDirection.descending);

  // Get the appropriate icon for the current sort direction
  IconData _getSortDirectionIcon() {
    return _currentSort.direction == SortDirection.ascending
        ? Icons.arrow_upward
        : Icons.arrow_downward;
  }

  // Build a menu item for the sort popup menu
  Widget _buildSortMenuItem(
    IconData icon,
    String text,
    SortField field,
    SortDirection direction,
  ) {
    // Check if this is the currently selected sort option
    final bool isSelected =
        _currentSort.field == field && _currentSort.direction == direction;

    return Row(
      children: [
        // Icon with field-specific color
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: _getSortFieldColor(field).withAlpha(50),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: 16,
            color: _getSortFieldColor(field),
          ),
        ),
        const SizedBox(width: 12),
        // Text
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        // Check mark for selected item
        if (isSelected)
          Icon(
            Icons.check,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
      ],
    );
  }

  // Get a color for each sort field
  Color _getSortFieldColor(SortField field) {
    final theme = Theme.of(context);
    switch (field) {
      case SortField.date:
        return theme.colorScheme.primary;
      case SortField.name:
        return theme.colorScheme.secondary;
      case SortField.id:
        return theme.colorScheme.tertiary;
      case SortField.status:
        return theme.colorScheme.error;
    }
  }

  // Get an icon for each sort field
  IconData _getSortFieldIcon(SortField field) {
    switch (field) {
      case SortField.date:
        return Icons.calendar_today;
      case SortField.name:
        return Icons.sort_by_alpha;
      case SortField.id:
        return Icons.tag;
      case SortField.status:
        return Icons.info_outline;
    }
  }

  // Format a date string to a human-readable format in IST
  String formatDate(String dateString) {
    try {
      // Parse the ISO date string
      final DateTime utcDate = DateTime.parse(dateString);

      // Convert to IST (UTC+5:30)
      final DateTime istDate =
          utcDate.add(const Duration(hours: 5, minutes: 30));

      // Format the date in a human-readable way with IST indicator
      return '${DateFormat('MMM d, yyyy - h:mm a').format(istDate)} IST';
    } catch (e) {
      // Return the original string if parsing fails
      return dateString;
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize tab controller but we'll use it just for state management
    _tabController = TabController(length: _statusFilters.length, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _currentFilter = _statusFilters[_tabController.index];
        });
      }
    });
    _fetchTransactionDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchTransactionDetails() async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.fetchTransactionDetails();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Transactions'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        actions: [
          // Sort button with popup menu
          PopupMenuButton<SortConfig>(
            tooltip: 'Sort by: ${_currentSort.displayName}',
            icon: Stack(
              alignment: Alignment.center,
              children: [
                const Icon(Icons.sort),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                      border: Border.all(
                          color: Theme.of(context).colorScheme.onPrimary,
                          width: 1),
                    ),
                    child: Icon(
                      _getSortDirectionIcon(),
                      size: 8,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ),
              ],
            ),
            // Add offset to show the menu below the button
            offset: const Offset(0, 40),
            // Add shape for rounded corners
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            // Add color for the menu
            color: Colors.white,
            // Add elevation for shadow
            elevation: 8,
            itemBuilder: (context) => [
              // Header for the menu
              PopupMenuItem(
                enabled: false,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sort Transactions',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Current: ${_currentSort.displayName}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withAlpha(153),
                      ),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              // Date sorting options
              PopupMenuItem(
                value:
                    const SortConfig(SortField.date, SortDirection.descending),
                child: _buildSortMenuItem(
                  Icons.calendar_today,
                  'Date (Newest first)',
                  SortField.date,
                  SortDirection.descending,
                ),
              ),
              PopupMenuItem(
                value:
                    const SortConfig(SortField.date, SortDirection.ascending),
                child: _buildSortMenuItem(
                  Icons.calendar_today,
                  'Date (Oldest first)',
                  SortField.date,
                  SortDirection.ascending,
                ),
              ),
              const PopupMenuDivider(),
              // Name sorting options
              PopupMenuItem(
                value:
                    const SortConfig(SortField.name, SortDirection.ascending),
                child: _buildSortMenuItem(
                  Icons.sort_by_alpha,
                  'Name (A-Z)',
                  SortField.name,
                  SortDirection.ascending,
                ),
              ),
              PopupMenuItem(
                value:
                    const SortConfig(SortField.name, SortDirection.descending),
                child: _buildSortMenuItem(
                  Icons.sort_by_alpha,
                  'Name (Z-A)',
                  SortField.name,
                  SortDirection.descending,
                ),
              ),
              const PopupMenuDivider(),
              // ID sorting options
              PopupMenuItem(
                value: const SortConfig(SortField.id, SortDirection.ascending),
                child: _buildSortMenuItem(
                  Icons.tag,
                  'ID (A-Z)',
                  SortField.id,
                  SortDirection.ascending,
                ),
              ),
              PopupMenuItem(
                value: const SortConfig(SortField.id, SortDirection.descending),
                child: _buildSortMenuItem(
                  Icons.tag,
                  'ID (Z-A)',
                  SortField.id,
                  SortDirection.descending,
                ),
              ),
              const PopupMenuDivider(),
              // Status sorting options
              PopupMenuItem(
                value:
                    const SortConfig(SortField.status, SortDirection.ascending),
                child: _buildSortMenuItem(
                  Icons.info_outline,
                  'Status (A-Z)',
                  SortField.status,
                  SortDirection.ascending,
                ),
              ),
              PopupMenuItem(
                value: const SortConfig(
                    SortField.status, SortDirection.descending),
                child: _buildSortMenuItem(
                  Icons.info_outline,
                  'Status (Z-A)',
                  SortField.status,
                  SortDirection.descending,
                ),
              ),
            ],
            onSelected: (SortConfig newSort) {
              setState(() {
                _currentSort = newSort;
              });
              // Show a snackbar to indicate the sort order
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _getSortFieldIcon(newSort.field),
                          size: 16,
                          color: _getSortFieldColor(newSort.field),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text('Sorted by ${newSort.displayName}'),
                    ],
                  ),
                  backgroundColor:
                      Theme.of(context).colorScheme.primary.withAlpha(220),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
        ],
      ),
      drawer: const AppNavigationDrawer(currentRoute: 'my_transactions'),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Status filter chips
                Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).shadowColor.withAlpha(13),
                        blurRadius: 1,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _statusFilters
                          .map((status) => _buildFilterChip(status))
                          .toList(),
                    ),
                  ),
                ),
                // Divider
                const Divider(height: 1),
                // Transactions list
                Expanded(child: _buildTransactionsList()),
              ],
            ),
    );
  }

  Widget _buildTransactionsList() {
    return Consumer<TransactionProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingTransactionDetails) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Error: ${provider.error}',
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _fetchTransactionDetails,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final allTransactions = provider.transactionDetails;

        // Group transactions by workflow ID
        final groupedTransactions =
            _groupTransactionsByWorkflowId(allTransactions);

        // Filter grouped transactions based on the selected tab
        final filteredGroups =
            _filterGroupedTransactions(groupedTransactions, _currentFilter);

        if (filteredGroups.isEmpty) {
          return Center(
            child: Text(_currentFilter == 'All'
                ? 'No transactions found'
                : 'No ${_currentFilter.toLowerCase()} transactions found'),
          );
        }

        return RefreshIndicator(
          onRefresh: _fetchTransactionDetails,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredGroups.length,
            itemBuilder: (context, index) {
              final group = filteredGroups[index];
              return _buildGroupedTransactionCard(group);
            },
          ),
        );
      },
    );
  }

  // Group transactions by workflow ID
  List<GroupedTransaction> _groupTransactionsByWorkflowId(
      List<TransactionDetail> transactions) {
    // Create a map to group transactions by workflow ID
    final Map<String, List<TransactionDetail>> groupedMap = {};

    // Group transactions by workflow ID
    for (final transaction in transactions) {
      if (!groupedMap.containsKey(transaction.workflowInstanceId)) {
        groupedMap[transaction.workflowInstanceId] = [];
      }
      groupedMap[transaction.workflowInstanceId]!.add(transaction);
    }

    // Convert the map to a list of GroupedTransaction objects
    final List<GroupedTransaction> result = [];

    groupedMap.forEach((workflowId, transactionsList) {
      // Use the first transaction for common properties
      final firstTransaction = transactionsList.first;

      // Find the latest transaction for createdAt and updatedAt
      transactionsList.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      final latestTransaction = transactionsList.first;

      result.add(GroupedTransaction(
        workflowInstanceId: workflowId,
        transactions: transactionsList,
        goName: firstTransaction.goName,
        goId: firstTransaction.goId,
        tenantId: firstTransaction.tenantId,
        userId: firstTransaction.userId,
        createdAt: firstTransaction.createdAt,
        updatedAt: latestTransaction.updatedAt,
      ));
    });

    return result;
  }

  // Filter grouped transactions based on status
  List<GroupedTransaction> _filterGroupedTransactions(
      List<GroupedTransaction> groups, String filter) {
    List<GroupedTransaction> filteredGroups;

    if (filter == 'All') {
      filteredGroups = List.from(groups);
    } else {
      filteredGroups = groups.where((group) {
        return group.status.toLowerCase() == filter.toLowerCase();
      }).toList();
    }

    // Sort the filtered groups based on current sort configuration
    return _sortGroupedTransactions(filteredGroups);
  }

  Widget _buildGroupedTransactionCard(GroupedTransaction group) {
    // Determine status color based on the group's status
    Color statusColor;
    final theme = Theme.of(context);
    switch (group.status.toLowerCase()) {
      case 'completed':
        statusColor = theme.colorScheme.primary;
        break;
      case 'pending':
        statusColor = theme.colorScheme.secondary;
        break;
      default:
        statusColor = theme.colorScheme.onSurface.withAlpha(153);
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    group.displayName,
                    style: AppTheme.headingSmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Row(
                  children: [
                    // Show the number of LOs in this group
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.tertiary.withAlpha(25),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: theme.colorScheme.tertiary),
                      ),
                      child: Text(
                        '${group.loCount} LO${group.loCount > 1 ? 's' : ''}',
                        style: TextStyle(
                            color: theme.colorScheme.tertiary, fontSize: 12),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Show the status
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: statusColor),
                      ),
                      child: Text(
                        group.status,
                        style: TextStyle(color: statusColor, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Text(
            //   _getGroupDescription(group),
            //   style: AppTheme.bodyMedium,
            //   maxLines: 2,
            //   overflow: TextOverflow.ellipsis,
            // ),
            const SizedBox(height: 8),
            Text(
              'Workflow ID: ${group.workflowInstanceId}',
              style: AppTheme.bodySmall.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withAlpha(153)),
            ),
            const SizedBox(height: 4),
            Text(
              'Created: ${formatDate(group.createdAt)}',
              style: AppTheme.bodySmall.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withAlpha(153)),
            ),
            const SizedBox(height: 4),
            Text(
              'Last Updated: ${formatDate(group.updatedAt)}',
              style: AppTheme.bodySmall.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withAlpha(153)),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // View Details button
                TextButton(
                  onPressed: () {
                    _showGroupedTransactionDetails(group);
                  },
                  child: const Text('View Details'),
                ),

                // Resume Transaction button (only for pending status)
                if (group.status.toLowerCase() == 'pending')
                  ElevatedButton(
                    onPressed: () {
                      _resumeGroupedTransaction(group);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    child: const Text('Resume'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Get a description for a grouped transaction
  String _getGroupDescription(GroupedTransaction group) {
    if (group.transactions.isEmpty) {
      return 'No local objectives';
    }

    // Get the latest transaction
    final latestTransaction = group.latestTransaction;

    // Get the input attributes from the latest transaction
    final attributes = latestTransaction.getInputAttributes();
    if (attributes.isNotEmpty) {
      final attributeTexts = attributes
          .map((attr) =>
              '${attr.attributeDisplayName}: ${_formatValue(attr.value)}')
          .take(2);
      return attributeTexts.join(' | ');
    }

    // If no attributes, show the LO names
    final loNames = group.transactions
        .map((t) => t.loName)
        .where((name) => name != null && name.isNotEmpty)
        .take(2)
        .join(', ');

    if (loNames.isNotEmpty) {
      return 'LOs: $loNames${group.loCount > 2 ? ' and ${group.loCount - 2} more' : ''}';
    }

    return 'Global Objective: ${group.goName}';
  }

  void _showGroupedTransactionDetails(GroupedTransaction group) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              controller: scrollController,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            group.displayName,
                            style: AppTheme.headingMedium,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .tertiary
                                .withAlpha(25),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                                color: Theme.of(context).colorScheme.tertiary),
                          ),
                          child: Text(
                            '${group.loCount} LO${group.loCount > 1 ? 's' : ''}',
                            style: TextStyle(
                                color: Theme.of(context).colorScheme.tertiary),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(group.status).withAlpha(25),
                        borderRadius: BorderRadius.circular(4),
                        border:
                            Border.all(color: _getStatusColor(group.status)),
                      ),
                      child: Text(
                        group.status,
                        style: TextStyle(color: _getStatusColor(group.status)),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildGlobalDetailsTable(group),
                    const SizedBox(height: 16),
                    Text(
                      'Local Objectives',
                      style: AppTheme.headingSmall,
                    ),
                    const SizedBox(height: 8),
                    _buildLocalObjectivesTable(group),
                    const SizedBox(height: 16),
                    Text(
                      'Latest Input Data',
                      style: AppTheme.headingSmall,
                    ),
                    const SizedBox(height: 8),
                    _buildInputAttributesSection(group.latestTransaction),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildLocalObjectivesTable(GroupedTransaction group) {
    if (group.transactions.isEmpty) {
      return const Text('No local objectives available');
    }

    // Sort transactions by updatedAt (newest first)
    final sortedTransactions = List<TransactionDetail>.from(group.transactions);
    sortedTransactions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return Column(
      children: [
        Table(
          columnWidths: const {
            0: FlexColumnWidth(2), // Name
            1: FlexColumnWidth(2), // Status
            2: FlexColumnWidth(2), // Updated At
          },
          border: TableBorder.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
          children: [
            // Header row
            TableRow(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'Local Objective',
                    style: AppTheme.bodyMedium
                        .copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'Status',
                    style: AppTheme.bodyMedium
                        .copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'Updated At',
                    style: AppTheme.bodyMedium
                        .copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            // Data rows
            ...sortedTransactions.map((transaction) => TableRow(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        transaction.loName ?? 'N/A',
                        style: AppTheme.bodyMedium,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color:
                              _getStatusColor(transaction.status).withAlpha(25),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          transaction.status,
                          style: TextStyle(
                            color: _getStatusColor(transaction.status),
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        formatDate(transaction.updatedAt),
                        style: AppTheme.bodyMedium,
                      ),
                    ),
                  ],
                )),
          ],
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    final theme = Theme.of(context);
    switch (status.toLowerCase()) {
      case 'completed':
        return theme.colorScheme.primary;
      case 'pending':
        return theme.colorScheme.secondary;

      default:
        return theme.colorScheme.onSurface.withAlpha(153);
    }
  }

  Widget _buildGlobalDetailsTable(dynamic transaction) {
    String workflowId;
    String goName;

    if (transaction is TransactionDetail) {
      workflowId = transaction.workflowInstanceId;
      goName = transaction.goName;
    } else if (transaction is GroupedTransaction) {
      workflowId = transaction.workflowInstanceId;
      goName = transaction.goName;
    } else {
      return const Text('Invalid transaction type');
    }

    return Column(
      children: [
        globalObjectiveText('Workflow ID', workflowId),
        globalObjectiveText('Global Objective', goName),
      ],
    );
  }

  Widget globalObjectiveText(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              label,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              value,
              style: AppTheme.bodyMedium,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputAttributesSection(TransactionDetail transaction) {
    final inputAttributes = transaction.getInputAttributes();

    if (inputAttributes.isEmpty) {
      if (transaction.inputStack is Map &&
          (transaction.inputStack as Map).isEmpty) {
        return const Text('No input data available');
      }

      // If it's not a list but some other non-empty structure
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Theme.of(context).dividerColor),
        ),
        child: Text(
          'Input data format: ${transaction.inputStack.runtimeType}\n${_formatValue(transaction.inputStack)}',
          style: AppTheme.bodyMedium,
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: inputAttributes.map((attribute) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attribute.attributeDisplayName,
                  style:
                      AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatValue(attribute.value),
                  style: AppTheme.bodyMedium,
                ),
                const Divider(),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  // Build a filter chip for the status filter
  Widget _buildFilterChip(String status) {
    final isSelected = status == _currentFilter;
    final theme = Theme.of(context);

    // Get status-specific colors
    Color chipColor;
    Color textColor;

    if (isSelected) {
      // Selected chip styling
      chipColor = theme.colorScheme.primary.withAlpha(80);
      textColor = theme.colorScheme.primary;
    } else {
      // Unselected chip styling - more appealing now
      switch (status.toLowerCase()) {
        case 'pending':
          chipColor = theme.colorScheme.secondary.withAlpha(30);
          textColor = theme.colorScheme.secondary;
          break;
        case 'completed':
          chipColor = theme.colorScheme.primary.withAlpha(30);
          textColor = theme.colorScheme.primary;
          break;
        default: // 'all'
          chipColor = theme.colorScheme.onSurface.withAlpha(30);
          textColor = theme.colorScheme.onSurface;
      }
    }

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(status),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            setState(() {
              _currentFilter = status;
              // Update tab controller to match
              final index = _statusFilters.indexOf(status);
              if (index >= 0) {
                _tabController.animateTo(index);
              }
            });
          }
        },
        backgroundColor: chipColor,
        selectedColor: theme.colorScheme.primary.withAlpha(80),
        checkmarkColor: theme.colorScheme.primary,
        labelStyle: TextStyle(
          color: textColor,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: isSelected
                ? theme.colorScheme.primary.withAlpha(150)
                : chipColor.withAlpha(150),
            width: 1,
          ),
        ),
        elevation: isSelected ? 2 : 1,
        shadowColor: isSelected
            ? theme.colorScheme.primary.withAlpha(60)
            : Colors.black12,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  // Sort grouped transactions based on current sort configuration
  List<GroupedTransaction> _sortGroupedTransactions(
      List<GroupedTransaction> groups) {
    final sortedGroups = List<GroupedTransaction>.from(groups);

    sortedGroups.sort((a, b) {
      int result;

      // Sort based on the selected field
      switch (_currentSort.field) {
        case SortField.date:
          final DateTime dateA = DateTime.parse(a.createdAt);
          final DateTime dateB = DateTime.parse(b.createdAt);
          result = dateA.compareTo(dateB);
          break;

        case SortField.name:
          result = a.displayName.compareTo(b.displayName);
          break;

        case SortField.id:
          result = a.workflowInstanceId.compareTo(b.workflowInstanceId);
          break;

        case SortField.status:
          result = a.status.compareTo(b.status);
          break;
      }

      // Apply sort direction
      return _currentSort.direction == SortDirection.ascending
          ? result // Ascending order (A-Z, oldest first)
          : -result; // Descending order (Z-A, newest first)
    });

    return sortedGroups;
  }

  // Resume a grouped transaction
  void _resumeGroupedTransaction(GroupedTransaction group) {
    // Clear the input store before resuming
    final inputStore = InputValueStore();
    inputStore.clear();

    // Get the latest transaction in the group
    final latestTransaction = group.latestTransaction;

    // Create a GlobalObjective from the group data
    final objective = GlobalObjective(
      objectiveId: group.goId,
      name: group.goName,
      tenantId: group.tenantId,
      version: '1.0', // Default version
      status: group.status,
    );

    // Navigate to the workflow detail screen with resume flag
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkflowDetailScreen(
          objective: objective,
          transaction: latestTransaction,
          isResuming: true,
        ),
      ),
    );
  }

  String _formatValue(dynamic value) {
    if (value is Map) {
      return value.entries
          .map((e) => '${e.key}: ${_formatValue(e.value)}')
          .join('\n');
    } else if (value is List) {
      if (value.isEmpty) return '[]';
      return value.map((item) => '- ${_formatValue(item)}').join('\n');
    } else if (value == null) {
      return 'null';
    } else {
      return value.toString();
    }
  }
}
