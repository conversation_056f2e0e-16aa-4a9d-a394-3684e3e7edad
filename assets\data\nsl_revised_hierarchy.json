{"system_info": {"title": "NSL Enhanced Organization Chart Data - Dynamic Financial Integration", "framework": "GO-LO-NP-Stack-SubNSL BET Structure with Multi-Level Financial Integration", "version": "3.0 - Dynamic Hierarchy with Level-Specific Data", "total_organizational_levels": 4, "bet_composition": "GOs + LOs + NP Functions + Input/Output Stacks + Subordinate NSL", "default_time_range": {"from": "2024-11-01T00:00", "to": "2024-11-30T23:59"}}, "financial_data_by_node": {"1": {"id": "1", "title": "CEO Operations", "level": "M4", "standalone": {"summary": {"total_revenue": {"value": "$7,000,000", "change": "+12.5% vs last month"}, "net_margin": {"value": "39.2%", "change": "****% vs last month"}, "total_transactions": {"value": "15,234", "change": "+8.7% vs last month"}, "bet_efficiency": {"value": "88.5%", "change": "+3.2% vs last month"}}, "income_statement": [{"item": "Service Revenue - Application Development", "amount": "$3,150,000", "percentage": "45.0%", "bet_contribution": "245 Active BETs"}, {"item": "Service Revenue - QA Testing", "amount": "$1,400,000", "percentage": "20.0%", "bet_contribution": "89 Active BETs"}, {"item": "Service Revenue - Infrastructure", "amount": "$1,400,000", "percentage": "20.0%", "bet_contribution": "67 Active BETs"}, {"item": "Service Revenue - Analytics", "amount": "$1,050,000", "percentage": "15.0%", "bet_contribution": "34 Active BETs"}, {"item": "Total Revenue", "amount": "$7,000,000", "percentage": "100.0%", "bet_contribution": "435 Total BETs", "type": "total"}, {"item": "Employee Benefit Expenses - Direct", "amount": "$2,123,456", "percentage": "30.3%", "bet_contribution": "Human Agent BETs"}, {"item": "Technology & Infrastructure", "amount": "$102,457", "percentage": "1.5%", "bet_contribution": "Digital Agent BETs"}, {"item": "Idle Capacity Costs", "amount": "$487,760", "percentage": "7.0%", "bet_contribution": "11.5% BET Inefficiency"}, {"item": "Net Income", "amount": "$2,743,275", "percentage": "39.2%", "bet_contribution": "", "type": "final"}]}, "consolidated": {"summary": [{"title": "Consolidated GOs", "value": "33", "change": "After eliminations"}, {"title": "Inter-GO Eliminations", "value": "-12", "change": "Overlapping solution spaces"}, {"title": "Consolidated Efficiency", "value": "91.2%", "change": "Integrated solution delivery"}, {"title": "BET Integration", "value": "+5.3%", "change": "Cross-LO coordination"}]}, "bet_breakdown": [{"name": "Employee_Salary", "type": "Entity.Attribute | Human Agent Cost", "cost": "$2,123,456", "details": {"Agent Count": "892", "Avg Cost/Agent": "$2,381", "GO Integration": "88.3%", "LO Participation": "234 LOs", "Active Execution Rate": "88.3%", "State Transitions": "Potentiality → Reality"}}, {"name": "Cloud_Infrastructure", "type": "Entity.Attribute | Digital Agent Cost", "cost": "$102,457", "details": {"vCPU Hours": "129,600", "Memory GB": "259,200", "GO Integration": "75.0%", "LO Distribution": "45 LOs", "Active Execution Rate": "75.0%", "Processing": "Compute → Service Delivery"}}, {"name": "Solution_Revenue", "type": "Entity.Attribute | Value Generation BET", "cost": "$7,000,000", "details": {"Active GOs": "45", "Solution Delivery": "98.2%", "LO Integration": "97.8%", "BET Coordination": "1,356 BETs", "Value Realization Rate": "97.8%", "Transformation": "Market Opportunity → Realized Value"}}], "trends": [{"title": "GO Evolution", "value": "+15.2%", "change": "Solution space expansion"}, {"title": "LO Optimization", "value": "+8.7%", "change": "Local solution efficiency"}, {"title": "BET Integration", "value": "+5.3%", "change": "Cross-LO coordination"}, {"title": "Solution Delivery", "value": "+3.1%", "change": "Macro-objective achievement"}]}, "1.1": {"id": "1.1", "title": "Technology", "level": "M3", "standalone": {"summary": {"total_revenue": {"value": "$4,200,000", "change": "+15.3% vs last month"}, "net_margin": {"value": "42.8%", "change": "+3.1% vs last month"}, "total_transactions": {"value": "8,934", "change": "+11.2% vs last month"}, "bet_efficiency": {"value": "91.2%", "change": "+4.1% vs last month"}}, "income_statement": [{"item": "Engineering Services", "amount": "$2,800,000", "percentage": "66.7%", "bet_contribution": "456 Engineering BETs"}, {"item": "QA & Testing Services", "amount": "$850,000", "percentage": "20.2%", "bet_contribution": "145 Testing BETs"}, {"item": "Infrastructure Services", "amount": "$380,000", "percentage": "9.0%", "bet_contribution": "89 Infra BETs"}, {"item": "Analytics Services", "amount": "$170,000", "percentage": "4.0%", "bet_contribution": "23 Analytics BETs"}, {"item": "Total Revenue", "amount": "$4,200,000", "percentage": "100.0%", "bet_contribution": "713 Total BETs", "type": "total"}, {"item": "Employee Costs - Direct", "amount": "$1,600,000", "percentage": "38.1%", "bet_contribution": "Human Agent BETs"}, {"item": "Technology Costs", "amount": "$600,000", "percentage": "14.3%", "bet_contribution": "Digital Agent BETs"}, {"item": "Infrastructure Costs", "amount": "$200,000", "percentage": "4.8%", "bet_contribution": "Platform BETs"}, {"item": "Net Income", "amount": "$1,800,000", "percentage": "42.8%", "bet_contribution": "", "type": "final"}]}, "consolidated": {"summary": [{"title": "Technology GOs", "value": "28", "change": "Cross-team integration"}, {"title": "Internal Eliminations", "value": "-3", "change": "Shared infrastructure"}, {"title": "Tech Efficiency", "value": "91.2%", "change": "Optimized delivery"}, {"title": "Team Coordination", "value": "+8.1%", "change": "Inter-team synergy"}]}, "bet_breakdown": [{"name": "Engineering_Resources", "type": "Entity.Attribute | Development BET", "cost": "$1,600,000", "details": {"Developers": "17", "Avg Cost/Dev": "$94,118", "Code Quality": "94.2%", "Sprint Velocity": "89 SP/Sprint", "Bug Rate": "0.02/KLOC", "Deployment Freq": "Daily"}}, {"name": "Tech_Infrastructure", "type": "Entity.Attribute | Platform BET", "cost": "$600,000", "details": {"Cloud Spend": "$45K/month", "Uptime": "99.9%", "API Calls": "2.1M/month", "Data Volume": "15TB", "Security Score": "98%", "Scaling Events": "245/month"}}, {"name": "Innovation_Pipeline", "type": "Entity.Attribute | R&D BET", "cost": "$400,000", "details": {"Research Projects": "8", "POCs Completed": "12", "Patents Filed": "3", "Tech Debt Ratio": "8.2%", "Innovation Index": "87%", "Time to Market": "4.2 weeks"}}], "trends": [{"title": "Development Velocity", "value": "+18.4%", "change": "Enhanced tooling & processes"}, {"title": "Code Quality", "value": "+12.1%", "change": "Improved testing coverage"}, {"title": "Infrastructure Efficiency", "value": "+9.8%", "change": "Cloud optimization"}, {"title": "Innovation Rate", "value": "+22.3%", "change": "Accelerated R&D cycles"}]}, "1.2": {"id": "1.2", "title": "Operations", "level": "M3", "standalone": {"summary": {"total_revenue": {"value": "$1,800,000", "change": "+8.7% vs last month"}, "net_margin": {"value": "38.9%", "change": "+1.8% vs last month"}, "total_transactions": {"value": "3,245", "change": "+6.3% vs last month"}, "bet_efficiency": {"value": "84.3%", "change": "+2.9% vs last month"}}, "income_statement": [{"item": "Customer Success Revenue", "amount": "$850,000", "percentage": "47.2%", "bet_contribution": "112 CS BETs"}, {"item": "Sales Operations Revenue", "amount": "$680,000", "percentage": "37.8%", "bet_contribution": "78 Sales BETs"}, {"item": "Business Development Revenue", "amount": "$270,000", "percentage": "15.0%", "bet_contribution": "44 BD BETs"}, {"item": "Total Revenue", "amount": "$1,800,000", "percentage": "100.0%", "bet_contribution": "234 Total BETs", "type": "total"}, {"item": "Employee Costs", "amount": "$720,000", "percentage": "40.0%", "bet_contribution": "Human Agent BETs"}, {"item": "Sales Tools & Systems", "amount": "$180,000", "percentage": "10.0%", "bet_contribution": "Digital Agent BETs"}, {"item": "Customer Success Platforms", "amount": "$200,000", "percentage": "11.1%", "bet_contribution": "Platform BETs"}, {"item": "Net Income", "amount": "$700,000", "percentage": "38.9%", "bet_contribution": "", "type": "final"}]}, "consolidated": {"summary": [{"title": "Operations GOs", "value": "12", "change": "Customer-focused delivery"}, {"title": "Process Eliminations", "value": "-2", "change": "Streamlined workflows"}, {"title": "Ops Efficiency", "value": "84.3%", "change": "Process optimization"}, {"title": "Customer Satisfaction", "value": "+7.2%", "change": "Enhanced service delivery"}]}, "bet_breakdown": [{"name": "Customer_Success_BETs", "type": "Entity.Attribute | Service BET", "cost": "$520,000", "details": {"CS Reps": "12", "Customer Health Score": "87%", "Churn Rate": "2.1%", "NPS Score": "72", "Resolution Time": "4.2hrs", "Renewal Rate": "94%"}}, {"name": "Sales_Operations_BETs", "type": "Entity.Attribute | Revenue BET", "cost": "$380,000", "details": {"Sales Reps": "8", "Win Rate": "34%", "Sales Cycle": "45 days", "Pipeline Value": "$2.1M", "Quota Attainment": "112%", "Lead Conversion": "18%"}}, {"name": "Business_Dev_BETs", "type": "Entity.Attribute | Growth BET", "cost": "$200,000", "details": {"BD Managers": "3", "Partnerships": "12", "New Markets": "2", "Revenue Growth": "23%", "Deal Size": "$45K avg", "Pipeline Growth": "+31%"}}], "trends": [{"title": "Customer Retention", "value": "+9.4%", "change": "Enhanced success programs"}, {"title": "Sales Performance", "value": "+14.2%", "change": "Improved processes"}, {"title": "Market Expansion", "value": "+19.8%", "change": "New partnership channels"}, {"title": "Revenue per Customer", "value": "+11.7%", "change": "Upselling success"}]}, "1.3": {"id": "1.3", "title": "Finance", "level": "M3", "standalone": {"summary": {"total_revenue": {"value": "Internal", "change": "Support Function"}, "total_cost": {"value": "$168,000", "change": "+2.3% vs last month"}, "efficiency": {"value": "76.8%", "change": "+1.9% vs last month"}, "bet_efficiency": {"value": "76.8%", "change": "Process optimization"}}, "income_statement": [{"item": "Financial Planning Services", "amount": "$95,000", "percentage": "56.5%", "bet_contribution": "55 Planning BETs"}, {"item": "Accounting & Control Services", "amount": "$73,000", "percentage": "43.5%", "bet_contribution": "34 Control BETs"}, {"item": "Total Internal Costs", "amount": "$168,000", "percentage": "100.0%", "bet_contribution": "89 Total BETs", "type": "total"}, {"item": "Staff Costs", "amount": "$120,000", "percentage": "71.4%", "bet_contribution": "Human Agent BETs"}, {"item": "Systems & Tools", "amount": "$30,000", "percentage": "17.9%", "bet_contribution": "Digital Agent BETs"}, {"item": "Compliance & Audit", "amount": "$18,000", "percentage": "10.7%", "bet_contribution": "Compliance BETs"}, {"item": "Net Support Value", "amount": "$168,000", "percentage": "100.0%", "bet_contribution": "Support Function", "type": "final"}]}, "consolidated": {"summary": [{"title": "Finance GOs", "value": "5", "change": "Support processes"}, {"title": "Process Automation", "value": "+15%", "change": "Digital transformation"}, {"title": "Finance Efficiency", "value": "76.8%", "change": "Continuous improvement"}, {"title": "Compliance Score", "value": "98.5%", "change": "Regulatory alignment"}]}, "bet_breakdown": [{"name": "Financial_Planning_BETs", "type": "Entity.Attribute | Planning BET", "cost": "$95,000", "details": {"Planners": "3", "Budget Cycles": "12/year", "Forecast Accuracy": "94%", "Report Generation": "Weekly", "Variance Analysis": "Monthly", "Strategic Reviews": "Quarterly"}}, {"name": "Accounting_Control_BETs", "type": "Entity.Attribute | Control BET", "cost": "$73,000", "details": {"Accountants": "2", "Monthly Closes": "3 days", "Audit Findings": "0", "Compliance Rate": "100%", "Financial Reports": "25/month", "Control Testing": "100%"}}], "trends": [{"title": "Process Automation", "value": "+25.3%", "change": "Digital workflows"}, {"title": "Reporting Speed", "value": "+18.7%", "change": "Automated systems"}, {"title": "Compliance Score", "value": "+8.2%", "change": "Enhanced controls"}, {"title": "Cost Efficiency", "value": "+12.4%", "change": "Process optimization"}]}, "1.1.1": {"id": "1.1.1", "title": "Engineering", "level": "M2", "standalone": {"summary": {"total_revenue": {"value": "$2,800,000", "change": "+17.2% vs last month"}, "net_margin": {"value": "42.9%", "change": "+4.2% vs last month"}, "total_transactions": {"value": "5,678", "change": "+13.4% vs last month"}, "bet_efficiency": {"value": "93.5%", "change": "+5.1% vs last month"}}, "income_statement": [{"item": "Frontend Development", "amount": "$1,200,000", "percentage": "42.9%", "bet_contribution": "67 Frontend BETs"}, {"item": "Backend Development", "amount": "$1,000,000", "percentage": "35.7%", "bet_contribution": "89 Backend BETs"}, {"item": "Platform Engineering", "amount": "$600,000", "percentage": "21.4%", "bet_contribution": "112 Platform BETs"}, {"item": "Total Revenue", "amount": "$2,800,000", "percentage": "100.0%", "bet_contribution": "268 Total BETs", "type": "total"}, {"item": "Engineer <PERSON>", "amount": "$1,200,000", "percentage": "42.9%", "bet_contribution": "17 Engineers"}, {"item": "Development Tools", "amount": "$200,000", "percentage": "7.1%", "bet_contribution": "Tool BETs"}, {"item": "Infrastructure Costs", "amount": "$200,000", "percentage": "7.1%", "bet_contribution": "Platform BETs"}, {"item": "Net Income", "amount": "$1,200,000", "percentage": "42.9%", "bet_contribution": "", "type": "final"}]}, "consolidated": {"summary": [{"title": "Engineering GOs", "value": "18", "change": "Feature-focused delivery"}, {"title": "Code Reuse", "value": "+23%", "change": "Shared components"}, {"title": "Dev Efficiency", "value": "93.5%", "change": "Optimized workflows"}, {"title": "Quality Score", "value": "96.2%", "change": "Enhanced testing"}]}, "bet_breakdown": [{"name": "Senior_Developers", "type": "Entity.Attribute | Development BET", "cost": "$804,000", "details": {"Senior Devs": "8", "Code Reviews": "234/month", "Commits": "1,245/month", "Bug Fix Rate": "95%", "Mentoring Hours": "40/month", "Architecture Decisions": "12"}}, {"name": "Development_Pipeline", "type": "Entity.Attribute | Process BET", "cost": "$300,000", "details": {"Deployments": "156/month", "Build Success": "98.7%", "Test Coverage": "87%", "Code Quality": "A+", "Security Scans": "100%", "Performance Score": "92%"}}, {"name": "Innovation_Projects", "type": "Entity.Attribute | R&D BET", "cost": "$200,000", "details": {"POCs": "6/quarter", "Tech Experiments": "12", "Performance Gains": "+15%", "New Features": "23", "Patent Applications": "2", "Research Hours": "320/month"}}], "trends": [{"title": "Code Quality", "value": "+16.8%", "change": "Enhanced review processes"}, {"title": "Development Speed", "value": "+21.4%", "change": "Improved tooling"}, {"title": "Bug Reduction", "value": "+31.2%", "change": "Better testing practices"}, {"title": "Team Productivity", "value": "+19.7%", "change": "Optimized workflows"}]}, "1.1.2": {"id": "1.1.2", "title": "QA & Testing", "level": "M2", "standalone": {"summary": {"total_revenue": {"value": "$850,000", "change": "+11.8% vs last month"}, "net_margin": {"value": "38.8%", "change": "+2.4% vs last month"}, "total_transactions": {"value": "2,145", "change": "+8.9% vs last month"}, "bet_efficiency": {"value": "87.2%", "change": "+3.7% vs last month"}}, "income_statement": [{"item": "Manual Testing Services", "amount": "$400,000", "percentage": "47.1%", "bet_contribution": "68 Manual BETs"}, {"item": "Automated Testing Services", "amount": "$300,000", "percentage": "35.3%", "bet_contribution": "45 Automation BETs"}, {"item": "Performance Testing Services", "amount": "$150,000", "percentage": "17.6%", "bet_contribution": "32 Performance BETs"}, {"item": "Total Revenue", "amount": "$850,000", "percentage": "100.0%", "bet_contribution": "145 Total BETs", "type": "total"}, {"item": "QA Engineer Sal<PERSON>", "amount": "$400,000", "percentage": "47.1%", "bet_contribution": "4 QA Engineers"}, {"item": "Testing Tools & Licenses", "amount": "$80,000", "percentage": "9.4%", "bet_contribution": "Tool BETs"}, {"item": "Test Environment Costs", "amount": "$40,000", "percentage": "4.7%", "bet_contribution": "Environment BETs"}, {"item": "Net Income", "amount": "$330,000", "percentage": "38.8%", "bet_contribution": "", "type": "final"}]}, "consolidated": {"summary": [{"title": "QA GOs", "value": "6", "change": "Quality-focused delivery"}, {"title": "Test Automation", "value": "+35%", "change": "Enhanced coverage"}, {"title": "QA Efficiency", "value": "87.2%", "change": "Optimized processes"}, {"title": "Bug Detection Rate", "value": "94.7%", "change": "Improved accuracy"}]}, "bet_breakdown": [{"name": "Manual_Testing_BETs", "type": "Entity.Attribute | Testing BET", "cost": "$400,000", "details": {"Test Cases": "1,245", "Bug Reports": "89", "Test Coverage": "92%", "Execution Rate": "87%", "Pass Rate": "94%", "Regression Tests": "Weekly"}}, {"name": "Automation_Framework_BETs", "type": "Entity.Attribute | Automation BET", "cost": "$300,000", "details": {"Automated Tests": "567", "Test Runs": "Daily", "Success Rate": "96%", "Maintenance Hours": "20/week", "CI/CD Integration": "100%", "Performance Baseline": "Established"}}], "trends": [{"title": "Test Automation Growth", "value": "+28.4%", "change": "Expanded coverage"}, {"title": "Bug Detection Improvement", "value": "+19.3%", "change": "Enhanced processes"}, {"title": "Testing Speed", "value": "+24.7%", "change": "Automation benefits"}, {"title": "Quality Score", "value": "+15.8%", "change": "Better methodologies"}]}, "1.1.1.1": {"id": "1.1.1.1", "title": "<PERSON> - Senior Developer", "level": "M1", "standalone": {"summary": {"annual_salary": {"value": "$89,000", "change": "Performance-based"}, "value_output": {"value": "$425,000", "change": "+12.3% vs last quarter"}, "efficiency": {"value": "90.2%", "change": "+3.8% vs last quarter"}, "bet_efficiency": {"value": "90.2%", "change": "Individual performance"}}, "performance_metrics": [{"metric": "Lines of Code", "value": "15,234", "target": "12,000", "variance": "+26.9%"}, {"metric": "Code Reviews", "value": "89", "target": "60", "variance": "+48.3%"}, {"metric": "Bug Fix Rate", "value": "96.7%", "target": "90%", "variance": "+7.4%"}, {"metric": "Feature Completion", "value": "23", "target": "18", "variance": "+27.8%"}, {"metric": "Mentoring Hours", "value": "40", "target": "30", "variance": "+33.3%"}, {"metric": "Technical Debt Reduction", "value": "12.4%", "target": "10%", "variance": "+24%"}]}, "consolidated": {"summary": [{"title": "Personal GOs", "value": "5", "change": "React component focus"}, {"title": "LO Completion", "value": "15/15", "change": "100% delivery rate"}, {"title": "Individual Efficiency", "value": "90.2%", "change": "High performer"}, {"title": "Team Contribution", "value": "+18.4%", "change": "Above average impact"}]}, "bet_breakdown": [{"name": "React_Component_Development", "type": "Entity.Attribute | Development BET", "cost": "$35,000", "details": {"Components Built": "67", "Reusability Score": "94%", "Performance Score": "92%", "Test Coverage": "96%", "Documentation": "Complete", "Code Reviews": "89"}}, {"name": "Code_Architecture", "type": "Entity.Attribute | Design BET", "cost": "$28,000", "details": {"Architecture Decisions": "12", "Design Patterns": "8", "Refactoring Tasks": "15", "Tech Debt Items": "23", "Performance Optimizations": "11", "Security Reviews": "6"}}, {"name": "Team_Mentoring", "type": "Entity.Attribute | Knowledge BET", "cost": "$15,000", "details": {"Junior Devs Mentored": "3", "Knowledge Sessions": "12", "Documentation Updates": "45", "Training Hours": "40", "Best Practices": "18", "Code Guidelines": "Created 8"}}], "trends": [{"title": "Code Quality Improvement", "value": "+14.2%", "change": "Enhanced practices"}, {"title": "Productivity Growth", "value": "+18.7%", "change": "Skill development"}, {"title": "Team Impact", "value": "+22.1%", "change": "Leadership growth"}, {"title": "Innovation Contribution", "value": "+16.9%", "change": "Technical excellence"}]}, "*******": {"id": "*******", "title": "<PERSON> - Lead Architect", "level": "M1", "standalone": {"summary": {"annual_salary": {"value": "$115,000", "change": "Architecture role"}, "value_output": {"value": "$380,000", "change": "****% vs last quarter"}, "efficiency": {"value": "87.5%", "change": "****% vs last quarter"}, "bet_efficiency": {"value": "87.5%", "change": "Technical leadership"}}, "performance_metrics": [{"metric": "Architecture Decisions", "value": "12", "target": "8", "variance": "+50%"}, {"metric": "System Design Reviews", "value": "24", "target": "20", "variance": "+20%"}, {"metric": "Technical Documentation", "value": "45", "target": "30", "variance": "+50%"}, {"metric": "Performance Optimizations", "value": "18", "target": "12", "variance": "+50%"}, {"metric": "Security Reviews", "value": "8", "target": "6", "variance": "+33%"}, {"metric": "Team Technical Guidance", "value": "35hrs", "target": "25hrs", "variance": "+40%"}]}, "consolidated": {"summary": [{"title": "Architecture GOs", "value": "4", "change": "System design focus"}, {"title": "LO Completion", "value": "18/18", "change": "100% delivery rate"}, {"title": "Technical Leadership", "value": "87.5%", "change": "Strong performer"}, {"title": "System Impact", "value": "+22.1%", "change": "Architecture excellence"}]}, "bet_breakdown": [{"name": "System_Architecture", "type": "Entity.Attribute | Architecture BET", "cost": "$45,000", "details": {"System Designs": "12", "Scalability Plans": "8", "Performance Models": "6", "Security Frameworks": "4", "Integration Patterns": "15", "Documentation": "45 docs"}}, {"name": "Technical_Leadership", "type": "Entity.Attribute | Leadership BET", "cost": "$35,000", "details": {"Team Reviews": "24", "Technical Decisions": "18", "Mentoring Sessions": "20", "Standards Created": "8", "Best Practices": "12", "Knowledge Sharing": "Weekly"}}, {"name": "Performance_Optimization", "type": "Entity.Attribute | Optimization BET", "cost": "$25,000", "details": {"Performance Reviews": "18", "Bottleneck Analysis": "12", "Optimization Strategies": "15", "Load Testing": "8", "Capacity Planning": "6", "Monitoring Setup": "Complete"}}], "trends": [{"title": "Architecture Quality", "value": "+18.9%", "change": "Enhanced design patterns"}, {"title": "System Performance", "value": "+24.3%", "change": "Optimization focus"}, {"title": "Team Mentoring", "value": "+31.7%", "change": "Leadership growth"}, {"title": "Technical Innovation", "value": "+19.2%", "change": "Advanced solutions"}]}}, "organizational_structure": {"M4": {"level_name": "Executive", "node": {"id": "1", "title": "CEO Operations", "type": "Executive Node", "total_bets": 1356, "bet_breakdown": {"gos": 45, "los": 234, "np_functions": 567, "input_output_stacks": 345, "subordinate_nsl": 165}, "children": ["1.1", "1.2", "1.3"], "selected": true, "metrics": {"m3_nodes": 6, "total_gos": 45, "total_los": 234, "transactions": "15.2K", "bet_efficiency": "88.5%"}, "financial_summary": {"revenue": "$7,000,000", "cost": "$4,256,725", "margin": "39.2%"}}}, "M3": {"level_name": "Departments", "nodes": [{"id": "1.1", "title": "Technology", "type": "Department Node", "parent": "1", "total_bets": 892, "bet_breakdown": {"gos": 28, "los": 145, "np_functions": 372, "input_output_stacks": 235, "subordinate_nsl": 112}, "children": ["1.1.1", "1.1.2", "1.1.3", "1.1.4"], "metrics": {"m2_nodes": 4, "gos": 28, "los": 145, "transactions": "8.9K", "bet_efficiency": "91.2%"}, "financial_summary": {"revenue": "$4,200,000", "cost": "$2,400,000", "margin": "42.8%"}}, {"id": "1.2", "title": "Operations", "type": "Department Node", "parent": "1", "total_bets": 234, "bet_breakdown": {"gos": 12, "los": 56, "np_functions": 98, "input_output_stacks": 52, "subordinate_nsl": 16}, "children": ["1.2.1", "1.2.2", "1.2.3"], "metrics": {"m2_nodes": 3, "gos": 12, "los": 56, "transactions": "3.2K", "bet_efficiency": "84.3%"}, "financial_summary": {"revenue": "$1,800,000", "cost": "$1,100,000", "margin": "38.9%"}}, {"id": "1.3", "title": "Finance", "type": "Department Node", "parent": "1", "total_bets": 89, "bet_breakdown": {"gos": 5, "los": 33, "np_functions": 35, "input_output_stacks": 12, "subordinate_nsl": 4}, "children": ["1.3.1", "1.3.2"], "metrics": {"m2_nodes": 2, "gos": 5, "los": 33, "transactions": "1.8K", "bet_efficiency": "76.8%"}, "financial_summary": {"revenue": "Internal", "cost": "$168,000", "margin": "Support Function"}}]}, "M2": {"level_name": "Teams", "nodes": [{"id": "1.1.1", "title": "Engineering", "type": "Team Node", "parent": "1.1", "total_bets": 456, "bet_breakdown": {"gos": 18, "los": 89, "np_functions": 189, "input_output_stacks": 115, "subordinate_nsl": 45}, "children": ["1.1.1.1", "*******", "1.1.1.3", "1.1.1.4"], "metrics": {"m1_employees": 8, "gos": 18, "los": 89, "team_efficiency": "93.5%"}, "financial_summary": {"revenue": "$2,800,000", "cost": "$1,600,000", "margin": "42.9%"}}, {"id": "1.1.2", "title": "QA & Testing", "type": "Team Node", "parent": "1.1", "total_bets": 145, "bet_breakdown": {"gos": 6, "los": 34, "np_functions": 68, "input_output_stacks": 28, "subordinate_nsl": 9}, "children": ["1.1.2.1", "1.1.2.2", "1.1.2.3"], "metrics": {"m1_employees": 3, "gos": 6, "los": 34, "team_efficiency": "87.2%"}, "financial_summary": {"revenue": "$850,000", "cost": "$520,000", "margin": "38.8%"}}, {"id": "1.2.1", "title": "Customer Success", "type": "Team Node", "parent": "1.2", "total_bets": 78, "bet_breakdown": {"gos": 4, "los": 18, "np_functions": 32, "input_output_stacks": 17, "subordinate_nsl": 7}, "children": ["1.2.1.1", "1.2.1.2", "1.2.1.3"], "metrics": {"m1_employees": 3, "gos": 4, "los": 18, "team_efficiency": "85.7%"}, "financial_summary": {"revenue": "$600,000", "cost": "$360,000", "margin": "40.0%"}}, {"id": "1.2.2", "title": "Sales Operations", "type": "Team Node", "parent": "1.2", "total_bets": 89, "bet_breakdown": {"gos": 5, "los": 22, "np_functions": 38, "input_output_stacks": 18, "subordinate_nsl": 6}, "children": ["1.2.2.1", "1.2.2.2", "1.2.2.3"], "metrics": {"m1_employees": 3, "gos": 5, "los": 22, "team_efficiency": "88.4%"}, "financial_summary": {"revenue": "$750,000", "cost": "$450,000", "margin": "40.0%"}}, {"id": "1.2.3", "title": "Business Development", "type": "Team Node", "parent": "1.2", "total_bets": 67, "bet_breakdown": {"gos": 3, "los": 16, "np_functions": 28, "input_output_stacks": 17, "subordinate_nsl": 3}, "children": ["*******", "*******", "*******"], "metrics": {"m1_employees": 3, "gos": 3, "los": 16, "team_efficiency": "82.1%"}, "financial_summary": {"revenue": "$450,000", "cost": "$290,000", "margin": "35.6%"}}]}, "M1": {"level_name": "Individual Employees", "nodes": [{"id": "1.1.1.1", "title": "<PERSON> - Senior Developer", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-001", "total_bets": 67, "bet_breakdown": {"gos": 5, "los": 15, "np_functions": 25, "input_output_stacks": 15, "subordinate_nsl": 7}, "metrics": {"local_objectives": 5, "personal_bets": 15, "lo_efficiency": "90.2%"}, "financial_summary": {"annual_salary": "$89,000", "value_output": "$425,000", "efficiency": "90.2%"}}, {"id": "*******", "title": "<PERSON> - Lead Architect", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-002", "total_bets": 89, "bet_breakdown": {"gos": 4, "los": 18, "np_functions": 35, "input_output_stacks": 22, "subordinate_nsl": 10}, "metrics": {"local_objectives": 4, "personal_bets": 18, "lo_efficiency": "87.5%"}, "financial_summary": {"annual_salary": "$115,000", "value_output": "$380,000", "efficiency": "87.5%"}}, {"id": "1.1.2.1", "title": "<PERSON>", "type": "Individual Employee", "parent": "1.1.2", "employee_id": "EMP-003", "total_bets": 52, "bet_breakdown": {"gos": 2, "los": 12, "np_functions": 22, "input_output_stacks": 12, "subordinate_nsl": 4}, "metrics": {"local_objectives": 2, "personal_bets": 12, "lo_efficiency": "89.1%"}, "financial_summary": {"annual_salary": "$78,000", "value_output": "$285,000", "efficiency": "89.1%"}}, {"id": "1.1.2.2", "title": "<PERSON> - Automation Engineer", "type": "Individual Employee", "parent": "1.1.2", "employee_id": "EMP-004", "total_bets": 48, "bet_breakdown": {"gos": 2, "los": 11, "np_functions": 23, "input_output_stacks": 8, "subordinate_nsl": 4}, "metrics": {"local_objectives": 2, "personal_bets": 11, "lo_efficiency": "91.3%"}, "financial_summary": {"annual_salary": "$82,000", "value_output": "$295,000", "efficiency": "91.3%"}}, {"id": "1.1.2.3", "title": "<PERSON> - Performance Tester", "type": "Individual Employee", "parent": "1.1.2", "employee_id": "EMP-005", "total_bets": 45, "bet_breakdown": {"gos": 2, "los": 11, "np_functions": 23, "input_output_stacks": 8, "subordinate_nsl": 1}, "metrics": {"local_objectives": 2, "personal_bets": 11, "lo_efficiency": "85.7%"}, "financial_summary": {"annual_salary": "$75,000", "value_output": "$270,000", "efficiency": "85.7%"}}, {"id": "1.2.1.1", "title": "<PERSON> - Customer Success Manager", "type": "Individual Employee", "parent": "1.2.1", "employee_id": "EMP-006", "total_bets": 26, "bet_breakdown": {"gos": 1, "los": 6, "np_functions": 11, "input_output_stacks": 6, "subordinate_nsl": 2}, "metrics": {"local_objectives": 1, "personal_bets": 6, "lo_efficiency": "87.4%"}, "financial_summary": {"annual_salary": "$65,000", "value_output": "$200,000", "efficiency": "87.4%"}}, {"id": "1.2.1.2", "title": "<PERSON> - Customer Support Specialist", "type": "Individual Employee", "parent": "1.2.1", "employee_id": "EMP-007", "total_bets": 26, "bet_breakdown": {"gos": 1, "los": 6, "np_functions": 11, "input_output_stacks": 6, "subordinate_nsl": 2}, "metrics": {"local_objectives": 1, "personal_bets": 6, "lo_efficiency": "84.2%"}, "financial_summary": {"annual_salary": "$58,000", "value_output": "$185,000", "efficiency": "84.2%"}}, {"id": "1.2.1.3", "title": "<PERSON> - Account Manager", "type": "Individual Employee", "parent": "1.2.1", "employee_id": "EMP-008", "total_bets": 26, "bet_breakdown": {"gos": 2, "los": 6, "np_functions": 10, "input_output_stacks": 5, "subordinate_nsl": 3}, "metrics": {"local_objectives": 2, "personal_bets": 6, "lo_efficiency": "85.1%"}, "financial_summary": {"annual_salary": "$72,000", "value_output": "$215,000", "efficiency": "85.1%"}}, {"id": "1.2.2.1", "title": "<PERSON> - Sales Operations Analyst", "type": "Individual Employee", "parent": "1.2.2", "employee_id": "EMP-009", "total_bets": 30, "bet_breakdown": {"gos": 2, "los": 7, "np_functions": 13, "input_output_stacks": 6, "subordinate_nsl": 2}, "metrics": {"local_objectives": 2, "personal_bets": 7, "lo_efficiency": "88.9%"}, "financial_summary": {"annual_salary": "$68,000", "value_output": "$250,000", "efficiency": "88.9%"}}, {"id": "1.2.2.2", "title": "<PERSON> - Sales Coordinator", "type": "Individual Employee", "parent": "1.2.2", "employee_id": "EMP-010", "total_bets": 30, "bet_breakdown": {"gos": 2, "los": 8, "np_functions": 12, "input_output_stacks": 6, "subordinate_nsl": 2}, "metrics": {"local_objectives": 2, "personal_bets": 8, "lo_efficiency": "87.6%"}, "financial_summary": {"annual_salary": "$62,000", "value_output": "$240,000", "efficiency": "87.6%"}}, {"id": "1.2.2.3", "title": "<PERSON> - CRM Specialist", "type": "Individual Employee", "parent": "1.2.2", "employee_id": "EMP-011", "total_bets": 29, "bet_breakdown": {"gos": 1, "los": 7, "np_functions": 13, "input_output_stacks": 6, "subordinate_nsl": 2}, "metrics": {"local_objectives": 1, "personal_bets": 7, "lo_efficiency": "89.2%"}, "financial_summary": {"annual_salary": "$70,000", "value_output": "$260,000", "efficiency": "89.2%"}}, {"id": "*******", "title": "<PERSON> - Business Development Representative", "type": "Individual Employee", "parent": "1.2.3", "employee_id": "EMP-012", "total_bets": 22, "bet_breakdown": {"gos": 1, "los": 5, "np_functions": 9, "input_output_stacks": 6, "subordinate_nsl": 1}, "metrics": {"local_objectives": 1, "personal_bets": 5, "lo_efficiency": "82.8%"}, "financial_summary": {"annual_salary": "$55,000", "value_output": "$150,000", "efficiency": "82.8%"}}, {"id": "*******", "title": "<PERSON> - Partnership Manager", "type": "Individual Employee", "parent": "1.2.3", "employee_id": "EMP-013", "total_bets": 23, "bet_breakdown": {"gos": 1, "los": 6, "np_functions": 10, "input_output_stacks": 5, "subordinate_nsl": 1}, "metrics": {"local_objectives": 1, "personal_bets": 6, "lo_efficiency": "81.5%"}, "financial_summary": {"annual_salary": "$67,000", "value_output": "$165,000", "efficiency": "81.5%"}}, {"id": "*******", "title": "<PERSON> - Market Research Analyst", "type": "Individual Employee", "parent": "1.2.3", "employee_id": "EMP-014", "total_bets": 22, "bet_breakdown": {"gos": 1, "los": 5, "np_functions": 9, "input_output_stacks": 6, "subordinate_nsl": 1}, "metrics": {"local_objectives": 1, "personal_bets": 5, "lo_efficiency": "81.8%"}, "financial_summary": {"annual_salary": "$60,000", "value_output": "$135,000", "efficiency": "81.8%"}}]}}, "bet_framework": {"description": "Binary Entities (BETs) - The Foundation of Structured Execution", "concept": {"origin": "Derived from dual-state nature, mirroring BiTs (Binary Digits) discovered by <PERSON>", "significance": "Just as BiTs quantify and process digital information, BETs quantify and process solutions", "binary_framework": "Like transistors/switches (on/off, open/closed), BETs operate in binary states for precise execution control", "state_transitions": "Potentiality → Reality"}, "entity_classification": {"bets": {"definition": "Binary Entities that actively participate in solution execution and undergo state transitions", "characteristics": ["Absorb information", "Process information", "Generate information", "Participate in functions"], "requirement": "All transformational elements must be BETs", "composition": "GOs + LOs + NP Functions + Input/Output Stacks + Subordinate NSL"}, "unary_entities": {"definition": "Entities serving only as informational references within enterprise", "characteristics": ["Do not actively participate in functions", "Purely informational elements"], "role": "Reference data without transformation capability"}}}, "consolidated_data": {"consolidated_gos": {"value": "45", "description": "Including all sub-level integrations"}, "inter_go_eliminations": {"value": "-12", "description": "Overlapping solution spaces"}, "net_effective_gos": {"value": "33", "description": "After consolidation"}, "consolidated_efficiency": {"value": "91.2%", "description": "Integrated solution delivery"}}, "trends": {"go_evolution": {"value": "+15.2%", "description": "Solution space expansion"}, "lo_optimization": {"value": "+8.7%", "description": "Local solution efficiency"}, "bet_integration": {"value": "+5.3%", "description": "Cross-LO coordination"}, "solution_delivery": {"value": "+3.1%", "description": "Macro-objective achievement"}}, "bet_aggregation_rules": {"total_bet_calculation": "GOs + LOs + NP_Functions + Input_Output_Stacks + Subordinate_NSL", "rollup_logic": {"M1_to_M2": "Sum all M1 employee BETs within team", "M2_to_M3": "Sum all M2 team BETs within department", "M3_to_M4": "Sum all M3 department BETs within organization"}, "validation": {"M4_total": 1356, "M3_sum": 1215, "calculation_note": "M4 total includes additional executive-level coordination BETs"}}, "performance_metrics": {"M4": {"average_bet_efficiency": "88.5%", "go_completion_rate": "89.2%", "lo_integration_rate": "91.5%"}, "M3": {"technology_efficiency": "91.2%", "operations_efficiency": "84.3%", "finance_efficiency": "76.8%"}, "M2": {"engineering_efficiency": "93.5%", "qa_efficiency": "87.2%", "infrastructure_efficiency": "89.6%", "analytics_efficiency": "92.1%"}, "M1": {"john_efficiency": "90.2%", "sarah_efficiency": "87.5%", "mike_efficiency": "45.0%", "emily_efficiency": "94.1%"}}}