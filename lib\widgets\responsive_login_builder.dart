import 'package:flutter/material.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/web/web_login_screen.dart';

class ResponsiveLoginBuilder extends StatelessWidget {
  const ResponsiveLoginBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use web layout for larger screens
        if (constraints.maxWidth >= 860) {
          return const WebLoginScreen();
        }
        // Use mobile layout for smaller screens
        return const LoginScreen();
      },
    );
  }
}
