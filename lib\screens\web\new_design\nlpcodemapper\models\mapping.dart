import 'line_selection.dart';

enum MappingType { entity, attribute, relation, validation, other }

enum MappingLevel {
  line, // For line-level mappings
  word // For word-level mappings
}

class Mapping {
  final String id;
  final String solutionId;
  final String nlpLine;
  final List<LineSelection> javaLines;
  final List<LineSelection> sqlLines;
  final MappingType type;
  final String color; // Hex color code (e.g., "#FF5733")
  final String tag; // Tag to identify what is being mapped
  final String? parentId; // ID of parent mapping (null for line mappings)
  final MappingLevel level; // Whether this is a line or word mapping

  Mapping({
    required this.id,
    required this.solutionId,
    required this.nlpLine,
    required this.javaLines,
    required this.sqlLines,
    required this.type,
    this.color = "#3498DB", // Default to a blue color
    this.tag = "", // Default to empty string
    this.parentId, // Null for line mappings, set for word mappings
    this.level = MappingLevel.line, // Default to line mapping
  });

  // Check if this is a word mapping
  bool get isWordMapping => level == MappingLevel.word;

  // Check if this is a line mapping
  bool get isLineMapping => level == MappingLevel.line;

  // JSON serialization methods
  factory Mapping.fromJson(Map<String, dynamic> json) {
    // Convert javaLines from List<dynamic> to List<LineSelection>
    List<LineSelection> javaLineSelections = [];
    if (json['javaLines'] != null) {
      if (json['javaLines'] is List) {
        // Handle both old format (List<String>) and new format (List<Map>)
        for (var i = 0; i < json['javaLines'].length; i++) {
          var line = json['javaLines'][i];
          if (line is String) {
            // Old format: just a string, use -1 as default lineIndex
            javaLineSelections.add(LineSelection(line, -1));
          } else if (line is Map) {
            // New format: map with content and lineIndex
            javaLineSelections.add(LineSelection(
              line['content'] ?? '',
              line['lineIndex'] ?? -1,
            ));
          }
        }
      }
    }

    // Convert sqlLines from List<dynamic> to List<LineSelection>
    List<LineSelection> sqlLineSelections = [];
    if (json['sqlLines'] != null) {
      if (json['sqlLines'] is List) {
        // Handle both old format (List<String>) and new format (List<Map>)
        for (var i = 0; i < json['sqlLines'].length; i++) {
          var line = json['sqlLines'][i];
          if (line is String) {
            // Old format: just a string, use -1 as default lineIndex
            sqlLineSelections.add(LineSelection(line, -1));
          } else if (line is Map) {
            // New format: map with content and lineIndex
            sqlLineSelections.add(LineSelection(
              line['content'] ?? '',
              line['lineIndex'] ?? -1,
            ));
          }
        }
      }
    }

    return Mapping(
      id: json['id'],
      solutionId: json['solutionId'],
      nlpLine: json['nlpLine'],
      javaLines: javaLineSelections,
      sqlLines: sqlLineSelections,
      type: MappingType.values.firstWhere(
        (e) => e.toString() == 'MappingType.${json['type']}',
        orElse: () => MappingType.other,
      ),
      color: json['color'] ?? "#3498DB", // Use default color if not provided
      tag: json['tag'] ?? "", // Use empty string if not provided
      parentId: json['parentId'], // Null for line mappings
      level: json['level'] == 'word'
          ? MappingLevel.word
          : MappingLevel.line, // Default to line mapping
    );
  }

  Map<String, dynamic> toJson() {
    // Convert javaLines from List<LineSelection> to List<Map>
    List<Map<String, dynamic>> javaLinesJson = javaLines
        .map((lineSelection) => {
              'content': lineSelection.content,
              'lineIndex': lineSelection.lineIndex,
            })
        .toList();

    // Convert sqlLines from List<LineSelection> to List<Map>
    List<Map<String, dynamic>> sqlLinesJson = sqlLines
        .map((lineSelection) => {
              'content': lineSelection.content,
              'lineIndex': lineSelection.lineIndex,
            })
        .toList();

    final Map<String, dynamic> json = {
      'id': id,
      'solutionId': solutionId,
      'nlpLine': nlpLine,
      'javaLines': javaLinesJson,
      'sqlLines': sqlLinesJson,
      'type': type.toString().split('.').last,
      'color': color,
      'tag': tag,
      'level': level.toString().split('.').last,
    };

    // Only include parentId if it's not null
    if (parentId != null) {
      json['parentId'] = parentId;
    }

    return json;
  }
}
