import 'package:flutter/material.dart';
import '../models/tree_node.dart';

class TreeView extends StatefulWidget {
  final TreeStructure treeStructure;
  final Function(TreeNode)? onNodeTap;

  const TreeView({
    super.key,
    required this.treeStructure,
    this.onNodeTap,
  });

  @override
  State<TreeView> createState() => _TreeViewState();
}

class _TreeViewState extends State<TreeView> {
  final Set<String> _expandedNodes = {};

  @override
  void initState() {
    super.initState();
    // Initially expand the root node
    final rootNode = widget.treeStructure.getRootNode();
    if (rootNode != null) {
      _expandedNodes.add(rootNode.id);
    }
  }

  void _toggleNode(String nodeId) {
    setState(() {
      if (_expandedNodes.contains(nodeId)) {
        _expandedNodes.remove(nodeId);
      } else {
        _expandedNodes.add(nodeId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final rootNode = widget.treeStructure.getRootNode();
    if (rootNode == null) {
      return const Center(child: Text('No tree data available'));
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTreeHeader(rootNode),
            _buildTreeNode(rootNode, 0),
          ],
        ),
      ),
    );
  }

  Widget _buildTreeHeader(TreeNode rootNode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8.0),
          topRight: Radius.circular(8.0),
        ),
      ),
      child: Text(
        rootNode.label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18.0,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTreeNode(TreeNode node, int level) {
    final children = widget.treeStructure.getChildrenOf(node.id);
    final hasChildren = children.isNotEmpty;
    final isExpanded = _expandedNodes.contains(node.id);

    // Skip rendering the root node itself, just render its children
    if (node.type == 'root') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            children.map((child) => _buildTreeNode(child, level)).toList(),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: () {
            if (hasChildren) {
              _toggleNode(node.id);
            }
            if (widget.onNodeTap != null) {
              widget.onNodeTap!(node);
            }
          },
          child: Container(
            padding: EdgeInsets.only(
              left: 16.0 * level,
              right: 16.0,
              top: 12.0,
              bottom: 12.0,
            ),
            decoration: BoxDecoration(
              color: _getNodeColor(node),
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1.0,
                ),
              ),
            ),
            child: Row(
              children: [
                if (hasChildren)
                  Icon(
                    isExpanded ? Icons.arrow_drop_down : Icons.arrow_right,
                    color: Colors.grey.shade700,
                  ),
                if (!hasChildren)
                  const SizedBox(width: 24.0), // Spacing for alignment
                const SizedBox(width: 8.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              node.label,
                              style: TextStyle(
                                fontWeight: _getNodeFontWeight(node),
                                fontSize: _getNodeFontSize(node),
                              ),
                            ),
                          ),
                          if (node.metadata != null &&
                              node.metadata!.containsKey('alt'))
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6.0,
                                vertical: 2.0,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              child: Text(
                                'Alt: ${node.metadata!['alt']}',
                                style: TextStyle(
                                  fontSize: 10.0,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ),
                          if (node.metadata != null &&
                              node.metadata!.containsKey('page'))
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6.0,
                                vertical: 2.0,
                              ),
                              margin: const EdgeInsets.only(left: 4.0),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              child: Text(
                                'Page: ${node.metadata!['page']}',
                                style: TextStyle(
                                  fontSize: 10.0,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ),
                          if (node.metadata != null &&
                              node.metadata!.containsKey('task'))
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6.0,
                                vertical: 2.0,
                              ),
                              margin: const EdgeInsets.only(left: 4.0),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade100,
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              child: Text(
                                'Task: ${node.metadata!['task']}',
                                style: TextStyle(
                                  fontSize: 10.0,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        if (isExpanded && hasChildren)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children
                .map((child) => _buildTreeNode(child, level + 1))
                .toList(),
          ),
      ],
    );
  }

  Color _getNodeColor(TreeNode node) {
    switch (node.type) {
      case 'milestone':
        return Colors.blue.shade50;
      case 'action':
        return Colors.white;
      default:
        return Colors.white;
    }
  }

  FontWeight _getNodeFontWeight(TreeNode node) {
    switch (node.type) {
      case 'milestone':
        return FontWeight.bold;
      case 'action':
        return FontWeight.normal;
      default:
        return FontWeight.normal;
    }
  }

  double _getNodeFontSize(TreeNode node) {
    switch (node.type) {
      case 'milestone':
        return 16.0;
      case 'action':
        return 14.0;
      default:
        return 14.0;
    }
  }
}
