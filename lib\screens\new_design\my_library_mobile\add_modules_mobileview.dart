import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/screens/new_design/my_library_mobile/inside_book_solutions_mobile.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/agent_table.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_table.dart';
import 'package:nsl/screens/mobile/new_design/widgets/validation_widgets/role_details_panel_mobile.dart';
import 'package:nsl/utils/navigation_service.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_entity_version_dropdown.dart';
import 'package:nsl/screens/mobile/new_design/widgets/validation_widgets/entity_details_panel_mobile.dart';
import 'package:nsl/screens/mobile/new_design/widgets/comments_bottom_sheet.dart';
import 'package:nsl/screens/web/new_design/widgets/library_side_drawer.dart';
import 'package:provider/provider.dart';

/// Model class for solution items
class SolutionItem {
  final String title;
  final String? bookName; // Optional book name - null means no book name shown

  SolutionItem({
    required this.title,
    this.bookName,
  });
}

/// Model class for module items
class ModuleItem {
  final String name;
  bool isExpanded;
  List<String> solutions; // List of solution titles associated with this module

  ModuleItem({
    required this.name,
    this.isExpanded = true,
    List<String>? solutions,
  }) : solutions = solutions ?? [];

  /// Creates a copy of this ModuleItem with the given fields replaced with new values
  ModuleItem copyWith({
    String? name,
    bool? isExpanded,
    List<String>? solutions,
  }) {
    return ModuleItem(
      name: name ?? this.name,
      isExpanded: isExpanded ?? this.isExpanded,
      solutions: solutions ?? this.solutions,
    );
  }
}

/// Constants for the AddModulesMobileView
class _Constants {
  static const double borderRadius = AppSpacing.xs;
  static const double smallBorderRadius = AppSpacing.xxs;
  static const double iconSize = AppSpacing.lg;
  static const double smallIconSize = AppSpacing.md;
  static const int animationDurationMs = 300;

  // Colors
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardColor = Colors.white;
  static const Color borderColor = Color(0xFFE5E7EB);
  static const Color modulesBackgroundColor = Color(0xFFF0F0F0);
  static const Color textPrimaryColor = Colors.black;
  static const Color textSecondaryColor = Color(0xff242424);

  // Text Styles
  static const TextStyle titleStyle = TextStyle(
    color: textPrimaryColor,
    fontSize: FontManager.s16,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle sectionTitleStyle = TextStyle(
    fontSize: FontManager.s14,
    fontWeight: FontWeight.w500,
    color: textPrimaryColor,
  );
}

class AddModulesMobileView extends StatefulWidget {
  final String? bookTitle;

  const AddModulesMobileView({super.key, this.bookTitle});

  @override
  State<AddModulesMobileView> createState() => _AddModulesMobileViewState();
}

class _AddModulesMobileViewState extends State<AddModulesMobileView>
    with TickerProviderStateMixin {
  // Controllers
  late final TextEditingController _searchController;
  late final TextEditingController _chatController;
  late final TextEditingController _moduleController;
  late final PageController _pageController;

  // Focus nodes
  late final FocusNode _moduleFocusNode;

  // Animation controllers
  late final AnimationController _searchAnimationController;
  late final AnimationController _moduleAnimationController;

  // State variables
  bool _showBookModulesDetail = false;
  bool _showSearchOverlay = false;
  bool _isModulesExpanded = false;
  String _selectedVersion = 'V00012'; // Add state for selected version

  // Navigation state tracking
  String? _pendingNavigation; // 'agent', 'entity', or null

  // Data
  final List<ModuleItem> _createdModules = [];
  final Set<String> _usedSolutions =
      {}; // Track solutions that have been dropped into modules

  // Version dropdown options - static for efficiency
  static const List<String> _versionOptions = ['V00012', 'V00001'];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _setupListeners();
  }

  void _initializeControllers() {
    _searchController = TextEditingController();
    _chatController = TextEditingController();
    _moduleController = TextEditingController();
    _pageController = PageController();
    _moduleFocusNode = FocusNode();
  }

  void _initializeAnimations() {
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: _Constants.animationDurationMs),
      vsync: this,
    );
    _moduleAnimationController = AnimationController(
      duration: const Duration(milliseconds: _Constants.animationDurationMs),
      vsync: this,
    );
  }

  void _setupListeners() {
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    // Debounce search if needed
    setState(() {
      // Rebuild when search text changes
    });
  }

  void _handleBackNavigation(ManualCreationProvider provider) {
    if (_showBookModulesDetail) {
      // Check if we're in a table view
      if (provider.showAgentTableForBook || provider.showEntityTableForBook) {
        // Clear the table view and go back to main view
        provider.clearBookResults();
        setState(() {
          _showBookModulesDetail = false;
          _pendingNavigation = null; // Clear pending navigation
        });
      } else {
        // Go back to the main view
        setState(() {
          _showBookModulesDetail = false;
          _pendingNavigation = null; // Clear pending navigation
        });
      }
    } else {
      // Use Navigator.pop() to go back to the previous screen
      Navigator.pop(context);
    }
  }

  String _getAppBarTitle(ManualCreationProvider provider) {
    if (_showBookModulesDetail) {
      if (provider.showAgentTableForBook) {
        return 'Agent';
      } else if (provider.showEntityTableForBook) {
        return 'Objects';
      } else {
        return widget.bookTitle ?? '';
      }
    }
    return widget.bookTitle ?? '';
  }

  void _navigateToBookModules() {
    setState(() {
      _showBookModulesDetail = true;
    });
  }

  void _sendMessage() {
    if (_chatController.text.trim().isNotEmpty) {
      debugPrint('Sending message: ${_chatController.text}');
      _chatController.clear();
    }
  }

  void _handleAgentNavigation(BuildContext context) {
    // Set pending navigation and navigate immediately for responsive UI
    setState(() {
      _showBookModulesDetail = true;
      _pendingNavigation = 'agent';
    });

    // Start loading agent data in the background
    Provider.of<ManualCreationProvider>(context, listen: false)
        .handleAgentValidationForBook()
        .then((_) {
      // Clear pending navigation once data is loaded
      if (mounted) {
        setState(() {
          _pendingNavigation = null;
        });
      }
    });
  }

  void _handleEntityNavigation(BuildContext context) {
    // Set pending navigation and navigate immediately for responsive UI
    setState(() {
      _showBookModulesDetail = true;
      _pendingNavigation = 'entity';
    });

    // Start loading entity data in the background
    Provider.of<ManualCreationProvider>(context, listen: false)
        .handleEntityValidationForBook()
        .then((_) {
      // Clear pending navigation once data is loaded
      if (mounted) {
        setState(() {
          _pendingNavigation = null;
        });
      }
    });
  }

  Widget _buildAppBarActions(ManualCreationProvider provider) {
    // Show chat icon for Agent and Entity table screens
    if (_showBookModulesDetail &&
        (provider.showAgentTableForBook || provider.showEntityTableForBook)) {
      return Container(
        margin: const EdgeInsets.only(right: AppSpacing.md),
        child: GestureDetector(
          onTap: () => _showChatBottomSheet(context, provider),
          child: SvgPicture.asset(
            'assets/images/comments_icon.svg',
            width: _Constants.iconSize,
            height: _Constants.iconSize,
          ),
        ),
      );
    }

    // Default action icon for other screens
    return Container(
      margin: const EdgeInsets.only(right: AppSpacing.xs),
      child: IconButton(
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
        icon: Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
          child: Icon(
            Icons.login,
            size: _Constants.iconSize,
            color: Colors.grey.shade600,
          ),
        ),
        onPressed: () => _showLibraryBottomSheet(context),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _Constants.backgroundColor,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: _Constants.backgroundColor,
        elevation: 0,
        titleSpacing: 0, // Remove default spacing between leading and title
        leading: Consumer<ManualCreationProvider>(
          builder: (context, manualCreationProvider, child) {
            return IconButton(
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(
                Icons.chevron_left,
                color: _Constants.textPrimaryColor,
                size: _Constants.iconSize,
              ),
              onPressed: () => _handleBackNavigation(manualCreationProvider),
            );
          },
        ),
        title: Consumer<ManualCreationProvider>(
          builder: (context, manualCreationProvider, child) {
            return Text(
              _getAppBarTitle(manualCreationProvider),
              style: _Constants.titleStyle,
            );
          },
        ),
        actions: [
          Consumer<ManualCreationProvider>(
            builder: (context, provider, child) =>
                _buildAppBarActions(provider),
          ),
        ],
      ),
      body: Column(
        children: [
          // Content area
          Expanded(
            child: _showBookModulesDetail
                ? Consumer<ManualCreationProvider>(
                    builder: (context, provider, child) {
                      // Show role details panel as bottom sheet when provider state changes
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (provider.showSidePanel &&
                            provider.selectedRole != null &&
                            !_isBottomSheetShowing) {
                          _showRoleDetailsBottomSheet(context, provider);
                        }
                      });

                      // Check if we have a pending navigation (immediate loading state)
                      if (_pendingNavigation != null) {
                        return _buildLoadingContent();
                      }

                      // Check if we're loading data (fallback loading state)
                      if (provider.isValidating ||
                          provider.isValidatingEntity) {
                        return _buildLoadingContent();
                      }

                      return provider.showAgentTableForBook ||
                              provider.showEntityTableForBook
                          ? _buildTableContent(context, provider)
                          : _buildBookModulesDetailContent();
                    },
                  )
                : SingleChildScrollView(
                    child: _buildMainContent(),
                  ),
          ),
          // Chat input field at the bottom
          _buildChatInputField(),
        ],
      ),
    );
  }

  Widget _buildChatInputField() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
          AppSpacing.md, 0, AppSpacing.md, AppSpacing.md),
      child: ChatInputField(
        chatController: _chatController,
        sendMessage: _sendMessage,
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: _Constants.cardColor,
        borderRadius: BorderRadius.circular(_Constants.borderRadius),
        border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
      ),
      child: const Center(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.xl),
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }

  Widget _buildTableContent(
      BuildContext context, ManualCreationProvider provider) {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: _Constants.cardColor,
        borderRadius: BorderRadius.circular(_Constants.borderRadius),
        border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
      ),
      child: provider.showAgentTableForBook
          ? AgentTable(
              provider: provider,
              onRoleSelected: (role) => provider.showRoleDetailsPanel(role),
            )
          : EntityTable(
              provider: provider,
              onEntitySelected: (entity) =>
                  _showEntityDetailsModal(context, entity),
            ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        // Add Modules Section
        GestureDetector(
          onTap: _navigateToBookModules,
          child: Container(
            margin: const EdgeInsets.fromLTRB(
                AppSpacing.md, AppSpacing.md, AppSpacing.md, AppSpacing.xs),
            decoration: BoxDecoration(
              color: _Constants.cardColor,
              borderRadius: BorderRadius.circular(_Constants.borderRadius),
              border: Border.all(color: _Constants.borderColor, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.md, vertical: AppSpacing.sm),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    child: SvgPicture.asset(
                      'assets/images/book_nsl.svg',
                      width: _Constants.smallIconSize,
                      height: _Constants.smallIconSize,
                      colorFilter: ColorFilter.mode(
                        Colors.grey[600]!,
                        BlendMode.srcIn,
                      ),
                    ),
                    // decoration: BoxDecoration(
                    //   border: Border.all(color: Colors.grey[400]!, width: 1),
                    //   borderRadius: BorderRadius.circular(2),
                    // ),
                  ),
                  const SizedBox(width: AppSpacing.xs),
                  const Text(
                    'Add Modules',
                    style: TextStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward,
                    color: Colors.blue,
                    size: _Constants.smallIconSize,
                  ),
                ],
              ),
            ),
          ),
        ),
        // Stats Section
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => _handleAgentNavigation(context),
                  child: Container(
                    margin: const EdgeInsets.only(right: AppSpacing.xxs),
                    decoration: BoxDecoration(
                      color: _Constants.cardColor,
                      borderRadius:
                          BorderRadius.circular(_Constants.borderRadius),
                      border:
                          Border.all(color: _Constants.borderColor, width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.sm, vertical: AppSpacing.sm),
                      child: _buildStatItem('Agent', '3'),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => _handleEntityNavigation(context),
                  child: Container(
                    margin:
                        const EdgeInsets.symmetric(horizontal: AppSpacing.xxs),
                    decoration: BoxDecoration(
                      color: _Constants.cardColor,
                      borderRadius:
                          BorderRadius.circular(_Constants.borderRadius),
                      border:
                          Border.all(color: _Constants.borderColor, width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.sm, vertical: AppSpacing.sm),
                      child: _buildStatItem('Objects', '12'),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    NavigationService.push(const InsideBookSolutionMobile());
                  },
                  child: Container(
                    margin: const EdgeInsets.only(left: AppSpacing.xxs),
                    decoration: BoxDecoration(
                      color: _Constants.cardColor,
                      borderRadius:
                          BorderRadius.circular(_Constants.borderRadius),
                      border:
                          Border.all(color: _Constants.borderColor, width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.sm, vertical: AppSpacing.sm),
                      child: _buildStatItem('Solutions', '15'),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.xs),

        // Create Solution Section
        Container(
          margin: const EdgeInsets.fromLTRB(
              AppSpacing.md, 0, AppSpacing.md, AppSpacing.md),
          decoration: BoxDecoration(
            color: _Constants.cardColor,
            borderRadius: BorderRadius.circular(_Constants.borderRadius),
            border: Border.all(color: _Constants.borderColor, width: 1),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Create a solution of a Product management',
                  style: TextStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: AppSpacing.xxs),
                Text(
                  'Last Message 16 hours ago',
                  style: TextStyle(
                    fontSize: FontManager.s12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: AppSpacing.md),
                Row(
                  children: [
                    CustomEntityVersionDropdown(
                      selectedVersion: _selectedVersion,
                      versions: _versionOptions,
                      onVersionSelected: (String version) {
                        setState(() {
                          _selectedVersion = version;
                        });
                        debugPrint('Selected version: $version');
                      },
                    ),
                    const Spacer(),
                    Text(
                      '22/04/2025',
                      style: TextStyle(
                        fontSize: FontManager.s12,
                        color: Color(0xff5C5C5C),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBookModulesDetailContent() {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: _Constants.cardColor,
        borderRadius: BorderRadius.circular(_Constants.borderRadius),
        border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title, count, and search
            Padding(
              padding: const EdgeInsets.fromLTRB(
                  AppSpacing.md, AppSpacing.md, AppSpacing.md, 0),
              child: _buildDragSolutionHeader(),
            ),
            // Content area
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: AppSpacing.sm),
                  // Drag and drop instruction
                  // Container(
                  //   padding: const EdgeInsets.all(AppSpacing.xxs),
                  //   margin: const EdgeInsets.only(bottom: AppSpacing.sm),
                  //   decoration: BoxDecoration(
                  //     color: Colors.blue.withValues(alpha: 0.1),
                  //     borderRadius: BorderRadius.circular(8),
                  //     border:
                  //         Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                  //   ),
                  //   child: Row(
                  //     children: [
                  //       Icon(
                  //         Icons.info_outline,
                  //         color: Colors.blue,
                  //         size: 20,
                  //       ),
                  //       const SizedBox(width: AppSpacing.xs),
                  //       Expanded(
                  //         child: Text(
                  //           'Drag solution cards to modules below to associate them',
                  //           style: TextStyle(
                  //             fontSize: FontManager.s12,
                  //             color: Colors.blue.shade700,
                  //             fontWeight: FontWeight.w500,
                  //           ),
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // Solution Cards - Natural height container
                  Column(
                    children: [
                      _buildSolutionContent(),
                      const SizedBox(
                          height: AppSpacing
                              .md), // 16px gap between grid and pagination
                      // Carousel Indicators
                      _buildCarouselIndicators(),
                      const SizedBox(height: AppSpacing.size18),
                    ],
                  ),
                  // Horizontal Divider
                  // Container(
                  //   height: 1,
                  //   color: const Color(0xFFE5E7EB),
                  // ),
                  // const SizedBox(height: AppSpacing.size18),
                  // Modules Section - Now uses intrinsic height
                  _buildModulesSection(),
                  const SizedBox(height: AppSpacing.size18), // Bottom padding
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDragSolutionHeader() {
    // Do not remove the commented code below
    // List<String> allSolutions = _getAllSolutions();
    // int solutionCount = allSolutions.length;

    return AnimatedContainer(
      duration: const Duration(milliseconds: _Constants.animationDurationMs),
      height: _showSearchOverlay ? 48 : 24,
      child: Stack(
        children: [
          // Default header (title + count + search icon)
          if (!_showSearchOverlay)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Text(
                      'Drag Your Solution',
                      style: TextStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    // Do not remove the commented code below
                    // const SizedBox(width: 8),
                    // Container(
                    //   padding: const EdgeInsets.symmetric(
                    //       horizontal: 4, vertical: 2),
                    //   decoration: BoxDecoration(
                    //     color: Colors.black,
                    //     borderRadius: BorderRadius.circular(12),
                    //   ),
                    //   child: Text(
                    //     '$solutionCount',
                    //     style: const TextStyle(
                    //       color: Colors.white,
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showSearchOverlay = true;
                    });
                  },
                  child: Icon(
                    Icons.search,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          // Search overlay
          if (_showSearchOverlay)
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: AppSpacing.md),
                      child: TextField(
                        controller: _searchController,
                        autofocus: true,
                        decoration: InputDecoration(
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          hintText: 'Search solutions...',
                          border: InputBorder.none,
                          hintStyle: TextStyle(
                              fontSize: FontManager.s14,
                              color: Colors.grey[500]),
                          isDense: true,
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: AppSpacing.sm),
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: Colors.grey[600],
                    ),
                    onPressed: () {
                      setState(() {
                        _showSearchOverlay = false;
                        _searchController.clear();
                      });
                    },
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Extract solution data into a separate method for reusability
  List<SolutionItem> _getAllSolutions() {
    // Simulate API data with JSON-like structure
    // Each solution has a title and book presence information
    final List<Map<String, dynamic>> apiSolutionsData = [
      {'title': 'Solutions PM with V01/V02', 'bookName': 'Project Management'},
      {'title': 'Solutions PM with V02/V01', 'bookName': null},
      {
        'title': 'Solutions PM with V03/V01',
        'bookName': 'Product Management Guide'
      },
      {'title': 'Solutions PM with V04/V01', 'bookName': null},
      {'title': 'Solutions PM with V05/V01', 'bookName': 'Business Strategy'},
      {'title': 'Solutions PM with V06/V01', 'bookName': null},
      {
        'title': 'Solutions PM with V07/V01',
        'bookName': 'Digital Marketing Essentials'
      },
      {'title': 'Solutions PM with V08/V01', 'bookName': null},
      {
        'title': 'Solutions PM with V09/V01',
        'bookName': 'User Experience Design'
      },
      {'title': 'Solutions PM with V10/V01', 'bookName': null},
      {'title': 'Solutions PM with V11/V01', 'bookName': 'Data Analytics'},
      {'title': 'Solutions PM with V12/V01', 'bookName': null},
      {
        'title': 'Solutions PM with V13/V01',
        'bookName': 'Software Development'
      },
      {'title': 'Solutions PM with V14/V01', 'bookName': null},
      {
        'title': 'Solutions PM with V15/V01',
        'bookName': 'Ecommerce Fundamentals'
      },
      {'title': 'Solutions PM with V16/V01', 'bookName': null},
      {'title': 'Solutions PM with V17/V01', 'bookName': 'Agile Methodology'},
      {'title': 'Solutions PM with V18/V01', 'bookName': null},
      {
        'title': 'Solutions PM with V19/V01',
        'bookName': 'Leadership Principles'
      },
      {'title': 'Solutions PM with V20/V01', 'bookName': null},
    ];

    // Convert API data to SolutionItem objects
    return apiSolutionsData.map((data) {
      return SolutionItem(
        title: data['title'] as String,
        bookName: data['bookName'] as String?,
      );
    }).toList();
  }

  Widget _buildSolutionContent() {
    List<SolutionItem> allSolutions = _getAllSolutions();

    // Filter out used solutions (solutions that have been dropped into modules)
    allSolutions = allSolutions
        .where((solution) => !_usedSolutions.contains(solution.title))
        .toList();

    // Filter solutions based on search query
    if (_searchController.text.isNotEmpty) {
      allSolutions = allSolutions
          .where((solution) => solution.title
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    }

    // If 4 or fewer cards, show simple grid without carousel
    if (allSolutions.length <= 4) {
      return _buildSimpleSolutionGrid(allSolutions);
    }

    // If more than 4 cards, show carousel
    return _buildSolutionCarousel(allSolutions);
  }

  // Helper method to calculate responsive card width
  double _calculateCardWidth(double availableWidth) {
    double spacing = AppSpacing.sm;
    double cardWidth = 148.0; // Target width for larger screens

    // Responsive card width calculation for different screen sizes
    if (availableWidth <= 375) {
      // Small screens (iPhone SE, etc.)
      cardWidth = (availableWidth - spacing) / 2;
    } else if (availableWidth <= 414) {
      // Medium screens (iPhone 6/7/8 Plus, etc.)
      cardWidth = (availableWidth - spacing) / 2;
    } else if (availableWidth <= 768) {
      // Tablet screens (iPad Mini, etc.)
      cardWidth = (availableWidth - spacing) / 2;
    } else {
      // Large tablet screens - use target width or adjust if needed
      cardWidth = 200.0 < (availableWidth - spacing) / 2
          ? 200.0
          : (availableWidth - spacing) / 2;
    }

    return cardWidth;
  }

  Widget _buildSimpleSolutionGrid(List<SolutionItem> solutions) {
    if (solutions.isEmpty) {
      return const Center(
        child: Text(
          'No solutions found',
          style: TextStyle(
            fontSize: FontManager.s14,
            color: Colors.grey,
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        double cardWidth = _calculateCardWidth(constraints.maxWidth);
        return _buildSolutionGrid(solutions, cardWidth: cardWidth);
      },
    );
  }

  Widget _buildSolutionCarousel(List<SolutionItem> allSolutions) {
    if (allSolutions.isEmpty) {
      return const Center(
        child: Text(
          'No solutions found',
          style: TextStyle(
            fontSize: FontManager.s14,
            color: Colors.grey,
          ),
        ),
      );
    }

    // Divide the flat list into pages of 4 cards each
    List<List<SolutionItem>> solutionPages = [];
    for (int i = 0; i < allSolutions.length; i += 4) {
      int end = (i + 4 < allSolutions.length) ? i + 4 : allSolutions.length;
      solutionPages.add(allSolutions.sublist(i, end));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        double cardWidth = _calculateCardWidth(constraints.maxWidth);
        double spacing = AppSpacing.sm;

        // Calculate dynamic height based on content in all solution pages
        double maxCardHeight = 0;
        for (List<SolutionItem> page in solutionPages) {
          for (SolutionItem solution in page) {
            double cardHeight = _calculateCardHeight(solution.title, cardWidth);
            if (cardHeight > maxCardHeight) {
              maxCardHeight = cardHeight;
            }
          }
        }

        // Grid height = (max card height * 2 rows) + spacing between them
        double gridHeight = (maxCardHeight * 2) + spacing;

        return SizedBox(
          height: gridHeight,
          child: PageView.builder(
            controller: _pageController,
            itemCount: solutionPages.length,
            itemBuilder: (context, pageIndex) {
              return _buildSolutionGrid(solutionPages[pageIndex],
                  cardWidth: cardWidth);
            },
          ),
        );
      },
    );
  }

  Widget _buildCarouselIndicators() {
    // Get filtered solutions
    List<SolutionItem> allSolutions = _getAllSolutions();

    // Filter out used solutions (solutions that have been dropped into modules)
    allSolutions = allSolutions
        .where((solution) => !_usedSolutions.contains(solution.title))
        .toList();

    // Filter solutions based on search query
    if (_searchController.text.isNotEmpty) {
      allSolutions = allSolutions
          .where((solution) => solution.title
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    }

    // Only show indicators if there are more than 4 cards (carousel mode)
    if (allSolutions.length <= 4) {
      return const SizedBox(
          height: AppSpacing.xs); // Minimal spacing when no indicators
    }

    int totalPages = (allSolutions.length / 4).ceil(); // 4 cards per page

    return Center(
      child: SmoothPageIndicator(
        controller: _pageController,
        count: totalPages > 1 ? totalPages : 1,
        effect: WormEffect(
            dotHeight: 8,
            dotWidth: 8,
            spacing: 4,
            activeDotColor: Colors.grey.shade600,
            dotColor: Colors.grey.shade600,
            strokeWidth: 1,
            paintStyle: PaintingStyle.stroke),
      ),
    );
  }

  // Helper method to calculate dynamic card height based on content
  double _calculateCardHeight(String title, double cardWidth) {
    // Base heights for different sections
    const double topPadding = AppSpacing.sm;
    const double bottomPadding = AppSpacing.xxs;
    const double bottomSectionHeight =
        AppSpacing.xl; // Height for "Book name" section (32px)
    const double fontSize = FontManager.s12;
    const double lineHeight = 1.17;
    const double horizontalPadding = AppSpacing.lg; // 24px (12 * 2)

    // Calculate text height for up to 2 lines
    final textPainter = TextPainter(
      text: TextSpan(
        text: title,
        style: const TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w400,
          height: lineHeight,
        ),
      ),
      maxLines: 2,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: cardWidth - horizontalPadding);
    final textHeight = textPainter.size.height;

    // Total height = top padding + text height + bottom padding + bottom section
    return topPadding + textHeight + bottomPadding + bottomSectionHeight;
  }

  Widget _buildSolutionGrid(List<SolutionItem> solutions, {double? cardWidth}) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double spacing = AppSpacing.sm; // 12px spacing
        double actualCardWidth =
            cardWidth ?? _calculateCardWidth(constraints.maxWidth);

        // Calculate dynamic height based on the longest title in current solutions
        double maxCardHeight = 0;
        for (SolutionItem solution in solutions) {
          double cardHeight =
              _calculateCardHeight(solution.title, actualCardWidth);
          if (cardHeight > maxCardHeight) {
            maxCardHeight = cardHeight;
          }
        }

        // Calculate dynamic aspect ratio
        double dynamicAspectRatio = actualCardWidth / maxCardHeight;

        return GridView.builder(
          padding: EdgeInsets.zero, // Remove default padding
          shrinkWrap: true, // Important: allows grid to size itself
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: dynamicAspectRatio, // Use dynamic aspect ratio
          ),
          itemCount: solutions.length,
          itemBuilder: (context, index) => _buildSolutionCard(solutions[index]),
        );
      },
    );
  }

  Widget _buildSolutionCard(SolutionItem solution) {
    // Calculate dynamic height for feedback using the existing method
    final double feedbackHeight = _calculateCardHeight(solution.title, 148);

    return LongPressDraggable<String>(
      data: solution.title,
      delay: const Duration(milliseconds: 300), // 300ms long press delay
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 148, // Fixed width for feedback
          height: feedbackHeight, // Dynamic height for feedback
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.blue, width: 2.0),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main content area (2/3 of the height)
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(
                      AppSpacing.sm, AppSpacing.sm, AppSpacing.sm, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        solution.title,
                        style: const TextStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontWeight.w400,
                          height: 1.17,
                          color: Color(0xff242424),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
              // Bottom section with light blue background (1/3 of the height)
              // Only show if bookName is not null
              if (solution.bookName != null)
                Expanded(
                  flex: 1,
                  child: Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      color: Color(0xFFE3F2FD), // Light blue background
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: AppSpacing.sm, right: AppSpacing.sm),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          solution.bookName!,
                          style: const TextStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontWeight.normal,
                            color: Color(0xff242424),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main content area (2/3 of the height)
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(
                      AppSpacing.sm, AppSpacing.sm, AppSpacing.sm, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        solution.title,
                        style: const TextStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontWeight.w400,
                          height: 1.17,
                          color: Color(0xff242424),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
              // Bottom section with light blue background (1/3 of the height)
              // Only show if bookName is not null
              if (solution.bookName != null)
                Expanded(
                  flex: 1,
                  child: Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      color: Color(0xFFE3F2FD), // Light blue background
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: AppSpacing.sm, right: AppSpacing.sm),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          solution.bookName!,
                          style: const TextStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontWeight.normal,
                            color: Color(0xff242424),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      child: GestureDetector(
        // onLongPress: () => _showSolutionPopupMenu(context, solution.title),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main content area (2/3 of the height)
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(
                      AppSpacing.sm, AppSpacing.sm, AppSpacing.sm, 0),
                  // padding: const EdgeInsets.all(AppSpacing.sm),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Solution icon placeholder
                      // Container(
                      //   width: 32,
                      //   height: 32,
                      //   decoration: BoxDecoration(
                      //     color: Colors.grey.shade100,
                      //     borderRadius: BorderRadius.circular(4),
                      //   ),
                      //   child: Icon(
                      //     Icons.description_outlined,
                      //     size: 20,
                      //     color: Colors.grey.shade600,
                      //   ),
                      // ),
                      // const SizedBox(height: 8),
                      Text(
                        solution.title,
                        style: const TextStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontWeight.w400,
                          height: 1.17,
                          color: Color(0xff242424),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
              // Bottom section with light blue background (1/3 of the height)
              // Only show if bookName is not null
              if (solution.bookName != null)
                Expanded(
                  flex: 1,
                  child: Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      color: Color(0xFFE3F2FD), // Light blue background
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: AppSpacing.sm, right: AppSpacing.sm),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          solution.bookName!,
                          style: const TextStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontWeight.normal,
                            color: Color(0xff242424),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // void _showSolutionPopupMenu(BuildContext context, String title) {
  //   showDialog(
  //     context: context,
  //     barrierColor: Colors.transparent,
  //     useSafeArea: false,
  //     builder: (BuildContext context) {
  //       return _SolutionModalView(
  //         title: title,
  //         onClose: () => Navigator.of(context).pop(),
  //         createdModules: createdModules,
  //         onModuleAdded: (ModuleItem module) {
  //           setState(() {
  //             createdModules.add(module);
  //           });
  //         },
  //         onModuleToggled: (int index) {
  //           setState(() {
  //             createdModules[index].isExpanded =
  //                 !createdModules[index].isExpanded;
  //           });
  //         },
  //       );
  //     },
  //   );
  // }

  // Track if bottom sheet is currently showing to prevent multiple instances
  bool _isBottomSheetShowing = false;
  DateTime? _lastBottomSheetOpenTime;

  void _showRoleDetailsBottomSheet(
      BuildContext context, ManualCreationProvider provider) {
    // Prevent multiple bottom sheets from opening
    if (_isBottomSheetShowing) {
      return;
    }

    // Add debounce mechanism to prevent rapid successive calls
    final now = DateTime.now();
    if (_lastBottomSheetOpenTime != null &&
        now.difference(_lastBottomSheetOpenTime!).inMilliseconds < 500) {
      return;
    }

    _isBottomSheetShowing = true;
    _lastBottomSheetOpenTime = now;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.95,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // RoleDetailsPanelMobile handles its own header and close button
              Expanded(
                child: RoleDetailsPanelMobile(
                  role: provider.selectedRole!,
                  onClose: () {
                    Navigator.pop(context);
                    provider.hideSidePanel();
                  },
                  showLegacySections: false,
                  users: provider.extractedUsers,
                ),
              ),
            ],
          ),
        ),
      ),
    ).whenComplete(() {
      // Reset the flag and timestamp, ensure provider state is cleared when bottom sheet is dismissed
      _isBottomSheetShowing = false;
      _lastBottomSheetOpenTime = null;
      provider.hideSidePanel();
    });
  }

  void _showEntityDetailsModal(BuildContext context, dynamic entity) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.95,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // EntityDetailsPanelMobile handles its own header and close button
              Expanded(
                child: EntityDetailsPanelMobile(
                  entity: entity,
                  onClose: () {
                    Navigator.pop(context);
                  },
                  globalEntityElements: const {},
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showChatBottomSheet(
      BuildContext context, ManualCreationProvider provider) {
    String? chatContext;

    // Determine chat context based on current screen
    if (provider.showAgentTableForBook) {
      chatContext = 'agents and roles';
    } else if (provider.showEntityTableForBook) {
      chatContext = 'entities and objects';
    } else {
      chatContext = 'the system';
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => CommentsBottomSheet(
          chatController: _chatController,
          onSendMessage: _sendMessage,
          context: chatContext,
        ),
      ),
    );
  }

  void _showLibraryBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(
                    AppSpacing.lg, AppSpacing.sm, AppSpacing.lg, AppSpacing.md),
                child: Row(
                  children: [
                    const Text(
                      'Library Overview',
                      style: TextStyle(
                        fontSize: FontManager.s18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              // Library content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: LibrarySideDrawer(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    IconData iconData;
    switch (label) {
      case 'Agent':
        iconData = Icons.group_outlined;
        break;
      case 'Objects':
        iconData = Icons.language_outlined;
        break;
      case 'Solutions':
        iconData = Icons.account_tree_outlined;
        break;
      default:
        iconData = Icons.circle_outlined;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              iconData,
              size: 14,
              color: Colors.black87,
            ),
            const SizedBox(width: AppSpacing.xxs),
            Text(
              value,
              style: const TextStyle(
                fontSize: FontManager.s16,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: FontManager.s12,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(width: AppSpacing.xxs),
            Icon(
              Icons.arrow_forward,
              color: Colors.blue,
              size: 14,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModulesSection() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Single expandable modules container
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: _Constants.modulesBackgroundColor,
              borderRadius: BorderRadius.circular(_Constants.borderRadius),
            ),
            child: AnimatedSize(
              duration:
                  const Duration(milliseconds: _Constants.animationDurationMs),
              curve: Curves.easeInOut,
              child: Column(
                children: [
                  // Modules header with + icon (always visible)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.md, vertical: AppSpacing.sm),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Modules',
                          style: _Constants.sectionTitleStyle,
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _isModulesExpanded = !_isModulesExpanded;
                            });
                            if (_isModulesExpanded) {
                              // Request focus after the animation completes
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                _moduleFocusNode.requestFocus();
                              });
                            }
                          },
                          child: SizedBox(
                            width: _Constants.iconSize,
                            height: _Constants.iconSize,
                            child: Icon(
                              Icons.add,
                              size: 20,
                              color: _Constants.textPrimaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Display created modules inside the main container
                  if (_createdModules.isNotEmpty) ...[
                    ..._createdModules.asMap().entries.map((entry) {
                      int index = entry.key;
                      ModuleItem module = entry.value;
                      return Padding(
                        padding: const EdgeInsets.fromLTRB(
                            AppSpacing.md, 0, AppSpacing.md, AppSpacing.xs),
                        child: _buildCreatedModuleItemInside(module, index),
                      );
                    }),
                  ],
                  // Expandable input section inside the same container
                  if (_isModulesExpanded) _buildExpandedModulesInputInside(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedModulesInputInside() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.fromLTRB(
          AppSpacing.md, 0, AppSpacing.md, AppSpacing.md),
      decoration: BoxDecoration(
        color: _Constants.cardColor,
        borderRadius: BorderRadius.circular(_Constants.borderRadius),
        border: Border.all(
          color: _Constants.borderColor,
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // + Modules text - centered
          const Text(
            '+ Modules',
            style: _Constants.sectionTitleStyle,
          ),
          const SizedBox(height: AppSpacing.xs),
          // Input field and buttons wrapped in a bordered container
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: _Constants.borderColor,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(_Constants.smallBorderRadius),
            ),
            padding: const EdgeInsets.all(AppSpacing.sm),
            child: Column(
              children: [
                // Input field
                TextField(
                  controller: _moduleController,
                  focusNode: _moduleFocusNode,
                  decoration: InputDecoration(
                    hintText: 'Type here',
                    hintStyle: TextStyle(
                      fontSize: FontManager.s14,
                      color: Color(0xffBEBEBE),
                    ),
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color(0xFFE5E7EB),
                        width: 1,
                      ),
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color(0xFFE5E7EB),
                        width: 1,
                      ),
                    ),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color(0xFF707070),
                        width: 1,
                      ),
                    ),
                    isDense: true,
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.xs),
                  ),
                  style: const TextStyle(
                    fontSize: FontManager.s14,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Cancel button
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isModulesExpanded = false;
                          _moduleController.clear();
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.sm, vertical: AppSpacing.xs),
                        decoration: BoxDecoration(
                          color: Colors.grey[600],
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: FontManager.s14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    // Add button
                    GestureDetector(
                      onTap: () {
                        if (_moduleController.text.trim().isNotEmpty) {
                          // Add the new module to the list
                          setState(() {
                            _createdModules.add(ModuleItem(
                              name: _moduleController.text.trim(),
                            ));
                            _isModulesExpanded = false;
                            _moduleController.clear();
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.md, vertical: AppSpacing.xs),
                        decoration: BoxDecoration(
                          color: Colors.grey[600],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Add',
                          style: TextStyle(
                            fontSize: FontManager.s14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreatedModuleItemInside(ModuleItem module, int index) {
    return DragTarget<String>(
      onAcceptWithDetails: (details) {
        // Find the solution item to check if it has a book name
        final solutionTitle = details.data;
        final allSolutions = _getAllSolutions();
        final droppedSolution = allSolutions.firstWhere(
          (solution) => solution.title == solutionTitle,
          orElse: () => SolutionItem(title: solutionTitle),
        );

        // Check if solution is already in a book (has book name)
        if (droppedSolution.bookName != null &&
            droppedSolution.bookName!.isNotEmpty) {
          // Show error popup
          _showSolutionInUseDialog(droppedSolution.bookName!);
          return;
        }

        setState(() {
          // Add the solution to this module if it's not already added
          if (!module.solutions.contains(details.data)) {
            module.solutions.add(details.data);
            // Add to used solutions set to remove from available solutions
            _usedSolutions.add(details.data);
          }
        });

        // Show a snackbar to confirm the drop
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Solution "${details.data}" added to module "${module.name}"'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      },
      builder: (context, candidateData, rejectedData) {
        // Change appearance when dragging over
        bool isHovering = candidateData.isNotEmpty;

        return Container(
          decoration: BoxDecoration(
            color: isHovering
                ? Colors.blue.withValues(alpha: 0.1)
                : _Constants.cardColor,
            borderRadius: BorderRadius.circular(_Constants.smallBorderRadius),
            border: Border.all(
              color: isHovering ? Colors.blue : _Constants.borderColor,
              width: isHovering ? 2.0 : 0.5,
            ),
          ),
          child: Column(
            children: [
              // Main module row
              InkWell(
                onTap: () {
                  setState(() {
                    module.isExpanded = !module.isExpanded;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.md, vertical: AppSpacing.sm),
                  child: Row(
                    children: [
                      // Dropdown arrow
                      AnimatedRotation(
                        turns: module.isExpanded ? 0.25 : 0.0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.chevron_right,
                          size: 20,
                          color: _Constants.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      // Module name
                      Expanded(
                        child: Text(
                          module.name,
                          style: const TextStyle(
                            fontSize: FontManager.s14,
                            color: _Constants.textSecondaryColor,
                          ),
                        ),
                      ),
                      // Show solution count if any solutions are added
                      // if (module.solutions.isNotEmpty)
                      //   Container(
                      //     padding: const EdgeInsets.symmetric(
                      //         horizontal: AppSpacing.xs, vertical: 2),
                      //     decoration: BoxDecoration(
                      //       color: Colors.blue,
                      //       borderRadius: BorderRadius.circular(10),
                      //     ),
                      //     child: Text(
                      //       '${module.solutions.length}',
                      //       style: const TextStyle(
                      //         color: Colors.white,
                      //         fontSize: FontManager.s12,
                      //         fontWeight: FontWeight.w500,
                      //       ),
                      //     ),
                      //   ),
                    ],
                  ),
                ),
              ),
              // Show solutions when expanded
              if (module.isExpanded && module.solutions.isNotEmpty)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.fromLTRB(
                      AppSpacing.md, 0, AppSpacing.md, AppSpacing.sm),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...module.solutions.map((solution) => Padding(
                            padding:
                                const EdgeInsets.only(bottom: AppSpacing.xs),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpacing.sm,
                                  vertical: AppSpacing.xs),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF8F9FA),
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: const Color(0xFFD0D0D0),
                                  width: 0.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0x339B9B9B),
                                    offset: const Offset(0, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      solution,
                                      style: const TextStyle(
                                        fontSize: FontManager.s14,
                                        color: _Constants.textSecondaryColor,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const SizedBox(width: AppSpacing.xs),
                                  // Remove button
                                  InkWell(
                                    onTap: () {
                                      _showRemoveSolutionDialog(
                                          module, solution);
                                    },
                                    borderRadius: BorderRadius.circular(4),
                                    child: Padding(
                                      padding: const EdgeInsets.all(2),
                                      child: const Icon(
                                        Icons.close,
                                        size: 18,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  // Commented out sub module functionality
  // Widget _buildAddSubModuleButton(ModuleItem module, int index) {
  //   return InkWell(
  //     onTap: () {
  //       setState(() {
  //         module.isSubModuleInputExpanded = !module.isSubModuleInputExpanded;
  //       });
  //       // Request focus after the animation completes if expanding
  //       if (module.isSubModuleInputExpanded) {
  //         WidgetsBinding.instance.addPostFrameCallback((_) {
  //           // Ensure we have a focus node for this module
  //           if (!subModuleFocusNodes.containsKey(index)) {
  //             subModuleFocusNodes[index] = FocusNode();
  //           }
  //           subModuleFocusNodes[index]?.requestFocus();
  //         });
  //       }
  //     },
  //     child: Container(
  //       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 11.5),
  //       child: Row(
  //         children: [
  //           Icon(
  //             Icons.add,
  //             size: 16,
  //             color: Colors.black,
  //           ),
  //           const SizedBox(width: 8),
  //           const Text(
  //             'Add Sub-module',
  //             style: TextStyle(
  //               fontSize: 14,
  //               color: Color(0xff242424),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Commented out sub module functionality
  // Widget _buildExpandedSubModuleInput(int moduleIndex) {
  //   // Ensure we have a controller for this module
  //   if (!subModuleControllers.containsKey(moduleIndex)) {
  //     subModuleControllers[moduleIndex] = TextEditingController();
  //   }

  //   // Ensure we have a focus node for this module
  //   if (!subModuleFocusNodes.containsKey(moduleIndex)) {
  //     subModuleFocusNodes[moduleIndex] = FocusNode();
  //   }

  //   return Container(
  //     padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
  //     child: Container(
  //       decoration: BoxDecoration(
  //         color: Colors.white,
  //         borderRadius: BorderRadius.circular(6),
  //         border: Border.all(
  //           color: const Color(0xFFD0D0D0),
  //           width: 0.5,
  //         ),
  //       ),
  //       padding: const EdgeInsets.all(12),
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           // Input field
  //           TextField(
  //             controller: subModuleControllers[moduleIndex],
  //             focusNode: subModuleFocusNodes[moduleIndex],
  //             decoration: InputDecoration(
  //               hintText: 'Type here',
  //               hintStyle: TextStyle(
  //                 fontSize: 14,
  //                 color: Color(0xffBEBEBE),
  //               ),
  //               border: InputBorder.none,
  //               enabledBorder: InputBorder.none,
  //               focusedBorder: InputBorder.none,
  //               isDense: true,
  //               contentPadding: EdgeInsets.zero,
  //             ),
  //             style: const TextStyle(
  //               fontSize: 14,
  //               color: Colors.black,
  //             ),
  //           ),
  //           const SizedBox(height: 10.5),
  //           // Divider line
  //           Container(
  //             height: 1,
  //             color: Colors.grey[300],
  //           ),
  //           const SizedBox(height: 12),
  //           // Action buttons
  //           Row(
  //             mainAxisAlignment: MainAxisAlignment.end,
  //             children: [
  //               // Cancel button
  //               GestureDetector(
  //                 onTap: () {
  //                   setState(() {
  //                     createdModules[moduleIndex].isSubModuleInputExpanded =
  //                         false;
  //                     subModuleControllers[moduleIndex]?.clear();
  //                   });
  //                 },
  //                 child: Container(
  //                   width: 28,
  //                   height: 28,
  //                   decoration: BoxDecoration(
  //                     color: Colors.grey[100],
  //                     shape: BoxShape.circle,
  //                   ),
  //                   child: Icon(
  //                     Icons.close,
  //                     size: 16,
  //                     color: Colors.black87,
  //                   ),
  //                 ),
  //               ),
  //               const SizedBox(width: 12),
  //               // Confirm button
  //               GestureDetector(
  //                 onTap: () {
  //                   final controller = subModuleControllers[moduleIndex];
  //                   if (controller != null &&
  //                       controller.text.trim().isNotEmpty) {
  //                     setState(() {
  //                       createdModules[moduleIndex]
  //                           .subModules
  //                           .add(controller.text.trim());
  //                       createdModules[moduleIndex].isSubModuleInputExpanded =
  //                           false;
  //                       controller.clear();
  //                     });
  //                   }
  //                 },
  //                 child: Container(
  //                   width: 28,
  //                   height: 28,
  //                   decoration: BoxDecoration(
  //                     color: Colors.grey[100],
  //                     shape: BoxShape.circle,
  //                   ),
  //                   child: Icon(
  //                     Icons.check,
  //                     size: 16,
  //                     color: Colors.black87,
  //                   ),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Commented out sub module functionality
  // Widget _buildSubModuleItem(String subModuleName) {
  //   return Container(
  //     decoration: BoxDecoration(
  //       color: const Color(0xFFF8F9FA),
  //       borderRadius: BorderRadius.circular(6),
  //       border: Border.all(
  //         color: const Color(0xFFD0D0D0),
  //         width: 0.5,
  //       ),
  //     ),
  //     child: Container(
  //       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  //       child: Row(
  //         children: [
  //           // Dropdown arrow
  //           Icon(
  //             Icons.chevron_right,
  //             size: 18,
  //             color: Colors.black,
  //           ),
  //           const SizedBox(width: 8),
  //           // Sub-module name
  //           Expanded(
  //             child: Text(
  //               subModuleName,
  //               style: const TextStyle(
  //                 fontSize: 14,
  //                 color: Colors.black87,
  //               ),
  //             ),
  //           ),
  //           // Plus icon
  //           Icon(
  //             Icons.add,
  //             size: 18,
  //             color: Colors.black,
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  /// Shows error dialog when trying to drop a solution that's already in a book
  void _showSolutionInUseDialog(String bookName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: const Text(
                        'Solution is currently in use',
                        style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFFC80404), // Red color
                            height: 1),
                      ),
                    ),
                    // const SizedBox(width: AppSpacing.xxs),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Color(0xFFC80404),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppSpacing.xs),
                // Horizontal divider
                const Divider(
                  color: Color(0xFFE5E7EB),
                  thickness: 1,
                  height: 1,
                ),
                const SizedBox(height: AppSpacing.md),
                // Error message
                const Text(
                  'This solution cannot be included in this book, as it has already been used in a client\'s book.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    height: 18 / 14,
                  ),
                ),
                const SizedBox(height: 8),
                // Book name info
                Text(
                  'Currently used in: $bookName',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    height: 18 / 14,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Shows confirmation dialog when trying to remove a solution from a module
  void _showRemoveSolutionDialog(ModuleItem module, String solution) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: const Text(
                        'Remove Solution',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFC80404),
                          height: 1,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Color(0xFFC80404),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppSpacing.xs),
                // Horizontal divider
                const Divider(
                  color: Color(0xFFE5E7EB),
                  thickness: 1,
                  height: 1,
                ),
                const SizedBox(height: AppSpacing.md),
                // Confirmation message
                const Text(
                  'Are you sure you want to remove this solution from the module?',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    height: 18 / 14,
                  ),
                ),
                const SizedBox(height: AppSpacing.md),
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // No button
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.md,
                          vertical: AppSpacing.xs,
                        ),
                      ),
                      child: const Text(
                        'No',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    // Yes button
                    TextButton(
                      onPressed: () {
                        // Remove the solution
                        setState(() {
                          module.solutions.remove(solution);
                          // Remove from used solutions set to make it available again
                          _usedSolutions.remove(solution);
                        });
                        Navigator.of(context).pop();

                        // Show confirmation snackbar
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Solution "$solution" removed from module "${module.name}"'),
                            duration: const Duration(seconds: 2),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.md,
                          vertical: AppSpacing.xs,
                        ),
                        backgroundColor: const Color(0xFFC80404),
                      ),
                      child: const Text(
                        'Yes',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    // Dispose controllers
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _chatController.dispose();
    _moduleController.dispose();
    _pageController.dispose();

    // Dispose focus nodes
    _moduleFocusNode.dispose();

    // Dispose animation controllers
    _searchAnimationController.dispose();
    _moduleAnimationController.dispose();

    super.dispose();
  }
}
