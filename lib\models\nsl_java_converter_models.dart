// To parse this JSON data, do
//
//     final nslJavaConverterData = nslJavaConverterDataFromJson(jsonString);

import 'dart:convert';

NslJavaConverterData nslJavaConverterDataFromJson(String str) =>
    NslJavaConverterData.fromJson(json.decode(str));

String nslJavaConverterDataToJson(NslJavaConverterData data) =>
    json.encode(data.toJson());

class NslJavaConverterData {
  final String? nslFileName;
  final String? javaFileName;
  final int? prescriptives;
  final int? subPrescriptives;
  final int? beTs;
  final int? pathways;
  final List<Code>? code;
  final List<StackModel>? stacks;

  NslJavaConverterData({
    this.nslFileName,
    this.javaFileName,
    this.prescriptives,
    this.subPrescriptives,
    this.beTs,
    this.pathways,
    this.code,
    this.stacks,
  });

  NslJavaConverterData copyWith({
    String? nslFileName,
    String? javaFileName,
    int? prescriptives,
    int? subPrescriptives,
    int? beTs,
    int? pathways,
    List<Code>? code,
    List<StackModel>? stacks,
  }) =>
      NslJavaConverterData(
        nslFileName: nslFileName ?? this.nslFileName,
        javaFileName: javaFileName ?? this.javaFileName,
        prescriptives: prescriptives ?? this.prescriptives,
        subPrescriptives: subPrescriptives ?? this.subPrescriptives,
        beTs: beTs ?? this.beTs,
        pathways: pathways ?? this.pathways,
        code: code ?? this.code,
        stacks: stacks ?? this.stacks,
      );

  factory NslJavaConverterData.fromJson(Map<String, dynamic> json) =>
      NslJavaConverterData(
        nslFileName: json["nsl_file_name"],
        javaFileName: json["java_file_name"],
        prescriptives: json["prescriptives"],
        subPrescriptives: json["sub_prescriptives"],
        beTs: json["BETs"],
        pathways: json["pathways"],
        code: json["code"] == null
            ? []
            : List<Code>.from(json["code"]!.map((x) => Code.fromJson(x))),
        stacks: json["stacks"] == null
            ? []
            : List<StackModel>.from(
                json["stacks"]!.map((x) => StackModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "nsl_file_name": nslFileName,
        "java_file_name": javaFileName,
        "prescriptives": prescriptives,
        "sub_prescriptives": subPrescriptives,
        "BETs": beTs,
        "pathways": pathways,
        "code": code == null
            ? []
            : List<dynamic>.from(code!.map((x) => x.toJson())),
        "stacks": stacks == null
            ? []
            : List<dynamic>.from(stacks!.map((x) => x.toJson())),
      };
}

class Code {
  final int? line;
  final String? code;

  Code({
    this.line,
    this.code,
  });

  Code copyWith({
    int? line,
    String? code,
  }) =>
      Code(
        line: line ?? this.line,
        code: code ?? this.code,
      );

  factory Code.fromJson(Map<String, dynamic> json) => Code(
        line: json["line"],
        code: json["code"],
      );

  Map<String, dynamic> toJson() => {
        "line": line,
        "code": code,
      };
}

class StackModel {
  final String? stackName;
  final List<Step>? steps;

  StackModel({
    this.stackName,
    this.steps,
  });

  StackModel copyWith({
    String? stackName,
    List<Step>? steps,
  }) =>
      StackModel(
        stackName: stackName ?? this.stackName,
        steps: steps ?? this.steps,
      );

  factory StackModel.fromJson(Map<String, dynamic> json) => StackModel(
        stackName: json["stack_name"],
        steps: json["steps"] == null
            ? []
            : List<Step>.from(json["steps"]!.map((x) => Step.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "stack_name": stackName,
        "steps": steps == null
            ? []
            : List<dynamic>.from(steps!.map((x) => x.toJson())),
      };
}

class Step {
  final String? id;
  final int? sentenceId;
  final String? sentence;
  final String? color;
  final List<Step>? innnerComponents;
  final List<int>? relatedLines;
  final List<RelatedKeyword>? relatedKeywords;
  final bool? clickable;

  Step({
    this.id,
    this.sentenceId,
    this.sentence,
    this.color,
    this.innnerComponents,
    this.relatedLines,
    this.relatedKeywords,
    this.clickable,
  });

  Step copyWith({
    String? id,
    int? sentenceId,
    String? sentence,
    String? color,
    List<Step>? innnerComponents,
    List<int>? relatedLines,
    List<RelatedKeyword>? relatedKeywords,
    bool? clickable,
  }) =>
      Step(
        id: id ?? this.id,
        sentenceId: sentenceId ?? this.sentenceId,
        sentence: sentence ?? this.sentence,
        color: color ?? this.color,
        innnerComponents: innnerComponents ?? this.innnerComponents,
        relatedLines: relatedLines ?? this.relatedLines,
        relatedKeywords: relatedKeywords ?? this.relatedKeywords,
        clickable: clickable ?? this.clickable,
      );

  factory Step.fromJson(Map<String, dynamic> json) => Step(
        id: json["id"],
        sentenceId: json["sentence_id"],
        sentence: json["sentence"],
        color: json["color"],
        innnerComponents: json["inner_components"] == null
            ? []
            : List<Step>.from(
                json["inner_components"]!.map((x) => Step.fromJson(x))),
        relatedLines: json["related_lines"] == null
            ? []
            : List<int>.from(json["related_lines"]!.map((x) => x)),
        relatedKeywords: json["related_keywords"] == null
            ? []
            : List<RelatedKeyword>.from(json["related_keywords"]!
                .map((x) => RelatedKeyword.fromJson(x))),
        clickable: json["clickable"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "sentence_id": sentenceId,
        "sentence": sentence,
        "color": color,
        "inner_components": innnerComponents == null
            ? []
            : List<dynamic>.from(innnerComponents!.map((x) => x.toJson())),
        "related_lines": relatedLines == null
            ? []
            : List<dynamic>.from(relatedLines!.map((x) => x)),
        "related_keywords": relatedKeywords == null
            ? []
            : List<dynamic>.from(relatedKeywords!.map((x) => x.toJson())),
        "clickable": clickable,
      };
}

class RelatedKeyword {
  final String? keyword;
  final String? description;

  RelatedKeyword({
    this.keyword,
    this.description,
  });

  RelatedKeyword copyWith({
    String? keyword,
    String? description,
  }) =>
      RelatedKeyword(
        keyword: keyword ?? this.keyword,
        description: description ?? this.description,
      );

  factory RelatedKeyword.fromJson(Map<String, dynamic> json) => RelatedKeyword(
        keyword: json["keyword"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "keyword": keyword,
        "description": description,
      };
}
