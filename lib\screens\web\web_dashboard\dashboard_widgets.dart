// import 'package:flutter/material.dart';
// import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;
// import '../../../utils/json_data_helper.dart';

// class DashboardWidgets extends StatefulWidget {
//   const DashboardWidgets({super.key});

//   @override
//   State<DashboardWidgets> createState() => _DashboardWidgetsState();
// }

// class _DashboardWidgetsState extends State<DashboardWidgets> {
  
//   /// Sample pie chart data for UI Control Types Distribution
//   Map<String, dynamic> get uiControlTypesChartData => {
//     "title": "UI Control Types Distribution",
//     "subtitle": "Distribution of different UI control types in the workflow",
//     "data": [
//       {"label": "Input Controls", "value": 35.0},
//       {"label": "Selection Controls", "value": 25.0},
//       {"label": "Date/Time Controls", "value": 15.0},
//       {"label": "Media Controls", "value": 12.0},
//       {"label": "Display Controls", "value": 8.0},
//       {"label": "Other Controls", "value": 5.0}
//     ],
//     "radius": 120.0,
//     "showPercentages": true,
//     "showLegend": true,
//     "legendPosition": "right",
//     "colors": ["#0058FF", "#00C853", "#FF5722", "#FFC107", "#9C27B0", "#00BCD4"],
//     "enableAnimation": true,
//     "animationDuration": 1500
//   };

//   /// Sample pie chart data for Data Types Distribution
//   Map<String, dynamic> get dataTypesChartData => {
//     "title": "Data Types Distribution",
//     "subtitle": "Distribution of data types across all inputs",
//     "data": [
//       {"label": "String", "value": 40.0},
//       {"label": "Integer/Number", "value": 20.0},
//       {"label": "Boolean", "value": 15.0},
//       {"label": "Date/DateTime", "value": 12.0},
//       {"label": "File/Image", "value": 8.0},
//       {"label": "Array/Object", "value": 5.0}
//     ],
//     "radius": 100.0,
//     "showPercentages": true,
//     "showLegend": true,
//     "legendPosition": "bottom",
//     "colors": ["#4CAF50", "#2196F3", "#FF9800", "#E91E63", "#9C27B0", "#607D8B"],
//     "enableAnimation": true,
//     "animationDuration": 2000
//   };

//   /// Sample pie chart data for Input Source Distribution
//   Map<String, dynamic> get inputSourceChartData => {
//     "title": "Input Source Distribution",
//     "subtitle": "How inputs are populated in the system",
//     "data": [
//       {"label": "User Input", "value": 75.0},
//       {"label": "System Generated", "value": 15.0},
//       {"label": "Information Display", "value": 10.0}
//     ],
//     "radius": 90.0,
//     "showPercentages": true,
//     "showLegend": true,
//     "legendPosition": "right",
//     "colors": ["#FF6B6B", "#4ECDC4", "#45B7D1"],
//     "enableAnimation": true,
//     "animationDuration": 1200,
//     "showCenterWidget": true,
//     "centerText": "Sources"
//   };

//   /// Sample pie chart data for Required vs Optional Fields
//   Map<String, dynamic> get requiredFieldsChartData => {
//     "title": "Field Requirements",
//     "subtitle": "Required vs Optional fields distribution",
//     "data": [
//       {"label": "Required Fields", "value": 85.0},
//       {"label": "Optional Fields", "value": 15.0}
//     ],
//     "radius": 80.0,
//     "showPercentages": true,
//     "showLegend": true,
//     "legendPosition": "bottom",
//     "colors": ["#FF5722", "#4CAF50"],
//     "enableAnimation": true,
//     "animationDuration": 1000,
//     "sectionsSpace": 4.0
//   };

//   /// Sample pie chart data based on actual JSON data analysis
//   Map<String, dynamic> get actualDataAnalysisChart => {
//     "title": "Workflow Input Analysis",
//     "subtitle": "Analysis based on actual JSON data structure",
//     "data": [
//       {"label": "Text Inputs", "value": 12.0},
//       {"label": "Selection Controls", "value": 8.0},
//       {"label": "Date/Time", "value": 6.0},
//       {"label": "Media/File", "value": 7.0},
//       {"label": "Numeric", "value": 5.0},
//       {"label": "Boolean", "value": 3.0},
//       {"label": "Special Controls", "value": 16.0}
//     ],
//     "radius": 110.0,
//     "showPercentages": true,
//     "showValues": false,
//     "showLegend": true,
//     "legendPosition": "right",
//     "colors": ["#3F51B5", "#009688", "#FF9800", "#E91E63", "#795548", "#607D8B", "#9E9E9E"],
//     "enableAnimation": true,
//     "animationDuration": 1800,
//     "legendShowPercentages": true,
//     "legendShowValues": true
//   };

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Dashboard - Pie Chart Widgets'),
//         backgroundColor: Colors.blue.shade600,
//         foregroundColor: Colors.white,
//       ),
//       body: SingleChildScrollView(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Header section
//             Container(
//               width: double.infinity,
//               padding: const EdgeInsets.all(20.0),
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   colors: [Colors.blue.shade50, Colors.blue.shade100],
//                   begin: Alignment.topLeft,
//                   end: Alignment.bottomRight,
//                 ),
//                 borderRadius: BorderRadius.circular(12.0),
//                 border: Border.all(color: Colors.blue.shade200),
//               ),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'Workflow Analytics Dashboard',
//                     style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                       fontWeight: FontWeight.bold,
//                       color: Colors.blue.shade800,
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                   Text(
//                     'Interactive pie charts showing distribution of UI controls, data types, and input sources from the workflow JSON data.',
//                     style: Theme.of(context).textTheme.bodyLarge?.copyWith(
//                       color: Colors.blue.shade700,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
            
//             const SizedBox(height: 24),
            
//             // Grid layout for pie charts
//             LayoutBuilder(
//               builder: (context, constraints) {
//                 // Responsive grid based on screen width
//                 int crossAxisCount = constraints.maxWidth > 1200 ? 3 : 
//                                    constraints.maxWidth > 800 ? 2 : 1;
                
//                 return GridView.count(
//                   shrinkWrap: true,
//                   physics: const NeverScrollableScrollPhysics(),
//                   crossAxisCount: crossAxisCount,
//                   crossAxisSpacing: 16.0,
//                   mainAxisSpacing: 16.0,
//                   childAspectRatio: 1.1,
//                   children: [
//                     // UI Control Types Chart
//                     _buildChartCard(
//                       ui_controls.PieChartWidget.fromJson(uiControlTypesChartData),
//                     ),
                    
//                     // Data Types Chart
//                     _buildChartCard(
//                       ui_controls.PieChartWidget.fromJson(dataTypesChartData),
//                     ),
                    
//                     // Input Source Chart
//                     _buildChartCard(
//                       ui_controls.PieChartWidget.fromJson(inputSourceChartData),
//                     ),
                    
//                     // Required Fields Chart
//                     _buildChartCard(
//                       ui_controls.PieChartWidget.fromJson(requiredFieldsChartData),
//                     ),
                    
//                     // Actual Data Analysis Chart
//                     _buildChartCard(
//                       ui_controls.PieChartWidget.fromJson(actualDataAnalysisChart),
//                     ),
                    
//                     // Custom configured chart
//                     _buildChartCard(
//                       _buildCustomPieChart(),
//                     ),
//                   ],
//                 );
//               },
//             ),
            
//             const SizedBox(height: 24),
            
//             // JSON Data Info Section
//             _buildJsonDataInfoSection(),
//           ],
//         ),
//       ),
//     );
//   }

//   /// Builds a card container for pie charts
//   Widget _buildChartCard(Widget chart) {
//     return Card(
//       elevation: 4,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(12.0),
//       ),
//       child: Container(
//         padding: const EdgeInsets.all(16.0),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(12.0),
//           gradient: LinearGradient(
//             colors: [Colors.white, Colors.grey.shade50],
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//           ),
//         ),
//         child: chart,
//       ),
//     );
//   }

//   /// Builds a custom pie chart with direct widget configuration
//   Widget _buildCustomPieChart() {
//     return ui_controls.PieChartWidget(
//       title: "Custom Configuration",
//       subtitle: "Directly configured pie chart widget",
//       data: const [
//         ui_controls.PieChartDataItem(
//           label: "Mobile Apps",
//           value: 45.0,
//           color: Color(0xFF0058FF),
//         ),
//         ui_controls.PieChartDataItem(
//           label: "Web Apps",
//           value: 35.0,
//           color: Color(0xFF00C853),
//         ),
//         ui_controls.PieChartDataItem(
//           label: "Desktop Apps",
//           value: 20.0,
//           color: Color(0xFFFF5722),
//         ),
//       ],
//       radius: 95.0,
//       showPercentages: true,
//       showLegend: true,
//       legendPosition: ui_controls.LegendPosition.bottom,
//       enableAnimation: true,
//       animationDuration: const Duration(milliseconds: 1600),
//       showCenterWidget: true,
//       centerText: "Apps",
//       centerTextStyle: const TextStyle(
//         fontSize: 16,
//         fontWeight: FontWeight.bold,
//         color: Colors.black87,
//       ),
//     );
//   }

//   /// Builds the JSON data information section
//   Widget _buildJsonDataInfoSection() {
//     final jsonData = JsonDataHelper.getJsonData();
//     final userInputs = jsonData['user_inputs'] as List;
    
//     return Card(
//       elevation: 2,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(12.0),
//       ),
//       child: Container(
//         width: double.infinity,
//         padding: const EdgeInsets.all(20.0),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(12.0),
//           color: Colors.grey.shade50,
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               children: [
//                 Icon(Icons.info_outline, color: Colors.blue.shade600),
//                 const SizedBox(width: 8),
//                 Text(
//                   'JSON Data Summary',
//                   style: Theme.of(context).textTheme.titleLarge?.copyWith(
//                     fontWeight: FontWeight.bold,
//                     color: Colors.blue.shade800,
//                   ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 16),
            
//             Wrap(
//               spacing: 24.0,
//               runSpacing: 12.0,
//               children: [
//                 _buildInfoChip(
//                   'Total Inputs',
//                   '${userInputs.length}',
//                   Icons.input,
//                   Colors.blue,
//                 ),
//                 _buildInfoChip(
//                   'Local Objective',
//                   jsonData['local_objective'] ?? 'N/A',
//                   Icons.flag,
//                   Colors.green,
//                 ),
//                 _buildInfoChip(
//                   'Required Fields',
//                   '${userInputs.where((input) => input['required'] == true).length}',
//                   Icons.star,
//                   Colors.orange,
//                 ),
//                 _buildInfoChip(
//                   'Optional Fields',
//                   '${userInputs.where((input) => input['required'] != true).length}',
//                   Icons.star_border,
//                   Colors.grey,
//                 ),
//               ],
//             ),
            
//             const SizedBox(height: 16),
            
//             Text(
//               'The pie charts above are generated based on the analysis of the JSON data structure from JsonDataHelper. '
//               'Each chart represents different aspects of the workflow configuration including UI control types, '
//               'data types, input sources, and field requirements.',
//               style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//                 color: Colors.grey.shade700,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   /// Builds an information chip
//   Widget _buildInfoChip(String label, String value, IconData icon, Color color) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
//       decoration: BoxDecoration(
//         color: color.withOpacity(0.1),
//         borderRadius: BorderRadius.circular(20.0),
//         border: Border.all(color: color.withOpacity(0.3)),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Icon(icon, size: 16, color: color),
//           const SizedBox(width: 6),
//           Text(
//             '$label: ',
//             style: TextStyle(
//               fontSize: 12,
//               fontWeight: FontWeight.w500,
//               color: color,
//             ),
//           ),
//           Text(
//             value,
//             style: TextStyle(
//               fontSize: 12,
//               fontWeight: FontWeight.bold,
//               color: color,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
