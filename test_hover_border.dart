import 'package:flutter/material.dart';
import 'ui_controls_library/ui_controls_library/lib/widgets/quill_rich_text_widget.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Hover Border Color Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: MyHomePage(),
    );
  }
}

class MyHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Hover Border Color Test'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Hover over the widget below to see the border color change to blue (0xFF0058FF):',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 20),
              QuillRichTextWidget(
                initialText: 'Class',
                width: 300,
                borderColor: Colors.grey,
                hoverBorderColor: Color(0xFF0058FF), // Blue hover border
                borderWidth: 2.0,
                hasBorder: true,
                onHover: (isHovered) {
                  print('Hover state: $isHovered');
                },
              ),
              SizedBox(height: 20),
              Text(
                'The border should animate from grey to blue when you hover over it.',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
