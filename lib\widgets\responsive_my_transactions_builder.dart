import 'package:flutter/material.dart';
import '../screens/my_transactions_screen.dart';
import '../screens/web/web_my_transactions_screen.dart';


/// A responsive builder that returns the appropriate transactions screen based on the device type
class ResponsiveMyTransactionsBuilder extends StatelessWidget {
  const ResponsiveMyTransactionsBuilder({super.key});

  @override
  Widget build(BuildContext context) {

    return LayoutBuilder(
      builder: (context, constraints) {
        // Use web layout for larger screens
        if (constraints.maxWidth >= 860) {
          return const WebMyTransactionsScreen();
        }
        // Use mobile layout for smaller screens
        return const MyTransactionsScreen();
      },
    );

  }
}
