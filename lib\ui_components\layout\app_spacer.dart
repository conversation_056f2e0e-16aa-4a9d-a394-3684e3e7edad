import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../../utils/constants.dart';

/// A customizable spacer component for adding consistent spacing
class AppSpacer extends StatelessWidget {
  /// The size of the spacer
  final AppSpacerSize size;

  /// Whether the spacer is horizontal
  final bool horizontal;

  /// Custom height for vertical spacers
  final double? height;

  /// Custom width for horizontal spacers
  final double? width;

  /// Whether to expand to fill available space
  final bool expanded;

  /// Creates a vertical spacer with predefined size
  const AppSpacer({
    super.key,
    this.size = AppSpacerSize.medium,
    this.height,
    this.expanded = false,
  })  : horizontal = false,
        width = null;

  /// Creates a horizontal spacer with predefined size
  const AppSpacer.horizontal({
    super.key,
    this.size = AppSpacerSize.medium,
    this.width,
    this.expanded = false,
  })  : horizontal = true,
        height = null;

  /// Creates an extra small vertical spacer
  const AppSpacer.xs({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.extraSmall,
        horizontal = false,
        height = null,
        width = null;

  /// Creates a small vertical spacer
  const AppSpacer.s({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.small,
        horizontal = false,
        height = null,
        width = null;

  /// Creates a medium vertical spacer
  const AppSpacer.m({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.medium,
        horizontal = false,
        height = null,
        width = null;

  /// Creates a large vertical spacer
  const AppSpacer.l({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.large,
        horizontal = false,
        height = null,
        width = null;

  /// Creates an extra large vertical spacer
  const AppSpacer.xl({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.extraLarge,
        horizontal = false,
        height = null,
        width = null;

  /// Creates an extra extra large vertical spacer
  const AppSpacer.xxl({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.extraExtraLarge,
        horizontal = false,
        height = null,
        width = null;

  /// Creates an extra small horizontal spacer
  const AppSpacer.horizontalXs({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.extraSmall,
        horizontal = true,
        height = null,
        width = null;

  /// Creates a small horizontal spacer
  const AppSpacer.horizontalS({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.small,
        horizontal = true,
        height = null,
        width = null;

  /// Creates a medium horizontal spacer
  const AppSpacer.horizontalM({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.medium,
        horizontal = true,
        height = null,
        width = null;

  /// Creates a large horizontal spacer
  const AppSpacer.horizontalL({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.large,
        horizontal = true,
        height = null,
        width = null;

  /// Creates an extra large horizontal spacer
  const AppSpacer.horizontalXl({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.extraLarge,
        horizontal = true,
        height = null,
        width = null;

  /// Creates an extra extra large horizontal spacer
  const AppSpacer.horizontalXxl({
    super.key,
    this.expanded = false,
  })  : size = AppSpacerSize.extraExtraLarge,
        horizontal = true,
        height = null,
        width = null;

  /// Creates a custom sized spacer
  const AppSpacer.custom({
    super.key,
    required this.height,
    required this.width,
    this.expanded = false,
  })  : size = AppSpacerSize.custom,
        horizontal = false;

  /// Creates a flexible spacer that expands to fill available space
  const AppSpacer.expanded({
    super.key,
  })  : size = AppSpacerSize.custom,
        horizontal = false,
        height = null,
        width = null,
        expanded = true;

  /// Get the size value based on the spacer size enum
  double _getSizeValue() {
    switch (size) {
      case AppSpacerSize.extraSmall:
        return AppTheme.spacingXs;
      case AppSpacerSize.small:
        return AppTheme.spacingS;
      case AppSpacerSize.medium:
        return AppTheme.spacingM;
      case AppSpacerSize.large:
        return AppTheme.spacingL;
      case AppSpacerSize.extraLarge:
        return AppTheme.spacingXl;
      case AppSpacerSize.extraExtraLarge:
        return AppTheme.spacingXxl;
      case AppSpacerSize.custom:
        return 0; // Custom size is handled separately
    }
  }

  @override
  Widget build(BuildContext context) {
    if (expanded) {
      return horizontal ? const Spacer() : const Expanded(child: SizedBox());
    }

    if (horizontal) {
      final double widthValue = width ?? _getSizeValue();
      return SizedBox(width: widthValue);
    } else {
      final double heightValue = height ?? _getSizeValue();
      return SizedBox(height: heightValue);
    }
  }
}

/// The size of the spacer
enum AppSpacerSize {
  /// Extra small spacing (4.0)
  extraSmall,

  /// Small spacing (8.0)
  small,

  /// Medium spacing (16.0)
  medium,

  /// Large spacing (24.0)
  large,

  /// Extra large spacing (32.0)
  extraLarge,

  /// Extra extra large spacing (48.0)
  extraExtraLarge,

  /// Custom spacing
  custom,
}

/// A responsive spacer that adapts to different screen sizes
class AppResponsiveSpacer extends StatelessWidget {
  /// The size of the spacer on small screens
  final AppSpacerSize sizeSmall;

  /// The size of the spacer on medium screens
  final AppSpacerSize sizeMedium;

  /// The size of the spacer on large screens
  final AppSpacerSize sizeLarge;

  /// Whether the spacer is horizontal
  final bool horizontal;

  /// Custom height for vertical spacers on small screens
  final double? heightSmall;

  /// Custom height for vertical spacers on medium screens
  final double? heightMedium;

  /// Custom height for vertical spacers on large screens
  final double? heightLarge;

  /// Custom width for horizontal spacers on small screens
  final double? widthSmall;

  /// Custom width for horizontal spacers on medium screens
  final double? widthMedium;

  /// Custom width for horizontal spacers on large screens
  final double? widthLarge;

  /// The breakpoint for small screens
  final double breakpointSmall;

  /// The breakpoint for medium screens
  final double breakpointMedium;

  const AppResponsiveSpacer({
    super.key,
    this.sizeSmall = AppSpacerSize.small,
    this.sizeMedium = AppSpacerSize.medium,
    this.sizeLarge = AppSpacerSize.large,
    this.horizontal = false,
    this.heightSmall,
    this.heightMedium,
    this.heightLarge,
    this.widthSmall,
    this.widthMedium,
    this.widthLarge,
    this.breakpointSmall = AppConstants.mobileBreakpoint,
    this.breakpointMedium = AppConstants.tabletBreakpoint,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double maxWidth = constraints.maxWidth;

        if (maxWidth <= breakpointSmall) {
          // Small screens
          return horizontal
              ? AppSpacer.horizontal(
                  size: sizeSmall,
                  width: widthSmall,
                )
              : AppSpacer(
                  size: sizeSmall,
                  height: heightSmall,
                );
        } else if (maxWidth <= breakpointMedium) {
          // Medium screens
          return horizontal
              ? AppSpacer.horizontal(
                  size: sizeMedium,
                  width: widthMedium,
                )
              : AppSpacer(
                  size: sizeMedium,
                  height: heightMedium,
                );
        } else {
          // Large screens
          return horizontal
              ? AppSpacer.horizontal(
                  size: sizeLarge,
                  width: widthLarge,
                )
              : AppSpacer(
                  size: sizeLarge,
                  height: heightLarge,
                );
        }
      },
    );
  }
}
