{"nodes": [{"id": "root", "type": "root", "label": "Created Student Fee For List", "children": ["node1"]}, {"id": "node1", "type": "milestone", "label": "Hybrid Milestone.", "parent": "root", "children": ["node2", "node3"]}, {"id": "node2", "type": "action", "label": "Pay New.", "parent": "node1", "metadata": {"alt": "1.0"}, "children": ["node4", "node5", "node6"]}, {"id": "node3", "type": "action", "label": "Pay Cash.", "parent": "node1", "metadata": {"alt": "2.0"}, "children": []}, {"id": "node4", "type": "action", "label": "Enter Inputdata_Cu1.", "parent": "node2", "metadata": {"alt": "1.0"}, "children": ["node7", "node8", "node9"]}, {"id": "node5", "type": "action", "label": "Add Nestedsample Creation.", "parent": "node2", "metadata": {"alt": "2.0"}, "children": []}, {"id": "node6", "type": "action", "label": "Add Dfghjnm_7788 Cu.", "parent": "node2", "metadata": {"alt": "3.0"}, "children": []}, {"id": "node7", "type": "action", "label": "Add Redact35467Gjh Cu. Gsi Hybrid Milestone. Gsi_Createdb Student Fee For List 2021. Sends Bulk Email. \nUpdate Application Data. Views Notification. Searches For Application. Accepts The Application \nSends An Email To Applicants. Accepts the Application. Sends An Email To Applicants. \n Calculates Total.Sends Sms. Enters Application Details. Accepts The Application.", "parent": "node4", "metadata": {"parl": [{"parlName": "Searches For Application.", "parlId": "1", "parlColor": "#DFEAE9", "parlChildren": [{"parlSubName": "Add Redact35467Gjh Cu.", "parlSubId": "1", "parlColor": "#DFEAE9"}, {"parlSubName": "Add Redact35467Gjh Cu.", "parlSubId": "2", "parlColor": "#DFEAE9"}]}, {"parlName": "Accepts the Application.", "parlId": "2", "parlColor": "#FCF4E9", "parlChildren": [{"parlSubName": "Add Redact35467Gjh Cu.", "parlSubId": "1", "parlColor": "#FCF4E9"}]}, {"parlName": "Accepts The Application.", "parlId": "3", "parlColor": "#DCDDDA", "parlChildren": [{"parlSubName": "Add Redact35467Gjh Cu.", "parlSubId": "1", "parlColor": "#DCDDDA"}, {"parlSubName": "Add Redact35467Gjh Cu.", "parlSubId": "2", "parlColor": "#DCDDDA"}, {"parlSubName": "Add Redact35467Gjh Cu.", "parlSubId": "3", "parlColor": "#DCDDDA"}]}]}, "children": []}, {"id": "node8", "type": "action", "label": "Search_Student <PERSON><PERSON> 2021.", "parent": "node4", "metadata": {}, "children": []}, {"id": "node9", "type": "action", "label": "Search_Student <PERSON><PERSON> 2021.", "parent": "node4", "metadata": {}, "children": []}], "metadata": {"stacks": [{"id": 1, "name": "Agent <PERSON>", "details": "• (H) Claims Adjuster/Customer Service/Customer has execution rights \n• (S) System has execution, read, and write rights"}, {"id": 2, "name": "Input Stack", "details": {"objectiveName": "Claim requires PolicyID, DateOfLoss, DateReported, ClaimType, Description; ClaimDocument requires DocumentType, File.", "objectiveDetails": "• (H) User provides PolicyID/PolicyNumber, DateOfLoss, ClaimType, Description, DocumentType and File for each Document \n• (S) Retriever fetches Policy Details from Policy as defined in “View Policy” under “Policy Management” \n• (S) Retriever fetches Customer Details from Customer as defined in “View Customer” under “Customer Management” \n• (S) Retriever fetches Coverage Details from Coverage as defined in “View Coverage” under “Policy Management” \n• (S) System validates ClaimType eligibility using nf003 (validate_coverage_for_claim) with policyID, dateOfLoss, and claimType parameters \n• (S) System determines required documents using nf004 (determine_required_documents) with claimType, policyType, and estimated claimValue parameters \n• (S) System validates for each Claim: • Policy is active on DateOfLoss • DateOfLoss is within policy period • ClaimType matches an active coverage • Required documents are attached based on ClaimType \n• (S) System sets default values: • DateReported defaults to current system date • Status defaults to “Submitted”"}}, {"id": 3, "name": "Output Stack", "details": {"objectiveName": "Claim outputs ClaimID, ClaimNumber, PolicyID, DateOfLoss, DateReported, ClaimType, Status, Description; ClaimDocument outputs DocumentID, ClaimID, FileName, FileType, UploadDate, DocumentType.", "objectiveDetails": "• (S) System generates unique ClaimID for the Claim \n• (S) System generates formatted ClaimNumber using nf001 (generate_claim_number) with prefix, year, and policyType parameters \n• (S) System captures DateReported from current system date \n• (S) System sets initial Status to “Submitted” \n• (S) System creates DocumentID for each ClaimDocument \n• (S) System captures UploadDate from current system date \n• (S) System provides Claim Details for Assign Claim Function \n Note: This icon represent Nested Function. Click to see the more details."}}, {"id": 4, "name": "Data Base Stack", "details": {"objectiveName": "Claim validates against Policy, Coverage, and ClaimType", "objectiveDetails": "• System verifies PolicyID exists in Policy table \n• System confirms Policy status is Active on DateOfLoss \n• System checks DateOfLoss falls within Policy period \n• System validates ClaimType is covered by an active Coverage \n• System ensures Required Documents are attached based on ClaimType \n• System validates all required fields have appropriate data format: \n\t• PolicyID must be valid existing policy \n\t• DateOfLoss must be valid date not in future \n\t• ClaimType must be valid claim type for policy \n\t• Description must be at least 50 characters \n\t• DocumentType must match claim type requirements"}}, {"id": 5, "name": "UI Stack", "details": {"objectiveName": "<PERSON><PERSON>m presents Form with Policy Lookup, Date Controls, Dropdown Menus, Text Areas, and File Upload", "objectiveDetails": "• System displays Claim Submission Form with responsive layout \n• System renders Policy Lookup as searchable dropdown with policy details display \n• System presents DateOfLoss as calendar date picker with date constraints \n• System provides ClaimType as dropdown menu populated from eligible claim types for policy \n• System displays Description as expandable text area with character counter \n• System shows Document Upload area with drag-and-drop functionality and file type indicators \n• System presents Submit and Cancel buttons with appropriate positioning \n• System displays validation error messages"}}, {"id": 6, "name": "AI Stack"}]}}