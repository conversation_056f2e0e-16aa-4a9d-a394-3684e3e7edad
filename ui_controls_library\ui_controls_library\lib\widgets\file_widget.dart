import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:ui_controls_library/widgets/file_widget_web.dart';
import '../utils/callback_interpreter.dart';
import 'utils/file_widget_json_parser.dart';
import 'components/file_widget_components.dart';

// Conditional imports for platform-specific implementations
import 'file_widget_mobile.dart'  as platform_widget;
import  'file_widget_web.dart' as platform_widget_web;

/// A configurable file upload widget that handles file selection and display.
/// 
/// This widget automatically uses platform-specific implementations:
/// - Mobile/Desktop: Uses FileWidgetMobile
/// - Web: Uses FileWidgetWeb
class FileWidget extends StatelessWidget {
  // Basic properties
  final bool isRequired;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final FileType fileType;
  final int? maxFileSizeBytes;
  final int? maxFiles;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String buttonText;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool showFileName;
  final bool showFileSize;
  final bool showFileType;
  final bool showClearButton;
  final bool showPreview;
  final bool uploadImmediately;
  final bool showProgressBar;
  final bool allowDragDrop;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callbacks
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function()? onClear;
  final Function(List<PlatformFile>)? onUpload;
  final Function(PlatformFile)? onViewFile;
  final Function(PlatformFile)? onOpenFile;
  final Function()? onCancelUpload;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // File-specific JSON configuration
  final bool useJsonFileHandling;
  final Map<String, dynamic>? fileHandlingConfig;

  const FileWidget({
    super.key,
    this.isRequired = false,
    this.allowMultiple = false,
    this.allowedExtensions,
    this.fileType = FileType.any,
    this.maxFileSizeBytes,
    this.maxFiles,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.buttonText = 'Choose File',
    this.showIcon = true,
    this.icon = Icons.upload_file,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showFileName = true,
    this.showFileSize = true,
    this.showFileType = true,
    this.showClearButton = true,
    this.showPreview = false,
    this.uploadImmediately = false,
    this.showProgressBar = false,
    this.allowDragDrop = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onFilesSelected,
    this.onClear,
    this.onUpload,
    this.onViewFile,
    this.onOpenFile,
    this.onCancelUpload,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    this.useJsonFileHandling = false,
    this.fileHandlingConfig,
  });

  /// Creates a FileWidget from a JSON map
  factory FileWidget.fromJson(Map<String, dynamic> json) {
    // Parse JSON callbacks using utility
    final callbackData = FileWidgetJsonParser.parseJsonCallbacks(json);
    var jsonCallbacks = callbackData['jsonCallbacks'] as Map<String, dynamic>?;
    var useJsonCallbacks = callbackData['useJsonCallbacks'] as bool;

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Parse file-specific configuration
    Map<String, dynamic>? fileHandlingConfig;
    bool useJsonFileHandling = json['useJsonFileHandling'] as bool? ?? false;

    if (json['fileHandlingConfig'] != null) {
      if (json['fileHandlingConfig'] is Map) {
        fileHandlingConfig = Map<String, dynamic>.from(
          json['fileHandlingConfig'] as Map,
        );
        useJsonFileHandling = true;
      } else if (json['fileHandlingConfig'] is String) {
        try {
          fileHandlingConfig =
              jsonDecode(json['fileHandlingConfig'] as String)
                  as Map<String, dynamic>;
          useJsonFileHandling = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON using utility parsers
    return FileWidget(
      isRequired: FileWidgetJsonParser.parseBool(json['isRequired']),
      allowMultiple: FileWidgetJsonParser.parseBool(json['allowMultiple']),
      allowedExtensions: FileWidgetJsonParser.parseStringList(
        json['allowedExtensions'],
      ),
      fileType: FileWidgetJsonParser.parseFileType(json['fileType']),
      maxFileSizeBytes: FileWidgetJsonParser.parseInt(json['maxFileSizeBytes']),
      maxFiles: FileWidgetJsonParser.parseInt(json['maxFiles']),
      textColor:
          FileWidgetJsonParser.parseColor(json['textColor']) ?? Colors.black,
      backgroundColor:
          FileWidgetJsonParser.parseColor(json['backgroundColor']) ??
          Colors.white,
      borderColor:
          FileWidgetJsonParser.parseColor(json['borderColor']) ??
          const Color(0xFFE0E0E0),
      borderWidth: FileWidgetJsonParser.parseDouble(
        json['borderWidth'],
        defaultValue: 1.0,
      ),
      borderRadius: FileWidgetJsonParser.parseDouble(
        json['borderRadius'],
        defaultValue: 4.0,
      ),
      hasBorder: FileWidgetJsonParser.parseBool(
        json['hasBorder'],
        defaultValue: true,
      ),
      fontSize: FileWidgetJsonParser.parseDouble(
        json['fontSize'],
        defaultValue: 16.0,
      ),
      fontWeight: FileWidgetJsonParser.parseFontWeight(json['fontWeight']),
      isCompact: FileWidgetJsonParser.parseBool(json['isCompact']),
      hasShadow: FileWidgetJsonParser.parseBool(json['hasShadow']),
      elevation: FileWidgetJsonParser.parseDouble(
        json['elevation'],
        defaultValue: 2.0,
      ),
      isDarkTheme: FileWidgetJsonParser.parseBool(json['isDarkTheme']),
      textAlign: FileWidgetJsonParser.parseTextAlign(json['textAlign']),
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      buttonText: json['buttonText'] as String? ?? 'Choose File',
      showIcon: FileWidgetJsonParser.parseBool(
        json['showIcon'],
        defaultValue: true,
      ),
      icon:
          FileWidgetJsonParser.parseIconData(json['icon']) ?? Icons.upload_file,
      isReadOnly: FileWidgetJsonParser.parseBool(json['isReadOnly']),
      isDisabled: FileWidgetJsonParser.parseBool(json['isDisabled']),
      showFileName: FileWidgetJsonParser.parseBool(
        json['showFileName'],
        defaultValue: true,
      ),
      showFileSize: FileWidgetJsonParser.parseBool(
        json['showFileSize'],
        defaultValue: true,
      ),
      showFileType: FileWidgetJsonParser.parseBool(
        json['showFileType'],
        defaultValue: true,
      ),
      showClearButton: FileWidgetJsonParser.parseBool(
        json['showClearButton'],
        defaultValue: true,
      ),
      showPreview: FileWidgetJsonParser.parseBool(json['showPreview']),
      uploadImmediately: FileWidgetJsonParser.parseBool(
        json['uploadImmediately'],
      ),
      showProgressBar: FileWidgetJsonParser.parseBool(json['showProgressBar']),
      allowDragDrop: FileWidgetJsonParser.parseBool(json['allowDragDrop']),
      width: FileWidgetJsonParser.parseDouble(
        json['width'],
        defaultValue: double.infinity,
      ),
      height: FileWidgetJsonParser.parseDouble(json['height']),
      padding: FileWidgetJsonParser.parseEdgeInsets(json['padding']),
      margin: FileWidgetJsonParser.parseEdgeInsets(json['margin']),
      // Advanced interaction properties
      hoverColor: FileWidgetJsonParser.parseColor(json['hoverColor']),
      focusColor: FileWidgetJsonParser.parseColor(json['focusColor']),
      enableFeedback: FileWidgetJsonParser.parseBool(
        json['enableFeedback'],
        defaultValue: true,
      ),
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      // File-specific JSON configuration
      useJsonFileHandling: useJsonFileHandling,
      fileHandlingConfig: fileHandlingConfig,
    );
  }

  /// Converts the widget configuration to a JSON map
  Map<String, dynamic> toJson() {
    return {};
  }

  @override
  Widget build(BuildContext context) {
    // Use conditional logic to determine which implementation to use
    if (kIsWeb) {
      // Web implementation
      return platform_widget_web.FileWidgetWeb(
        key: key,
        isRequired: isRequired,
        allowMultiple: allowMultiple,
        allowedExtensions: allowedExtensions,
        fileType: fileType,
        maxFileSizeBytes: maxFileSizeBytes,
        maxFiles: maxFiles,
        textColor: textColor,
        backgroundColor: backgroundColor,
        borderColor: borderColor,
        borderWidth: borderWidth,
        borderRadius: borderRadius,
        hasBorder: hasBorder,
        fontSize: fontSize,
        fontWeight: fontWeight,
        isCompact: isCompact,
        hasShadow: hasShadow,
        elevation: elevation,
        isDarkTheme: isDarkTheme,
        textAlign: textAlign,
        label: label,
        hint: hint,
        helperText: helperText,
        errorText: errorText,
        buttonText: buttonText,
        showIcon: showIcon,
        icon: icon,
        isReadOnly: isReadOnly,
        isDisabled: isDisabled,
        showFileName: showFileName,
        showFileSize: showFileSize,
        showFileType: showFileType,
        showClearButton: showClearButton,
        showPreview: showPreview,
        uploadImmediately: uploadImmediately,
        showProgressBar: showProgressBar,
        allowDragDrop: allowDragDrop,
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        onFilesSelected: onFilesSelected,
        onClear: onClear,
        onUpload: onUpload,
        onViewFile: onViewFile,
        onOpenFile: onOpenFile,
        onCancelUpload: onCancelUpload,
        onHover: onHover,
        onFocus: onFocus,
        focusNode: focusNode,
        hoverColor: hoverColor,
        focusColor: focusColor,
        enableFeedback: enableFeedback,
        onTap: onTap,
        onDoubleTap: onDoubleTap,
        onLongPress: onLongPress,
        jsonCallbacks: jsonCallbacks,
        useJsonCallbacks: useJsonCallbacks,
        callbackState: callbackState,
        customCallbackHandlers: customCallbackHandlers,
        jsonConfig: jsonConfig,
        useJsonValidation: useJsonValidation,
        useJsonStyling: useJsonStyling,
        useJsonFormatting: useJsonFormatting,
        useJsonFileHandling: useJsonFileHandling,
        fileHandlingConfig: fileHandlingConfig,
      );
    } else {
      // Mobile/Desktop implementation
      return platform_widget.FileWidgetMobile(
        key: key,
        isRequired: isRequired,
        allowMultiple: allowMultiple,
        allowedExtensions: allowedExtensions,
        fileType: fileType,
        maxFileSizeBytes: maxFileSizeBytes,
        maxFiles: maxFiles,
        textColor: textColor,
        backgroundColor: backgroundColor,
        borderColor: borderColor,
        borderWidth: borderWidth,
        borderRadius: borderRadius,
        hasBorder: hasBorder,
        fontSize: fontSize,
        fontWeight: fontWeight,
        isCompact: isCompact,
        hasShadow: hasShadow,
        elevation: elevation,
        isDarkTheme: isDarkTheme,
        textAlign: textAlign,
        label: label,
        hint: hint,
        helperText: helperText,
        errorText: errorText,
        buttonText: buttonText,
        showIcon: showIcon,
        icon: icon,
        isReadOnly: isReadOnly,
        isDisabled: isDisabled,
        showFileName: showFileName,
        showFileSize: showFileSize,
        showFileType: showFileType,
        showClearButton: showClearButton,
        showPreview: showPreview,
        uploadImmediately: uploadImmediately,
        showProgressBar: showProgressBar,
        allowDragDrop: allowDragDrop,
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        onFilesSelected: onFilesSelected,
        onClear: onClear,
        onUpload: onUpload,
        onViewFile: onViewFile,
        onOpenFile: onOpenFile,
        onCancelUpload: onCancelUpload,
        onHover: onHover,
        onFocus: onFocus,
        focusNode: focusNode,
        hoverColor: hoverColor,
        focusColor: focusColor,
        enableFeedback: enableFeedback,
        onTap: onTap,
        onDoubleTap: onDoubleTap,
        onLongPress: onLongPress,
        jsonCallbacks: jsonCallbacks,
        useJsonCallbacks: useJsonCallbacks,
        callbackState: callbackState,
        customCallbackHandlers: customCallbackHandlers,
        jsonConfig: jsonConfig,
        useJsonValidation: useJsonValidation,
        useJsonStyling: useJsonStyling,
        useJsonFormatting: useJsonFormatting,
        useJsonFileHandling: useJsonFileHandling,
        fileHandlingConfig: fileHandlingConfig,
      );
    }
  }
}
