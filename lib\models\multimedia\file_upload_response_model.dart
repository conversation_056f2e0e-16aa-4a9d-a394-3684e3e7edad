class FileUploadResponseModel {
  final bool success;
  final String filename;
  final String filePath;
  final String contentType;
  final String? message;
  final String? error;

  FileUploadResponseModel({
    required this.success,
    required this.filename,
    required this.filePath,
    required this.contentType,
    this.message,
    this.error,
  });

  factory FileUploadResponseModel.fromJson(Map<String, dynamic> json) {
    return FileUploadResponseModel(
      success: json['success'] ?? false,
      filename: json['filename'] ?? '',
      filePath: json['file_path'] ?? '',
      contentType: json['content_type'] ?? '',
      message: json['message'],
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'filename': filename,
      'file_path': filePath,
      'content_type': contentType,
      if (message != null) 'message': message,
      if (error != null) 'error': error,
    };
  }
}
