import 'package:flutter/material.dart';

@immutable
class BetBreakdown {
  final int gos;
  final int los;
  final int npFunctions;
  final int inputOutputStacks;
  final int subordinateNsl;

  const BetBreakdown({
    required this.gos,
    required this.los,
    required this.npFunctions,
    required this.inputOutputStacks,
    required this.subordinateNsl,
  });

  factory BetBreakdown.fromJson(Map<String, dynamic> json) {
    return BetBreakdown(
      gos: json['gos'] ?? 0,
      los: json['los'] ?? 0,
      npFunctions: json['np_functions'] ?? 0,
      inputOutputStacks: json['input_output_stacks'] ?? 0,
      subordinateNsl: json['subordinate_nsl'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gos': gos,
      'los': los,
      'np_functions': npFunctions,
      'input_output_stacks': inputOutputStacks,
      'subordinate_nsl': subordinateNsl,
    };
  }
}

@immutable
class DetailedBetBreakdownItem {
  final String name;
  final String type;
  final String cost;
  final Map<String, dynamic> details;

  const DetailedBetBreakdownItem({
    required this.name,
    required this.type,
    required this.cost,
    required this.details,
  });

  factory DetailedBetBreakdownItem.fromJson(Map<String, dynamic> json) {
    return DetailedBetBreakdownItem(
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      cost: json['cost'] ?? '',
      details: Map<String, dynamic>.from(json['details'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'cost': cost,
      'details': details,
    };
  }
}

@immutable
class IncomeStatementItem {
  final String item;
  final String amount;
  final String percentage;
  final String betContribution;
  final String? type;

  const IncomeStatementItem({
    required this.item,
    required this.amount,
    required this.percentage,
    required this.betContribution,
    this.type,
  });

  factory IncomeStatementItem.fromJson(Map<String, dynamic> json) {
    return IncomeStatementItem(
      item: json['item'] ?? '',
      amount: json['amount'] ?? '',
      percentage: json['percentage'] ?? '',
      betContribution: json['bet_contribution'] ?? '',
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'item': item,
      'amount': amount,
      'percentage': percentage,
      'bet_contribution': betContribution,
      if (type != null) 'type': type,
    };
  }
}

@immutable
class StandaloneSummary {
  final Map<String, dynamic> totalRevenue;
  final Map<String, dynamic> netMargin;
  final Map<String, dynamic> totalTransactions;
  final Map<String, dynamic> betEfficiency;
  final Map<String, dynamic>? totalCost;
  final Map<String, dynamic>? efficiency;
  final Map<String, dynamic>? annualSalary;
  final Map<String, dynamic>? valueOutput;

  const StandaloneSummary({
    required this.totalRevenue,
    required this.netMargin,
    required this.totalTransactions,
    required this.betEfficiency,
    this.totalCost,
    this.efficiency,
    this.annualSalary,
    this.valueOutput,
  });

  factory StandaloneSummary.fromJson(Map<String, dynamic> json) {
    return StandaloneSummary(
      totalRevenue: Map<String, dynamic>.from(json['total_revenue'] ?? {}),
      netMargin: Map<String, dynamic>.from(json['net_margin'] ?? {}),
      totalTransactions: Map<String, dynamic>.from(json['total_transactions'] ?? {}),
      betEfficiency: Map<String, dynamic>.from(json['bet_efficiency'] ?? {}),
      totalCost: json['total_cost'] != null ? Map<String, dynamic>.from(json['total_cost']) : null,
      efficiency: json['efficiency'] != null ? Map<String, dynamic>.from(json['efficiency']) : null,
      annualSalary: json['annual_salary'] != null ? Map<String, dynamic>.from(json['annual_salary']) : null,
      valueOutput: json['value_output'] != null ? Map<String, dynamic>.from(json['value_output']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_revenue': totalRevenue,
      'net_margin': netMargin,
      'total_transactions': totalTransactions,
      'bet_efficiency': betEfficiency,
      if (totalCost != null) 'total_cost': totalCost,
      if (efficiency != null) 'efficiency': efficiency,
      if (annualSalary != null) 'annual_salary': annualSalary,
      if (valueOutput != null) 'value_output': valueOutput,
    };
  }
}

@immutable
class StandaloneData {
  final StandaloneSummary summary;
  final List<IncomeStatementItem> incomeStatement;
  final List<Map<String, dynamic>>? performanceMetrics;

  const StandaloneData({
    required this.summary,
    required this.incomeStatement,
    this.performanceMetrics,
  });

  factory StandaloneData.fromJson(Map<String, dynamic> json) {
    final incomeStatementJson = json['income_statement'] as List<dynamic>? ?? [];
    final performanceMetricsJson = json['performance_metrics'] as List<dynamic>?;
    
    return StandaloneData(
      summary: StandaloneSummary.fromJson(json['summary'] ?? {}),
      incomeStatement: incomeStatementJson
          .map((item) => IncomeStatementItem.fromJson(item))
          .toList(),
      performanceMetrics: performanceMetricsJson
          ?.map((item) => Map<String, dynamic>.from(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'summary': summary.toJson(),
      'income_statement': incomeStatement.map((item) => item.toJson()).toList(),
      if (performanceMetrics != null) 
        'performance_metrics': performanceMetrics!.map((item) => item).toList(),
    };
  }
}

@immutable
class ConsolidatedData {
  final List<Map<String, dynamic>> summary;

  const ConsolidatedData({
    required this.summary,
  });

  factory ConsolidatedData.fromJson(Map<String, dynamic> json) {
    final summaryJson = json['summary'] as List<dynamic>? ?? [];
    return ConsolidatedData(
      summary: summaryJson
          .map((item) => Map<String, dynamic>.from(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'summary': summary.map((item) => item).toList(),
    };
  }
}

@immutable
class TrendsData {
  final List<Map<String, dynamic>> trends;

  const TrendsData({
    required this.trends,
  });

  factory TrendsData.fromJson(List<dynamic> json) {
    return TrendsData(
      trends: json.map((item) => Map<String, dynamic>.from(item)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trends': trends.map((item) => item).toList(),
    };
  }
}

@immutable
class FinancialDataByNode {
  final String id;
  final String title;
  final String level;
  final StandaloneData standalone;
  final ConsolidatedData consolidated;
  final List<DetailedBetBreakdownItem> betBreakdown;
  final TrendsData trends;

  const FinancialDataByNode({
    required this.id,
    required this.title,
    required this.level,
    required this.standalone,
    required this.consolidated,
    required this.betBreakdown,
    required this.trends,
  });

  factory FinancialDataByNode.fromJson(String nodeId, Map<String, dynamic> json) {
    final betBreakdownJson = json['bet_breakdown'] as List<dynamic>? ?? [];
    final trendsJson = json['trends'] as List<dynamic>? ?? [];
    
    return FinancialDataByNode(
      id: nodeId,
      title: json['title'] ?? '',
      level: json['level'] ?? '',
      standalone: StandaloneData.fromJson(json['standalone'] ?? {}),
      consolidated: ConsolidatedData.fromJson(json['consolidated'] ?? {}),
      betBreakdown: betBreakdownJson
          .map((item) => DetailedBetBreakdownItem.fromJson(item))
          .toList(),
      trends: TrendsData.fromJson(trendsJson),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'level': level,
      'standalone': standalone.toJson(),
      'consolidated': consolidated.toJson(),
      'bet_breakdown': betBreakdown.map((item) => item.toJson()).toList(),
      'trends': trends.toJson(),
    };
  }
}

@immutable
class FinancialSummary {
  final String revenue;
  final String cost;
  final String margin;

  const FinancialSummary({
    required this.revenue,
    required this.cost,
    required this.margin,
  });

  factory FinancialSummary.fromJson(Map<String, dynamic> json) {
    return FinancialSummary(
      revenue: json['revenue']?.toString() ?? '',
      cost: json['cost']?.toString() ?? '',
      margin: json['margin']?.toString() ?? json['efficiency']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'revenue': revenue,
      'cost': cost,
      'margin': margin,
    };
  }
}

@immutable
class NodeMetrics {
  // M4 Level metrics
  final int? m3Nodes;
  final int? totalGos;
  final int? totalLos;
  final String? transactions;
  final String? betEfficiency;
  
  // M3 Level metrics
  final int? m2Nodes;
  final int? gos;
  final int? los;
  
  // M2 Level metrics
  final int? m1Employees;
  final String? teamEfficiency;
  
  // M1 Level metrics
  final int? localObjectives;
  final int? personalBets;
  final String? loEfficiency;

  const NodeMetrics({
    // M4 Level
    this.m3Nodes,
    this.totalGos,
    this.totalLos,
    this.transactions,
    this.betEfficiency,
    // M3 Level
    this.m2Nodes,
    this.gos,
    this.los,
    // M2 Level
    this.m1Employees,
    this.teamEfficiency,
    // M1 Level
    this.localObjectives,
    this.personalBets,
    this.loEfficiency,
  });

  factory NodeMetrics.fromJson(Map<String, dynamic> json) {
    return NodeMetrics(
      // M4 Level
      m3Nodes: json['m3_nodes'],
      totalGos: json['total_gos'],
      totalLos: json['total_los'],
      transactions: json['transactions']?.toString(),
      betEfficiency: json['bet_efficiency']?.toString(),
      // M3 Level
      m2Nodes: json['m2_nodes'],
      gos: json['gos'],
      los: json['los'],
      // M2 Level
      m1Employees: json['m1_employees'],
      teamEfficiency: json['team_efficiency']?.toString(),
      // M1 Level
      localObjectives: json['local_objectives'],
      personalBets: json['personal_bets'],
      loEfficiency: json['lo_efficiency']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // M4 Level
      if (m3Nodes != null) 'm3_nodes': m3Nodes,
      if (totalGos != null) 'total_gos': totalGos,
      if (totalLos != null) 'total_los': totalLos,
      if (transactions != null) 'transactions': transactions,
      if (betEfficiency != null) 'bet_efficiency': betEfficiency,
      // M3 Level
      if (m2Nodes != null) 'm2_nodes': m2Nodes,
      if (gos != null) 'gos': gos,
      if (los != null) 'los': los,
      // M2 Level
      if (m1Employees != null) 'm1_employees': m1Employees,
      if (teamEfficiency != null) 'team_efficiency': teamEfficiency,
      // M1 Level
      if (localObjectives != null) 'local_objectives': localObjectives,
      if (personalBets != null) 'personal_bets': personalBets,
      if (loEfficiency != null) 'lo_efficiency': loEfficiency,
    };
  }
}

@immutable
class NSLHierarchyData1 {
  final String id;
  final String title;
  final String type;
  final String? parent;
  final int totalBets;
  final BetBreakdown betBreakdown;
  final FinancialSummary financialSummary;
  final NodeMetrics? metrics;
  final List<String> children;
  final String levelName;
  final String level; // M4, M3, M2, M1
  final bool isExpanded;
  final String? employeeId; // For M1 level
  final List<NSLHierarchyData1> childNodes;
  final FinancialDataByNode? financialDataByNode; // New field for detailed financial data

  const NSLHierarchyData1({
    required this.id,
    required this.title,
    required this.type,
    this.parent,
    required this.totalBets,
    required this.betBreakdown,
    required this.financialSummary,
    this.metrics,
    this.children = const [],
    required this.levelName,
    required this.level,
    this.isExpanded = true,
    this.employeeId,
    this.childNodes = const [],
    this.financialDataByNode,
  });

  factory NSLHierarchyData1.fromJson(Map<String, dynamic> json, String level, String levelName) {
    final childrenJson = json['children'] as List<dynamic>? ?? [];
    final children = childrenJson.map((child) => child.toString()).toList();

    // Set initial expansion state: M4 and M3 should be expanded by default (second level open)
    bool defaultExpanded = level == 'M4' ;

    return NSLHierarchyData1(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      parent: json['parent']?.toString(),
      totalBets: json['total_bets'] ?? 0,
      betBreakdown: BetBreakdown.fromJson(json['bet_breakdown'] ?? {}),
      financialSummary: FinancialSummary.fromJson(json['financial_summary'] ?? {}),
      metrics: json['metrics'] != null ? NodeMetrics.fromJson(json['metrics']) : null,
      children: children,
      levelName: levelName,
      level: level,
      isExpanded: json['isExpanded'] ?? defaultExpanded,
      employeeId: json['employee_id']?.toString(),
      childNodes: const [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'parent': parent,
      'total_bets': totalBets,
      'bet_breakdown': betBreakdown.toJson(),
      'financial_summary': financialSummary.toJson(),
      if (metrics != null) 'metrics': metrics!.toJson(),
      'children': children,
      'level_name': levelName,
      'level': level,
      'isExpanded': isExpanded,
      'employee_id': employeeId,
      'child_nodes': childNodes.map((child) => child.toJson()).toList(),
    };
  }

  // Helper method to get all nodes in a flat list (for search functionality)
  List<NSLHierarchyData1> getAllNodes() {
    List<NSLHierarchyData1> allNodes = [this];
    for (var child in childNodes) {
      allNodes.addAll(child.getAllNodes());
    }
    return allNodes;
  }

  // Helper method to update expansion state
  NSLHierarchyData1 copyWith({
    String? id,
    String? title,
    String? type,
    String? parent,
    int? totalBets,
    BetBreakdown? betBreakdown,
    FinancialSummary? financialSummary,
    NodeMetrics? metrics,
    List<String>? children,
    String? levelName,
    String? level,
    bool? isExpanded,
    String? employeeId,
    List<NSLHierarchyData1>? childNodes,
    FinancialDataByNode? financialDataByNode,
  }) {
    return NSLHierarchyData1(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      parent: parent ?? this.parent,
      totalBets: totalBets ?? this.totalBets,
      betBreakdown: betBreakdown ?? this.betBreakdown,
      financialSummary: financialSummary ?? this.financialSummary,
      metrics: metrics ?? this.metrics,
      children: children ?? this.children,
      levelName: levelName ?? this.levelName,
      level: level ?? this.level,
      isExpanded: isExpanded ?? this.isExpanded,
      employeeId: employeeId ?? this.employeeId,
      childNodes: childNodes ?? this.childNodes,
      financialDataByNode: financialDataByNode ?? this.financialDataByNode,
    );
  }
}

@immutable
class NSLNode {
  final String id;
  final String title;
  final String type;
  final String? parent;
  final int totalBets;
  final BetBreakdown betBreakdown;
  final FinancialSummary financialSummary;
  final String levelName;
  final String level; // M4, M3, M2, M1
  final Color levelColor;
  final List<NSLNode> children;
  final bool isExpanded;
  final String? employeeId;
  final NSLHierarchyData1 originalData;

  const NSLNode({
    required this.id,
    required this.title,
    required this.type,
    this.parent,
    required this.totalBets,
    required this.betBreakdown,
    required this.financialSummary,
    required this.levelName,
    required this.level,
    this.levelColor = Colors.grey,
    this.children = const [],
    this.isExpanded = true,
    this.employeeId,
    required this.originalData,
  });

  NSLNode copyWith({
    String? id,
    String? title,
    String? type,
    String? parent,
    int? totalBets,
    BetBreakdown? betBreakdown,
    FinancialSummary? financialSummary,
    String? levelName,
    String? level,
    Color? levelColor,
    List<NSLNode>? children,
    bool? isExpanded,
    String? employeeId,
    NSLHierarchyData1? originalData,
  }) {
    return NSLNode(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      parent: parent ?? this.parent,
      totalBets: totalBets ?? this.totalBets,
      betBreakdown: betBreakdown ?? this.betBreakdown,
      financialSummary: financialSummary ?? this.financialSummary,
      levelName: levelName ?? this.levelName,
      level: level ?? this.level,
      levelColor: levelColor ?? this.levelColor,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      employeeId: employeeId ?? this.employeeId,
      originalData: originalData ?? this.originalData,
    );
  }

  factory NSLNode.fromNSLData(NSLHierarchyData1 data) {
    // Convert children recursively
    final childNodes = data.childNodes.map((child) => NSLNode.fromNSLData(child)).toList();
    
    return NSLNode(
      id: data.id,
      title: data.title,
      type: data.type,
      parent: data.parent,
      totalBets: data.totalBets,
      betBreakdown: data.betBreakdown,
      financialSummary: data.financialSummary,
      levelName: data.levelName,
      level: data.level,
      levelColor: getLevelColor(data.level),
      children: childNodes,
      isExpanded: data.isExpanded,
      employeeId: data.employeeId,
      originalData: data,
    );
  }

 static Color getLevelColor(String level) {
  // Define a rich color palette for 20+ levels
  final colorPalette = [
   
    Color(0xFFE4EDFF), // Deep Orange
    Color(0xFFE7F8F5), // Red
    Color(0xFFFFF5E6), // Pink
    Color(0xFFFFE6E6), 
     Color(0xFFFFE4E1), // Deep Orange
    Color(0xFFE0F6FF), // Red
    Color(0xFFF0FFF0), // Pink
    Color(0xFFFFFACD), // Purple
     Color(0xFFE6E6FA), // Deep Orange
    Color(0xFFE7F8F5), // Red
    Color(0xFFFFF5E6), // Pink
    Color(0xFFFFE6E6), 
     Color(0xFFE4EDFF), // Deep Orange
    Color(0xFFE7F8F5), // Red
    Color(0xFFFFF5E6), // Pink
    Color(0xFFFFE6E6), 
     Color(0xFFE4EDFF), // Deep Orange
    Color(0xFFE7F8F5), // Red
    Color(0xFFFFF5E6), // Pink
    Color(0xFFFFE6E6),
  ];
  
  // Extract level number
  final levelNumber = int.tryParse(level.substring(1)) ?? 0;
  
  // Use modulo to cycle through colors if more than 20 levels
  final colorIndex = (levelNumber - 1) % colorPalette.length;
  
  if (colorIndex >= 0 && colorIndex < colorPalette.length) {
    return colorPalette[colorIndex];
  }
  
  return Colors.grey; // Fallback
}

}

class NSLHierarchyBuilder {
  static NSLNode? buildHierarchyFromNSLData(Map<String, dynamic> jsonData) {
    try {
      // Try new structure first (organizational_structure), then fall back to old structure (nsl_hierarchy)
      final organizationalStructure = jsonData['organizational_structure'] ?? jsonData['nsl_hierarchy'];
      if (organizationalStructure == null) return null;

      // Parse financial_data_by_node
      final financialDataByNodeJson = jsonData['financial_data_by_node'] as Map<String, dynamic>? ?? {};
      final Map<String, FinancialDataByNode> financialDataMap = {};
      
      for (var entry in financialDataByNodeJson.entries) {
        final nodeId = entry.key;
        final nodeData = entry.value as Map<String, dynamic>;
        financialDataMap[nodeId] = FinancialDataByNode.fromJson(nodeId, nodeData);
      }

      // Build the complete hierarchy
      final allNodes = <String, NSLHierarchyData1>{};
      
      // Parse M4 (Executive level)
      final m4Data = organizationalStructure['M4'];
      if (m4Data != null) {
        final m4Node = NSLHierarchyData1.fromJson(m4Data['node'], 'M4', m4Data['level_name']);
        final financialData = financialDataMap[m4Node.id];
        allNodes[m4Node.id] = m4Node.copyWith(financialDataByNode: financialData);
      }

      // Parse M3 (Department level)
      final m3Data = organizationalStructure['M3'];
      if (m3Data != null && m3Data['nodes'] != null) {
        for (var nodeJson in m3Data['nodes']) {
          final m3Node = NSLHierarchyData1.fromJson(nodeJson, 'M3', m3Data['level_name']);
          final financialData = financialDataMap[m3Node.id];
          allNodes[m3Node.id] = m3Node.copyWith(financialDataByNode: financialData);
        }
      }

      // Parse M2 (Team level)
      final m2Data = organizationalStructure['M2'];
      if (m2Data != null && m2Data['nodes'] != null) {
        for (var nodeJson in m2Data['nodes']) {
          final m2Node = NSLHierarchyData1.fromJson(nodeJson, 'M2', m2Data['level_name']);
          final financialData = financialDataMap[m2Node.id];
          allNodes[m2Node.id] = m2Node.copyWith(financialDataByNode: financialData);
        }
      }

      // Parse M1 (Individual Employee level)
      final m1Data = organizationalStructure['M1'];
      if (m1Data != null && m1Data['nodes'] != null) {
        for (var nodeJson in m1Data['nodes']) {
          final m1Node = NSLHierarchyData1.fromJson(nodeJson, 'M1', m1Data['level_name']);
          final financialData = financialDataMap[m1Node.id];
          allNodes[m1Node.id] = m1Node.copyWith(financialDataByNode: financialData);
        }
      }

      // Build parent-child relationships
      final Map<String, List<NSLHierarchyData1>> childrenMap = {};
      NSLHierarchyData1? rootNode;

      // Initialize children map
      for (var node in allNodes.values) {
        childrenMap[node.id] = [];
        if (node.level == 'M4') {
          rootNode = node;
        }
      }

      if (rootNode == null) return null;

      // Build relationships based on children IDs
      for (var node in allNodes.values) {
        for (var childId in node.children) {
          if (allNodes.containsKey(childId)) {
            childrenMap[node.id]!.add(allNodes[childId]!);
          }
        }
      }

      // Recursively build the tree with child nodes
      NSLHierarchyData1 buildNodeWithChildren(NSLHierarchyData1 node) {
        final children = childrenMap[node.id] ?? [];
        final childNodes = children.map((child) => buildNodeWithChildren(child)).toList();
        return node.copyWith(childNodes: childNodes);
      }

      final completeTree = buildNodeWithChildren(rootNode);
      return NSLNode.fromNSLData(completeTree);
    } catch (e) {
      print('Error building NSL hierarchy: $e');
      return null;
    }
  }

  static NSLNode? buildFilteredHierarchy(List<NSLHierarchyData1> nodes, String rootNodeId) {
    final rootNode = nodes.where((node) => node.id == rootNodeId).firstOrNull;
    if (rootNode == null) return null;

    // Find all subordinates of the root node
    List<NSLHierarchyData1> filteredNodes = [rootNode];
    _addSubordinates(nodes, rootNodeId, filteredNodes);

    // Build hierarchy from filtered nodes
    // This is a simplified version - you might need to adapt based on your needs
    return NSLNode.fromNSLData(rootNode);
  }

  static void _addSubordinates(List<NSLHierarchyData1> allNodes, String parentId, List<NSLHierarchyData1> result) {
    final subordinates = allNodes.where((node) => node.parent == parentId).toList();
    for (var subordinate in subordinates) {
      if (!result.any((node) => node.id == subordinate.id)) {
        result.add(subordinate);
        _addSubordinates(allNodes, subordinate.id, result);
      }
    }
  }
}
