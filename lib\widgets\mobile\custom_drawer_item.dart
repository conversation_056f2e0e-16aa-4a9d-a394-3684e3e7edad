import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDrawerItem extends StatefulWidget {
  final String icon;
  final String title;
  final VoidCallback? onTap;
  final List<Widget>? children;
  final bool useImage; // set true if using Image.asset instead of SVG

  const CustomDrawerItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.children,
    this.useImage = false,
  });

  @override
  State<CustomDrawerItem> createState() => _CustomDrawerItemState();
}

class _CustomDrawerItemState extends State<CustomDrawerItem> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    Widget leadingWidget = widget.useImage
        ? Image.asset(
          widget.icon,
          width: 20,
          height: 20,
        )
        : SvgPicture.asset(
            widget.icon,
            width: 20,
            height: 20,
            colorFilter: const ColorFilter.mode(
              Colors.black87,
              BlendMode.srcIn,
            ),
          );

    if (widget.children != null && widget.children!.isNotEmpty) {
      return Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
            child: ExpansionTile(
             tilePadding: EdgeInsets.zero,
              childrenPadding: EdgeInsets.only(left: 30),
              leading: null, // remove this, include in title directly
              title: Row(
                children: [
                  SizedBox(width: 20, height: 20, child: leadingWidget),
                  const SizedBox(width: 8),
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'inter',
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              trailing: AnimatedRotation(
                turns: _isExpanded ? 0.5 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.black87,
                  size: 16,
                ),
              ),
              onExpansionChanged: (bool expanded) {
                setState(() {
                  _isExpanded = expanded;
                });
              },
              minTileHeight: 35,
              shape: const Border(),
              collapsedShape: const Border(),
              children: widget.children!,
            ),
          ));
    }

    return ListTile(
      minTileHeight: 35,
      minVerticalPadding: 0,
      horizontalTitleGap: 8,
      leading: leadingWidget,
      title: Text(
        widget.title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          fontFamily: 'inter',
          color: Colors.black87,
        ),
      ),
      onTap: widget.onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }
}
