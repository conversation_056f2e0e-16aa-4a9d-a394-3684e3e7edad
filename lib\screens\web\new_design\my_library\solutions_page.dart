import 'package:flutter/material.dart';

class SolutionsPage extends StatefulWidget {
  const SolutionsPage({super.key});

  @override
  State<SolutionsPage> createState() => _SolutionsPageState();
}

class _SolutionsPageState extends State<SolutionsPage> {
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Side Navigation
          SideNavigation(selectedIndex: 1),
          
          // Main Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top Bar with stats and back button
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    border: Border(bottom: BorderSide(color: Colors.grey.shade300))
                  ),
                  child: Row(
                    children: [
                      const Spacer(),
                      _buildStatItem(Icons.book, '12 Books'),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.article_outlined, '35 Solution'),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.category, '102 Object'),
                      const SizedBox(width: 32),
                      IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.blue),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                
                // Page Header with search and create
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Text(
                        'My Solutions',
                        style: TextStyle(
                          fontSize: 24, 
                          fontWeight: FontWeight.bold
                        ),
                      ),
                      const Spacer(),
                      // Search box
                      Container(
                        width: 300,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search',
                            border: InputBorder.none,
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.tune),
                              onPressed: () {},
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Create Solution Button
                      ElevatedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.add),
                        label: const Text('Create Solution'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.black,
                          elevation: 1,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Grid of Solutions - matching 12-column grid from design
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // Calculate columns based on available width
                        // Default to 5 columns for larger screens matching first page grid
                        int crossAxisCount = 5;
                        
                        // Desktop layout with 5 columns
                        if (constraints.maxWidth >= 1400) {
                          crossAxisCount = 5;
                        } 
                        // Medium width with 4 columns
                        else if (constraints.maxWidth >= 1100) {
                          crossAxisCount = 4;
                        }
                        // Smaller screens with 3 columns 
                        else if (constraints.maxWidth >= 800) {
                          crossAxisCount = 3;
                        }
                        // Mobile layout with 2 columns
                        else if (constraints.maxWidth >= 600) {
                          crossAxisCount = 2;
                        }
                        // Very small screens with 1 column
                        else {
                          crossAxisCount = 1;
                        }
                        
                        return GridView.builder(
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: crossAxisCount,
                            childAspectRatio: 0.8,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: solutionsList.length,
                          itemBuilder: (context, index) {
                            final solution = solutionsList[index];
                            return SolutionCard(solution: solution);
                          },
                        );
                      }
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStatItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey.shade700),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(color: Colors.grey.shade700, fontSize: 14),
        ),
      ],
    );
  }
}

class SolutionCard extends StatelessWidget {
  final Solution solution;
  
  const SolutionCard({super.key, required this.solution});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Solution Document Icon
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.description_outlined,
                    size: 48,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
            
            // Solution Info
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    solution.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    solution.categoryType,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    solution.version,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SideNavigation extends StatelessWidget {
  final int selectedIndex;
  
  const SideNavigation({super.key, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 16),
          // Logo
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                'nb',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Chat Icon
          _buildNavItem(context, Icons.chat_bubble_outline, 0),
          
          // App Icon (Selected)
          _buildNavItem(context, Icons.apps, 1, isSelected: selectedIndex == 1),
          
          // Briefcase Icon
          _buildNavItem(context, Icons.work_outline, 2),
          
          // Document Icon
          _buildNavItem(context, Icons.description_outlined, 3),
          
          // Calendar Icon
          _buildNavItem(context, Icons.calendar_today, 4),
          
          // Notification Icon
          _buildNavItem(context, Icons.notifications_none, 5),
          
          const Spacer(),
          
          // Profile Icon
          _buildNavItem(context, Icons.person_outline, 6),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
  
  Widget _buildNavItem(BuildContext context, IconData icon, int index, {bool isSelected = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade100 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: () {},
        icon: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey.shade600,
        ),
      ),
    );
  }
}

// Model class for Solution
class Solution {
  final String id;
  final String title;
  final String categoryType;
  final String version;
  
  Solution({
    required this.id,
    required this.title,
    required this.categoryType,
    required this.version,
  });
}

// Sample data for solutions
final List<Solution> solutionsList = List.generate(
  10,
  (index) => Solution(
    id: (index + 1).toString(),
    title: 'Ecommerce',
    categoryType: 'B2C',
    version: 'V00172',
  ),
);
