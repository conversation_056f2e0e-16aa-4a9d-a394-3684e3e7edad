import 'package:flutter/material.dart';
import 'app_colors.dart';
import '../utils/constants.dart';

/// AppThemeConstants provides all the theme-related constants for the application
class AppThemeConstants {
  // Private constructor to prevent instantiation
  AppThemeConstants._();
  
  // Font Families
  static const String sfProTextFontFamily = AppConstants.primaryFontFamily;
  static const String tiemposTextFontFamily = AppConstants.secondaryFontFamily;
  
  // Spacing
  static const double spacingXxs = 2.0;
  static const double spacingXs = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;
  
  // Border radius
  static const double borderRadiusXs = 2.0;
  static const double borderRadiusS = 4.0;
  static const double borderRadiusM = 8.0;
  static const double borderRadiusL = 16.0;
  static const double borderRadiusXl = 24.0;
  static const double borderRadiusCircular = 100.0;
  
  // Elevation
  static const double elevationXs = 0.5;
  static const double elevationS = 1.0;
  static const double elevationM = 2.0;
  static const double elevationL = 4.0;
  static const double elevationXl = 8.0;
  
  // Animation durations
  static const Duration durationShort = Duration(milliseconds: 150);
  static const Duration durationMedium = Duration(milliseconds: 300);
  static const Duration durationLong = Duration(milliseconds: 500);
  
  // Icon sizes
  static const double iconSizeS = 16.0;
  static const double iconSizeM = 24.0;
  static const double iconSizeL = 32.0;
  static const double iconSizeXl = 48.0;
  
  // Button sizes
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  
  // Input field heights
  static const double inputHeightS = 40.0;
  static const double inputHeightM = 48.0;
  static const double inputHeightL = 56.0;
  
  // Avatar sizes
  static const double avatarSizeS = 32.0;
  static const double avatarSizeM = 40.0;
  static const double avatarSizeL = 56.0;
  static const double avatarSizeXl = 80.0;
  
  // Card padding
  static const EdgeInsets cardPaddingS = EdgeInsets.all(spacingS);
  static const EdgeInsets cardPaddingM = EdgeInsets.all(spacingM);
  static const EdgeInsets cardPaddingL = EdgeInsets.all(spacingL);
  
  // Button padding
  static const EdgeInsets buttonPaddingS = EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingXs);
  static const EdgeInsets buttonPaddingM = EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingS);
  static const EdgeInsets buttonPaddingL = EdgeInsets.symmetric(horizontal: spacingXl, vertical: spacingM);
  
  // Input padding
  static const EdgeInsets inputPaddingS = EdgeInsets.symmetric(horizontal: spacingS, vertical: spacingXs);
  static const EdgeInsets inputPaddingM = EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingS);
  static const EdgeInsets inputPaddingL = EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM);
  
  // Text styles - Light Theme
  static TextStyle getHeadingLarge(bool isDark) => TextStyle(
    fontFamily: sfProTextFontFamily,
    fontSize: 28.0,
    fontWeight: FontWeight.bold,
    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
    height: 1.3,
  );
  
  static TextStyle getHeadingMedium(bool isDark) => TextStyle(
    fontFamily: sfProTextFontFamily,
    fontSize: 24.0,
    fontWeight: FontWeight.bold,
    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
    height: 1.3,
  );
  
  static TextStyle getHeadingSmall(bool isDark) => TextStyle(
    fontFamily: sfProTextFontFamily,
    fontSize: 20.0,
    fontWeight: FontWeight.w600,
    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
    height: 1.3,
  );
  
  static TextStyle getBodyLarge(bool isDark) => TextStyle(
    fontFamily: tiemposTextFontFamily,
    fontSize: 16.0,
    fontWeight: FontWeight.normal,
    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
    height: 1.5,
  );
  
  static TextStyle getBodyMedium(bool isDark) => TextStyle(
    fontFamily: tiemposTextFontFamily,
    fontSize: 15.0,
    fontWeight: FontWeight.normal,
    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
    height: 1.5,
  );
  
  static TextStyle getBodySmall(bool isDark) => TextStyle(
    fontFamily: tiemposTextFontFamily,
    fontSize: 14.0,
    fontWeight: FontWeight.normal,
    color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
    height: 1.5,
  );
  
  static TextStyle getButtonText(bool isDark) => TextStyle(
    fontFamily: sfProTextFontFamily,
    fontSize: 16.0,
    fontWeight: FontWeight.w500,
    color: Colors.white,
    height: 1.5,
  );
  
  static TextStyle getLabelMedium(bool isDark) => TextStyle(
    fontFamily: sfProTextFontFamily,
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
  );
  
  static TextStyle getLabelSmall(bool isDark) => TextStyle(
    fontFamily: sfProTextFontFamily,
    fontSize: 12.0,
    fontWeight: FontWeight.w500,
    color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
  );
}
