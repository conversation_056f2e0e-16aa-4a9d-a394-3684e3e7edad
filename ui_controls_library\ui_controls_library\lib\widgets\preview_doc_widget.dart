import 'dart:io';
import 'dart:convert';
// ignore: deprecated_member_use
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Enum for preview document states
enum PreviewDocState {
  defaultState,    // Shows filename with 3 icons
  hoverState,      // Blue border on hover
  touchState,      // Touch interaction
  disabledState,   // Grayed out
}

/// Document preview widget with file display and preview functionality
///
/// This widget provides a document preview interface with features like:
/// - File display with name and icons
/// - Hover state with border color change
/// - Full-screen document preview modal
/// - Action icons (eye, navigate, download)
/// - Responsive design
class PreviewDocWidget extends StatefulWidget {
  /// The document source URL or path
  final String? documentSource;

  /// The document filename to display
  final String fileName;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// Whether the widget is read-only
  final bool isReadOnly;

  /// The theme color for the preview
  final Color themeColor;

  /// The background color for the preview
  final Color backgroundColor;

  /// The text color for the preview
  final Color textColor;

  /// The border radius for the preview container
  final double borderRadius;

  /// Whether to show a border around the preview
  final bool hasBorder;

  /// The border color for the preview
  final Color borderColor;

  /// The border width for the preview
  final double borderWidth;

  /// Whether to show a shadow under the preview
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The width of the preview
  final double? width;

  /// The height of the preview
  final double? height;

  /// The padding around the preview
  final EdgeInsetsGeometry padding;

  /// The margin around the preview
  final EdgeInsetsGeometry margin;

  /// Whether to show the eye icon
  final bool showEyeIcon;

  /// Whether to show the navigate icon
  final bool showNavigateIcon;

  /// Whether to show the download icon
  final bool showDownloadIcon;

  /// The font size for the filename text
  final double fontSize;

  /// The font weight for the filename text
  final FontWeight fontWeight;

  /// Whether to use dark theme
  final bool isDarkTheme;

  /// The callback when the eye icon is clicked (preview)
  final VoidCallback? onPreview;

  /// The callback when the navigate icon is clicked (open externally)
  final VoidCallback? onNavigate;

  /// The callback when the download icon is clicked
  final VoidCallback? onDownload;

  /// The callback when the widget is tapped
  final VoidCallback? onTap;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Document-specific JSON configuration
  /// Whether to use JSON document configuration
  final bool useJsonDocumentConfig;

  /// Document-specific JSON configuration
  final Map<String, dynamic>? documentConfig;

  /// Creates a document preview widget.
  const PreviewDocWidget({
    super.key,
    this.documentSource,
    this.fileName = 'Member',
    this.isDisabled = false,
    this.isReadOnly = false,
    this.themeColor = const Color(0xFF0058FF),
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.showEyeIcon = true,
    this.showNavigateIcon = true,
    this.showDownloadIcon = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isDarkTheme = false,
    this.onPreview,
    this.onNavigate,
    this.onDownload,
    this.onTap,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Document-specific JSON configuration
    this.useJsonDocumentConfig = false,
    this.documentConfig,
  });

  /// Creates a PreviewDocWidget from a JSON map
  factory PreviewDocWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color themeColor = const Color(0xFF0058FF);
    if (json.containsKey('themeColor')) {
      themeColor = _parseColor(json['themeColor']);
    }

    Color backgroundColor = Colors.white;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color textColor = Colors.black;
    if (json.containsKey('textColor')) {
      textColor = _parseColor(json['textColor']);
    }

    Color borderColor =  const Color(0xFFCCCCCC);
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = const EdgeInsets.all(0.0);
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    return PreviewDocWidget(
      // Basic properties
      documentSource: json['documentSource'] as String?,
      fileName: json['fileName'] as String? ?? 'Document',
      isDisabled: json['isDisabled'] as bool? ?? false,
      isReadOnly: json['isReadOnly'] as bool? ?? false,

      // Display properties
      themeColor: themeColor,
      backgroundColor: backgroundColor,
      textColor: textColor,
      borderRadius: json['borderRadius'] != null ? (json['borderRadius'] as num).toDouble() : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderColor: _parseColor(json['borderColor']) ?? const Color(0xFFCCCCCC),
      borderWidth: json['borderWidth'] != null ? (json['borderWidth'] as num).toDouble() : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: json['elevation'] != null ? (json['elevation'] as num).toDouble() : 2.0,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: padding,
      margin: margin,

      // Icon properties
      showEyeIcon: json['showEyeIcon'] as bool? ?? true,
      showNavigateIcon: json['showNavigateIcon'] as bool? ?? true,
      showDownloadIcon: json['showDownloadIcon'] as bool? ?? true,

      // Text properties
      fontSize: json['fontSize'] != null ? (json['fontSize'] as num).toDouble() : 16.0,
      fontWeight: _parseFontWeight(json['fontWeight']),
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,

      // Advanced interaction properties
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks: json.containsKey('callbacks') ? json['callbacks'] as Map<String, dynamic> : null,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      callbackState: json.containsKey('callbackState') ? json['callbackState'] as Map<String, dynamic> : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonDocumentConfig: json['useJsonDocumentConfig'] as bool? ?? false,
      documentConfig: json.containsKey('documentConfig') ? json['documentConfig'] as Map<String, dynamic> : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return const Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return const Color(0xFF0058FF);
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return const Color(0xFF0058FF); // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal: value.containsKey('horizontal') ? (value['horizontal'] as num).toDouble() : 0.0,
          vertical: value.containsKey('vertical') ? (value['vertical'] as num).toDouble() : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom') ? (value['bottom'] as num).toDouble() : 0.0,
        );
      }
    }
    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0); // Default padding
  }

  /// Parses font weight from string or int
  static FontWeight _parseFontWeight(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'thin':
          return FontWeight.w100;
        case 'extralight':
          return FontWeight.w200;
        case 'light':
          return FontWeight.w300;
        case 'regular':
          return FontWeight.w400;
        case 'medium':
          return FontWeight.w500;
        case 'semibold':
          return FontWeight.w600;
        case 'bold':
          return FontWeight.w700;
        case 'extrabold':
          return FontWeight.w800;
        case 'black':
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    } else if (value is int) {
      switch (value) {
        case 100:
          return FontWeight.w100;
        case 200:
          return FontWeight.w200;
        case 300:
          return FontWeight.w300;
        case 400:
          return FontWeight.w400;
        case 500:
          return FontWeight.w500;
        case 600:
          return FontWeight.w600;
        case 700:
          return FontWeight.w700;
        case 800:
          return FontWeight.w800;
        case 900:
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    }
    return FontWeight.normal;
  }

  /// Converts the widget to a JSON map
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'documentSource': documentSource,
      'fileName': fileName,
      'isDisabled': isDisabled,
      'isReadOnly': isReadOnly,

      // Display properties
      'themeColor': '#${themeColor.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'textColor': '#${textColor.toHexString()}',
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'width': width,
      'height': height,

      // Icon properties
      'showEyeIcon': showEyeIcon,
      'showNavigateIcon': showNavigateIcon,
      'showDownloadIcon': showDownloadIcon,

      // Text properties
      'fontSize': fontSize,
      'isDarkTheme': isDarkTheme,

      // Advanced interaction properties
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonDocumentConfig': useJsonDocumentConfig,
    };
  }

  @override
  State<PreviewDocWidget> createState() => _PreviewDocWidgetState();
  
  Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

  
}

class _PreviewDocWidgetState extends State<PreviewDocWidget> {
  PreviewDocState _currentState = PreviewDocState.defaultState;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  // Icon colors
  final Color hoverIconColor = const Color(0xFF0058FF);
  final Color defaultIconColor = const Color(0xFFCCCCCC);

  @override
  void initState() {
    super.initState();
    
    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Set initial state based on disabled property
    if (widget.isDisabled) {
      _currentState = PreviewDocState.disabledState;
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  void _onHoverChange(bool isHovered) {
    if (widget.isDisabled) return;
    
    setState(() {
      _isHovered = isHovered;
      _currentState = isHovered ? PreviewDocState.hoverState : PreviewDocState.defaultState;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  @override
  void didUpdateWidget(PreviewDocWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null && widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Update state based on disabled property
    if (widget.isDisabled != oldWidget.isDisabled) {
      setState(() {
        _currentState = widget.isDisabled ? PreviewDocState.disabledState : PreviewDocState.defaultState;
      });
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  /// Shows the document preview dialog
  void _showDocumentPreview() {
    if (widget.isDisabled || widget.isReadOnly) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return DocumentPreviewDialog(
          documentSource: widget.documentSource,
          fileName: widget.fileName,
          themeColor: widget.themeColor,
        );
      },
    );

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks && widget.jsonCallbacks != null && widget.jsonCallbacks!.containsKey('onPreview')) {
      _executeJsonCallback('onPreview');
    }

    // Call standard callback
    if (widget.onPreview != null) {
      widget.onPreview!();
    }
  }

  /// Opens the document in a new window/tab
  void _openDocument() {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      if (kIsWeb) {
        // Web implementation - open in new tab/window
        if (widget.documentSource != null && widget.documentSource!.isNotEmpty) {
          // If we have a document source URL, open it directly
          html.window.open(widget.documentSource!, '_blank');
        } else {
          // Create a blob URL and open it in new tab
          final bytes = _generateSamplePdfBytes();
          final blob = html.Blob([bytes]);
          final url = html.Url.createObjectUrlFromBlob(blob);
          html.window.open(url, '_blank');
          
          // Clean up the blob URL after a delay
          Future.delayed(const Duration(seconds: 5), () {
            html.Url.revokeObjectUrl(url);
          });
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Opened ${widget.fileName} in new tab'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // Mobile/Desktop implementation
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Opening ${widget.fileName} externally...'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to open document: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks && widget.jsonCallbacks != null && widget.jsonCallbacks!.containsKey('onNavigate')) {
      _executeJsonCallback('onNavigate');
    }

    // Call standard callback
    if (widget.onNavigate != null) {
      widget.onNavigate!();
    }
  }

  // Generate sample PDF bytes for download/navigation
  List<int> _generateSamplePdfBytes() {
    // This is a simple text file for demo purposes
    // In a real implementation, you would generate actual PDF bytes
    final content = '''
${widget.fileName}

Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

• Morbi viverra semper lorem nec molestie.
• Maecenas tincidunt est efficitur ligula euismod.
• Sit amet ornare est vulputate.

Vestibulum neque massa, scelerisque sit amet ligula eu, congue molestie mi. 
Praesent ut varius sem.

Generated on: ${DateTime.now().toString()}
''';
    return utf8.encode(content);
  }

  /// Downloads the document
  void _downloadDocument() {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      if (kIsWeb) {
        // Web download implementation
        final bytes = _generateSamplePdfBytes();
        final blob = html.Blob([bytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.document.createElement('a') as html.AnchorElement
          ..href = url
          ..style.display = 'none'
          ..download = widget.fileName;
        html.document.body?.children.add(anchor);
        anchor.click();
        html.document.body?.children.remove(anchor);
        html.Url.revokeObjectUrl(url);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Downloaded ${widget.fileName}'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // Mobile/Desktop download implementation
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download started: ${widget.fileName}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Download failed: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks && widget.jsonCallbacks != null && widget.jsonCallbacks!.containsKey('onDownload')) {
      _executeJsonCallback('onDownload');
    }

    // Call standard callback
    if (widget.onDownload != null) {
      widget.onDownload!();
    }
  }

  /// Gets responsive font size based on screen width
  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return widget.fontSize * 1.125; // 18.0 for 16.0 base
    } else if (screenWidth >= 1440) {
      return widget.fontSize; // 16.0
    } else if (screenWidth >= 1280) {
      return widget.fontSize * 0.875; // 14.0 for 16.0 base
    } else if (screenWidth >= 768) {
      return widget.fontSize * 0.75; // 12.0 for 16.0 base
    } else {
      return widget.fontSize * 0.625; // 10.0 for 16.0 base
    }
  }

  /// Gets responsive icon size based on screen width
  double _getResponsiveIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 24.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 22.0; // Large
    } else if (screenWidth >= 1280) {
      return 20.0; // Medium
    } else if (screenWidth >= 768) {
      return 18.0; // Small
    } else {
      return 18.0; // Extra Small
    }
  }

  /// Gets responsive height based on screen width
  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 48.0; // Large
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium
    } else if (screenWidth >= 768) {
      return 32.0; // Small
    } else {
      return 32.0; // Default for very small screens
    }
  }

  /// Gets responsive padding based on screen width
  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0);
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0);
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0);
    } else {
      return const EdgeInsets.symmetric(horizontal: 6.0, vertical: 1.0);
    }
  }

  /// Gets the file icon based on filename extension
  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.folder_zip;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// Gets the file icon color based on filename extension
  Color _getFileIconColor(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return const Color(0xFFD32F2F); // Red for PDF
      case 'doc':
      case 'docx':
        return const Color(0xFF1976D2); // Blue for Word
      case 'xls':
      case 'xlsx':
        return const Color(0xFF388E3C); // Green for Excel
      case 'ppt':
      case 'pptx':
        return const Color(0xFFFF5722); // Orange for PowerPoint
      case 'txt':
        return const Color(0xFF757575); // Gray for text files
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return const Color(0xFF9C27B0); // Purple for images
      case 'mp4':
      case 'avi':
      case 'mov':
        return const Color(0xFFE91E63); // Pink for videos
      case 'mp3':
      case 'wav':
      case 'flac':
        return const Color(0xFF4CAF50); // Green for audio
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF795548); // Brown for archives
      default:
        return const Color(0xFF666666); // Default gray
    }
  }

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = widget.width ?? double.infinity;
    final effectiveHeight = widget.height ?? _getResponsiveHeight(context);
    final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Determine border color based on state
    Color borderColor;
    if (_currentState == PreviewDocState.disabledState) {
      borderColor = const Color(0xFFCCCCCC);
    } else if (_currentState == PreviewDocState.hoverState) {
      borderColor = widget.hoverColor ?? widget.themeColor;
    } else if (_isFocused) {
      borderColor = widget.focusColor ?? widget.themeColor;
    } else {
      borderColor = widget.borderColor;
    }

    // Determine icon color based on state
    Color iconColor;
    if (_currentState == PreviewDocState.disabledState) {
      iconColor = Colors.grey.shade400;
    } else if (_isHovered) {
      iconColor = hoverIconColor;
    } else {
      iconColor = defaultIconColor;
    }

    Widget content = Container(
      width: effectiveWidth,
      height: effectiveHeight,
      padding: _getResponsivePadding(context),
      margin: widget.margin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color:  
                //const Color(0xFFCCCCCC),
                borderColor,
                //width: widget.borderWidth,
                width: 1,
              )
            : null,
        // boxShadow: widget.hasShadow
        //     ? [
        //         BoxShadow(
        //           color: Colors.black.withAlpha(51),
        //           blurRadius: widget.elevation,
        //           offset: Offset(0, widget.elevation / 2),
        //         ),
        //       ]
        //     : null,
      ),
      child: Row(
        children: [
          // File type icon
          Icon(
            _getFileIcon(widget.fileName),
            size: _getResponsiveIconSize(context),
            color: _currentState == PreviewDocState.disabledState
                ? Colors.grey.shade400
                : _getFileIconColor(widget.fileName),
          ),
          const SizedBox(width: 8),
          
          // File name
          Expanded(
            child: Text(
              widget.fileName,
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                fontWeight: widget.fontWeight,
                color: _currentState == PreviewDocState.disabledState
                    ? Colors.grey.shade400
                    : widget.textColor,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Action icons
          if (widget.showEyeIcon) ...[
            GestureDetector(
              onTap: _showDocumentPreview,
              child: Icon(
                Icons.visibility,
                size: _getResponsiveIconSize(context),
                color: _getBorderColor(),
              ),
            ),
            const SizedBox(width: 5),
          ],
          
          if (widget.showNavigateIcon) ...[
            GestureDetector(
              onTap: _openDocument,
              child: Icon(
                Icons.open_in_new,
                size: _getResponsiveIconSize(context),
                color: _getBorderColor(),
                //iconColor,
              ),
            ),
            const SizedBox(width: 5),
          ],
          
          if (widget.showDownloadIcon) ...[
            GestureDetector(
              onTap: _downloadDocument,
              child: Icon(
                Icons.download,
                size: _getResponsiveIconSize(context),
                color: _getBorderColor(),
              ),
            ),
          ],
        ],
      ),
    );

    // Apply hover detection
    content = MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor: widget.isDisabled ? SystemMouseCursors.forbidden : SystemMouseCursors.click,
      child: content,
    );

    // Apply focus handling
    content = Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onFocusChange: (hasFocus) => _onFocusChange(),
      child: content,
    );

    // Apply gesture detection
    if (widget.onTap != null || widget.onDoubleTap != null || widget.onLongPress != null) {
      content = GestureDetector(
        onTap: widget.onTap != null && !widget.isDisabled
            ? () {
                // Execute onTap callback if defined in JSON
                if (widget.useJsonCallbacks &&
                    widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onTap')) {
                  _executeJsonCallback('onTap');
                }

                // Call standard callback
                widget.onTap!();
              }
            : null,
        onDoubleTap: widget.onDoubleTap != null && !widget.isDisabled
            ? () {
                // Execute onDoubleTap callback if defined in JSON
                if (widget.useJsonCallbacks &&
                    widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                  _executeJsonCallback('onDoubleTap');
                }

                // Call standard callback
                widget.onDoubleTap!();
              }
            : null,
        onLongPress: widget.onLongPress != null && !widget.isDisabled
            ? () {
                // Execute onLongPress callback if defined in JSON
                if (widget.useJsonCallbacks &&
                    widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onLongPress')) {
                  _executeJsonCallback('onLongPress');
                }

                // Call standard callback
                widget.onLongPress!();
              }
            : null,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }
  
  Color _getBorderColor() {
    if (_isFocused) {
      return widget.focusColor ?? const Color(0xFF0058FF);
    } else if (_isHovered) {
      return widget.hoverColor ?? const Color(0xFF0058FF);
    } else {
      return widget.borderColor;
    }
  }
}

/// Document Preview Dialog for dialog-based document viewing
class DocumentPreviewDialog extends StatefulWidget {
  final String? documentSource;
  final String fileName;
  final Color themeColor;

  const DocumentPreviewDialog({
    super.key,
    this.documentSource,
    required this.fileName,
    this.themeColor = const Color(0xFF0058FF),
  });

  @override
  State<DocumentPreviewDialog> createState() => _DocumentPreviewDialogState();
}

class _DocumentPreviewDialogState extends State<DocumentPreviewDialog> {
  double _zoomLevel = 1.0;
  int _currentPage = 1;
  final int _totalPages = 5; // Simulated total pages
  bool _isLoading = true;
  bool _hasError = false;
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  @override
  void initState() {
    super.initState();
    
    // Simulate document loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Simulate error if no document source
          if (widget.documentSource == null || widget.documentSource!.isEmpty) {
            _hasError = false; // For demo, we'll show content anyway
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _closeDialog() {
    Navigator.of(context).pop();
  }

  void _zoomIn() {
    setState(() {
      _zoomLevel = math.min(3.0, _zoomLevel + 0.25);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = math.max(0.5, _zoomLevel - 0.25);
    });
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  void _toggleSearch() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        _searchController.clear();
      }
    });
  }

  void _downloadDocument() {
    try {
      if (kIsWeb) {
        // Web download implementation
        final bytes = _generateSamplePdfBytes();
        final blob = html.Blob([bytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.document.createElement('a') as html.AnchorElement
          ..href = url
          ..style.display = 'none'
          ..download = widget.fileName;
        html.document.body?.children.add(anchor);
        anchor.click();
        html.document.body?.children.remove(anchor);
        html.Url.revokeObjectUrl(url);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Downloaded ${widget.fileName}'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // Mobile/Desktop download implementation
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download started: ${widget.fileName}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Download failed: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Generate sample PDF bytes for download
  List<int> _generateSamplePdfBytes() {
    // This is a simple text file for demo purposes
    // In a real implementation, you would generate actual PDF bytes
    final content = '''
${widget.fileName}

Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

Page $_currentPage of $_totalPages

• Morbi viverra semper lorem nec molestie.
• Maecenas tincidunt est efficitur ligula euismod.
• Sit amet ornare est vulputate.

Vestibulum neque massa, scelerisque sit amet ligula eu, congue molestie mi. 
Praesent ut varius sem.
''';
    return utf8.encode(content);
  }

  Widget _buildToolbar() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Left side - Menu icon and file name
          IconButton(
            icon: const Icon(Icons.menu, size: 18),
            onPressed: () {},
            tooltip: 'Menu',
            color: Colors.grey.shade600,
          ),
          Expanded(
            flex: 3,
            child: Text(
              widget.fileName.length > 12 ? '${widget.fileName.substring(0, 12)}...' : widget.fileName,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          // Page navigation
          Text(
            '$_currentPage',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '/',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '$_totalPages',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Zoom controls
          IconButton(
            icon: const Icon(Icons.remove, size: 16),
            onPressed: _zoomOut,
            tooltip: 'Zoom Out',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade400),
              borderRadius: BorderRadius.circular(2),
            ),
            child: Text(
              '${(_zoomLevel * 100).round()}%',
              style: const TextStyle(fontSize: 11),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.add, size: 16),
            onPressed: _zoomIn,
            tooltip: 'Zoom In',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          
          const SizedBox(width: 8),
          
          // Right side actions
          IconButton(
            icon: const Icon(Icons.file_download, size: 18),
            onPressed: _downloadDocument,
            tooltip: 'Download',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.print, size: 18),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Print ${widget.fileName}'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            tooltip: 'Print',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 18),
            onPressed: _closeDialog,
            tooltip: 'Close',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, size: 18),
            onPressed: () {
              // Show more options menu
              _showMoreOptions();
            },
            tooltip: 'More options',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  void _showMoreOptions() {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        MediaQuery.of(context).size.width - 200,
        50,
        MediaQuery.of(context).size.width,
        100,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.search, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Search'),
            ],
          ),
          onTap: _toggleSearch,
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.fullscreen, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Full Screen'),
            ],
          ),
          onTap: () {
            // Toggle full screen
          },
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Document Info'),
            ],
          ),
          onTap: () {
            // Show document info
          },
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    if (!_showSearchBar) return const SizedBox.shrink();
    
    return Container(
      height: 40,
      color: Colors.grey.shade100,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search in document...',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 12),
              onSubmitted: (value) {
                // Implement search functionality
              },
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.arrow_upward, size: 16),
            onPressed: () {
              // Previous search result
            },
            tooltip: 'Previous',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_downward, size: 16),
            onPressed: () {
              // Next search result
            },
            tooltip: 'Next',
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 16),
            onPressed: _toggleSearch,
            tooltip: 'Close Search',
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Failed to load document',
              style: TextStyle(color: Colors.grey.shade700, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Transform.scale(
          scale: _zoomLevel,
          child: Container(
            width: 400,
            height: 500,
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Document header
                  Text(
                    widget.fileName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Page $_currentPage',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // Document content
                  const Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                            style: TextStyle(fontSize: 12, height: 1.5),
                          ),
                          SizedBox(height: 16),
                          
                          Text('• Morbi viverra semper lorem nec molestie.'),
                          Text('• Maecenas tincidunt est efficitur ligula euismod.'),
                          Text('• Sit amet ornare est vulputate.'),
                          
                          SizedBox(height: 20),
                          
                          Text(
                            'Vestibulum neque massa, scelerisque sit amet ligula eu, congue molestie mi. Praesent ut varius sem.',
                            style: TextStyle(fontSize: 12, height: 1.5),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: Container(
        width: screenSize.width * 0.8,
        height: screenSize.height * 0.8,
        constraints: const BoxConstraints(
          maxWidth: 800,
          maxHeight: 600,
        ),
        child: Column(
          children: [
            _buildToolbar(),
            _buildSearchBar(),
            Expanded(
              child: _buildDocumentContent(),
            ),
          ],
        ),
      ),
    );
  }
}

/// Document Preview Modal for full-screen document viewing (kept for backward compatibility)
class DocumentPreviewModal extends StatefulWidget {
  final String? documentSource;
  final String fileName;
  final Color themeColor;

  const DocumentPreviewModal({
    super.key,
    this.documentSource,
    required this.fileName,
    this.themeColor = const Color(0xFF0058FF),
  });

  @override
  State<DocumentPreviewModal> createState() => _DocumentPreviewModalState();
}

class _DocumentPreviewModalState extends State<DocumentPreviewModal> {
  double _zoomLevel = 1.0;
  int _currentPage = 1;
  final int _totalPages = 5; // Simulated total pages
  bool _isLoading = true;
  bool _hasError = false;
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  @override
  void initState() {
    super.initState();
    
    // Simulate document loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Simulate error if no document source
          if (widget.documentSource == null || widget.documentSource!.isEmpty) {
            _hasError = false; // For demo, we'll show content anyway
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _closeModal() {
    Navigator.of(context).pop();
  }

  void _zoomIn() {
    setState(() {
      _zoomLevel = math.min(3.0, _zoomLevel + 0.25);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = math.max(0.5, _zoomLevel - 0.25);
    });
  }

  void _resetZoom() {
    setState(() {
      _zoomLevel = 1.0;
    });
  }

  void _fitToWidth() {
    setState(() {
      _zoomLevel = 1.2; // Simulated fit to width
    });
  }

  void _fitToHeight() {
    setState(() {
      _zoomLevel = 0.8; // Simulated fit to height
    });
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  void _toggleSearch() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        _searchController.clear();
      }
    });
  }

  void _downloadDocument() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading ${widget.fileName}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _printDocument() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Printing ${widget.fileName}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareDocument() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing ${widget.fileName}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      height: 60,
      color: Colors.white,
      child: Row(
        children: [
          // Left side - Menu and file info
          IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () {},
            tooltip: 'Menu',
           iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.fileName,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'Page $_currentPage of $_totalPages',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          
          // Center - Zoom controls
          IconButton(
            icon: const Icon(Icons.zoom_out),
            onPressed: _zoomOut,
            tooltip: 'Zoom Out',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text('${(_zoomLevel * 100).round()}%'),
          ),
          IconButton(
            icon: const Icon(Icons.zoom_in),
            onPressed: _zoomIn,
            tooltip: 'Zoom In',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          IconButton(
            icon: const Icon(Icons.fit_screen),
            onPressed: _fitToWidth,
            tooltip: 'Fit to Width',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          IconButton(
            icon: const Icon(Icons.fullscreen),
            onPressed: _fitToHeight,
            tooltip: 'Fit to Height',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          
          const VerticalDivider(),
          
          // Page navigation
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _currentPage > 1 ? _previousPage : null,
            tooltip: 'Previous Page',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward),
            onPressed: _currentPage < _totalPages ? _nextPage : null,
            tooltip: 'Next Page',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          
          const VerticalDivider(),
          
          // Right side - Actions
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _toggleSearch,
            tooltip: 'Search',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _downloadDocument,
            tooltip: 'Download',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printDocument,
            tooltip: 'Print',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareDocument,
            tooltip: 'Share',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _closeModal,
            tooltip: 'Close',
              iconSize: _getResponsiveIconSize(context),
          color: Color(0xFFCCCCCC),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    if (!_showSearchBar) return const SizedBox.shrink();
    
    return Container(
      height: 50,
      color: Colors.grey.shade100,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search in document...',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onSubmitted: (value) {
                // Implement search functionality
              },
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.arrow_upward),
            onPressed: () {
              // Previous search result
            },
            tooltip: 'Previous',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_downward),
            onPressed: () {
              // Next search result
            },
            tooltip: 'Next',
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _toggleSearch,
            tooltip: 'Close Search',
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentViewer() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Failed to load document',
              style: TextStyle(color: Colors.grey.shade700, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Transform.scale(
          scale: _zoomLevel,
          child: Container(
            width: 600,
            height: 800,
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                // Document header
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.fileName,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Page $_currentPage',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Document content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Sample content
                        const Text(
                          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                          style: TextStyle(fontSize: 14, height: 1.5),
                        ),
                        const SizedBox(height: 16),
                        
                        // Bullet points
                        const Text('• Morbi viverra semper lorem nec molestie.'),
                        const Text('• Maecenas tincidunt est efficitur ligula euismod.'),
                        const Text('• Sit amet ornare est vulputate.'),
                        
                        const SizedBox(height: 24),
                        
                        // Sample chart (simulated)
                        Container(
                          height: 200,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.bar_chart,
                                  size: 48,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Sample Chart',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade300,
      body: Column(
        children: [
          _buildToolbar(),
          _buildSearchBar(),
          Expanded(
            child: _buildDocumentViewer(),
          ),
        ],
      ),
    );
  }
  
  _getResponsiveIconSize(BuildContext context) {}
}
