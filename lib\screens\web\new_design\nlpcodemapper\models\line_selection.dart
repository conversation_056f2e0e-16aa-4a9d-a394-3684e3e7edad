class LineSelection {
  final String content;
  final int lineIndex;

  LineSelection(this.content, this.lineIndex);

  // Add string conversion for compatibility with existing code
  @override
  String toString() => content;

  // Add trim method for compatibility with existing code
  String trim() => content.trim();

  // Add contains method for compatibility with existing code
  bool contains(String other) => content.contains(other);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LineSelection &&
        other.content == content &&
        other.lineIndex == lineIndex;
  }

  @override
  int get hashCode => content.hashCode ^ lineIndex.hashCode;
}