import 'package:flutter/material.dart';
import '../screens/transaction_screen.dart';
import '../screens/web/web_transact_screen.dart';
import 'base_responsive_builder.dart';

class ResponsiveTransactBuilder extends BaseResponsiveBuilder {
  const ResponsiveTransactBuilder({super.key}) : super(builderKey: 'transact');

  @override
  BaseResponsiveBuilderState<BaseResponsiveBuilder> createState() => _ResponsiveTransactBuilderState();

  @override
  Widget buildWebLayout(BuildContext context, String? currentRoute) {
    return const WebTransactScreen();
  }

  @override
  Widget buildMobileLayout(BuildContext context, String? currentRoute) {
    return const TransactionScreen();
  }
}

class _ResponsiveTransactBuilderState extends BaseResponsiveBuilderState<ResponsiveTransactBuilder> {}
