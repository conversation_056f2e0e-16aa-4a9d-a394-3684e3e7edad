/// Model class for file upload API response
class FileUploadResponse {
  /// Whether the upload was successful
  final bool success;
  
  /// The name of the uploaded file
  final String filename;
  
  /// The path to the uploaded file on the server
  final String filePath;
  
  /// The content type of the uploaded file
  final String contentType;

  /// Constructor
  FileUploadResponse({
    required this.success,
    required this.filename,
    required this.filePath,
    required this.contentType,
  });

  /// Factory constructor to create a FileUploadResponse from a JSON map
  factory FileUploadResponse.fromJson(Map<String, dynamic> json) {
    return FileUploadResponse(
      success: json['success'] ?? false,
      filename: json['filename'] ?? '',
      filePath: json['file_path'] ?? '',
      contentType: json['content_type'] ?? 'application/octet-stream',
    );
  }

  /// Convert this FileUploadResponse to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'filename': filename,
      'file_path': filePath,
      'content_type': contentType,
    };
  }
  
  /// Get a string representation of this FileUploadResponse
  @override
  String toString() {
    return 'FileUploadResponse{success: $success, filename: $filename, filePath: $filePath, contentType: $contentType}';
  }
}
