import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class BookDetailPage extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const BookDetailPage({super.key, this.initialData});

  @override
  State<BookDetailPage> createState() => _BookDetailPageState();
}

class _BookDetailPageState extends State<BookDetailPage> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Industry dropdown options
  final List<String> _industries = [
    'E-commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology'
  ];
  String? _selectedIndustry;

  @override
  void initState() {
    super.initState();
    // Initialize with data if provided
    if (widget.initialData != null) {
      _nameController.text = widget.initialData!['name'] ?? '';
      _descriptionController.text = widget.initialData!['description'] ?? '';
      _selectedIndustry = widget.initialData!['industry'];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Side Navigation
          // SideNavigation(selectedIndex: 1),

          // Main Content
          Expanded(
            child: Center(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate the card width based on 12-column grid
                  double cardWidth = constraints.maxWidth;

                  // Make card responsive
                  if (constraints.maxWidth > 860) {
                    // Desktop: 6 columns (half width)
                    cardWidth = constraints.maxWidth * 0.43;
                  } else if (constraints.maxWidth > 800) {
                    // Tablet: 8 columns (two-thirds width)
                    cardWidth = constraints.maxWidth * 0.67;
                  } else if (constraints.maxWidth > 600) {
                    // Small tablet: 10 columns
                    cardWidth = constraints.maxWidth * 0.83;
                  }
                  // Mobile: full width (12 columns)

                  return SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(1.0),
                      child: Container(
                        width: cardWidth,
                        padding: const EdgeInsets.all(25),
                        decoration: BoxDecoration(
                          border:
                              Border.all(color: Color(0xffD0D0D0), width: 1),
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          // boxShadow: [
                          //   BoxShadow(
                          //     color: Colors.black.withOpacity(0.05),
                          //     spreadRadius: 0,
                          //     blurRadius: 4,
                          //     offset: const Offset(0, 2),
                          //   ),
                          // ],
                        ),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Title
                              Center(
                                child: Text(
                                  AppLocalizations.of(context)
                                      .translate('bookdetails.createYourBook'),
                                      style: FontManager.getCustomStyle(
                                        fontSize: FontManager.s32,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: FontManager.fontFamilyTiemposText,
                                        color: Color(0xff0058FF),
                                      ),
                                ),
                              ),
                              const SizedBox(height: 20),

                              // Project Name Field
                              Text(
                                AppLocalizations.of(context)
                                    .translate('bookdetails.name'),
                                    style: FontManager.getCustomStyle(
                                        fontSize: FontManager.s14,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: FontManager.fontFamilyTiemposText,
                                        color: Colors.black87,
                                      ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                decoration: BoxDecoration(
                                    // boxShadow: [
                                    //   BoxShadow(
                                    //     color: Colors.black.withOpacity(0.05),
                                    //     blurRadius: 6,
                                    //     offset: Offset(0, 2),
                                    //   ),
                                    // ],
                                    ),
                                child: TextFormField(
                                  controller: _nameController,
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: const BorderSide(
                                        color: Color(0xff0058FF),
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter a project name';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(height: 24),

                              // Industry Dropdown Field
                              Text(
                                AppLocalizations.of(context)
                                    .translate('bookdetails.industry'),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'TiemposText',
                                  color: Color(0xff000000),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                decoration: BoxDecoration(
                                    // boxShadow: [
                                    //   BoxShadow(
                                    //     color: Colors.black.withOpacity(0.05),
                                    //     blurRadius: 6,
                                    //     offset: Offset(0, 2),
                                    //   ),
                                    // ],
                                    ),
                                child: DropdownButtonFormField<String>(
                                  value: _selectedIndustry,
                                  decoration: InputDecoration(
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 16),
                                    filled: true,
                                    fillColor: Colors.white,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: const BorderSide(
                                        color: Color(0xff0058FF),
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  hint: Text(
                                    AppLocalizations.of(context)
                                        .translate('bookdetails.industry'),
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontFamily: 'TiemposText',
                                    ),
                                  ),
                                  isExpanded: true,
                                  icon: const Icon(Icons.arrow_drop_down),
                                  items: _industries.map((String industry) {
                                    return DropdownMenuItem<String>(
                                      value: industry,
                                      child: Text(
                                        industry,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontFamily: 'TiemposText',
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    setState(() {
                                      _selectedIndustry = newValue;
                                    });
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select an industry';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(height: 24),
                              // Description Field
                              Text(
                                AppLocalizations.of(context).translate(
                                    'bookdetails.descriptionAboutTheProject'),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'TiemposText',
                                  color: Color(0xff000000),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                decoration: BoxDecoration(
                                    // boxShadow: [
                                    //   BoxShadow(
                                    //     color: Colors.black.withOpacity(0.05),
                                    //     blurRadius: 6,
                                    //     offset: Offset(0, 2),
                                    //   ),
                                    // ],
                                    ),
                                child: TextFormField(
                                  controller: _descriptionController,
                                  maxLines: 4,
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: const BorderSide(
                                        color: Color(0xff0058FF),
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 32),

                              Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Cancel Button
                                    Expanded(
                                      child: SizedBox(
                                        height: 46,
                                        child: ElevatedButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                Colors.grey.shade300,
                                            foregroundColor: Colors.black,
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                          ),
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .translate('Cancel'),
                                            style: TextStyle(
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(
                                        width: 16), // Space between buttons
                                    // Start Button
                                    Expanded(
                                      child: SizedBox(
                                        height: 46,
                                        child: ElevatedButton(
                                          onPressed: () {
                                            if (_formKey.currentState!
                                                .validate()) {
                                              final bookData = {
                                                'name': _nameController.text,
                                                'description':
                                                    _descriptionController.text,
                                                'industry': _selectedIndustry,
                                              };

                                              print('Book Data: $bookData');
                                              Provider.of<WebHomeProvider>(
                                                          context,
                                                          listen: false)
                                                      .currentScreenIndex =
                                                  ScreenConstants.webMyLibrary;
                                            }
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Color(0xff0058FF),
                                            foregroundColor: Colors.white,
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                          ),
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .translate('bookdetails.start'),
                                            style: TextStyle(
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SideNavigation extends StatelessWidget {
  final int selectedIndex;

  const SideNavigation({super.key, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 16),
          // Logo
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                'nb',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Chat Icon
          _buildNavItem(context, Icons.chat_bubble_outline, 0),

          // App Icon (Selected)
          _buildNavItem(context, Icons.apps, 1, isSelected: selectedIndex == 1),

          // Briefcase Icon
          _buildNavItem(context, Icons.work_outline, 2),

          // Document Icon
          _buildNavItem(context, Icons.description_outlined, 3),

          // Calendar Icon
          _buildNavItem(context, Icons.calendar_today, 4),

          // Notification Icon
          _buildNavItem(context, Icons.notifications_none, 5),

          const Spacer(),

          // Profile Icon
          _buildNavItem(context, Icons.person_outline, 6),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, IconData icon, int index,
      {bool isSelected = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade100 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: () {},
        icon: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey.shade600,
        ),
      ),
    );
  }
}

// Helper for responsive sizing
class ResponsiveUtil {
  // Get card width based on 12-column grid and container width
  static double getCardWidth(double containerWidth, {int columns = 6}) {
    // Total columns in grid
    const int totalColumns = 12;

    // Calculate grid column width
    final double columnWidth = containerWidth / totalColumns;

    // Return width based on number of columns
    return columnWidth * columns;
  }

  // Get the number of columns to use based on screen width
  static int getResponsiveColumns(double screenWidth) {
    if (screenWidth > 860) {
      return 6; // Desktop
    } else if (screenWidth > 800) {
      return 8; // Tablet
    } else if (screenWidth > 600) {
      return 10; // Small tablet
    } else {
      return 12; // Mobile
    }
  }
}
