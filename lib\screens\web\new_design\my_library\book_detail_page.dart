import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/my_library/main_app_with_json.dart';

class BookDetailPage extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const BookDetailPage({super.key, this.initialData});

  @override
  State<BookDetailPage> createState() => _BookDetailPageState();
}

class _BookDetailPageState extends State<BookDetailPage> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Initialize with data if provided
    if (widget.initialData != null) {
      _nameController.text = widget.initialData!['name'] ?? '';
      _descriptionController.text = widget.initialData!['description'] ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Side Navigation
          // SideNavigation(selectedIndex: 1),

          // Main Content
          Expanded(
            child: Center(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate the card width based on 12-column grid
                  double cardWidth = constraints.maxWidth;

                  // Make card responsive
                  if (constraints.maxWidth > 860) {
                    // Desktop: 6 columns (half width)
                    cardWidth = constraints.maxWidth * 0.5;
                  } else if (constraints.maxWidth > 800) {
                    // Tablet: 8 columns (two-thirds width)
                    cardWidth = constraints.maxWidth * 0.67;
                  } else if (constraints.maxWidth > 600) {
                    // Small tablet: 10 columns
                    cardWidth = constraints.maxWidth * 0.83;
                  }
                  // Mobile: full width (12 columns)

                  return SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Container(
                        width: cardWidth,
                        padding: const EdgeInsets.all(32.0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.0),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Title
                              const Center(
                                child: Text(
                                  'Book',
                                  style: TextStyle(
                                    fontSize: 34,
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xff003EE6)
                                  ),
                                ),
                              ),
                              const SizedBox(height: 32),

                              // Project Name Field
                              const Text(
                                'Name Of The Project',
                                style: TextStyle(
                                  fontFamily: 'TiemposText',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextFormField(
                                controller: _nameController,
                                decoration: InputDecoration(
                                  filled: true,
                                  fillColor: Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Colors.blue,
                                      width: 2,
                                    ),
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter a project name';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),

                              // Description Field
                              const Text(
                                'Description About The Project',
                                style: TextStyle(
                                  fontFamily: 'TiemposText',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextFormField(
                                controller: _descriptionController,
                                maxLines: 5,
                                decoration: InputDecoration(
                                  filled: true,
                                  fillColor: Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Colors.blue,
                                      width: 2,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 32),

                              // Start Button
                              Center(
                                child: SizedBox(
                                  width: 200,
                                  height: 48,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      if (_formKey.currentState!.validate()) {
                                        // Navigate to AppDataLoader
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const AppDataLoader(),
                                          ),
                                        );
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Color(0xff0058FF),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(AppSpacing.xxs),
                                      ),
                                    ),
                                    child: const Text(
                                      'Start',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontFamily: 'TiemposText',
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SideNavigation extends StatelessWidget {
  final int selectedIndex;

  const SideNavigation({super.key, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 16),
          // Logo
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                'nb',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Chat Icon
          _buildNavItem(context, Icons.chat_bubble_outline, 0),

          // App Icon (Selected)
          _buildNavItem(context, Icons.apps, 1, isSelected: selectedIndex == 1),

          // Briefcase Icon
          _buildNavItem(context, Icons.work_outline, 2),

          // Document Icon
          _buildNavItem(context, Icons.description_outlined, 3),

          // Calendar Icon
          _buildNavItem(context, Icons.calendar_today, 4),

          // Notification Icon
          _buildNavItem(context, Icons.notifications_none, 5),

          const Spacer(),

          // Profile Icon
          _buildNavItem(context, Icons.person_outline, 6),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, IconData icon, int index, {bool isSelected = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade100 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: () {},
        icon: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey.shade600,
        ),
      ),
    );
  }
}

// Helper for responsive sizing
class ResponsiveUtil {
  // Get card width based on 12-column grid and container width
  static double getCardWidth(double containerWidth, {int columns = 6}) {
    // Total columns in grid
    const int totalColumns = 12;

    // Calculate grid column width
    final double columnWidth = containerWidth / totalColumns;

    // Return width based on number of columns
    return columnWidth * columns;
  }

  // Get the number of columns to use based on screen width
  static int getResponsiveColumns(double screenWidth) {
    if (screenWidth > 860) {
      return 6;  // Desktop
    } else if (screenWidth > 800) {
      return 8;  // Tablet
    } else if (screenWidth > 600) {
      return 10; // Small tablet
    } else {
      return 12; // Mobile
    }
  }
}
