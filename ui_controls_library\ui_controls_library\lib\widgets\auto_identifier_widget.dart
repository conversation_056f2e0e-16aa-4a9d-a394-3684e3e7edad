import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// Format type for the auto identifier
enum AutoIdFormatType {
  /// Numeric format (e.g., "123456")
  numeric,

  /// Alphabetic format (e.g., "ABCDEF")
  alphabetic,

  /// Alphanumeric format (e.g., "A1B2C3")
  alphanumeric,

  /// Custom format using pattern (e.g., "XXX-000-XXX")
  custom
}

/// A widget that displays and generates automatic identifiers.
///
/// This widget provides extensive customization options for displaying
/// and generating automatic identifiers with various formats and styles.
class AutoIdentifierWidget extends StatefulWidget {
  /// The initial identifier value (if null, one will be auto-generated)
  final String? initialValue;

  /// The format type of the identifier
  final AutoIdFormatType formatType;

  /// Custom format pattern (used when formatType is AutoIdFormatType.custom)
  /// Use 'X' for alphabetic characters, '0' for numeric characters,
  /// and any other characters will be preserved as-is
  final String? formatPattern;

  /// The length of the generated identifier (ignored if formatPattern is provided)
  final int length;

  /// Prefix to add before the identifier
  final String? prefix;

  /// Suffix to add after the identifier
  final String? suffix;

  /// Whether to use uppercase for alphabetic characters
  final bool uppercase;

  /// Whether the identifier is read-only
  final bool readOnly;

  /// Whether the identifier is disabled
  final bool disabled;

  /// Whether to show a copy button
  final bool showCopyButton;

  /// Whether to show a refresh button to generate a new identifier
  final bool showRefreshButton;

  /// Whether to show a label
  final bool showLabel;

  /// The label text to display
  final String? labelText;

  /// Whether to show a tooltip
  final bool showTooltip;

  /// The tooltip text to display
  final String? tooltipText;

  /// The text style for the identifier
  final TextStyle? textStyle;

  /// The text color for the identifier
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The font size for the identifier
  final double fontSize;

  /// The font weight for the identifier
  final FontWeight fontWeight;

  /// The font family for the identifier
  final String? fontFamily;

  /// The text alignment for the identifier
  final TextAlign textAlign;

  /// Whether to show a border
  final bool hasBorder;

  /// The border radius
  final double borderRadius;

  /// The border color
  final Color borderColor;

  /// The border width
  final double borderWidth;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// Whether to use a compact layout
  final bool isCompact;

  /// The padding inside the widget
  final EdgeInsetsGeometry padding;

  /// The margin around the widget
  final EdgeInsetsGeometry margin;

  /// The prefix icon
  final IconData? prefixIcon;

  /// The suffix icon
  final IconData? suffixIcon;

  /// The color of the prefix icon
  final Color? prefixIconColor;

  /// The color of the suffix icon
  final Color? suffixIconColor;

  /// The color of the copy button
  final Color? copyButtonColor;

  /// The color of the refresh button
  final Color? refreshButtonColor;

  /// Whether to show a success message after copying
  final bool showCopyFeedback;

  /// The duration of the copy feedback message
  final Duration copyFeedbackDuration;

  /// Whether to animate the identifier when it changes
  final bool isAnimated;

  /// Whether to show a monospace font for the identifier
  final bool isMonospace;

  /// Whether to show a helper text
  final bool showHelperText;

  /// The helper text to display
  final String? helperText;

  /// The color of the helper text
  final Color? helperTextColor;

  /// Whether to show an error text
  final bool showErrorText;

  /// The error text to display
  final String? errorText;

  /// The color of the error text
  final Color errorTextColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the identifier changes
  final void Function(String)? onChanged;

  /// Callback when the identifier is copied
  final void Function(String)? onCopied;

  /// Callback when a new identifier is generated
  final void Function(String)? onGenerated;

  /// Creates an auto identifier widget.
  const AutoIdentifierWidget({
    super.key,
    this.initialValue,
    this.formatType = AutoIdFormatType.alphanumeric,
    this.formatPattern,
    this.length = 8,
    this.prefix,
    this.suffix,
    this.uppercase = true,
    this.readOnly = true,
    this.disabled = false,
    this.showCopyButton = true,
    this.showRefreshButton = true,
    this.showLabel = false,
    this.labelText = 'ID',
    this.showTooltip = false,
    this.tooltipText,
    this.textStyle,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.textAlign = TextAlign.start,
    this.hasBorder = true,
    this.borderRadius = 4.0,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isCompact = false,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixIconColor,
    this.suffixIconColor,
    this.copyButtonColor,
    this.refreshButtonColor,
    this.showCopyFeedback = true,
    this.copyFeedbackDuration = const Duration(seconds: 2),
    this.isAnimated = false,
    this.isMonospace = true,
    this.showHelperText = false,
    this.helperText,
    this.helperTextColor,
    this.showErrorText = false,
    this.errorText,
    this.errorTextColor = Colors.red,
    this.width,
    this.height,
    this.onChanged,
    this.onCopied,
    this.onGenerated,
  });

  @override
  State<AutoIdentifierWidget> createState() => _AutoIdentifierWidgetState();
}

class _AutoIdentifierWidgetState extends State<AutoIdentifierWidget> with SingleTickerProviderStateMixin {
  late String _identifier;
  bool _showCopyFeedback = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize the identifier
    _identifier = widget.initialValue ?? _generateIdentifier();

    // Set up animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isAnimated) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Generates a new identifier based on the specified format
  String _generateIdentifier() {
    if (widget.formatType == AutoIdFormatType.custom && widget.formatPattern != null) {
      return _generateCustomFormat(widget.formatPattern!);
    } else {
      return _generateStandardFormat();
    }
  }

  /// Generates an identifier using a custom format pattern
  String _generateCustomFormat(String pattern) {
    final result = StringBuffer();

    for (int i = 0; i < pattern.length; i++) {
      final char = pattern[i];

      if (char == 'X' || char == 'x') {
        // Alphabetic character
        final randomChar = String.fromCharCode(Random().nextInt(26) + 65); // A-Z
        result.write(widget.uppercase ? randomChar : randomChar.toLowerCase());
      } else if (char == '0') {
        // Numeric character
        result.write(Random().nextInt(10)); // 0-9
      } else {
        // Preserve any other character
        result.write(char);
      }
    }

    return result.toString();
  }

  /// Generates an identifier using a standard format
  String _generateStandardFormat() {
    final result = StringBuffer();

    for (int i = 0; i < widget.length; i++) {
      switch (widget.formatType) {
        case AutoIdFormatType.numeric:
          result.write(Random().nextInt(10)); // 0-9
          break;
        case AutoIdFormatType.alphabetic:
          final randomChar = String.fromCharCode(Random().nextInt(26) + 65); // A-Z
          result.write(widget.uppercase ? randomChar : randomChar.toLowerCase());
          break;
        case AutoIdFormatType.alphanumeric:
          final isChar = Random().nextBool();
          if (isChar) {
            final randomChar = String.fromCharCode(Random().nextInt(26) + 65); // A-Z
            result.write(widget.uppercase ? randomChar : randomChar.toLowerCase());
          } else {
            result.write(Random().nextInt(10)); // 0-9
          }
          break;
        case AutoIdFormatType.custom:
          // This should not happen, but just in case
          result.write(Random().nextInt(10)); // 0-9
          break;
      }
    }

    return result.toString();
  }

  /// Generates a new identifier and updates the state
  void _refreshIdentifier() {
    if (widget.disabled) return;

    final newIdentifier = _generateIdentifier();

    setState(() {
      _identifier = newIdentifier;

      // Trigger animation if enabled
      if (widget.isAnimated) {
        _animationController.reset();
        _animationController.forward();
      }
    });

    if (widget.onGenerated != null) {
      widget.onGenerated!(newIdentifier);
    }

    if (widget.onChanged != null) {
      widget.onChanged!(newIdentifier);
    }
  }

  /// Copies the identifier to the clipboard
  void _copyToClipboard() {
    if (widget.disabled) return;

    final fullIdentifier = _getFullIdentifier();
    Clipboard.setData(ClipboardData(text: fullIdentifier));

    if (widget.showCopyFeedback) {
      setState(() {
        _showCopyFeedback = true;
      });

      Future.delayed(widget.copyFeedbackDuration, () {
        if (mounted) {
          setState(() {
            _showCopyFeedback = false;
          });
        }
      });
    }

    if (widget.onCopied != null) {
      widget.onCopied!(fullIdentifier);
    }
  }

  /// Gets the full identifier including prefix and suffix
  String _getFullIdentifier() {
    final buffer = StringBuffer();

    if (widget.prefix != null) {
      buffer.write(widget.prefix);
    }

    buffer.write(_identifier);

    if (widget.suffix != null) {
      buffer.write(widget.suffix);
    }

    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Determine effective colors
    final effectiveTextColor = widget.disabled
        ? theme.disabledColor
        : widget.textColor;

    final effectiveBorderColor = widget.disabled
        ? theme.disabledColor
        : widget.showErrorText
            ? widget.errorTextColor
            : widget.borderColor;

    final effectiveCopyButtonColor = widget.copyButtonColor ?? theme.colorScheme.primary;
    final effectiveRefreshButtonColor = widget.refreshButtonColor ?? theme.colorScheme.primary;

    // Build text style
    TextStyle textStyle = widget.textStyle ??
        TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.isMonospace
              ? 'Courier'
              : widget.fontFamily,
        );

    // Get the full identifier
    final fullIdentifier = _getFullIdentifier();

    // Build the main content
    Widget content = Row(
      children: [
        if (widget.prefixIcon != null) ...[
          Icon(
            widget.prefixIcon,
            size: widget.fontSize,
            color: widget.prefixIconColor ?? effectiveTextColor.withAlpha(179), // ~0.7 opacity
          ),
          const SizedBox(width: 8),
        ],

        Expanded(
          child: widget.isAnimated
              ? FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    fullIdentifier,
                    style: textStyle,
                    textAlign: widget.textAlign,
                  ),
                )
              : Text(
                  fullIdentifier,
                  style: textStyle,
                  textAlign: widget.textAlign,
                ),
        ),

        if (widget.suffixIcon != null) ...[
          const SizedBox(width: 8),
          Icon(
            widget.suffixIcon,
            size: widget.fontSize,
            color: widget.suffixIconColor ?? effectiveTextColor.withAlpha(179), // ~0.7 opacity
          ),
        ],

        if (widget.showCopyButton && !widget.disabled) ...[
          const SizedBox(width: 8),
          IconButton(
            icon: Icon(_showCopyFeedback ? Icons.check : Icons.copy),
            iconSize: widget.fontSize,
            color: effectiveCopyButtonColor,
            tooltip: 'Copy to clipboard',
            onPressed: _copyToClipboard,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],

        if (widget.showRefreshButton && !widget.disabled && !widget.readOnly) ...[
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.refresh),
            iconSize: widget.fontSize,
            color: effectiveRefreshButtonColor,
            tooltip: 'Generate new ID',
            onPressed: _refreshIdentifier,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],
      ],
    );

    // Apply container styling
    content = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: effectiveBorderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: content,
    );

    // Apply tooltip if enabled
    if (widget.showTooltip) {
      content = Tooltip(
        message: widget.tooltipText ?? 'Auto-generated ID',
        child: content,
      );
    }

    // Build the final widget with label and helper/error text
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabel && widget.labelText != null) ...[
          Text(
            widget.labelText!,
            style: TextStyle(
              color: widget.disabled ? theme.disabledColor : theme.textTheme.bodyLarge?.color,
              fontSize: widget.fontSize * 0.8,
            ),
          ),
          const SizedBox(height: 4),
        ],

        content,

        if (widget.showHelperText && widget.helperText != null && !widget.showErrorText) ...[
          const SizedBox(height: 4),
          Text(
            widget.helperText!,
            style: TextStyle(
              color: widget.helperTextColor ?? theme.textTheme.bodySmall?.color,
              fontSize: widget.fontSize * 0.7,
            ),
          ),
        ],

        if (widget.showErrorText && widget.errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.errorText!,
            style: TextStyle(
              color: widget.errorTextColor,
              fontSize: widget.fontSize * 0.7,
            ),
          ),
        ],
      ],
    );
  }
}