{"intent": {"type": "data_visualization", "query": "Show me sales data"}, "layout": {"type": "responsive_dashboard", "device_type": "desktop", "containers": [{"id": "top_row", "size": "25%", "layout": "horizontal_split", "components": ["summary_kpis"]}, {"id": "middle_row", "size": "35%", "layout": "horizontal_split", "components": ["sales_trend", "region_breakdown"]}, {"id": "bottom_row", "size": "40%", "layout": "full_width", "components": ["sales_details"]}]}, "components": [{"id": "summary_kpis", "widget": "KpiCardGroup", "config": {"title": "Sales Overview", "cards": [{"label": "Total Revenue", "data_field": "total_revenue", "format": "currency", "icon": "trending_up", "comparison": {"label": "vs Previous Quarter", "data_field": "revenue_pct_change"}}, {"label": "Total Units", "data_field": "total_units", "format": "number", "icon": "inventory", "comparison": {"label": "vs Previous Quarter", "data_field": "units_pct_change"}}], "actions": [{"widget": "DropdownButton", "icon": "calendar_today", "label": "Time Period", "options": [{"label": "This Quarter", "value": "current_quarter"}, {"label": "Last Quarter", "value": "previous_quarter"}, {"label": "YTD", "value": "ytd"}, {"label": "Last 12 Months", "value": "last_12_months"}], "default": "current_quarter", "callback": "updateTimePeriod"}, {"widget": "IconButton", "icon": "download", "tooltip": "Export Data", "callback": "exportKpiData"}]}}, {"id": "sales_trend", "widget": "ChartContainer", "config": {"title": "Quarterly Sales Trend", "chart_toolbar": {"widget": "ButtonToggleGroup", "options": [{"icon": "show_chart", "value": "line", "tooltip": "Line Chart"}, {"icon": "bar_chart", "value": "bar", "tooltip": "Bar Chart"}, {"icon": "stacked_line_chart", "value": "area", "tooltip": "Area Chart"}], "default": "line", "callback": "changeChartType"}, "chart_config": {"type": "Line<PERSON>hart", "data_source": "quarterly_trend", "x_axis": {"data_field": "quarter_year", "title": "Quarter"}, "y_axis": {"data_field": "revenue", "title": "Revenue (USD)", "format": "currency"}, "series": [{"name": "Revenue", "data_field": "revenue", "color": "#4C78A8"}], "legend_visible": true, "grid_lines": true, "animation_duration": 500, "tooltip_enabled": true}, "actions": [{"widget": "ButtonGroup", "label": "Time Range", "options": [{"label": "1Y", "value": "1_year"}, {"label": "2Y", "value": "2_year"}, {"label": "All", "value": "all"}], "default": "1_year", "callback": "updateTimeRange"}, {"widget": "IconButton", "icon": "download", "tooltip": "Export Chart", "callback": "exportChart"}], "event_handlers": {"onDataPointClick": "showPointDetails", "onLegendClick": "toggleSeries"}}}, {"id": "region_breakdown", "widget": "ChartContainer", "config": {"title": "Sales by Region", "chart_toolbar": {"widget": "ButtonToggleGroup", "options": [{"icon": "bar_chart", "value": "bar", "tooltip": "Bar Chart"}, {"icon": "pie_chart", "value": "pie", "tooltip": "Pie Chart"}], "default": "bar", "callback": "changeChartType"}, "chart_config": {"type": "<PERSON><PERSON><PERSON>", "data_source": "region_breakdown", "x_axis": {"data_field": "region", "title": "Region"}, "y_axis": {"data_field": "revenue", "title": "Revenue (USD)", "format": "currency"}, "orientation": "horizontal", "sort_order": "value_desc", "legend_visible": false, "data_labels": true, "animation_duration": 500}, "actions": [{"widget": "IconButton", "icon": "sort", "tooltip": "Sort Data", "options": [{"label": "Highest First", "value": "value_desc"}, {"label": "Lowest First", "value": "value_asc"}, {"label": "Alphabetical", "value": "name_asc"}], "default": "value_desc", "callback": "sortRegionChart"}], "event_handlers": {"onBarClick": "selectRegion"}}}, {"id": "sales_details", "widget": "DataTable", "config": {"title": "Detailed Sales Data", "data_source": "detailed_sales", "pagination": true, "rows_per_page": 10, "rows_per_page_options": [5, 10, 25, 50], "sorting_enabled": true, "default_sort": {"column": "revenue", "direction": "desc"}, "filtering_enabled": true, "search_enabled": true, "selection_enabled": true, "columns": [{"data_field": "region", "title": "Region", "sortable": true, "filterable": true, "width": "20%"}, {"data_field": "product_category", "title": "Product Category", "sortable": true, "filterable": true, "width": "25%"}, {"data_field": "quarter", "title": "Quarter", "sortable": true, "filterable": true, "width": "15%"}, {"data_field": "revenue", "title": "Revenue", "sortable": true, "filterable": true, "format": "currency", "width": "20%", "conditional_formatting": [{"condition": "value > 1000000", "style": {"color": "green", "font_weight": "bold"}}]}, {"data_field": "units_sold", "title": "Units Sold", "sortable": true, "filterable": true, "format": "number", "width": "20%"}], "actions": [{"widget": "<PERSON><PERSON>", "icon": "download", "label": "Export", "options": [{"label": "CSV", "value": "csv"}, {"label": "Excel", "value": "excel"}], "callback": "exportTableData"}], "row_actions": [{"widget": "IconButton", "icon": "visibility", "tooltip": "View Details", "callback": "viewRowDetails"}, {"widget": "IconButton", "icon": "edit", "tooltip": "Edit", "callback": "editRow", "condition": "user_has_edit_permission"}], "event_handlers": {"onRowClick": "selectRow", "onSort": "sortTable", "onFilter": "filterTable", "onPageChange": "changePage"}}}], "global_filters": [{"id": "date_range_filter", "widget": "DateRangePicker", "label": "Date Range", "default_preset": "current_quarter", "presets": [{"label": "Current Quarter", "value": "current_quarter"}, {"label": "Previous Quarter", "value": "previous_quarter"}, {"label": "Year to Date", "value": "ytd"}, {"label": "Last 12 Months", "value": "last_12_months"}, {"label": "Custom Range", "value": "custom"}], "affects_components": ["summary_kpis", "sales_trend", "region_breakdown", "sales_details"], "callback": "updateDateRange"}], "global_actions": [{"widget": "<PERSON><PERSON>", "icon": "refresh", "label": "Refresh All", "callback": "refreshAllData"}, {"widget": "<PERSON><PERSON>", "icon": "download", "label": "Export Dashboard", "options": [{"label": "PDF Report", "value": "pdf"}, {"label": "Excel Workbook", "value": "excel"}], "callback": "exportDashboard"}], "data_requirements": {"sources": [{"id": "summary_metrics", "query": "sales_data", "aggregation": "sum", "metrics": ["revenue", "units_sold"], "calculate": [{"field": "revenue_pct_change", "formula": "percentage_change(revenue, previous_period)"}, {"field": "units_pct_change", "formula": "percentage_change(units_sold, previous_period)"}]}, {"id": "quarterly_trend", "query": "sales_data", "group_by": ["quarter", "year"], "create_field": {"name": "quarter_year", "formula": "concat(quarter, ' ', year)"}, "aggregation": "sum", "metrics": ["revenue", "units_sold"], "sort": [{"field": "year", "direction": "asc"}, {"field": "quarter", "direction": "asc"}]}, {"id": "region_breakdown", "query": "sales_data", "group_by": ["region"], "aggregation": "sum", "metrics": ["revenue"], "sort": [{"field": "revenue", "direction": "desc"}]}, {"id": "detailed_sales", "query": "sales_data", "fields": ["region", "product_category", "quarter", "year", "revenue", "units_sold"]}]}, "cross_component_interaction": [{"source": "region_breakdown", "event": "selectRegion", "targets": ["sales_trend", "sales_details"], "action": "applyFilter", "parameters": {"field": "region", "value": "{selected_value}"}}, {"source": "sales_trend", "event": "showPointDetails", "targets": ["region_breakdown", "sales_details"], "action": "applyFilter", "parameters": {"field": "quarter_year", "value": "{selected_value}"}}]}