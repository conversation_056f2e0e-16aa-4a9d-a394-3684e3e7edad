import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A highly configurable scaffold widget that provides a basic app structure.
///
/// This widget wraps Flutter's Scaffold component and provides
/// additional customization options for creating app layouts.
class ScaffoldWidget extends StatefulWidget {
  /// The primary content of the scaffold.
  final Widget body;

  /// The app bar to display at the top of the scaffold.
  final PreferredSizeWidget? appBar;

  /// The drawer to display on the left side of the scaffold.
  final Widget? drawer;

  /// The end drawer to display on the right side of the scaffold.
  final Widget? endDrawer;

  /// The bottom navigation bar to display at the bottom of the scaffold.
  final Widget? bottomNavigationBar;

  /// The floating action button to display.
  final Widget? floatingActionButton;

  /// The position of the floating action button.
  final FloatingActionButtonLocation? floatingActionButtonLocation;

  /// The background color of the scaffold.
  final Color backgroundColor;

  /// Whether to resize the body to avoid the bottom inset.
  final bool resizeToAvoidBottomInset;

  /// Whether the scaffold has a drawer.
  final bool hasDrawer;

  /// Whether the scaffold has an end drawer.
  final bool hasEndDrawer;

  /// Whether the scaffold has a floating action button.
  final bool hasFloatingActionButton;

  /// Whether the scaffold has a bottom navigation bar.
  final bool hasBottomNavigationBar;

  /// Whether the scaffold has an app bar.
  final bool hasAppBar;

  /// Whether to use a safe area for the body.
  final bool useSafeArea;

  /// Whether to use a dark theme.
  final bool isDarkTheme;

  /// Whether the scaffold is disabled.
  final bool isDisabled;

  /// The width of the drawer.
  final double? drawerWidth;

  /// The width of the end drawer.
  final double? endDrawerWidth;

  /// The edge drag width for the drawer.
  final double? drawerEdgeDragWidth;

  /// The edge drag width for the end drawer.
  final double? endDrawerEdgeDragWidth;

  /// Whether the drawer can be opened by dragging.
  final bool drawerEnableOpenDragGesture;

  /// Whether the end drawer can be opened by dragging.
  final bool endDrawerEnableOpenDragGesture;

  /// Whether the body is scrollable.
  final bool isBodyScrollable;

  /// The padding for the body.
  final EdgeInsetsGeometry bodyPadding;

  /// The system UI overlay style.
  final SystemUiOverlayStyle? systemUiOverlayStyle;

  /// Whether the body should extend behind the app bar.
  final bool extendBodyBehindAppBar;

  /// Whether the body should extend behind the bottom navigation bar.
  final bool extendBody;

  /// The persistent footer buttons to display at the bottom of the scaffold.
  final List<Widget>? persistentFooterButtons;

  /// The height of the persistent footer buttons.
  final double? persistentFooterButtonsHeight;

  /// The alignment of the persistent footer buttons.
  final AlignmentDirectional persistentFooterButtonsAlignment;

  /// The bottom sheet to display.
  final Widget? bottomSheet;

  /// The primary color of the scaffold.
  final Color? primaryColor;

  /// The drawer scrim color.
  final Color? drawerScrimColor;

  /// The drawer edge drag width.
  final double? drawerDragStartBehavior;

  /// The shape of the floating action button.
  final ShapeBorder? floatingActionButtonShape;

  /// The size of the floating action button.
  final double? floatingActionButtonSize;

  /// Whether the floating action button is extended.
  final bool isFloatingActionButtonExtended;

  /// The label for the extended floating action button.
  final String? floatingActionButtonLabel;

  /// The current index of the bottom navigation bar.
  final int bottomNavigationBarCurrentIndex;

  /// The type of the bottom navigation bar.
  final BottomNavigationBarType bottomNavigationBarType;

  /// The shape of the bottom navigation bar items.
  final ShapeBorder? bottomNavigationBarItemShape;

  /// The actions for the app bar.
  final List<Widget>? appBarActions;

  /// The leading widget for the app bar.
  final Widget? appBarLeading;

  /// The bottom widget for the app bar.
  final PreferredSizeWidget? appBarBottom;

  /// Creates a scaffold widget with the specified properties.
  const ScaffoldWidget({
    super.key,
    required this.body,
    this.appBar,
    this.drawer,
    this.endDrawer,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.backgroundColor = Colors.white,
    this.resizeToAvoidBottomInset = true,
    this.hasDrawer = false,
    this.hasEndDrawer = false,
    this.hasFloatingActionButton = false,
    this.hasBottomNavigationBar = false,
    this.hasAppBar = false,
    this.useSafeArea = true,
    this.isDarkTheme = false,
    this.isDisabled = false,
    this.drawerWidth,
    this.endDrawerWidth,
    this.drawerEdgeDragWidth,
    this.endDrawerEdgeDragWidth,
    this.drawerEnableOpenDragGesture = true,
    this.endDrawerEnableOpenDragGesture = true,
    this.isBodyScrollable = false,
    this.bodyPadding = EdgeInsets.zero,
    this.systemUiOverlayStyle,
    this.extendBodyBehindAppBar = false,
    this.extendBody = false,
    this.persistentFooterButtons,
    this.persistentFooterButtonsHeight,
    this.persistentFooterButtonsAlignment = AlignmentDirectional.centerEnd,
    this.bottomSheet,
    this.primaryColor,
    this.drawerScrimColor,
    this.drawerDragStartBehavior,
    this.floatingActionButtonShape,
    this.floatingActionButtonSize,
    this.isFloatingActionButtonExtended = false,
    this.floatingActionButtonLabel,
    this.bottomNavigationBarCurrentIndex = 0,
    this.bottomNavigationBarType = BottomNavigationBarType.fixed,
    this.bottomNavigationBarItemShape,
    this.appBarActions,
    this.appBarLeading,
    this.appBarBottom,
  });

  @override
  State<ScaffoldWidget> createState() => _ScaffoldWidgetState();

  /// Creates a ScaffoldWidget from a JSON map.
  ///
  /// This factory constructor allows creating a ScaffoldWidget from a JSON object,
  /// making it easy to configure the widget dynamically.
  factory ScaffoldWidget.fromJson(Map<String, dynamic> json) {
    // Helper function to get a color from a string or int
    Color getColorFromValue(dynamic value) {
      if (value is String) {
        if (value.startsWith('#')) {
          // Handle hex color
          final hex = value.replaceFirst('#', '');
          final hexValue = int.tryParse(hex, radix: 16);
          if (hexValue != null) {
            return Color(hexValue | 0xFF000000); // Add alpha if needed
          }
        }

        // Handle named colors
        switch (value.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.white; // Default color
        }
      } else if (value is int) {
        return Color(value);
      }
      return Colors.white; // Default color
    }

    // Helper function to get FloatingActionButtonLocation from string
    FloatingActionButtonLocation? getFabLocationFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'centerDocked':
          case 'center_docked': return FloatingActionButtonLocation.centerDocked;
          case 'centerFloat':
          case 'center_float': return FloatingActionButtonLocation.centerFloat;
          case 'centerTop':
          case 'center_top': return FloatingActionButtonLocation.centerTop;
          case 'endDocked':
          case 'end_docked': return FloatingActionButtonLocation.endDocked;
          case 'endFloat':
          case 'end_float': return FloatingActionButtonLocation.endFloat;
          case 'endTop':
          case 'end_top': return FloatingActionButtonLocation.endTop;
          case 'startDocked':
          case 'start_docked': return FloatingActionButtonLocation.startDocked;
          case 'startFloat':
          case 'start_float': return FloatingActionButtonLocation.startFloat;
          case 'startTop':
          case 'start_top': return FloatingActionButtonLocation.startTop;
          case 'miniCenterDocked':
          case 'mini_center_docked': return FloatingActionButtonLocation.miniCenterDocked;
          case 'miniCenterFloat':
          case 'mini_center_float': return FloatingActionButtonLocation.miniCenterFloat;
          case 'miniCenterTop':
          case 'mini_center_top': return FloatingActionButtonLocation.miniCenterTop;
          case 'miniEndDocked':
          case 'mini_end_docked': return FloatingActionButtonLocation.miniEndDocked;
          case 'miniEndFloat':
          case 'mini_end_float': return FloatingActionButtonLocation.miniEndFloat;
          case 'miniEndTop':
          case 'mini_end_top': return FloatingActionButtonLocation.miniEndTop;
          case 'miniStartDocked':
          case 'mini_start_docked': return FloatingActionButtonLocation.miniStartDocked;
          case 'miniStartFloat':
          case 'mini_start_float': return FloatingActionButtonLocation.miniStartFloat;
          case 'miniStartTop':
          case 'mini_start_top': return FloatingActionButtonLocation.miniStartTop;
          default: return null;
        }
      }
      return null;
    }

    // Create a simple body widget if not provided
    Widget createBodyWidget(dynamic bodyValue) {
      if (bodyValue is String) {
        return Center(
          child: Text(
            bodyValue,
            style: const TextStyle(fontSize: 16.0),
          ),
        );
      } else if (bodyValue is Map<String, dynamic>) {
        // Handle structured body content
        if (bodyValue.containsKey('type')) {
          final type = bodyValue['type'] as String;
          switch (type) {
            case 'text':
              return Center(
                child: Text(
                  bodyValue['text'] as String? ?? 'Body Content',
                  style: TextStyle(
                    fontSize: (bodyValue['fontSize'] as num?)?.toDouble() ?? 16.0,
                    color: bodyValue.containsKey('color')
                        ? getColorFromValue(bodyValue['color'])
                        : Colors.black,
                  ),
                ),
              );
            case 'column':
              final children = (bodyValue['children'] as List?)?.map((child) {
                return createBodyWidget(child);
              }).toList() ?? <Widget>[];

              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: children,
              );
            case 'row':
              final children = (bodyValue['children'] as List?)?.map((child) {
                return createBodyWidget(child);
              }).toList() ?? <Widget>[];

              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: children,
              );
            case 'container':
              return Container(
                color: bodyValue.containsKey('color')
                    ? getColorFromValue(bodyValue['color'])
                    : null,
                child: bodyValue.containsKey('child')
                    ? createBodyWidget(bodyValue['child'])
                    : const SizedBox.shrink(),
              );
            default:
              return Center(
                child: Text(
                  'Unsupported body type: $type',
                  style: const TextStyle(color: Colors.red),
                ),
              );
          }
        }
      }

      // Default body
      return const Center(
        child: Text(
          'Scaffold Body',
          style: TextStyle(fontSize: 16.0),
        ),
      );
    }

    // Create a simple AppBar if needed
    PreferredSizeWidget? createAppBar(dynamic appBarValue) {
      if (appBarValue == null) return null;

      String title = 'App Title';
      Color backgroundColor = Colors.blue;
      Color foregroundColor = Colors.white;
      List<Widget> actions = [];
      bool centerTitle = false;
      double elevation = 4.0;

      if (appBarValue is String) {
        title = appBarValue;
      } else if (appBarValue is Map<String, dynamic>) {
        title = appBarValue['title'] as String? ?? 'App Title';
        backgroundColor = appBarValue.containsKey('backgroundColor')
            ? getColorFromValue(appBarValue['backgroundColor'])
            : Colors.blue;
        foregroundColor = appBarValue.containsKey('foregroundColor')
            ? getColorFromValue(appBarValue['foregroundColor'])
            : Colors.white;
        centerTitle = appBarValue['centerTitle'] as bool? ?? false;
        elevation = (appBarValue['elevation'] as num?)?.toDouble() ?? 4.0;
      }

      return AppBar(
        title: Text(title),
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        centerTitle: centerTitle,
        elevation: elevation,
        actions: actions,
      );
    }

    // Create a simple Drawer if needed
    Widget? createDrawer(dynamic drawerValue) {
      if (drawerValue == null) return null;

      String headerTitle = 'Drawer Header';
      Color headerColor = Colors.blue;
      Color headerTextColor = Colors.white;
      List<Widget> drawerItems = [];

      if (drawerValue is String) {
        headerTitle = drawerValue;
      } else if (drawerValue is Map<String, dynamic>) {
        headerTitle = drawerValue['headerTitle'] as String? ?? 'Drawer Header';
        headerColor = drawerValue.containsKey('headerColor')
            ? getColorFromValue(drawerValue['headerColor'])
            : Colors.blue;
        headerTextColor = drawerValue.containsKey('headerTextColor')
            ? getColorFromValue(drawerValue['headerTextColor'])
            : Colors.white;

        // Add drawer items if provided
        if (drawerValue.containsKey('items') && drawerValue['items'] is List) {
          final items = drawerValue['items'] as List;
          drawerItems = items.map((item) {
            if (item is String) {
              return ListTile(
                title: Text(item),
                onTap: () {},
              );
            } else if (item is Map<String, dynamic>) {
              final title = item['title'] as String? ?? 'Item';
              final icon = item.containsKey('icon') ? Icons.circle : null;

              return ListTile(
                title: Text(title),
                leading: icon != null ? Icon(icon) : null,
                onTap: () {},
              );
            }
            return const SizedBox.shrink();
          }).toList();
        }
      }

      return Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: headerColor,
              ),
              child: Text(
                headerTitle,
                style: TextStyle(
                  color: headerTextColor,
                  fontSize: 24,
                ),
              ),
            ),
            ...drawerItems,
          ],
        ),
      );
    }

    // Create a simple BottomNavigationBar if needed
    Widget? createBottomNavigationBar(dynamic bottomNavValue) {
      if (bottomNavValue == null) return null;

      List<BottomNavigationBarItem> items = [
        const BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        const BottomNavigationBarItem(
          icon: Icon(Icons.business),
          label: 'Business',
        ),
        const BottomNavigationBarItem(
          icon: Icon(Icons.school),
          label: 'School',
        ),
      ];

      Color backgroundColor = Colors.white;
      Color selectedItemColor = Colors.blue;
      Color unselectedItemColor = Colors.grey;

      if (bottomNavValue is Map<String, dynamic>) {
        backgroundColor = bottomNavValue.containsKey('backgroundColor')
            ? getColorFromValue(bottomNavValue['backgroundColor'])
            : Colors.white;
        selectedItemColor = bottomNavValue.containsKey('selectedItemColor')
            ? getColorFromValue(bottomNavValue['selectedItemColor'])
            : Colors.blue;
        unselectedItemColor = bottomNavValue.containsKey('unselectedItemColor')
            ? getColorFromValue(bottomNavValue['unselectedItemColor'])
            : Colors.grey;

        // Add custom items if provided
        if (bottomNavValue.containsKey('items') && bottomNavValue['items'] is List) {
          final navItems = bottomNavValue['items'] as List;
          if (navItems.isNotEmpty) {
            items = navItems.map((item) {
              if (item is Map<String, dynamic>) {
                final label = item['label'] as String? ?? 'Item';
                final iconName = item['icon'] as String? ?? 'circle';

                IconData icon = Icons.circle;
                switch (iconName.toLowerCase()) {
                  case 'home': icon = Icons.home; break;
                  case 'business': icon = Icons.business; break;
                  case 'school': icon = Icons.school; break;
                  case 'settings': icon = Icons.settings; break;
                  case 'person': icon = Icons.person; break;
                  case 'search': icon = Icons.search; break;
                  case 'favorite': icon = Icons.favorite; break;
                  case 'list': icon = Icons.list; break;
                  case 'menu': icon = Icons.menu; break;
                  case 'more': icon = Icons.more_horiz; break;
                  case 'add': icon = Icons.add; break;
                  case 'edit': icon = Icons.edit; break;
                  case 'delete': icon = Icons.delete; break;
                  case 'share': icon = Icons.share; break;
                  case 'info': icon = Icons.info; break;
                  case 'help': icon = Icons.help; break;
                  case 'warning': icon = Icons.warning; break;
                  case 'error': icon = Icons.error; break;
                  case 'notification': icon = Icons.notifications; break;
                  case 'email': icon = Icons.email; break;
                  case 'phone': icon = Icons.phone; break;
                  case 'message': icon = Icons.message; break;
                  case 'chat': icon = Icons.chat; break;
                  case 'calendar': icon = Icons.calendar_today; break;
                  case 'camera': icon = Icons.camera_alt; break;
                  case 'photo': icon = Icons.photo; break;
                  case 'video': icon = Icons.videocam; break;
                  case 'music': icon = Icons.music_note; break;
                  case 'file': icon = Icons.insert_drive_file; break;
                  case 'folder': icon = Icons.folder; break;
                  case 'cloud': icon = Icons.cloud; break;
                  case 'download': icon = Icons.download; break;
                  case 'upload': icon = Icons.upload; break;
                  case 'link': icon = Icons.link; break;
                  case 'location': icon = Icons.location_on; break;
                  case 'map': icon = Icons.map; break;
                  case 'navigation': icon = Icons.navigation; break;
                  case 'time': icon = Icons.access_time; break;
                  case 'alarm': icon = Icons.alarm; break;
                  case 'timer': icon = Icons.timer; break;
                  case 'watch': icon = Icons.watch; break;
                  case 'lock': icon = Icons.lock; break;
                  case 'unlock': icon = Icons.lock_open; break;
                  case 'security': icon = Icons.security; break;
                  case 'key': icon = Icons.vpn_key; break;
                  case 'wifi': icon = Icons.wifi; break;
                  case 'bluetooth': icon = Icons.bluetooth; break;
                  case 'battery': icon = Icons.battery_full; break;
                  case 'power': icon = Icons.power_settings_new; break;
                  case 'play': icon = Icons.play_arrow; break;
                  case 'pause': icon = Icons.pause; break;
                  case 'stop': icon = Icons.stop; break;
                  case 'skip_next': icon = Icons.skip_next; break;
                  case 'skip_previous': icon = Icons.skip_previous; break;
                  case 'volume': icon = Icons.volume_up; break;
                  case 'mute': icon = Icons.volume_off; break;
                  case 'mic': icon = Icons.mic; break;
                  case 'mic_off': icon = Icons.mic_off; break;
                  default: icon = Icons.circle;
                }

                return BottomNavigationBarItem(
                  icon: Icon(icon),
                  label: label,
                );
              }
              return const BottomNavigationBarItem(
                icon: Icon(Icons.circle),
                label: 'Item',
              );
            }).toList();
          }
        }
      }

      return BottomNavigationBar(
        items: items,
        backgroundColor: backgroundColor,
        selectedItemColor: selectedItemColor,
        unselectedItemColor: unselectedItemColor,
        type: BottomNavigationBarType.fixed,
        currentIndex: 0,
        onTap: (_) {},
      );
    }

    // Create a simple FloatingActionButton if needed
    Widget? createFloatingActionButton(dynamic fabValue) {
      if (fabValue == null) return null;

      String tooltip = 'Floating Action Button';
      Color backgroundColor = Colors.blue;
      Color foregroundColor = Colors.white;
      IconData icon = Icons.add;

      if (fabValue is Map<String, dynamic>) {
        tooltip = fabValue['tooltip'] as String? ?? 'Floating Action Button';
        backgroundColor = fabValue.containsKey('backgroundColor')
            ? getColorFromValue(fabValue['backgroundColor'])
            : Colors.blue;
        foregroundColor = fabValue.containsKey('foregroundColor')
            ? getColorFromValue(fabValue['foregroundColor'])
            : Colors.white;

        // Set icon if provided
        if (fabValue.containsKey('icon')) {
          final iconName = fabValue['icon'] as String? ?? 'add';
          switch (iconName.toLowerCase()) {
            case 'add': icon = Icons.add; break;
            case 'edit': icon = Icons.edit; break;
            case 'delete': icon = Icons.delete; break;
            case 'share': icon = Icons.share; break;
            case 'search': icon = Icons.search; break;
            case 'favorite': icon = Icons.favorite; break;
            case 'camera': icon = Icons.camera_alt; break;
            case 'mic': icon = Icons.mic; break;
            case 'send': icon = Icons.send; break;
            case 'save': icon = Icons.save; break;
            case 'done': icon = Icons.done; break;
            case 'check': icon = Icons.check; break;
            case 'close': icon = Icons.close; break;
            case 'menu': icon = Icons.menu; break;
            case 'more': icon = Icons.more_vert; break;
            case 'refresh': icon = Icons.refresh; break;
            case 'settings': icon = Icons.settings; break;
            case 'person': icon = Icons.person; break;
            case 'home': icon = Icons.home; break;
            case 'info': icon = Icons.info; break;
            case 'help': icon = Icons.help; break;
            case 'warning': icon = Icons.warning; break;
            case 'error': icon = Icons.error; break;
            case 'notification': icon = Icons.notifications; break;
            case 'email': icon = Icons.email; break;
            case 'phone': icon = Icons.phone; break;
            case 'message': icon = Icons.message; break;
            case 'chat': icon = Icons.chat; break;
            case 'calendar': icon = Icons.calendar_today; break;
            case 'photo': icon = Icons.photo; break;
            case 'video': icon = Icons.videocam; break;
            case 'music': icon = Icons.music_note; break;
            case 'file': icon = Icons.insert_drive_file; break;
            case 'folder': icon = Icons.folder; break;
            case 'cloud': icon = Icons.cloud; break;
            case 'download': icon = Icons.download; break;
            case 'upload': icon = Icons.upload; break;
            case 'link': icon = Icons.link; break;
            case 'location': icon = Icons.location_on; break;
            case 'map': icon = Icons.map; break;
            case 'navigation': icon = Icons.navigation; break;
            case 'time': icon = Icons.access_time; break;
            case 'alarm': icon = Icons.alarm; break;
            case 'timer': icon = Icons.timer; break;
            case 'watch': icon = Icons.watch; break;
            case 'lock': icon = Icons.lock; break;
            case 'unlock': icon = Icons.lock_open; break;
            case 'security': icon = Icons.security; break;
            case 'key': icon = Icons.vpn_key; break;
            case 'wifi': icon = Icons.wifi; break;
            case 'bluetooth': icon = Icons.bluetooth; break;
            case 'battery': icon = Icons.battery_full; break;
            case 'power': icon = Icons.power_settings_new; break;
            case 'play': icon = Icons.play_arrow; break;
            case 'pause': icon = Icons.pause; break;
            case 'stop': icon = Icons.stop; break;
            case 'skip_next': icon = Icons.skip_next; break;
            case 'skip_previous': icon = Icons.skip_previous; break;
            case 'volume': icon = Icons.volume_up; break;
            case 'mute': icon = Icons.volume_off; break;
            default: icon = Icons.add;
          }
        }
      }

      return FloatingActionButton(
        onPressed: () {},
        tooltip: tooltip,
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        child: Icon(icon),
      );
    }

    // Extract properties from JSON
    final body = createBodyWidget(json['body']);

    // Extract flags
    final hasAppBar = json['hasAppBar'] as bool? ?? false;
    final hasDrawer = json['hasDrawer'] as bool? ?? false;
    final hasEndDrawer = json['hasEndDrawer'] as bool? ?? false;
    final hasBottomNavigationBar = json['hasBottomNavigationBar'] as bool? ?? false;
    final hasFloatingActionButton = json['hasFloatingActionButton'] as bool? ?? false;

    // Create components if needed
    final appBar = hasAppBar ? createAppBar(json['appBar']) : null;
    final drawer = hasDrawer ? createDrawer(json['drawer']) : null;
    final endDrawer = hasEndDrawer ? createDrawer(json['endDrawer']) : null;
    final bottomNavigationBar = hasBottomNavigationBar ? createBottomNavigationBar(json['bottomNavigationBar']) : null;
    final floatingActionButton = hasFloatingActionButton ? createFloatingActionButton(json['floatingActionButton']) : null;

    // Extract other properties
    final backgroundColor = json.containsKey('backgroundColor')
        ? getColorFromValue(json['backgroundColor'])
        : Colors.white;
    final resizeToAvoidBottomInset = json['resizeToAvoidBottomInset'] as bool? ?? true;
    final useSafeArea = json['useSafeArea'] as bool? ?? true;
    final isDarkTheme = json['isDarkTheme'] as bool? ?? false;
    final isDisabled = json['isDisabled'] as bool? ?? false;
    final floatingActionButtonLocation = json.containsKey('floatingActionButtonLocation')
        ? getFabLocationFromValue(json['floatingActionButtonLocation'])
        : null;

    // Extract drawer properties
    final drawerWidth = (json['drawerWidth'] as num?)?.toDouble();
    final endDrawerWidth = (json['endDrawerWidth'] as num?)?.toDouble();
    final drawerEdgeDragWidth = (json['drawerEdgeDragWidth'] as num?)?.toDouble();
    final endDrawerEdgeDragWidth = (json['endDrawerEdgeDragWidth'] as num?)?.toDouble();
    final drawerEnableOpenDragGesture = json['drawerEnableOpenDragGesture'] as bool? ?? true;
    final endDrawerEnableOpenDragGesture = json['endDrawerEnableOpenDragGesture'] as bool? ?? true;
    final drawerScrimColor = json.containsKey('drawerScrimColor')
        ? getColorFromValue(json['drawerScrimColor'])
        : null;

    // Helper function to get EdgeInsets from value
    EdgeInsetsGeometry getEdgeInsetsFromValue(dynamic value) {
      if (value is String) {
        // Parse formats like "16.0" or "16.0,8.0,16.0,8.0" (top,right,bottom,left)
        final parts = value.split(',');
        if (parts.length == 1) {
          final all = double.tryParse(parts[0]) ?? 16.0;
          return EdgeInsets.all(all);
        } else if (parts.length == 2) {
          final vertical = double.tryParse(parts[0]) ?? 16.0;
          final horizontal = double.tryParse(parts[1]) ?? 16.0;
          return EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal);
        } else if (parts.length == 4) {
          final top = double.tryParse(parts[0]) ?? 16.0;
          final right = double.tryParse(parts[1]) ?? 16.0;
          final bottom = double.tryParse(parts[2]) ?? 16.0;
          final left = double.tryParse(parts[3]) ?? 16.0;
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (value is double) {
        return EdgeInsets.all(value);
      } else if (value is int) {
        return EdgeInsets.all(value.toDouble());
      } else if (value is Map) {
        final top = (value['top'] as num?)?.toDouble() ?? 0.0;
        final right = (value['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (value['bottom'] as num?)?.toDouble() ?? 0.0;
        final left = (value['left'] as num?)?.toDouble() ?? 0.0;

        if (value.containsKey('all')) {
          final all = (value['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
          final horizontal = (value['horizontal'] as num?)?.toDouble() ?? 0.0;
          final vertical = (value['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      }
      return const EdgeInsets.all(16.0);
    }

    // Extract body properties
    final isBodyScrollable = json['isBodyScrollable'] as bool? ?? false;
    final bodyPadding = json.containsKey('bodyPadding')
        ? getEdgeInsetsFromValue(json['bodyPadding'])
        : EdgeInsets.zero;

    // Extract system UI properties
    final systemUiOverlayStyle = json.containsKey('systemUiOverlayStyle')
        ? SystemUiOverlayStyle(
            statusBarColor: json.containsKey('statusBarColor')
                ? getColorFromValue(json['statusBarColor'])
                : null,
            statusBarBrightness: json['statusBarBrightness'] == 'dark'
                ? Brightness.dark
                : Brightness.light,
            statusBarIconBrightness: json['statusBarIconBrightness'] == 'dark'
                ? Brightness.dark
                : Brightness.light,
            systemNavigationBarColor: json.containsKey('systemNavigationBarColor')
                ? getColorFromValue(json['systemNavigationBarColor'])
                : null,
            systemNavigationBarDividerColor: json.containsKey('systemNavigationBarDividerColor')
                ? getColorFromValue(json['systemNavigationBarDividerColor'])
                : null,
            systemNavigationBarIconBrightness: json['systemNavigationBarIconBrightness'] == 'dark'
                ? Brightness.dark
                : Brightness.light,
          )
        : null;

    // Extract layout properties
    final extendBodyBehindAppBar = json['extendBodyBehindAppBar'] as bool? ?? false;
    final extendBody = json['extendBody'] as bool? ?? false;

    // Extract footer properties
    final persistentFooterButtons = json.containsKey('persistentFooterButtons') && json['persistentFooterButtons'] is List
        ? (json['persistentFooterButtons'] as List).map((button) {
            if (button is Map<String, dynamic> && button.containsKey('text')) {
              return TextButton(
                onPressed: () {},
                child: Text(button['text'] as String),
              );
            }
            return TextButton(
              onPressed: () {},
              child: const Text('Button'),
            );
          }).toList()
        : null;
    final persistentFooterButtonsHeight = (json['persistentFooterButtonsHeight'] as num?)?.toDouble();

    // Extract bottom sheet
    final bottomSheet = json.containsKey('bottomSheet')
        ? Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[200],
            child: Center(
              child: Text(
                json['bottomSheet'] is String
                    ? json['bottomSheet'] as String
                    : 'Bottom Sheet',
              ),
            ),
          )
        : null;

    // Extract primary color
    final primaryColor = json.containsKey('primaryColor')
        ? getColorFromValue(json['primaryColor'])
        : null;

    // Extract floating action button properties
    final floatingActionButtonShape = json.containsKey('floatingActionButtonShape')
        ? json['floatingActionButtonShape'] == 'circle'
            ? const CircleBorder()
            : json['floatingActionButtonShape'] == 'stadium'
                ? const StadiumBorder()
                : json['floatingActionButtonShape'] == 'rounded'
                    ? RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      )
                    : null
        : null;
    final floatingActionButtonSize = (json['floatingActionButtonSize'] as num?)?.toDouble();
    final isFloatingActionButtonExtended = json['isFloatingActionButtonExtended'] as bool? ?? false;
    final floatingActionButtonLabel = json['floatingActionButtonLabel'] as String?;

    // Extract bottom navigation bar properties
    final bottomNavigationBarCurrentIndex = json['bottomNavigationBarCurrentIndex'] as int? ?? 0;
    final bottomNavigationBarType = json['bottomNavigationBarType'] == 'shifting'
        ? BottomNavigationBarType.shifting
        : BottomNavigationBarType.fixed;
    final bottomNavigationBarItemShape = json.containsKey('bottomNavigationBarItemShape')
        ? json['bottomNavigationBarItemShape'] == 'circle'
            ? const CircleBorder()
            : json['bottomNavigationBarItemShape'] == 'stadium'
                ? const StadiumBorder()
                : json['bottomNavigationBarItemShape'] == 'rounded'
                    ? RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      )
                    : null
        : null;

    // Extract app bar properties
    List<Widget>? appBarActions;
    if (json.containsKey('appBarActions') && json['appBarActions'] is List) {
      appBarActions = (json['appBarActions'] as List).map((action) {
        if (action is Map<String, dynamic> && action.containsKey('icon')) {
          final iconName = action['icon'] as String;
          IconData icon = Icons.more_vert;
          switch (iconName.toLowerCase()) {
            case 'settings': icon = Icons.settings; break;
            case 'search': icon = Icons.search; break;
            case 'more': icon = Icons.more_vert; break;
            case 'menu': icon = Icons.menu; break;
            case 'share': icon = Icons.share; break;
            case 'favorite': icon = Icons.favorite; break;
            case 'bookmark': icon = Icons.bookmark; break;
            case 'info': icon = Icons.info; break;
            case 'help': icon = Icons.help; break;
            case 'person': icon = Icons.person; break;
            case 'account': icon = Icons.account_circle; break;
            case 'notifications': icon = Icons.notifications; break;
            default: icon = Icons.more_vert;
          }
          return IconButton(
            icon: Icon(icon),
            onPressed: () {},
          );
        }
        return IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {},
        );
      }).toList();
    }

    Widget? appBarLeading;
    if (json.containsKey('appBarLeading') && json['appBarLeading'] is Map<String, dynamic>) {
      final leading = json['appBarLeading'] as Map<String, dynamic>;
      if (leading.containsKey('icon')) {
        final iconName = leading['icon'] as String;
        IconData icon = Icons.menu;
        switch (iconName.toLowerCase()) {
          case 'back': icon = Icons.arrow_back; break;
          case 'menu': icon = Icons.menu; break;
          case 'close': icon = Icons.close; break;
          case 'home': icon = Icons.home; break;
          case 'settings': icon = Icons.settings; break;
          case 'person': icon = Icons.person; break;
          default: icon = Icons.menu;
        }
        appBarLeading = IconButton(
          icon: Icon(icon),
          onPressed: () {},
        );
      }
    }

    PreferredSizeWidget? appBarBottom;
    if (json.containsKey('appBarBottom') && json['appBarBottom'] is Map<String, dynamic>) {
      final bottom = json['appBarBottom'] as Map<String, dynamic>;
      if (bottom.containsKey('type') && bottom['type'] == 'tabBar') {
        final tabs = bottom.containsKey('tabs') && bottom['tabs'] is List
            ? (bottom['tabs'] as List).map((tab) {
                if (tab is String) {
                  return Tab(text: tab);
                } else if (tab is Map<String, dynamic> && tab.containsKey('text')) {
                  return Tab(text: tab['text'] as String);
                }
                return const Tab(text: 'Tab');
              }).toList()
            : <Widget>[
                const Tab(text: 'Tab 1'),
                const Tab(text: 'Tab 2'),
              ];
        appBarBottom = TabBar(
          tabs: tabs,
        );
      } else if (bottom.containsKey('type') && bottom['type'] == 'searchBar') {
        appBarBottom = PreferredSize(
          preferredSize: const Size.fromHeight(56.0),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: bottom['hintText'] as String? ?? 'Search',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
            ),
          ),
        );
      }
    }

    // Return the ScaffoldWidget with properties from JSON
    return ScaffoldWidget(
      body: body,
      appBar: appBar,
      drawer: drawer,
      endDrawer: endDrawer,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      hasDrawer: hasDrawer,
      hasEndDrawer: hasEndDrawer,
      hasFloatingActionButton: hasFloatingActionButton,
      hasBottomNavigationBar: hasBottomNavigationBar,
      hasAppBar: hasAppBar,
      useSafeArea: useSafeArea,
      isDarkTheme: isDarkTheme,
      isDisabled: isDisabled,
      drawerWidth: (json['drawerWidth'] as num?)?.toDouble(),
      endDrawerWidth: (json['endDrawerWidth'] as num?)?.toDouble(),
      drawerEdgeDragWidth: (json['drawerEdgeDragWidth'] as num?)?.toDouble(),
      endDrawerEdgeDragWidth: (json['endDrawerEdgeDragWidth'] as num?)?.toDouble(),
      drawerEnableOpenDragGesture: json['drawerEnableOpenDragGesture'] as bool? ?? true,
      endDrawerEnableOpenDragGesture: json['endDrawerEnableOpenDragGesture'] as bool? ?? true,
      isBodyScrollable: json['isBodyScrollable'] as bool? ?? false,
      bodyPadding: json.containsKey('bodyPadding')
          ? EdgeInsets.all((json['bodyPadding'] as num?)?.toDouble() ?? 16.0)
          : EdgeInsets.zero,
      systemUiOverlayStyle: json.containsKey('systemUiOverlayStyle')
          ? SystemUiOverlayStyle(
              statusBarColor: json.containsKey('statusBarColor')
                  ? getColorFromValue(json['statusBarColor'])
                  : null,
              statusBarBrightness: json['statusBarBrightness'] == 'dark'
                  ? Brightness.dark
                  : Brightness.light,
              statusBarIconBrightness: json['statusBarIconBrightness'] == 'dark'
                  ? Brightness.dark
                  : Brightness.light,
              systemNavigationBarColor: json.containsKey('systemNavigationBarColor')
                  ? getColorFromValue(json['systemNavigationBarColor'])
                  : null,
              systemNavigationBarDividerColor: json.containsKey('systemNavigationBarDividerColor')
                  ? getColorFromValue(json['systemNavigationBarDividerColor'])
                  : null,
              systemNavigationBarIconBrightness: json['systemNavigationBarIconBrightness'] == 'dark'
                  ? Brightness.dark
                  : Brightness.light,
            )
          : null,
      extendBodyBehindAppBar: json['extendBodyBehindAppBar'] as bool? ?? false,
      extendBody: json['extendBody'] as bool? ?? false,
      persistentFooterButtons: json.containsKey('persistentFooterButtons') && json['persistentFooterButtons'] is List
          ? (json['persistentFooterButtons'] as List).map<Widget>((button) {
              if (button is Map<String, dynamic> && button.containsKey('text')) {
                return TextButton(
                  onPressed: () {},
                  child: Text(button['text'] as String),
                );
              }
              return TextButton(
                onPressed: () {},
                child: const Text('Button'),
              );
            }).toList()
          : null,
      persistentFooterButtonsHeight: (json['persistentFooterButtonsHeight'] as num?)?.toDouble(),
      persistentFooterButtonsAlignment: AlignmentDirectional.centerEnd,
      bottomSheet: json.containsKey('bottomSheet')
          ? Container(
              padding: const EdgeInsets.all(16.0),
              color: Colors.grey[200],
              child: Center(
                child: Text(
                  json['bottomSheet'] is String
                      ? json['bottomSheet'] as String
                      : 'Bottom Sheet',
                ),
              ),
            )
          : null,
      primaryColor: json.containsKey('primaryColor')
          ? getColorFromValue(json['primaryColor'])
          : null,
      drawerScrimColor: json.containsKey('drawerScrimColor')
          ? getColorFromValue(json['drawerScrimColor'])
          : null,
      floatingActionButtonShape: json.containsKey('floatingActionButtonShape')
          ? json['floatingActionButtonShape'] == 'circle'
              ? const CircleBorder()
              : json['floatingActionButtonShape'] == 'stadium'
                  ? const StadiumBorder()
                  : json['floatingActionButtonShape'] == 'rounded'
                      ? RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.0),
                        )
                      : null
          : null,
      floatingActionButtonSize: (json['floatingActionButtonSize'] as num?)?.toDouble(),
      isFloatingActionButtonExtended: json['isFloatingActionButtonExtended'] as bool? ?? false,
      floatingActionButtonLabel: json['floatingActionButtonLabel'] as String?,
      bottomNavigationBarCurrentIndex: json['bottomNavigationBarCurrentIndex'] as int? ?? 0,
      bottomNavigationBarType: json['bottomNavigationBarType'] == 'shifting'
          ? BottomNavigationBarType.shifting
          : BottomNavigationBarType.fixed,
      bottomNavigationBarItemShape: json.containsKey('bottomNavigationBarItemShape')
          ? json['bottomNavigationBarItemShape'] == 'circle'
              ? const CircleBorder()
              : json['bottomNavigationBarItemShape'] == 'stadium'
                  ? const StadiumBorder()
                  : json['bottomNavigationBarItemShape'] == 'rounded'
                      ? RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.0),
                        )
                      : null
          : null,
      appBarActions: json.containsKey('appBarActions') && json['appBarActions'] is List
          ? (json['appBarActions'] as List).map<Widget>((action) {
              if (action is Map<String, dynamic> && action.containsKey('icon')) {
                final iconName = action['icon'] as String;
                IconData icon = Icons.more_vert;
                switch (iconName.toLowerCase()) {
                  case 'settings': icon = Icons.settings; break;
                  case 'search': icon = Icons.search; break;
                  case 'more': icon = Icons.more_vert; break;
                  case 'menu': icon = Icons.menu; break;
                  case 'share': icon = Icons.share; break;
                  case 'favorite': icon = Icons.favorite; break;
                  case 'bookmark': icon = Icons.bookmark; break;
                  case 'info': icon = Icons.info; break;
                  case 'help': icon = Icons.help; break;
                  case 'person': icon = Icons.person; break;
                  case 'account': icon = Icons.account_circle; break;
                  case 'notifications': icon = Icons.notifications; break;
                  default: icon = Icons.more_vert;
                }
                return IconButton(
                  icon: Icon(icon),
                  onPressed: () {},
                );
              }
              return IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: () {},
              );
            }).toList()
          : null,
      appBarLeading: json.containsKey('appBarLeading') && json['appBarLeading'] is Map<String, dynamic>
          ? (() {
              final leading = json['appBarLeading'] as Map<String, dynamic>;
              if (leading.containsKey('icon')) {
                final iconName = leading['icon'] as String;
                IconData icon = Icons.menu;
                switch (iconName.toLowerCase()) {
                  case 'back': icon = Icons.arrow_back; break;
                  case 'menu': icon = Icons.menu; break;
                  case 'close': icon = Icons.close; break;
                  case 'home': icon = Icons.home; break;
                  case 'settings': icon = Icons.settings; break;
                  case 'person': icon = Icons.person; break;
                  default: icon = Icons.menu;
                }
                return IconButton(
                  icon: Icon(icon),
                  onPressed: () {},
                );
              }
              return null;
            })()
          : null,
      appBarBottom: json.containsKey('appBarBottom') && json['appBarBottom'] is Map<String, dynamic>
          ? (() {
              final bottom = json['appBarBottom'] as Map<String, dynamic>;
              if (bottom.containsKey('type') && bottom['type'] == 'tabBar') {
                final tabs = bottom.containsKey('tabs') && bottom['tabs'] is List
                    ? (bottom['tabs'] as List).map<Widget>((tab) {
                        if (tab is String) {
                          return Tab(text: tab);
                        } else if (tab is Map<String, dynamic> && tab.containsKey('text')) {
                          return Tab(text: tab['text'] as String);
                        }
                        return const Tab(text: 'Tab');
                      }).toList()
                    : <Widget>[
                        const Tab(text: 'Tab 1'),
                        const Tab(text: 'Tab 2'),
                      ];
                return TabBar(
                  tabs: tabs,
                );
              } else if (bottom.containsKey('type') && bottom['type'] == 'searchBar') {
                return PreferredSize(
                  preferredSize: const Size.fromHeight(56.0),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: bottom['hintText'] as String? ?? 'Search',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                    ),
                  ),
                );
              }
              return null;
            })()
          : null,
    );
  }

  /// Converts the ScaffoldWidget to a JSON map.
  ///
  /// This method allows serializing the widget's configuration to JSON,
  /// making it easy to save and restore widget states.
  Map<String, dynamic> toJson() {
    // Helper function to convert Color to hex string
    String colorToHex(Color color) {
      return '#${color.toString().substring(10, 16)}';
    }

    // Helper function to convert ShapeBorder to string
    String? shapeBorderToString(ShapeBorder? shape) {
      if (shape == null) return null;
      if (shape is CircleBorder) return 'circle';
      if (shape is StadiumBorder) return 'stadium';
      if (shape is RoundedRectangleBorder) return 'rounded';
      return null;
    }

    final Map<String, dynamic> json = {
      'backgroundColor': colorToHex(backgroundColor),
      'resizeToAvoidBottomInset': resizeToAvoidBottomInset,
      'hasDrawer': hasDrawer,
      'hasEndDrawer': hasEndDrawer,
      'hasFloatingActionButton': hasFloatingActionButton,
      'hasBottomNavigationBar': hasBottomNavigationBar,
      'hasAppBar': hasAppBar,
      'useSafeArea': useSafeArea,
      'isDarkTheme': isDarkTheme,
      'isDisabled': isDisabled,
      'floatingActionButtonLocation': floatingActionButtonLocation?.toString(),
      // We can't serialize the actual widget content, so we'll just indicate it was serialized
      'bodySerialized': true,
      'appBarSerialized': hasAppBar,
      'drawerSerialized': hasDrawer,
      'endDrawerSerialized': hasEndDrawer,
      'bottomNavigationBarSerialized': hasBottomNavigationBar,
      'floatingActionButtonSerialized': hasFloatingActionButton,
    };

    // Add drawer properties
    if (drawerWidth != null) json['drawerWidth'] = drawerWidth;
    if (endDrawerWidth != null) json['endDrawerWidth'] = endDrawerWidth;
    if (drawerEdgeDragWidth != null) json['drawerEdgeDragWidth'] = drawerEdgeDragWidth;
    if (endDrawerEdgeDragWidth != null) json['endDrawerEdgeDragWidth'] = endDrawerEdgeDragWidth;
    json['drawerEnableOpenDragGesture'] = drawerEnableOpenDragGesture;
    json['endDrawerEnableOpenDragGesture'] = endDrawerEnableOpenDragGesture;
    if (drawerScrimColor != null) json['drawerScrimColor'] = colorToHex(drawerScrimColor!);

    // Add body properties
    json['isBodyScrollable'] = isBodyScrollable;
    if (bodyPadding != EdgeInsets.zero) {
      if (bodyPadding is EdgeInsets) {
        final edgeInsets = bodyPadding as EdgeInsets;
        if (edgeInsets.left == edgeInsets.top && edgeInsets.left == edgeInsets.right && edgeInsets.left == edgeInsets.bottom) {
          json['bodyPadding'] = edgeInsets.left;
        } else {
          json['bodyPadding'] = {
            'left': edgeInsets.left,
            'top': edgeInsets.top,
            'right': edgeInsets.right,
            'bottom': edgeInsets.bottom,
          };
        }
      }
    }

    // Add system UI properties
    if (systemUiOverlayStyle != null) {
      json['systemUiOverlayStyle'] = true;
      if (systemUiOverlayStyle!.statusBarColor != null) {
        json['statusBarColor'] = colorToHex(systemUiOverlayStyle!.statusBarColor!);
      }
      json['statusBarBrightness'] = systemUiOverlayStyle!.statusBarBrightness == Brightness.dark ? 'dark' : 'light';
      json['statusBarIconBrightness'] = systemUiOverlayStyle!.statusBarIconBrightness == Brightness.dark ? 'dark' : 'light';
      if (systemUiOverlayStyle!.systemNavigationBarColor != null) {
        json['systemNavigationBarColor'] = colorToHex(systemUiOverlayStyle!.systemNavigationBarColor!);
      }
      if (systemUiOverlayStyle!.systemNavigationBarDividerColor != null) {
        json['systemNavigationBarDividerColor'] = colorToHex(systemUiOverlayStyle!.systemNavigationBarDividerColor!);
      }
      json['systemNavigationBarIconBrightness'] = systemUiOverlayStyle!.systemNavigationBarIconBrightness == Brightness.dark ? 'dark' : 'light';
    }

    // Add layout properties
    json['extendBodyBehindAppBar'] = extendBodyBehindAppBar;
    json['extendBody'] = extendBody;

    // Add footer properties
    if (persistentFooterButtons != null && persistentFooterButtons!.isNotEmpty) {
      json['persistentFooterButtonsSerialized'] = true;
    }
    if (persistentFooterButtonsHeight != null) {
      json['persistentFooterButtonsHeight'] = persistentFooterButtonsHeight;
    }

    // Add bottom sheet
    if (bottomSheet != null) {
      json['bottomSheetSerialized'] = true;
    }

    // Add primary color
    if (primaryColor != null) {
      json['primaryColor'] = colorToHex(primaryColor!);
    }

    // Add floating action button properties
    if (floatingActionButtonShape != null) {
      json['floatingActionButtonShape'] = shapeBorderToString(floatingActionButtonShape);
    }
    if (floatingActionButtonSize != null) {
      json['floatingActionButtonSize'] = floatingActionButtonSize;
    }
    json['isFloatingActionButtonExtended'] = isFloatingActionButtonExtended;
    if (floatingActionButtonLabel != null) {
      json['floatingActionButtonLabel'] = floatingActionButtonLabel;
    }

    // Add bottom navigation bar properties
    json['bottomNavigationBarCurrentIndex'] = bottomNavigationBarCurrentIndex;
    json['bottomNavigationBarType'] = bottomNavigationBarType == BottomNavigationBarType.shifting ? 'shifting' : 'fixed';
    if (bottomNavigationBarItemShape != null) {
      json['bottomNavigationBarItemShape'] = shapeBorderToString(bottomNavigationBarItemShape);
    }

    // Add app bar properties
    if (appBarActions != null && appBarActions!.isNotEmpty) {
      json['appBarActionsSerialized'] = true;
    }
    if (appBarLeading != null) {
      json['appBarLeadingSerialized'] = true;
    }
    if (appBarBottom != null) {
      json['appBarBottomSerialized'] = true;
    }

    return json;
  }
}

class _ScaffoldWidgetState extends State<ScaffoldWidget> {
  @override
  Widget build(BuildContext context) {
    // Prepare the body with scrolling and padding if needed
    Widget effectiveBody = widget.body;

    // Apply padding if specified
    if (widget.bodyPadding != EdgeInsets.zero) {
      effectiveBody = Padding(
        padding: widget.bodyPadding,
        child: effectiveBody,
      );
    }

    // Apply scrolling if specified
    if (widget.isBodyScrollable) {
      effectiveBody = SingleChildScrollView(
        child: effectiveBody,
      );
    }

    // Apply safe area if specified
    if (widget.useSafeArea) {
      effectiveBody = SafeArea(child: effectiveBody);
    }

    // Create custom drawer if width is specified
    Widget? effectiveDrawer = widget.drawer;
    if (widget.hasDrawer && widget.drawer != null && widget.drawerWidth != null) {
      effectiveDrawer = SizedBox(
        width: widget.drawerWidth,
        child: widget.drawer,
      );
    }

    // Create custom end drawer if width is specified
    Widget? effectiveEndDrawer = widget.endDrawer;
    if (widget.hasEndDrawer && widget.endDrawer != null && widget.endDrawerWidth != null) {
      effectiveEndDrawer = SizedBox(
        width: widget.endDrawerWidth,
        child: widget.endDrawer,
      );
    }

    // Create custom floating action button if extended
    Widget? effectiveFab = widget.floatingActionButton;
    if (widget.hasFloatingActionButton && widget.isFloatingActionButtonExtended &&
        widget.floatingActionButtonLabel != null && widget.floatingActionButton is Icon) {
      final icon = widget.floatingActionButton as Icon;
      effectiveFab = FloatingActionButton.extended(
        onPressed: () {},
        label: Text(widget.floatingActionButtonLabel!),
        icon: Icon(icon.icon),
        backgroundColor: icon.color,
      );
    } else if (widget.hasFloatingActionButton && widget.floatingActionButtonSize != null &&
               widget.floatingActionButton is FloatingActionButton) {
      final fab = widget.floatingActionButton as FloatingActionButton;
      effectiveFab = SizedBox(
        width: widget.floatingActionButtonSize,
        height: widget.floatingActionButtonSize,
        child: FloatingActionButton(
          onPressed: fab.onPressed,
          backgroundColor: fab.backgroundColor,
          foregroundColor: fab.foregroundColor,
          shape: widget.floatingActionButtonShape,
          child: fab.child,
        ),
      );
    }

    // Create custom app bar if actions, leading, or bottom are specified
    PreferredSizeWidget? effectiveAppBar = widget.appBar;
    if (widget.hasAppBar && widget.appBar is AppBar) {
      final originalAppBar = widget.appBar as AppBar;
      effectiveAppBar = AppBar(
        title: originalAppBar.title,
        backgroundColor: originalAppBar.backgroundColor,
        foregroundColor: originalAppBar.foregroundColor,
        centerTitle: originalAppBar.centerTitle,
        elevation: originalAppBar.elevation,
        actions: widget.appBarActions ?? originalAppBar.actions,
        leading: widget.appBarLeading ?? originalAppBar.leading,
        bottom: widget.appBarBottom ?? originalAppBar.bottom,
        systemOverlayStyle: widget.systemUiOverlayStyle,
      );
    }

    // Create custom bottom navigation bar if current index or type are specified
    Widget? effectiveBottomNavBar = widget.bottomNavigationBar;
    if (widget.hasBottomNavigationBar && widget.bottomNavigationBar is BottomNavigationBar) {
      final originalNavBar = widget.bottomNavigationBar as BottomNavigationBar;
      effectiveBottomNavBar = BottomNavigationBar(
        items: originalNavBar.items,
        currentIndex: widget.bottomNavigationBarCurrentIndex,
        onTap: originalNavBar.onTap ?? (_) {},
        backgroundColor: originalNavBar.backgroundColor,
        selectedItemColor: originalNavBar.selectedItemColor,
        unselectedItemColor: originalNavBar.unselectedItemColor,
        type: widget.bottomNavigationBarType,
      );
    }

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: widget.systemUiOverlayStyle ?? SystemUiOverlayStyle.dark,
      child: Opacity(
        opacity: widget.isDisabled ? 0.5 : 1.0,
        child: Scaffold(
          appBar: widget.hasAppBar ? effectiveAppBar : null,
          drawer: widget.hasDrawer ? effectiveDrawer : null,
          endDrawer: widget.hasEndDrawer ? effectiveEndDrawer : null,
          bottomNavigationBar: widget.hasBottomNavigationBar ? effectiveBottomNavBar : null,
          floatingActionButton: widget.hasFloatingActionButton ? effectiveFab : null,
          floatingActionButtonLocation: widget.floatingActionButtonLocation,
          backgroundColor: widget.backgroundColor,
          resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
          body: effectiveBody,
          drawerEdgeDragWidth: widget.drawerEdgeDragWidth,
          endDrawerEnableOpenDragGesture: widget.endDrawerEnableOpenDragGesture,
          drawerEnableOpenDragGesture: widget.drawerEnableOpenDragGesture,
          extendBody: widget.extendBody,
          extendBodyBehindAppBar: widget.extendBodyBehindAppBar,
          persistentFooterButtons: widget.persistentFooterButtons,
          bottomSheet: widget.bottomSheet,
          primary: true,
          drawerScrimColor: widget.drawerScrimColor,
        ),
      ),
    );
  }
}
