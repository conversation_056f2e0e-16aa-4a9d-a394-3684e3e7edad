import 'package:flutter/material.dart';
import 'package:nsl/models/tree_node.dart';
import 'package:nsl/services/tree_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/widgets/web_tree_view.dart';
import 'package:nsl/widgets/simple_divider.dart';
import 'package:nsl/widgets/resizable_divider.dart';

class WebBuildScreenNew extends StatefulWidget {
  const WebBuildScreenNew({super.key});

  @override
  State<WebBuildScreenNew> createState() => _WebBuildScreenNewState();
}

class _WebBuildScreenNewState extends State<WebBuildScreenNew> {
  late TreeStructure _treeStructure;
  bool _isLoading = true;
  String? _error;
  bool _isInitialized = false;
  TreeNode? _selectedNode;
  bool isRightSidebarVisible = true;

  // Content width for responsive layout
  double _contentWidth = 0;

  // Right sidebar width (flex ratio)
  double _rightSidebarFlex = 4; // Initial flex value

  @override
  void initState() {
    super.initState();
    // We'll load the data in didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _loadTreeData();
      _isInitialized = true;
    }
  }

  Future<void> _loadTreeData() async {
    try {
      // Use the TreeService to load the tree data
      final treeStructure =
          await TreeService.loadTreeData('assets/data/tree_structure.json');

      if (mounted) {
        setState(() {
          _treeStructure = treeStructure;
          final stacks = _treeStructure.metadata!['stacks'] as List<dynamic>;
          final stack = stacks.first;
          final stackId = stack['id'] as int;
          final virtualNode = TreeNode(
            id: 'node$stackId',
            type: 'stack',
            label: stack['name'],
            children: [],
          );

          _selectedNode = virtualNode;

          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error loading tree data: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Calculate content width based on screen size
    final screenWidth = MediaQuery.of(context).size.width;
    _contentWidth = screenWidth - 70;
    // _contentWidth = screenWidth - 70; // Subtract sidebar width
    // _contentWidth = _contentWidth.clamp(_minContentWidth, _maxContentWidth);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface.withAlpha(245),
      body: Row(
        children: [
          // Navigation Sidebar
          // const WebNavigationSidebar(currentScreen: 'build'),

          // Main Content Area
          SizedBox(
            width: _contentWidth,
            child: _buildBody(),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48.0,
              ),
              const SizedBox(height: 16.0),
              Text(
                'Error loading tree data:',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8.0),
              Text(_error!),
              const SizedBox(height: 16.0),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                  _loadTreeData();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left sidebar - Projects and Claims sections
          _buildLeftSidebar(),

          // Separator between sidebar and main content
          const SimpleDivider(
            thickness: 1,
            color: Colors.grey,
            isVertical: true,
          ),

          // Main content - Tree view
          Expanded(
            flex: 3,
            child: _buildMainContent(),
          ),

          // Resizable divider between main content and right sidebar
          ResizableDivider(
            onResize: (delta) {
              // Adjust the width of the right sidebar based on the drag delta
              setState(() {
                // Decrease flex value when dragging right (positive delta)
                // Increase flex value when dragging left (negative delta)
                // The flex value controls the right sidebar width
                _rightSidebarFlex =
                    (_rightSidebarFlex - delta * 0.01).clamp(2.0, 6.0);
              });
            },
            color: Colors.grey,
            activeColor: Theme.of(context).colorScheme.primary,
          ),

          // Right sidebar - Stacks info
          _buildRightSidebar(),
        ],
      ),
    );
  }

  Widget _buildLeftSidebar() {
    return SizedBox(
      width: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor.withAlpha(50),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.folder_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Projects',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),

          // Claims Processing subsections
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildLeftSidebarItem(
                  'Claims Processing',
                  isSelected: true,
                ),
                _buildLeftSidebarItem(
                  'Claims Approval/Reject',
                  isSelected: false,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeftSidebarItem(String title, {required bool isSelected}) {
    return Container(
      color: isSelected
          ? Theme.of(context).colorScheme.primary.withAlpha(30)
          : null,
      padding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 12.0,
      ),
      child: Row(
        children: [
          const SizedBox(width: 16.0), // Indentation
          Text(
            title,
            style: TextStyle(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              fontSize: 14.0,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with document icon and ID
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor.withAlpha(50),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.description_outlined,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(150),
                size: 20.0,
              ),
              const SizedBox(width: 8.0),
              Text(
                'V00012',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(150),
                  fontSize: 14.0,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                  _loadTreeData();
                },
                tooltip: 'Refresh tree data',
              ),
            ],
          ),
        ),

        // Main tree content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  // Container(
                  //   padding: const EdgeInsets.all(16.0),
                  //   child: Text(
                  //     'Created Student Fee For List.',
                  //     style: TextStyle(
                  //       fontWeight: FontWeight.bold,
                  //       fontSize: 18.0,
                  //       color: Theme.of(context).colorScheme.primary,
                  //     ),
                  //   ),
                  // ),

                  // Tree view
                  WebTreeView(
                    treeStructure: _treeStructure,
                    onNodeTap: _handleNodeTap,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRightSidebar() {
    return Expanded(
      flex:
          _rightSidebarFlex.round(), // Use the dynamic flex value for resizing
      child: Padding(
        padding: const EdgeInsets.only(
            top: AppSpacing.md, left: AppSpacing.md, right: AppSpacing.md),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left sidebar with stacks
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: _buildStacksAndPermissions(),
              ),
            ),
            const SizedBox(width: 16.0),
            // Right content area with details
            !isRightSidebarVisible
                ? Container()
                : Expanded(
                    flex: 2,
                    child: Container(
                        decoration: BoxDecoration(color: Colors.white),
                        height: double.infinity,
                        padding: EdgeInsets.all(AppSpacing.md),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                InkWell(
                                    onTap: () {
                                      setState(() {
                                        isRightSidebarVisible = false;
                                      });
                                    },
                                    child: Icon(Icons.close))
                              ],
                            ),
                            Expanded(
                              child: SingleChildScrollView(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    _buildNodeDetails(),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        )))
          ],
        ),
      ),
    );
  }

  Widget _buildStacksAndPermissions() {
    final stacks = _treeStructure.metadata!['stacks'] as List<dynamic>;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16.0),

          // Stacks section
          ...stacks.map((stack) {
            final stackId = stack['id'] as int;
            final isSelected =
                _selectedNode != null && _selectedNode!.id == 'node$stackId';

            return InkWell(
              onTap: () {
                // Create a virtual node with the stack ID to display its details
                final virtualNode = TreeNode(
                  id: 'node$stackId',
                  type: 'stack',
                  label: stack['name'],
                  children: [],
                );

                setState(() {
                  _selectedNode = virtualNode;
                  isRightSidebarVisible = true;
                });
              },
              child: Container(
                color:
                    isSelected ? Colors.blue.withAlpha(20) : Colors.transparent,
                padding: const EdgeInsets.symmetric(
                    vertical: 12.0, horizontal: 16.0),
                child: Row(
                  children: [
                    SizedBox(
                      width: 24.0,
                      child: Text(
                        '${stack['id']}',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: isSelected
                              ? Colors.blue
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8.0),
                    Expanded(
                      child: Text(
                        stack['name'],
                        style: TextStyle(
                          fontSize: 14.0,
                          fontWeight:
                              isSelected ? FontWeight.w500 : FontWeight.normal,
                          color: isSelected
                              ? Colors.blue
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildNodeDetails() {
    if (_selectedNode == null) {
      return const SizedBox.shrink();
    }

    // Find the stack details for the selected node
    final stacks = _treeStructure.metadata!['stacks'] as List<dynamic>;
    final stackIndex =
        int.tryParse(_selectedNode!.id.replaceAll('node', '')) ?? 0;

    // If the stack index is out of range, return empty widget
    if (stackIndex <= 0 || stackIndex > stacks.length) {
      return const Text('No details available for this node.');
    }

    final stack = stacks[stackIndex - 1];
    final details = stack['details'];

    // If details is a string, display it directly
    if (details is String) {
      return Card(
        margin: const EdgeInsets.only(top: 16.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.0),
          side: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            details,
            style: TextStyle(
              fontSize: 14.0,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
      );
    }

    // If details is a map, display it as an expansion tile with card styling
    if (details is Map<String, dynamic>) {
      final objectiveName = details['objectiveName'] as String?;
      final objectiveDetails = details['objectiveDetails'] as String?;

      if (objectiveName == null) {
        return const SizedBox.shrink();
      }

      return Card(
        margin: const EdgeInsets.only(top: 16.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.0),
          side: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        elevation: 0,
        child: Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent,
          ),
          child: ExpansionTile(
            title: Text(
              objectiveName,
              style: const TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            initiallyExpanded: true,
            tilePadding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            childrenPadding:
                const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
            trailing: const Icon(Icons.keyboard_arrow_down),
            children: [
              if (objectiveDetails != null)
                _buildBulletPoints(objectiveDetails),
            ],
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildBulletPoints(String text) {
    final lines = text.split('\n');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: lines.map((line) {
        line = line.trim();
        if (line.isEmpty) return const SizedBox.shrink();

        if (line.startsWith('•')) {
          // This is a bullet point
          final indentLevel = line.indexOf('•');
          final content = line.substring(line.indexOf('•') + 1).trim();

          return Padding(
            padding: EdgeInsets.only(left: indentLevel * 8.0, bottom: 8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• ', style: TextStyle(fontSize: 14.0)),
                Expanded(
                  child: Text(
                    content,
                    style: const TextStyle(fontSize: 14.0),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Regular text
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              line,
              style: const TextStyle(fontSize: 14.0),
            ),
          );
        }
      }).toList(),
    );
  }

  void _handleNodeTap(TreeNode node) {
    setState(() {
      _selectedNode = node;
    });
  }
}
