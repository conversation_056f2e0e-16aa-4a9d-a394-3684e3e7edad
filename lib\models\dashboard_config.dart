import 'package:flutter/material.dart';

enum DashboardWidgetType {
  metric,
  chart,
  list,
  healthIndicator,
  customWidget
}

class DashboardConfig {
  final List<DashboardSection> sections;

  DashboardConfig({required this.sections});

  factory DashboardConfig.fromJson(Map<String, dynamic> json) {
    return DashboardConfig(
      sections: (json['sections'] as List)
          .map((section) => DashboardSection.fromJson(section))
          .toList(),
    );
  }
}

class DashboardSection {
  final String title;
  final DashboardWidgetType type;
  final Map<String, dynamic> data;
  final Color? color;
  final int? flex;

  DashboardSection({
    required this.title,
    required this.type,
    required this.data,
    this.color,
    this.flex,
  });

  factory DashboardSection.fromJson(Map<String, dynamic> json) {
    return DashboardSection(
      title: json['title'],
      type: DashboardWidgetType.values.firstWhere(
        (e) => e.toString() == 'DashboardWidgetType.${json['type']}',
      ),
      data: json['data'],
      color: json['color'] != null ? Color(json['color']) : null,
      flex: json['flex'],
    );
  }
}