import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';
import 'base_provider.dart';

class ThemeProvider extends BaseProvider {
  ThemeMode _themeMode = ThemeMode.light;

  ThemeMode get themeMode => _themeMode;

  ThemeProvider() {
    _loadTheme();
  }

  void setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    notifyListeners();

    // Save to preferences
    await runWithLoadingAndErrorHandling<void>(
      () async {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(AppConstants.themeKey, mode.index);
        Logger.info('Theme mode saved: ${mode.toString()}');
      },
      context: 'ThemeProvider.setThemeMode',
      showLoading: false,
    );
  }

  Future<void> _loadTheme() async {
    await runWithLoadingAndErrorHandling<void>(
      () async {
        final prefs = await SharedPreferences.getInstance();
        final index = prefs.getInt(AppConstants.themeKey) ?? 1;
        _themeMode = ThemeMode.values[index];
        Logger.info('Theme mode loaded: ${_themeMode.toString()}');
      },
      context: 'ThemeProvider._loadTheme',
      showLoading: false,
    );
    notifyListeners();
  }
}
