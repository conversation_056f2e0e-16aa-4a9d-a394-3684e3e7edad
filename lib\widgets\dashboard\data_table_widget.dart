import 'package:flutter/material.dart';

/// Column definition for the data table
class DataTableColumn {
  final String dataField;
  final String title;
  final bool sortable;
  final bool filterable;
  final String? format;
  final String? width;
  final List<Map<String, dynamic>>? conditionalFormatting;

  DataTableColumn({
    required this.dataField,
    required this.title,
    required this.sortable,
    required this.filterable,
    this.format,
    this.width,
    this.conditionalFormatting,
  });

  factory DataTableColumn.fromJson(Map<String, dynamic> json) {
    return DataTableColumn(
      dataField: json['data_field'],
      title: json['title'],
      sortable: json['sortable'] ?? false,
      filterable: json['filterable'] ?? false,
      format: json['format'],
      width: json['width'],
      conditionalFormatting: json['conditional_formatting'] != null
          ? (json['conditional_formatting'] as List)
              .map((formatting) => formatting as Map<String, dynamic>)
              .toList()
          : null,
    );
  }
}

/// A widget for displaying tabular data
class DataTableWidget extends StatefulWidget {
  final String title;
  final List<dynamic> data;
  final List<DataTableColumn> columns;
  final bool pagination;
  final int rowsPerPage;

  const DataTableWidget({
    super.key,
    required this.title,
    required this.data,
    required this.columns,
    required this.pagination,
    required this.rowsPerPage,
  });

  @override
  _DataTableWidgetState createState() => _DataTableWidgetState();
}

class _DataTableWidgetState extends State<DataTableWidget> {
  int _currentPage = 0;
  String? _sortColumn;
  bool _sortAscending = true;
  List<dynamic> _filteredData = [];

  @override
  void initState() {
    super.initState();
    _filteredData = List.from(widget.data);
    
    // Apply initial sorting if specified
    _sortData();
  }

  void _sortData() {
    if (_sortColumn != null) {
      setState(() {
        _filteredData.sort((a, b) {
          final aValue = a[_sortColumn];
          final bValue = b[_sortColumn];
          
          if (aValue == null && bValue == null) {
            return 0;
          } else if (aValue == null) {
            return _sortAscending ? -1 : 1;
          } else if (bValue == null) {
            return _sortAscending ? 1 : -1;
          }
          
          int comparison;
          if (aValue is num && bValue is num) {
            comparison = aValue.compareTo(bValue);
          } else {
            comparison = aValue.toString().compareTo(bValue.toString());
          }
          
          return _sortAscending ? comparison : -comparison;
        });
      });
    }
  }

  void _onSort(String columnName) {
    setState(() {
      if (_sortColumn == columnName) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = columnName;
        _sortAscending = true;
      }
      
      _sortData();
    });
  }

  List<dynamic> _getPaginatedData() {
    if (!widget.pagination) {
      return _filteredData;
    }
    
    final startIndex = _currentPage * widget.rowsPerPage;
    final endIndex = startIndex + widget.rowsPerPage;
    
    if (startIndex >= _filteredData.length) {
      return [];
    }
    
    return _filteredData.sublist(
      startIndex,
      endIndex > _filteredData.length ? _filteredData.length : endIndex,
    );
  }

  @override
  Widget build(BuildContext context) {
    final paginatedData = _getPaginatedData();
    
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16.0),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                child: DataTable(
                  columns: widget.columns.map((column) {
                    return DataColumn(
                      label: Text(column.title),
                      tooltip: column.title,
                      onSort: column.sortable
                          ? (_, __) => _onSort(column.dataField)
                          : null,
                    );
                  }).toList(),
                  rows: paginatedData.map((row) {
                    return DataRow(
                      cells: widget.columns.map((column) {
                        final value = row[column.dataField];
                        final formattedValue = _formatValue(value, column.format);
                        
                        // Apply conditional formatting if defined
                        TextStyle? textStyle;
                        if (column.conditionalFormatting != null) {
                          for (final formatting in column.conditionalFormatting!) {
                            final condition = formatting['condition'] as String;
                            if (_evaluateCondition(condition, value)) {
                              final style = formatting['style'] as Map<String, dynamic>;
                              textStyle = TextStyle(
                                color: _parseColor(style['color']),
                                fontWeight: style['font_weight'] == 'bold'
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              );
                              break;
                            }
                          }
                        }
                        
                        return DataCell(
                          Text(
                            formattedValue,
                            style: textStyle,
                          ),
                        );
                      }).toList(),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
          if (widget.pagination)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Showing ${_currentPage * widget.rowsPerPage + 1} to ${(_currentPage + 1) * widget.rowsPerPage > _filteredData.length ? _filteredData.length : (_currentPage + 1) * widget.rowsPerPage} of ${_filteredData.length}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.chevron_left),
                        onPressed: _currentPage > 0
                            ? () {
                                setState(() {
                                  _currentPage--;
                                });
                              }
                            : null,
                      ),
                      Text(
                        '${_currentPage + 1}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      IconButton(
                        icon: const Icon(Icons.chevron_right),
                        onPressed: (_currentPage + 1) * widget.rowsPerPage < _filteredData.length
                            ? () {
                                setState(() {
                                  _currentPage++;
                                });
                              }
                            : null,
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  String _formatValue(dynamic value, String? format) {
    if (value == null) return '';

    switch (format) {
      case 'currency':
        if (value is num) {
          return '\$${value.toStringAsFixed(2)}';
        }
        break;
      case 'number':
        if (value is num) {
          return value.toString();
        }
        break;
      case 'percentage':
        if (value is num) {
          return '${value.toStringAsFixed(1)}%';
        }
        break;
    }

    return value.toString();
  }

  bool _evaluateCondition(String condition, dynamic value) {
    if (value == null) return false;

    // Simple condition parser
    // Example: "value > 1000000"
    final parts = condition.split(' ');
    if (parts.length != 3) return false;

    final operator = parts[1];
    final threshold = num.tryParse(parts[2]);
    if (threshold == null) return false;

    if (value is! num) return false;

    switch (operator) {
      case '>':
        return value > threshold;
      case '<':
        return value < threshold;
      case '>=':
        return value >= threshold;
      case '<=':
        return value <= threshold;
      case '==':
        return value == threshold;
      case '!=':
        return value != threshold;
      default:
        return false;
    }
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null) return null;

    switch (colorString) {
      case 'green':
        return Colors.green;
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      default:
        return null;
    }
  }
}
