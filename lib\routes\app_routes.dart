import 'package:flutter/material.dart';
import 'package:nsl/widgets/responsive_home_new_builder.dart';
import 'package:nsl/widgets/responsive_widget_binder.dart';
import '../screens/workflow/screens/full_node_view_screen.dart';
import '../widgets/responsive_login_builder.dart';
import '../widgets/responsive_register_builder.dart';
import '../screens/auth/profile_screen.dart';
import '../screens/workflow_detail_screen_fixed.dart';
import '../widgets/responsive_build_builder.dart';
import '../widgets/responsive_build_new_builder.dart';
import '../widgets/responsive_chat_builder.dart';
import '../widgets/responsive_components_builder.dart';
import '../widgets/responsive_my_transactions_builder.dart';
import '../models/global_objective.dart';
import '../utils/constants.dart';
import '../widgets/responsive_settings_builder.dart';
import '../widgets/responsive_transact_builder.dart';

/// A class that defines all the routes in the application
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Route names
  static const String login = AppConstants.loginRoute;
  static const String register = AppConstants.registerRoute;
  static const String home = AppConstants.homeRoute;
  static const String chat = AppConstants.chatRoute;
  static const String build = AppConstants.buildRoute;
  static const String buildNew = AppConstants.buildNewRoute;
  static const String setting = AppConstants.settingsRoute;
  static const String profile = AppConstants.profileRoute;
  static const String transact = AppConstants.transactionRoute;
  static const String myTransactions = AppConstants.myTransactionsRoute;
  static const String workflow = AppConstants.workflowRoute;
  static const String workflowView = AppConstants.workflowView;

  static const String components = AppConstants.componentsRoute;
  static const String widgetBinder = AppConstants.widgetBinderRoute;

  /// Get all routes for the applicationh
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      home: (context) => ResponsiveHomeNewBuilder(),
      // home: (context) => WebHomeScreen(),
      login: (context) => const ResponsiveLoginBuilder(),
      register: (context) => const ResponsiveRegisterBuilder(),
      chat: (context) => const ResponsiveChatBuilder(),
      build: (context) => const ResponsiveBuildBuilder(),
      buildNew: (context) => const ResponsiveBuildNewBuilder(),
      setting: (context) => const ResponsiveSettingsBuilder(),
      profile: (context) => const ProfileScreen(),
      transact: (context) => const ResponsiveTransactBuilder(),
      myTransactions: (context) => const ResponsiveMyTransactionsBuilder(),
      workflow: (context) =>
          WorkflowDetailScreen(objective: GlobalObjective.empty()),
      workflowView: (context) => const FullNodeViewScreen(),
      components: (context) => const ResponsiveComponentsBuilder(),
      widgetBinder: (context) => const ResponsiveWidgetBinderBuilder(),
    };
  }

  /// Generate a route for the given settings
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    // Extract route arguments if any
    final args = settings.arguments;

    switch (settings.name) {
      case login:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveLoginBuilder());
      case register:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveRegisterBuilder());
      case chat:
        return MaterialPageRoute(builder: (_) => const ResponsiveChatBuilder());
      case build:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveBuildBuilder());
      case buildNew:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveBuildNewBuilder());
      case setting:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveSettingsBuilder());
      case profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      case transact:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveTransactBuilder());
      case myTransactions:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveMyTransactionsBuilder());
      case components:
        return MaterialPageRoute(
            builder: (_) => const ResponsiveComponentsBuilder());
      case workflow:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => WorkflowDetailScreen(
              objective: GlobalObjective(
                objectiveId: args['objectiveId'] ?? '',
                name: args['objectiveTitle'] ?? '',
                tenantId: 't001',
                version: '1.0',
                status: 'active',
              ),
            ),
          );
        }
        return MaterialPageRoute(
            builder: (_) =>
                WorkflowDetailScreen(objective: GlobalObjective.empty()));
      default:
        // If the route is not found, show a 404 page
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: const Center(
              child: Text('The requested page was not found.'),
            ),
          ),
        );
    }
  }
}
