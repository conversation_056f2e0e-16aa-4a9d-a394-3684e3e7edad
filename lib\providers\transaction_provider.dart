import 'package:flutter/material.dart';
import '../models/transaction.dart';
import '../models/transaction_detail.dart';
import '../models/global_objective.dart';
import '../models/transaction_status.dart';
import '../services/transaction_service.dart';
import '../utils/logger.dart';

class TransactionProvider extends ChangeNotifier {
  final List<Transaction> _transactions = [];
  final List<GlobalObjective> _globalObjectives = [];
  final List<TransactionDetail> _transactionDetails = [];
  final TransactionService _transactionService = TransactionService();
  bool _isLoading = false;
  bool _isLoadingObjectives = false;
  bool _isLoadingTransactionDetails = false;
  String? _error;
  Map<String, dynamic>? _lastExecutionResult;
  Transaction? _selectedTransaction;
  GlobalObjective? _selectedGlobalObjective;

  List<Transaction> get transactions => _transactions;
  List<GlobalObjective> get globalObjectives => _globalObjectives;
  List<TransactionDetail> get transactionDetails => _transactionDetails;
  bool get isLoading => _isLoading;
  bool get isLoadingObjectives => _isLoadingObjectives;
  bool get isLoadingTransactionDetails => _isLoadingTransactionDetails;
  String? get error => _error;
  Map<String, dynamic>? get lastExecutionResult => _lastExecutionResult;
  Transaction? get selectedTransaction => _selectedTransaction;
  GlobalObjective? get selectedGlobalObjective => _selectedGlobalObjective;

  TransactionProvider() {
    fetchGlobalObjectives();
    // fetchTransactions();
    // Don't fetch transaction details by default, only when requested
  }

  Future<void> fetchGlobalObjectives({String tenantId = 't001'}) async {
    _isLoadingObjectives = true;
    notifyListeners();

    try {
      final objectives =
          await _transactionService.getGlobalObjectives(tenantId: tenantId);
      _globalObjectives.clear();
      _globalObjectives.addAll(objectives);
    } catch (e) {
      Logger.error('Error in fetchGlobalObjectives: $e');
      // We don't set _error here to avoid affecting the main error state
    } finally {
      _isLoadingObjectives = false;
      notifyListeners();
    }
  }

  Future<void> fetchTransactions() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final transactions = await _transactionService.getTransactions();
      _transactions.clear();
      _transactions.addAll(transactions);
    } catch (e) {
      Logger.error('Error in fetchTransactions: $e');
      _error = 'Failed to load transactions: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearExecutionResult() {
    _lastExecutionResult = null;
    notifyListeners();
  }

  // Select a transaction for viewing details
  void selectTransaction(Transaction transaction) {
    _selectedTransaction = transaction;
    notifyListeners();
  }

  // Clear selected transaction
  void clearSelectedTransaction() {
    _selectedTransaction = null;
    notifyListeners();
  }

  // Select a global objective for viewing details
  void selectGlobalObjective(GlobalObjective objective) {
    _selectedGlobalObjective = objective;
    // Clear any selected transaction to avoid confusion
    _selectedTransaction = null;
    notifyListeners();
  }

  // Clear selected global objective
  void clearSelectedGlobalObjective() {
    _selectedGlobalObjective = null;
    notifyListeners();
  }

  // Clear all selections (transaction and global objective)
  void clearAllSelections() {
    _selectedTransaction = null;
    _selectedGlobalObjective = null;
    notifyListeners();
  }

  // Add a new transaction (for demo purposes)
  void addTransaction(Transaction transaction) {
    _transactions.add(transaction);
    notifyListeners();
  }

  // Update transaction status
  void updateTransactionStatus(String id, TransactionStatus newStatus) {
    final index = _transactions.indexWhere((t) => t.id == id);
    if (index != -1) {
      final transaction = _transactions[index];
      final updatedTransaction = transaction.copyWith(status: newStatus);

      _transactions[index] = updatedTransaction;

      // Update selected transaction if it's the same one
      if (_selectedTransaction?.id == id) {
        _selectedTransaction = updatedTransaction;
      }

      notifyListeners();
    }
  }

  // Load transactions (for web UI)
  Future<void> loadTransactions() async {
    return fetchTransactions();
  }

  // Check if a global objective is available in the user's transactions
  Future<List<dynamic>> validateGoAvailableInMyTransaction(String goId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final transactions =
          await _transactionService.validateGoAvailableInMyTransaction(goId);
      Logger.info('GO availability check result: $transactions');
      return transactions;
    } catch (e) {
      Logger.error('Error checking GO availability: $e');
      return [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch transaction details
  Future<void> fetchTransactionDetails() async {
    _isLoadingTransactionDetails = true;
    _error = null;
    //notifyListeners();

    try {
      final details = await _transactionService.getTransactionDetails();

      _transactionDetails.clear();
      _transactionDetails.addAll(details);

      Logger.info('Fetched ${details.length} transaction details');
    } catch (e) {
      _error = e.toString();
      Logger.error('Error fetching transaction details: $e');
    } finally {
      _isLoadingTransactionDetails = false;
      notifyListeners();
    }
  }
}
