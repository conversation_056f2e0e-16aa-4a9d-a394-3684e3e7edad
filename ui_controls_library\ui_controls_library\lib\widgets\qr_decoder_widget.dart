import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension QrDecoderColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

class QrDecoderWidget extends StatefulWidget {
  /// Whether to show a gallery button
  final bool showGalleryButton;

  /// Whether to show a URL input field
  final bool showUrlInput;

  /// Whether to show a preview of the selected image
  final bool showPreview;

  /// Whether to show a result dialog after decoding
  final bool showResultDialog;

  /// Whether to show a copy button in the result
  final bool showCopyButton;

  /// Whether to show an open button in the result
  final bool showOpenButton;

  /// Whether to show a clear button
  final bool showClearButton;

  /// Whether to show a help button
  final bool showHelpButton;

  /// The color of the gallery button
  final Color galleryButtonColor;

  /// The color of the URL button
  final Color urlButtonColor;

  /// The color of the clear button
  final Color clearButtonColor;

  /// The color of the help button
  final Color helpButtonColor;

  /// The color of the result text
  final Color resultTextColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The border color of the image preview
  final Color previewBorderColor;

  /// The border width of the image preview
  final double previewBorderWidth;

  /// The border radius of the image preview
  final double previewBorderRadius;

  /// The width of the image preview
  final double previewWidth;

  /// The height of the image preview
  final double previewHeight;

  /// The text to display on the gallery button
  final String galleryButtonText;

  /// The text to display on the URL button
  final String urlButtonText;

  /// The text to display on the clear button
  final String clearButtonText;

  /// The text to display on the help button
  final String helpButtonText;

  /// The text to display on the copy button
  final String copyButtonText;

  /// The text to display on the open button
  final String openButtonText;

  /// The text to display on the close button
  final String closeButtonText;

  /// The text to display when no image is selected
  final String noImageText;

  /// The text to display when no QR code is found
  final String noQrCodeText;

  /// The text to display when decoding is in progress
  final String decodingText;

  /// The text to display as a hint for the URL input
  final String urlInputHint;

  /// The text to display as the title of the result dialog
  final String resultDialogTitle;

  /// The text to display as the title of the help dialog
  final String helpDialogTitle;

  /// The text to display in the help dialog
  final String helpText;

  /// The text to display on the close button of the help dialog
  final String helpCloseButtonText;

  /// The icon to display on the gallery button
  final IconData galleryButtonIcon;

  /// The icon to display on the URL button
  final IconData urlButtonIcon;

  /// The icon to display on the clear button
  final IconData clearButtonIcon;

  /// The icon to display on the help button
  final IconData helpButtonIcon;

  /// The icon to display on the copy button
  final IconData copyButtonIcon;

  /// The icon to display on the open button
  final IconData openButtonIcon;

  /// Callback when a QR code is decoded
  final Function(String)? onDecode;

  /// Callback when an error occurs
  final Function(String)? onError;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // QR-specific JSON configuration
  /// Whether to use JSON QR configuration
  final bool useJsonQrConfig;

  /// QR-specific JSON configuration
  final Map<String, dynamic>? qrConfig;

  const QrDecoderWidget({
    super.key,
    this.showGalleryButton = true,
    this.showUrlInput = false,
    this.showPreview = true,
    this.showResultDialog = false,
    this.showCopyButton = true,
    this.showOpenButton = true,
    this.showClearButton = true,
    this.showHelpButton = false,
    this.galleryButtonColor = const Color(0xFF0058FF),
    this.urlButtonColor = Colors.green,
    this.clearButtonColor = Colors.red,
    this.helpButtonColor = Colors.purple,
    this.resultTextColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.previewBorderColor = Colors.grey,
    this.previewBorderWidth = 1.0,
    this.previewBorderRadius = 8.0,
    this.previewWidth = 300.0,
    this.previewHeight = 300.0,
    this.galleryButtonText = 'Select from Gallery',
    this.urlButtonText = 'Decode from URL',
    this.clearButtonText = 'Clear',
    this.helpButtonText = 'Help',
    this.copyButtonText = 'Copy',
    this.openButtonText = 'Open',
    this.closeButtonText = 'Close',
    this.noImageText = 'No image selected',
    this.noQrCodeText = 'No QR code found in the image',
    this.decodingText = 'Decoding QR code...',
    this.urlInputHint = 'Enter image URL',
    this.resultDialogTitle = 'QR Code Result',
    this.helpDialogTitle = 'Help',
    this.helpText = 'Select an image containing a QR code to decode it.',
    this.helpCloseButtonText = 'Got it',
    this.galleryButtonIcon = Icons.photo_library,
    this.urlButtonIcon = Icons.link,
    this.clearButtonIcon = Icons.clear,
    this.helpButtonIcon = Icons.help,
    this.copyButtonIcon = Icons.copy,
    this.openButtonIcon = Icons.open_in_new,
    this.onDecode,
    this.onError,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // QR-specific JSON configuration
    this.useJsonQrConfig = false,
    this.qrConfig,
  });

  /// Creates a QrDecoderWidget from a JSON map
  ///
  /// This factory constructor allows for creating a QrDecoderWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory QrDecoderWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color galleryButtonColor = Color(0xFF0058FF);
    if (json.containsKey('galleryButtonColor')) {
      galleryButtonColor = _parseColor(json['galleryButtonColor']);
    }

    Color urlButtonColor = Colors.green;
    if (json.containsKey('urlButtonColor')) {
      urlButtonColor = _parseColor(json['urlButtonColor']);
    }

    Color clearButtonColor = Colors.red;
    if (json.containsKey('clearButtonColor')) {
      clearButtonColor = _parseColor(json['clearButtonColor']);
    }

    Color helpButtonColor = Colors.purple;
    if (json.containsKey('helpButtonColor')) {
      helpButtonColor = _parseColor(json['helpButtonColor']);
    }

    Color resultTextColor = Colors.black;
    if (json.containsKey('resultTextColor')) {
      resultTextColor = _parseColor(json['resultTextColor']);
    }

    Color backgroundColor = Colors.white;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color previewBorderColor = Colors.grey;
    if (json.containsKey('previewBorderColor')) {
      previewBorderColor = _parseColor(json['previewBorderColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse icons
    IconData galleryButtonIcon = Icons.photo_library;
    if (json.containsKey('galleryButtonIcon')) {
      galleryButtonIcon =
          _parseIconData(json['galleryButtonIcon']) ?? Icons.photo_library;
    }

    IconData urlButtonIcon = Icons.link;
    if (json.containsKey('urlButtonIcon')) {
      urlButtonIcon = _parseIconData(json['urlButtonIcon']) ?? Icons.link;
    }

    IconData clearButtonIcon = Icons.clear;
    if (json.containsKey('clearButtonIcon')) {
      clearButtonIcon = _parseIconData(json['clearButtonIcon']) ?? Icons.clear;
    }

    IconData helpButtonIcon = Icons.help;
    if (json.containsKey('helpButtonIcon')) {
      helpButtonIcon = _parseIconData(json['helpButtonIcon']) ?? Icons.help;
    }

    IconData copyButtonIcon = Icons.copy;
    if (json.containsKey('copyButtonIcon')) {
      copyButtonIcon = _parseIconData(json['copyButtonIcon']) ?? Icons.copy;
    }

    IconData openButtonIcon = Icons.open_in_new;
    if (json.containsKey('openButtonIcon')) {
      openButtonIcon =
          _parseIconData(json['openButtonIcon']) ?? Icons.open_in_new;
    }

    return QrDecoderWidget(
      // Display options
      showGalleryButton: json['showGalleryButton'] as bool? ?? true,
      showUrlInput: json['showUrlInput'] as bool? ?? false,
      showPreview: json['showPreview'] as bool? ?? true,
      showResultDialog: json['showResultDialog'] as bool? ?? false,
      showCopyButton: json['showCopyButton'] as bool? ?? true,
      showOpenButton: json['showOpenButton'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? true,
      showHelpButton: json['showHelpButton'] as bool? ?? false,

      // Colors
      galleryButtonColor: galleryButtonColor,
      urlButtonColor: urlButtonColor,
      clearButtonColor: clearButtonColor,
      helpButtonColor: helpButtonColor,
      resultTextColor: resultTextColor,
      backgroundColor: backgroundColor,
      previewBorderColor: previewBorderColor,

      // Dimensions
      previewBorderWidth:
          json['previewBorderWidth'] != null
              ? (json['previewBorderWidth'] as num).toDouble()
              : 1.0,
      previewBorderRadius:
          json['previewBorderRadius'] != null
              ? (json['previewBorderRadius'] as num).toDouble()
              : 8.0,
      previewWidth:
          json['previewWidth'] != null
              ? (json['previewWidth'] as num).toDouble()
              : 300.0,
      previewHeight:
          json['previewHeight'] != null
              ? (json['previewHeight'] as num).toDouble()
              : 300.0,

      // Button texts
      galleryButtonText:
          json['galleryButtonText'] as String? ?? 'Select from Gallery',
      urlButtonText: json['urlButtonText'] as String? ?? 'Decode from URL',
      clearButtonText: json['clearButtonText'] as String? ?? 'Clear',
      helpButtonText: json['helpButtonText'] as String? ?? 'Help',
      copyButtonText: json['copyButtonText'] as String? ?? 'Copy',
      openButtonText: json['openButtonText'] as String? ?? 'Open',
      closeButtonText: json['closeButtonText'] as String? ?? 'Close',

      // Status texts
      noImageText: json['noImageText'] as String? ?? 'No image selected',
      noQrCodeText:
          json['noQrCodeText'] as String? ?? 'No QR code found in the image',
      decodingText: json['decodingText'] as String? ?? 'Decoding QR code...',
      urlInputHint: json['urlInputHint'] as String? ?? 'Enter image URL',
      resultDialogTitle:
          json['resultDialogTitle'] as String? ?? 'QR Code Result',
      helpDialogTitle: json['helpDialogTitle'] as String? ?? 'Help',
      helpText:
          json['helpText'] as String? ??
          'Select an image containing a QR code to decode it.',
      helpCloseButtonText: json['helpCloseButtonText'] as String? ?? 'Got it',

      // Icons
      galleryButtonIcon: galleryButtonIcon,
      urlButtonIcon: urlButtonIcon,
      clearButtonIcon: clearButtonIcon,
      helpButtonIcon: helpButtonIcon,
      copyButtonIcon: copyButtonIcon,
      openButtonIcon: openButtonIcon,

      // Advanced interaction properties
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonQrConfig: json['useJsonQrConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return Color(0xFF0058FF);
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.blue; // Default color
  }

  /// Parses icon data from a string
  static IconData? _parseIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'photo_library':
        return Icons.photo_library;
      case 'link':
        return Icons.link;
      case 'clear':
        return Icons.clear;
      case 'help':
        return Icons.help;
      case 'copy':
        return Icons.copy;
      case 'open_in_new':
        return Icons.open_in_new;
      case 'qr_code':
        return Icons.qr_code;
      case 'qr_code_scanner':
        return Icons.qr_code_scanner;
      case 'image':
        return Icons.image;
      case 'camera':
        return Icons.camera;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'photo_camera':
        return Icons.photo_camera;
      case 'photo':
        return Icons.photo;
      case 'add_photo_alternate':
        return Icons.add_photo_alternate;
      case 'close':
        return Icons.close;
      case 'delete':
        return Icons.delete;
      case 'refresh':
        return Icons.refresh;
      case 'save':
        return Icons.save;
      case 'share':
        return Icons.share;
      case 'info':
        return Icons.info;
      case 'info_outline':
        return Icons.info_outline;
      case 'settings':
        return Icons.settings;
      case 'search':
        return Icons.search;
      default:
        return null;
    }
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Display options
      'showGalleryButton': showGalleryButton,
      'showUrlInput': showUrlInput,
      'showPreview': showPreview,
      'showResultDialog': showResultDialog,
      'showCopyButton': showCopyButton,
      'showOpenButton': showOpenButton,
      'showClearButton': showClearButton,
      'showHelpButton': showHelpButton,

      // Colors
      'galleryButtonColor': '#${galleryButtonColor.toHexString()}',
      'urlButtonColor': '#${urlButtonColor.toHexString()}',
      'clearButtonColor': '#${clearButtonColor.toHexString()}',
      'helpButtonColor': '#${helpButtonColor.toHexString()}',
      'resultTextColor': '#${resultTextColor.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'previewBorderColor': '#${previewBorderColor.toHexString()}',

      // Dimensions
      'previewBorderWidth': previewBorderWidth,
      'previewBorderRadius': previewBorderRadius,
      'previewWidth': previewWidth,
      'previewHeight': previewHeight,

      // Button texts
      'galleryButtonText': galleryButtonText,
      'urlButtonText': urlButtonText,
      'clearButtonText': clearButtonText,
      'helpButtonText': helpButtonText,
      'copyButtonText': copyButtonText,
      'openButtonText': openButtonText,
      'closeButtonText': closeButtonText,

      // Status texts
      'noImageText': noImageText,
      'noQrCodeText': noQrCodeText,
      'decodingText': decodingText,
      'urlInputHint': urlInputHint,
      'resultDialogTitle': resultDialogTitle,
      'helpDialogTitle': helpDialogTitle,
      'helpText': helpText,
      'helpCloseButtonText': helpCloseButtonText,

      // Icons
      'galleryButtonIcon': _iconDataToString(galleryButtonIcon),
      'urlButtonIcon': _iconDataToString(urlButtonIcon),
      'clearButtonIcon': _iconDataToString(clearButtonIcon),
      'helpButtonIcon': _iconDataToString(helpButtonIcon),
      'copyButtonIcon': _iconDataToString(copyButtonIcon),
      'openButtonIcon': _iconDataToString(openButtonIcon),

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonQrConfig': useJsonQrConfig,
    };
  }

  /// Converts IconData to a string representation
  static String _iconDataToString(IconData icon) {
    if (icon == Icons.photo_library) return 'photo_library';
    if (icon == Icons.link) return 'link';
    if (icon == Icons.clear) return 'clear';
    if (icon == Icons.help) return 'help';
    if (icon == Icons.copy) return 'copy';
    if (icon == Icons.open_in_new) return 'open_in_new';
    if (icon == Icons.qr_code) return 'qr_code';
    if (icon == Icons.qr_code_scanner) return 'qr_code_scanner';
    if (icon == Icons.image) return 'image';
    if (icon == Icons.camera) return 'camera';
    if (icon == Icons.camera_alt) return 'camera_alt';
    if (icon == Icons.photo_camera) return 'photo_camera';
    if (icon == Icons.photo) return 'photo';
    if (icon == Icons.add_photo_alternate) return 'add_photo_alternate';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.delete) return 'delete';
    if (icon == Icons.refresh) return 'refresh';
    if (icon == Icons.save) return 'save';
    if (icon == Icons.share) return 'share';
    if (icon == Icons.info) return 'info';
    if (icon == Icons.info_outline) return 'info_outline';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.search) return 'search';
    return 'unknown';
  }

  @override
  State<QrDecoderWidget> createState() => _QrDecoderWidgetState();
}

class _QrDecoderWidgetState extends State<QrDecoderWidget> {
  final ImagePicker _picker = ImagePicker();
  XFile? _imageFile;
  String? _decodedData;
  bool _isDecoding = false;
  final TextEditingController _urlController = TextEditingController();
  final MobileScannerController _scannerController = MobileScannerController();

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(QrDecoderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _urlController.dispose();
    _scannerController.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
      );
      if (pickedFile != null) {
        setState(() {
          _imageFile = pickedFile;
          _decodedData = null;
          _isDecoding = true;
        });
        await _decodeQR();
      }
    } catch (e) {
      _handleError('Error picking image: $e');
    }
  }

  Future<void> _decodeFromUrl() async {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      _handleError('Please enter a valid URL');
      return;
    }

    setState(() {
      _imageFile = null;
      _decodedData = null;
      _isDecoding = true;
    });

    try {
      // This is a placeholder for actual URL image fetching and decoding
      // In a real implementation, you would download the image and then decode it
      _handleError('Decoding from URL is not implemented in this demo');
      setState(() {
        _isDecoding = false;
      });
    } catch (e) {
      _handleError('Error decoding from URL: $e');
    }
  }

  Future<void> _decodeQR() async {
    if (_imageFile == null) {
      setState(() {
        _isDecoding = false;
      });
      return;
    }

    try {
      if (kIsWeb) {
        // Web platform doesn't support image analysis yet
        _handleError('QR code decoding from images is not supported on web');
        setState(() {
          _isDecoding = false;
        });
        return;
      } else {
        try {
          // Set up a listener for barcode detection events
          _scannerController.barcodes.listen((barcodes) {
            if (barcodes.barcodes.isNotEmpty) {
              final String data = barcodes.barcodes.first.rawValue ?? '';
              setState(() {
                _decodedData = data;
                _isDecoding = false;
              });

              if (widget.onDecode != null) {
                widget.onDecode!(data);
              }

              if (widget.showResultDialog) {
                _showResultDialog(data);
              }
            }
          });

          // Use mobile_scanner's analyzeImage method
          final result = await _scannerController.analyzeImage(
            _imageFile!.path,
          );

          // If no barcode was found after a reasonable time
          if (result == null) {
            setState(() {
              _isDecoding = false;
              _decodedData = null;
            });
            _handleError(widget.noQrCodeText);
          }
        } catch (e) {
          _handleError('Error analyzing image: $e');
          setState(() {
            _isDecoding = false;
          });
          return;
        }
      }
    } catch (e) {
      setState(() {
        _isDecoding = false;
        _decodedData = null;
      });
      _handleError('Error decoding QR code: $e');
    }
  }

  void _clearImage() {
    setState(() {
      _imageFile = null;
      _decodedData = null;
      _isDecoding = false;
      _urlController.clear();
    });
  }

  void _handleError(String error) {
    if (widget.onError != null) {
      widget.onError!(error);
    }

    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(error)));
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(widget.helpDialogTitle),
            content: Text(widget.helpText),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(widget.helpCloseButtonText),
              ),
            ],
          ),
    );
  }

  void _showResultDialog(String result) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(widget.resultDialogTitle),
            content: SelectableText(result),
            actions: [
              if (widget.showCopyButton)
                TextButton.icon(
                  icon: Icon(widget.copyButtonIcon),
                  label: Text(widget.copyButtonText),
                  onPressed: () {
                    // Copy to clipboard functionality would go here
                    Navigator.pop(context);
                  },
                ),
              if (widget.showOpenButton)
                TextButton.icon(
                  icon: Icon(widget.openButtonIcon),
                  label: Text(widget.openButtonText),
                  onPressed: () {
                    // Open URL functionality would go here
                    Navigator.pop(context);
                  },
                ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(widget.closeButtonText),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget content = Container(
      color: widget.backgroundColor,
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (widget.showGalleryButton)
                ElevatedButton.icon(
                  icon: Icon(widget.galleryButtonIcon),
                  label: Text(widget.galleryButtonText),
                  onPressed: _pickImage,
                  style: ElevatedButton.styleFrom(
                    //backgroundColor: widget.galleryButtonColor,
                    backgroundColor: const Color(0xFF0058FF),
                    foregroundColor: const Color(0xFFFFFFFF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              const SizedBox(width: 8),
              if (widget.showClearButton)
                ElevatedButton.icon(
                  icon: Icon(widget.clearButtonIcon),
                  label: Text(widget.clearButtonText),
                  onPressed: _clearImage,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.clearButtonColor,
                  ),
                ),
              const SizedBox(width: 8),
              if (widget.showHelpButton)
                ElevatedButton.icon(
                  icon: Icon(widget.helpButtonIcon),
                  label: Text(widget.helpButtonText),
                  onPressed: _showHelpDialog,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.helpButtonColor,
                  ),
                ),
            ],
          ),

          // URL input
          if (widget.showUrlInput) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _urlController,
                    decoration: InputDecoration(
                      hintText: widget.urlInputHint,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  icon: Icon(widget.urlButtonIcon),
                  label: Text(widget.urlButtonText),
                  onPressed: _decodeFromUrl,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.urlButtonColor,
                  ),
                ),
              ],
            ),
          ],

          // Image preview
          if (widget.showPreview) ...[
            const SizedBox(height: 16),
            Container(
              width: widget.previewWidth,
              height: widget.previewHeight,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.previewBorderColor,
                  width: widget.previewBorderWidth,
                ),
                borderRadius: BorderRadius.circular(widget.previewBorderRadius),
              ),
              child:
                  _isDecoding
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const CircularProgressIndicator(),
                            const SizedBox(height: 16),
                            Text(widget.decodingText),
                          ],
                        ),
                      )
                      : _imageFile != null
                      ? ClipRRect(
                        borderRadius: BorderRadius.circular(
                          widget.previewBorderRadius -
                              widget.previewBorderWidth,
                        ),
                        child:
                            kIsWeb
                                ? Image.network(
                                  _imageFile!.path,
                                  fit: BoxFit.cover,
                                )
                                : Image.file(
                                  File(_imageFile!.path),
                                  fit: BoxFit.cover,
                                ),
                      )
                      : Center(child: Text(widget.noImageText)),
            ),
          ],

          // Result display
          if (_decodedData != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Decoded QR Code:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  SelectableText(
                    _decodedData!,
                    style: TextStyle(color: widget.resultTextColor),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );

    // Apply hover detection
    if (widget.onHover != null) {
      content = MouseRegion(
        onEnter: (event) {
          widget.onHover!(true);
        },
        onExit: (event) {
          widget.onHover!(false);
        },
        child: content,
      );
    }

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Apply advanced gesture detection
    if (widget.onDoubleTap != null || widget.onLongPress != null) {
      content = GestureDetector(
        onDoubleTap:
            widget.onDoubleTap != null
                ? () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                }
                : null,
        onLongPress:
            widget.onLongPress != null
                ? () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                }
                : null,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }
}
