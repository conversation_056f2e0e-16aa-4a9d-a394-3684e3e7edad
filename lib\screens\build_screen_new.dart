import 'package:flutter/material.dart';
import 'package:nsl/models/tree_node.dart';
import 'package:nsl/services/tree_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/widgets/navigation_drawer.dart';
import 'package:nsl/widgets/web_tree_view.dart';
import 'package:nsl/widgets/simple_divider.dart';

class BuildScreenNew extends StatefulWidget {
  const BuildScreenNew({super.key});

  @override
  State<BuildScreenNew> createState() => _BuildScreenNewState();
}

class _BuildScreenNewState extends State<BuildScreenNew> {
  late TreeStructure _treeStructure;
  bool _isLoading = true;
  String? _error;
  bool _isInitialized = false;
  TreeNode? _selectedNode;
  bool _showRightSidebar = false;

  // List of stacks from metadata
  List<dynamic> _stacks = [];

  @override
  void initState() {
    super.initState();
    // We'll load the data in didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _loadTreeData();
      _isInitialized = true;
    }
  }

  Future<void> _loadTreeData() async {
    try {
      // Use the TreeService to load the tree data
      final treeStructure =
          await TreeService.loadTreeData('assets/data/tree_structure.json');

      if (mounted) {
        setState(() {
          _treeStructure = treeStructure;

          // Extract stacks from metadata
          if (_treeStructure.metadata != null &&
              _treeStructure.metadata!.containsKey('stacks')) {
            _stacks = _treeStructure.metadata!['stacks'] as List<dynamic>;

            // Set initial selected node (first stack)
            if (_stacks.isNotEmpty) {
              final stack = _stacks.first;
              final stackId = stack['id'] as int;
              final virtualNode = TreeNode(
                id: 'node$stackId',
                type: 'stack',
                label: stack['name'],
                children: [],
              );
              _selectedNode = virtualNode;
            }
          }

          // Always show right sidebar on desktop
          if (MediaQuery.of(context).size.width > 900) {
            _showRightSidebar = true;
          }

          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error loading tree data: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: EdgeInsets.zero,
      child: Scaffold(
        drawer: const AppNavigationDrawer(currentRoute: 'build'),
        appBar: AppBar(
          title: const Text('Claims Processing'),
          actions: [
            // Button to show stacks in right sidebar or bottom sheet
            IconButton(
              icon: const Icon(Icons.layers_outlined),
              onPressed: () {
                // For mobile, show stacks in bottom sheet
                if (MediaQuery.of(context).size.width <= 900) {
                  _showStacksBottomSheet(context);
                } else {
                  // For desktop, always show sidebar with stacks
                  setState(() {
                    _showRightSidebar = true;
                  });
                }
              },
              tooltip: 'Show stacks',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _loadTreeData();
              },
              tooltip: 'Refresh tree data',
            ),
          ],
        ),
        body: _buildBody(),
      ),
    );
  }

  // Show stacks in bottom sheet (for mobile)
  void _showStacksBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Handle bar at top
                  Center(
                    child: Container(
                      width: 40,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Title
                  const Text(
                    'Available Stacks',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // List of stacks
                  Expanded(
                    child: ListView.builder(
                      controller: scrollController,
                      itemCount: _stacks.length,
                      itemBuilder: (context, index) {
                        final stack = _stacks[index];
                        final stackId = stack['id'] as int;
                        final stackName = stack['name'] as String;

                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: Theme.of(context)
                                .colorScheme
                                .primary
                                .withAlpha(25),
                            child: Text('$stackId'),
                          ),
                          title: Text(stackName),
                          onTap: () {
                            // Create a virtual node for this stack
                            final virtualNode = TreeNode(
                              id: 'node$stackId',
                              type: 'stack',
                              label: stackName,
                              children: [],
                            );

                            setState(() {
                              _selectedNode = virtualNode;
                            });

                            // Close the stacks bottom sheet
                            Navigator.pop(context);

                            // Show stack details in bottom sheet
                            _showStackDetailsBottomSheet(context, stack);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48.0,
              ),
              const SizedBox(height: 16.0),
              Text(
                'Error loading tree data:',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8.0),
              Text(_error!),
              const SizedBox(height: 16.0),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                  _loadTreeData();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    // For wider screens, show layout with optional right sidebar
    if (MediaQuery.of(context).size.width > 900) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main content - Tree view
            Expanded(
              flex: _showRightSidebar ? 3 : 5,
              child: _buildMainContent(),
            ),

            // Right sidebar - only show if toggled on
            if (_showRightSidebar) ...[
              // Separator between main content and right sidebar
              const SimpleDivider(
                thickness: 1,
                color: Colors.grey,
                isVertical: true,
              ),

              // Right sidebar - Stacks info
              Expanded(
                flex: 2,
                child: _buildRightSidebar(),
              ),
            ],
          ],
        ),
      );
    }

    // For narrower screens, show only the tree view (sidebar appears as bottom sheet)
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor.withAlpha(50),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.account_tree_outlined,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8.0),
              Text(
                'Workflow Tree',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16.0,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),

        // Main tree content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tree view - use WebTreeView for better visualization
                  WebTreeView(
                    treeStructure: _treeStructure,
                    onNodeTap: _handleNodeTap,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRightSidebar() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor.withAlpha(50),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Available Stacks',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                // Close button
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _showRightSidebar = false;
                    });
                  },
                  tooltip: 'Close sidebar',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),

          // List of stacks
          Expanded(
            child: ListView.builder(
              itemCount: _stacks.length,
              itemBuilder: (context, index) {
                final stack = _stacks[index];
                final stackId = stack['id'] as int;
                final stackName = stack['name'] as String;

                // Check if this stack is selected
                final isSelected = _selectedNode != null &&
                    _selectedNode!.id == 'node$stackId';

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.primary.withAlpha(50),
                    foregroundColor: isSelected
                        ? Colors.white
                        : Theme.of(context).colorScheme.onSurface,
                    child: Text('$stackId'),
                  ),
                  title: Text(
                    stackName,
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  selected: isSelected,
                  selectedTileColor:
                      Theme.of(context).colorScheme.primary.withAlpha(30),
                  onTap: () {
                    // Create a virtual node for this stack
                    final virtualNode = TreeNode(
                      id: 'node$stackId',
                      type: 'stack',
                      label: stackName,
                      children: [],
                    );

                    setState(() {
                      _selectedNode = virtualNode;
                    });

                    // Show stack details in bottom sheet
                    _showStackDetailsBottomSheet(context, stack);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Show stack details in bottom sheet
  void _showStackDetailsBottomSheet(BuildContext context, dynamic stack) {
    final stackName = stack['name'] as String;
    final stackDetails = stack['details'];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Handle bar at top
                  Center(
                    child: Container(
                      width: 40,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Title with back button (only for mobile)
                  MediaQuery.of(context).size.width <= 900
                      ? Row(
                          children: [
                            // Back button to return to stacks
                            IconButton(
                              icon: const Icon(Icons.arrow_back),
                              onPressed: () {
                                Navigator.pop(context);
                                _showStacksBottomSheet(context);
                              },
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                            const SizedBox(width: 8),

                            // Title
                            Expanded(
                              child: Text(
                                stackName,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        )
                      : Text(
                          stackName,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                  const SizedBox(height: 16),

                  // Content - handle different types of details
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      child: _buildStackDetailsContent(stackDetails),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Build content based on stack details type
  Widget _buildStackDetailsContent(dynamic details) {
    // If details is null or empty
    if (details == null) {
      return const Text(
        'No details available for this stack.',
        style: TextStyle(
          fontStyle: FontStyle.italic,
          color: Colors.grey,
        ),
      );
    }

    // If details is a string, show directly
    if (details is String) {
      return Text(
        details,
        style: const TextStyle(fontSize: 16),
      );
    }

    // If details is a map/object, use ExpansionTile
    if (details is Map<String, dynamic>) {
      final objectiveName = details.containsKey('objectiveName')
          ? details['objectiveName'] as String? ?? 'Objective'
          : 'Objective';

      final objectiveDetails = details.containsKey('objectiveDetails')
          ? details['objectiveDetails'] as String? ?? ''
          : '';

      return Card(
        elevation: 1,
        margin: const EdgeInsets.only(bottom: 8),
        child: ExpansionTile(
          title: Text(
            objectiveName,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          initiallyExpanded: true,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                objectiveDetails,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      );
    }

    // Fallback for other types
    return Text(
      details.toString(),
      style: const TextStyle(fontSize: 16),
    );
  }

  void _handleNodeTap(TreeNode node) {
    setState(() {
      _selectedNode = node;
    });

    // Show node details in bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Handle bar at top
                  Center(
                    child: Container(
                      width: 40,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Title with back button (only for mobile)
                  MediaQuery.of(context).size.width <= 900
                      ? Row(
                          children: [
                            // Back button to return to stacks
                            IconButton(
                              icon: const Icon(Icons.arrow_back),
                              onPressed: () {
                                Navigator.pop(context);
                                _showStacksBottomSheet(context);
                              },
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                            const SizedBox(width: 8),

                            // Title
                            Expanded(
                              child: Text(
                                node.label,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        )
                      : Text(
                          node.label,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                  const SizedBox(height: 16),

                  // Content
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      child: node.metadata != null
                          ? _buildStackDetailsContent(node.metadata)
                          : const Text(
                              'No additional details available for this node.',
                              style: TextStyle(
                                fontStyle: FontStyle.italic,
                                color: Colors.grey,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );

    // For desktop, ensure the sidebar with stacks is visible
    if (MediaQuery.of(context).size.width > 900 && !_showRightSidebar) {
      setState(() {
        _showRightSidebar = true;
      });
    }
  }
}
