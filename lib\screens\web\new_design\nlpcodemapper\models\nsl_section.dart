class NslSection {
  final String id;
  final String title;
  final String content;
  final String type; // e.g., "prescriptive", "sub-prescriptive", "bet", "pathway"
  final int order; // Order in which the section appears
  final String solutionId;
  final List<String> tags; // Tags associated with this section

  NslSection({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.order,
    required this.solutionId,
    this.tags = const [],
  });

  // JSON serialization methods
  factory NslSection.fromJson(Map<String, dynamic> json) {
    return NslSection(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      type: json['type'],
      order: json['order'],
      solutionId: json['solutionId'],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type,
      'order': order,
      'solutionId': solutionId,
      'tags': tags,
    };
  }
}
