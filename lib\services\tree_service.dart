import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/tree_node.dart';

/// Service class for handling tree data operations
class TreeService {
  /// Loads tree data from a JSON file in the assets
  static Future<TreeStructure> loadTreeData(String assetPath) async {
    try {
      // Load the JSON data from the assets file
      final jsonData = await rootBundle.loadString(assetPath);
      final Map<String, dynamic> treeData = json.decode(jsonData);
      
      return TreeStructure.fromJson(treeData);
    } catch (e) {
      throw Exception('Failed to load tree data: ${e.toString()}');
    }
  }
}
