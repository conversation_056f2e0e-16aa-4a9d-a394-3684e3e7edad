import 'package:flutter/material.dart';

/// A customizable tag widget that can be used to display labels, categories, or status indicators.
///
/// This widget provides a variety of customization options including colors, shapes, icons,
/// and interaction capabilities.
class TagWidget extends StatefulWidget {
  /// The text to display in the tag.
  final String text;

  /// The background color of the tag.
  final Color backgroundColor;

  /// The text color of the tag.
  final Color textColor;

  /// The border color of the tag. Defaults to blue.
  final Color? borderColor;

  /// The width of the border. Only applicable if borderColor is not null.
  final double borderWidth;

  /// The border radius of the tag.
  final double borderRadius;

  /// The icon to display before the text. If null, no icon is shown.
  final IconData? icon;

  /// The color of the icon. If null, uses textColor.
  final Color? iconColor;

  /// The size of the icon.
  final double iconSize;

  /// The spacing between the icon and the text.
  final double iconSpacing;

  /// The padding inside the tag.
  final EdgeInsetsGeometry padding;

  /// Whether the tag is dismissible (can be removed).
  final bool isDismissible;

  /// The icon to show for dismissing the tag. Only applicable if isDismissible is true.
  final IconData dismissIcon;

  /// The color of the dismiss icon. If null, uses textColor.
  final Color? dismissIconColor;

  /// The callback when the tag is tapped.
  final VoidCallback? onTap;

  /// The callback when the tag is dismissed. Only called if isDismissible is true.
  final VoidCallback? onDismiss;

  /// The elevation of the tag (shadow).
  final double elevation;

  /// The font size of the text.
  final double fontSize;

  /// The font weight of the text.
  final FontWeight fontWeight;

  /// Whether the tag is outlined (transparent background with border).
  final bool isOutlined;

  /// Whether the tag is filled (solid background).
  final bool isFilled;

  /// Whether the tag has a gradient background.
  final bool hasGradient;

  /// The gradient colors if hasGradient is true.
  final List<Color>? gradientColors;

  /// The gradient begin alignment.
  final Alignment gradientBegin;

  /// The gradient end alignment.
  final Alignment gradientEnd;

  // Additional text styling properties

  /// The text decoration (underline, line-through, etc.)
  final TextDecoration textDecoration;

  /// How to handle text overflow
  final TextOverflow textOverflow;

  /// Maximum number of lines for the text
  final int? maxLines;

  /// Alignment of the text within the tag
  final TextAlign textAlign;

  /// Spacing between letters in the text
  final double letterSpacing;

  /// Spacing between words in the text
  final double wordSpacing;

  /// Direction of the text (LTR, RTL)
  final TextDirection? textDirection;

  // Additional visual enhancement properties

  /// Color of the shadow
  final Color shadowColor;

  /// Opacity of the entire tag (0.0 to 1.0)
  final double opacity;

  /// Blur radius for the shadow
  final double blurRadius;

  /// Spread radius for the shadow
  final double spreadRadius;

  /// Whether the tag has a shadow
  final bool hasShadow;

  // Additional layout properties

  /// Fixed width for the tag (null for auto-sizing)
  final double? width;

  /// Fixed height for the tag (null for auto-sizing)
  final double? height;

  /// Margin around the tag
  final EdgeInsetsGeometry margin;

  /// Alignment within the parent
  final Alignment? alignment;

  /// Transform for rotation, scaling, etc.
  final Matrix4? transform;

  // Additional animation properties

  /// Duration for animations
  final Duration animationDuration;

  /// Curve for animations
  final Curve animationCurve;

  /// Type of animation (fade, scale, slide)
  final String animationType;

  // Additional interaction properties

  /// Whether long press is enabled
  final bool longPressEnabled;

  /// Callback for long press
  final VoidCallback? onLongPress;

  /// Whether double tap is enabled
  final bool doubleTapEnabled;

  /// Callback for double tap
  final VoidCallback? onDoubleTap;

  /// Color when hovered
  final Color? hoverColor;

  /// Color of the splash effect when tapped
  final Color? splashColor;

  /// Color of the highlight effect when tapped
  final Color? highlightColor;

  /// Color when focused
  final Color? focusColor;

  /// Creates a tag widget.
  const TagWidget({
    super.key,
    required this.text,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.blue,
    this.borderColor = Colors.blue,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.icon,
    this.iconColor,
    this.iconSize = 16.0,
    this.iconSpacing = 4.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.isDismissible = false,
    this.dismissIcon = Icons.close,
    this.dismissIconColor,
    this.onTap,
    this.onDismiss,
    this.elevation = 0.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.isOutlined = true,
    this.isFilled = false,
    this.hasGradient = false,
    this.gradientColors,
    this.gradientBegin = Alignment.centerLeft,
    this.gradientEnd = Alignment.centerRight,
    // Additional text styling properties
    this.textDecoration = TextDecoration.none,
    this.textOverflow = TextOverflow.ellipsis,
    this.maxLines,
    this.textAlign = TextAlign.center,
    this.letterSpacing = 0.0,
    this.wordSpacing = 0.0,
    this.textDirection,
    // Additional visual enhancement properties
    this.shadowColor = Colors.black,
    this.opacity = 1.0,
    this.blurRadius = 3.0,
    this.spreadRadius = 0.0,
    this.hasShadow = false,
    // Additional layout properties
    this.width,
    this.height,
    this.margin = EdgeInsets.zero,
    this.alignment,
    this.transform,
    // Additional animation properties
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.animationType = 'fade',
    // Additional interaction properties
    this.longPressEnabled = false,
    this.onLongPress,
    this.doubleTapEnabled = false,
    this.onDoubleTap,
    this.hoverColor,
    this.splashColor,
    this.highlightColor,
    this.focusColor,
  });

  /// Creates a TagWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the TagWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "text": "Featured",
  ///   "backgroundColor": "blue",
  ///   "textColor": "white",
  ///   "icon": "star",
  ///   "isOutlined": true
  /// }
  /// ```
  factory TagWidget.fromJson(Map<String, dynamic> json) {
    // Handle padding
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0);
    if (json['padding'] != null) {
      if (json['padding'] is double) {
        double paddingValue = json['padding'] as double;
        padding = EdgeInsets.all(paddingValue);
      } else if (json['padding'] is Map) {
        Map<String, dynamic> paddingMap = json['padding'] as Map<String, dynamic>;
        padding = EdgeInsets.fromLTRB(
          paddingMap['left'] as double? ?? 12.0,
          paddingMap['top'] as double? ?? 6.0,
          paddingMap['right'] as double? ?? 12.0,
          paddingMap['bottom'] as double? ?? 6.0,
        );
      }
    }

    // Handle font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      } else if (json['fontWeight'] is int) {
        final weight = json['fontWeight'] as int;
        switch (weight) {
          case 100: fontWeight = FontWeight.w100; break;
          case 200: fontWeight = FontWeight.w200; break;
          case 300: fontWeight = FontWeight.w300; break;
          case 400: fontWeight = FontWeight.w400; break;
          case 500: fontWeight = FontWeight.w500; break;
          case 600: fontWeight = FontWeight.w600; break;
          case 700: fontWeight = FontWeight.w700; break;
          case 800: fontWeight = FontWeight.w800; break;
          case 900: fontWeight = FontWeight.w900; break;
        }
      }
    }

    // Handle icon
    IconData? icon;
    if (json['icon'] != null) {
      icon = _getIconData(json['icon'].toString());
    }

    // Handle dismiss icon
    IconData dismissIcon = Icons.close;
    if (json['dismissIcon'] != null) {
      dismissIcon = _getIconData(json['dismissIcon'].toString()) ?? Icons.close;
    }

    // Handle gradient colors
    List<Color>? gradientColors;
    if (json['gradientColors'] != null && json['gradientColors'] is List) {
      gradientColors = (json['gradientColors'] as List)
          .map((colorValue) => _colorFromJson(colorValue) ?? Colors.blue)
          .toList();
    }

    // Handle gradient begin/end
    Alignment gradientBegin = Alignment.centerLeft;
    if (json['gradientBegin'] != null) {
      gradientBegin = _alignmentFromString(json['gradientBegin'].toString());
    }

    Alignment gradientEnd = Alignment.centerRight;
    if (json['gradientEnd'] != null) {
      gradientEnd = _alignmentFromString(json['gradientEnd'].toString());
    }

    // Handle text decoration
    TextDecoration textDecoration = TextDecoration.none;
    if (json['textDecoration'] != null) {
      switch (json['textDecoration'].toString().toLowerCase()) {
        case 'underline':
          textDecoration = TextDecoration.underline;
          break;
        case 'overline':
          textDecoration = TextDecoration.overline;
          break;
        case 'line-through':
        case 'linethrough':
        case 'strikethrough':
          textDecoration = TextDecoration.lineThrough;
          break;
        case 'none':
        default:
          textDecoration = TextDecoration.none;
          break;
      }
    }

    // Handle text overflow
    TextOverflow textOverflow = TextOverflow.ellipsis;
    if (json['textOverflow'] != null) {
      switch (json['textOverflow'].toString().toLowerCase()) {
        case 'clip':
          textOverflow = TextOverflow.clip;
          break;
        case 'fade':
          textOverflow = TextOverflow.fade;
          break;
        case 'visible':
          textOverflow = TextOverflow.visible;
          break;
        case 'ellipsis':
        default:
          textOverflow = TextOverflow.ellipsis;
          break;
      }
    }

    // Handle text align
    TextAlign textAlign = TextAlign.center;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'left':
        case 'start':
          textAlign = TextAlign.left;
          break;
        case 'right':
        case 'end':
          textAlign = TextAlign.right;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
        case 'center':
        default:
          textAlign = TextAlign.center;
          break;
      }
    }

    // Handle text direction
    TextDirection? textDirection;
    if (json['textDirection'] != null) {
      switch (json['textDirection'].toString().toLowerCase()) {
        case 'ltr':
          textDirection = TextDirection.ltr;
          break;
        case 'rtl':
          textDirection = TextDirection.rtl;
          break;
        default:
          textDirection = null;
          break;
      }
    }

    // Handle alignment
    Alignment? alignment;
    if (json['alignment'] != null) {
      alignment = _alignmentFromString(json['alignment'].toString());
    }

    // Handle margin
    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json['margin'] != null) {
      if (json['margin'] is double) {
        double marginValue = json['margin'] as double;
        margin = EdgeInsets.all(marginValue);
      } else if (json['margin'] is Map) {
        Map<String, dynamic> marginMap = json['margin'] as Map<String, dynamic>;
        margin = EdgeInsets.fromLTRB(
          marginMap['left'] as double? ?? 0.0,
          marginMap['top'] as double? ?? 0.0,
          marginMap['right'] as double? ?? 0.0,
          marginMap['bottom'] as double? ?? 0.0,
        );
      }
    }

    // Handle transform
    Matrix4? transform;
    if (json['transform'] != null && json['transform'] is Map) {
      Map<String, dynamic> transformMap = json['transform'] as Map<String, dynamic>;

      // Start with identity matrix
      transform = Matrix4.identity();

      // Apply rotation if specified (in degrees)
      if (transformMap['rotation'] != null) {
        double rotation = (transformMap['rotation'] as num).toDouble();
        transform.rotateZ(rotation * 3.1415926535 / 180); // Convert degrees to radians
      }

      // Apply scale if specified
      if (transformMap['scaleX'] != null || transformMap['scaleY'] != null) {
        double scaleX = (transformMap['scaleX'] as num?)?.toDouble() ?? 1.0;
        double scaleY = (transformMap['scaleY'] as num?)?.toDouble() ?? 1.0;
        transform.scale(scaleX, scaleY);
      }

      // Apply translation if specified
      if (transformMap['translateX'] != null || transformMap['translateY'] != null) {
        double translateX = (transformMap['translateX'] as num?)?.toDouble() ?? 0.0;
        double translateY = (transformMap['translateY'] as num?)?.toDouble() ?? 0.0;
        transform.translate(translateX, translateY);
      }
    }

    // Handle animation duration
    Duration animationDuration = const Duration(milliseconds: 300);
    if (json['animationDuration'] != null) {
      int milliseconds = json['animationDuration'] as int? ?? 300;
      animationDuration = Duration(milliseconds: milliseconds);
    }

    // Handle animation curve
    Curve animationCurve = Curves.easeInOut;
    if (json['animationCurve'] != null) {
      switch (json['animationCurve'].toString().toLowerCase()) {
        case 'linear':
          animationCurve = Curves.linear;
          break;
        case 'decelerate':
          animationCurve = Curves.decelerate;
          break;
        case 'ease':
          animationCurve = Curves.ease;
          break;
        case 'easein':
        case 'ease_in':
          animationCurve = Curves.easeIn;
          break;
        case 'easeout':
        case 'ease_out':
          animationCurve = Curves.easeOut;
          break;
        case 'easeinout':
        case 'ease_in_out':
        default:
          animationCurve = Curves.easeInOut;
          break;
      }
    }

    return TagWidget(
      text: json['text'] as String? ?? 'Tag',
      backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.blue,
      textColor: _colorFromJson(json['textColor']) ?? Colors.white,
      borderColor: _colorFromJson(json['borderColor']) ?? Color(0xFF0058FF),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      icon: icon,
      iconColor: _colorFromJson(json['iconColor']),
      iconSize: (json['iconSize'] as num?)?.toDouble() ?? 16.0,
      iconSpacing: (json['iconSpacing'] as num?)?.toDouble() ?? 4.0,
      padding: padding,
      isDismissible: json['isDismissible'] as bool? ?? false,
      dismissIcon: dismissIcon,
      dismissIconColor: _colorFromJson(json['dismissIconColor']),
      elevation: (json['elevation'] as num?)?.toDouble() ?? 0.0,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 14.0,
      fontWeight: fontWeight,
      isOutlined: json['isOutlined'] as bool? ?? false,
      isFilled: json['isFilled'] as bool? ?? true,
      hasGradient: json['hasGradient'] as bool? ?? false,
      gradientColors: gradientColors,
      gradientBegin: gradientBegin,
      gradientEnd: gradientEnd,

      // Additional text styling properties
      textDecoration: textDecoration,
      textOverflow: textOverflow,
      maxLines: json['maxLines'] as int?,
      textAlign: textAlign,
      letterSpacing: (json['letterSpacing'] as num?)?.toDouble() ?? 0.0,
      wordSpacing: (json['wordSpacing'] as num?)?.toDouble() ?? 0.0,
      textDirection: textDirection,

      // Additional visual enhancement properties
      shadowColor: _colorFromJson(json['shadowColor']) ?? Colors.black,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1.0,
      blurRadius: (json['blurRadius'] as num?)?.toDouble() ?? 3.0,
      spreadRadius: (json['spreadRadius'] as num?)?.toDouble() ?? 0.0,

      // Additional layout properties
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      margin: margin,
      alignment: alignment,
      transform: transform,

      // Additional animation properties
      animationDuration: animationDuration,
      animationCurve: animationCurve,
      animationType: json['animationType'] as String? ?? 'fade',

      // Additional interaction properties
      longPressEnabled: json['longPressEnabled'] as bool? ?? false,
      onLongPress: json['longPressEnabled'] == true ? () {} : null,
      doubleTapEnabled: json['doubleTapEnabled'] as bool? ?? false,
      onDoubleTap: json['doubleTapEnabled'] == true ? () {} : null,
      hoverColor: _colorFromJson(json['hoverColor']),
      splashColor: _colorFromJson(json['splashColor']),
      highlightColor: _colorFromJson(json['highlightColor']),
      focusColor: _colorFromJson(json['focusColor']),

      // Callbacks
      onTap: json['isClickable'] == true || json['isTappable'] == true ? () {} : null,
      onDismiss: json['isDismissible'] == true ? () {} : null,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'amber': return Colors.amber;
        case 'cyan': return Colors.cyan;
        case 'indigo': return Colors.indigo;
        case 'lime': return Colors.lime;
        case 'teal': return Colors.teal;
        default: return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Gets an IconData from a string name
  static IconData? _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'add': return Icons.add;
      case 'remove': return Icons.remove;
      case 'close': return Icons.close;
      case 'check': return Icons.check;
      case 'star': return Icons.star;
      case 'favorite': return Icons.favorite;
      case 'heart': return Icons.favorite;
      case 'info': return Icons.info;
      case 'warning': return Icons.warning;
      case 'error': return Icons.error;
      case 'help': return Icons.help;
      case 'settings': return Icons.settings;
      case 'person': return Icons.person;
      case 'people': return Icons.people;
      case 'home': return Icons.home;
      case 'search': return Icons.search;
      case 'label': return Icons.label;
      case 'tag': return Icons.local_offer;
      case 'bookmark': return Icons.bookmark;
      case 'flag': return Icons.flag;
      case 'priority_high': return Icons.priority_high;
      case 'new': return Icons.fiber_new;
      case 'trending': return Icons.trending_up;
      case 'verified': return Icons.verified;
      case 'thumb_up': return Icons.thumb_up;
      case 'thumb_down': return Icons.thumb_down;
      case 'delete': return Icons.delete;
      case 'edit': return Icons.edit;
      case 'save': return Icons.save;
      case 'share': return Icons.share;
      case 'download': return Icons.download;
      case 'upload': return Icons.upload;
      case 'attach': return Icons.attach_file;
      case 'link': return Icons.link;
      case 'send': return Icons.send;
      case 'mail': return Icons.mail;
      case 'email': return Icons.email;
      case 'phone': return Icons.phone;
      case 'message': return Icons.message;
      case 'chat': return Icons.chat;
      case 'comment': return Icons.comment;
      case 'notifications': return Icons.notifications;
      case 'calendar': return Icons.calendar_today;
      case 'event': return Icons.event;
      case 'schedule': return Icons.schedule;
      case 'clock': return Icons.access_time;
      case 'location': return Icons.location_on;
      case 'place': return Icons.place;
      case 'map': return Icons.map;
      case 'directions': return Icons.directions;
      case 'camera': return Icons.camera_alt;
      case 'photo': return Icons.photo;
      case 'image': return Icons.image;
      case 'video': return Icons.videocam;
      case 'music': return Icons.music_note;
      case 'audio': return Icons.audiotrack;
      case 'volume': return Icons.volume_up;
      case 'mic': return Icons.mic;
      case 'play': return Icons.play_arrow;
      case 'pause': return Icons.pause;
      case 'stop': return Icons.stop;
      case 'skip_next': return Icons.skip_next;
      case 'skip_previous': return Icons.skip_previous;
      case 'fast_forward': return Icons.fast_forward;
      case 'fast_rewind': return Icons.fast_rewind;
      case 'repeat': return Icons.repeat;
      case 'shuffle': return Icons.shuffle;
      case 'playlist': return Icons.playlist_play;
      case 'album': return Icons.album;
      case 'artist': return Icons.person;
      case 'genre': return Icons.category;
      case 'file': return Icons.insert_drive_file;
      case 'folder': return Icons.folder;
      case 'cloud': return Icons.cloud;
      case 'wifi': return Icons.wifi;
      case 'bluetooth': return Icons.bluetooth;
      case 'battery': return Icons.battery_full;
      case 'power': return Icons.power_settings_new;
      case 'refresh': return Icons.refresh;
      case 'sync': return Icons.sync;
      case 'update': return Icons.update;
      case 'more': return Icons.more_horiz;
      case 'menu': return Icons.menu;
      case 'list': return Icons.list;
      case 'grid': return Icons.grid_view;
      case 'dashboard': return Icons.dashboard;
      case 'filter': return Icons.filter_list;
      case 'sort': return Icons.sort;
      case 'shopping_cart': return Icons.shopping_cart;
      case 'shop': return Icons.shop;
      case 'store': return Icons.store;
      case 'payment': return Icons.payment;
      case 'credit_card': return Icons.credit_card;
      case 'money': return Icons.attach_money;
      case 'wallet': return Icons.account_balance_wallet;
      case 'account': return Icons.account_circle;
      case 'login': return Icons.login;
      case 'logout': return Icons.logout;
      case 'security': return Icons.security;
      case 'lock': return Icons.lock;
      case 'unlock': return Icons.lock_open;
      case 'visibility': return Icons.visibility;
      case 'visibility_off': return Icons.visibility_off;
      case 'flash_on': return Icons.flash_on;
      case 'flash_off': return Icons.flash_off;
      case 'lightbulb': return Icons.lightbulb;
      case 'dark_mode': return Icons.dark_mode;
      case 'light_mode': return Icons.light_mode;
      case 'color_lens': return Icons.color_lens;
      case 'brush': return Icons.brush;
      case 'format': return Icons.format_paint;
      case 'text': return Icons.text_fields;
      case 'font': return Icons.font_download;
      case 'bold': return Icons.format_bold;
      case 'italic': return Icons.format_italic;
      case 'underline': return Icons.format_underlined;
      case 'align_left': return Icons.format_align_left;
      case 'align_center': return Icons.format_align_center;
      case 'align_right': return Icons.format_align_right;
      case 'align_justify': return Icons.format_align_justify;
      case 'indent': return Icons.format_indent_increase;
      case 'outdent': return Icons.format_indent_decrease;
      case 'list_bulleted': return Icons.format_list_bulleted;
      case 'list_numbered': return Icons.format_list_numbered;
      case 'clear': return Icons.clear;
      case 'undo': return Icons.undo;
      case 'redo': return Icons.redo;
      case 'copy': return Icons.content_copy;
      case 'paste': return Icons.content_paste;
      case 'cut': return Icons.content_cut;
      case 'select_all': return Icons.select_all;
      case 'print': return Icons.print;
      case 'scanner': return Icons.scanner;
      case 'local_offer': return Icons.local_offer;
      default: return null;
    }
  }

  /// Converts a string to an Alignment
  static Alignment _alignmentFromString(String alignmentStr) {
    switch (alignmentStr.toLowerCase()) {
      case 'topleft':
      case 'top_left':
      case 'top left':
        return Alignment.topLeft;
      case 'topcenter':
      case 'top_center':
      case 'top center':
        return Alignment.topCenter;
      case 'topright':
      case 'top_right':
      case 'top right':
        return Alignment.topRight;
      case 'centerleft':
      case 'center_left':
      case 'center left':
        return Alignment.centerLeft;
      case 'center':
        return Alignment.center;
      case 'centerright':
      case 'center_right':
      case 'center right':
        return Alignment.centerRight;
      case 'bottomleft':
      case 'bottom_left':
      case 'bottom left':
        return Alignment.bottomLeft;
      case 'bottomcenter':
      case 'bottom_center':
      case 'bottom center':
        return Alignment.bottomCenter;
      case 'bottomright':
      case 'bottom_right':
      case 'bottom right':
        return Alignment.bottomRight;
      default:
        return Alignment.center;
    }
  }

  /// Converts an Alignment to a string
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.topLeft) return 'top_left';
    if (alignment == Alignment.topCenter) return 'top_center';
    if (alignment == Alignment.topRight) return 'top_right';
    if (alignment == Alignment.centerLeft) return 'center_left';
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.centerRight) return 'center_right';
    if (alignment == Alignment.bottomLeft) return 'bottom_left';
    if (alignment == Alignment.bottomCenter) return 'bottom_center';
    if (alignment == Alignment.bottomRight) return 'bottom_right';
    return 'center';
  }

  /// Gets a string name from an IconData
  static String? _getIconName(IconData? icon) {
    if (icon == null) return null;

    if (icon == Icons.add) return 'add';
    if (icon == Icons.remove) return 'remove';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.check) return 'check';
    if (icon == Icons.star) return 'star';
    if (icon == Icons.favorite) return 'favorite';
    if (icon == Icons.info) return 'info';
    if (icon == Icons.warning) return 'warning';
    if (icon == Icons.error) return 'error';
    if (icon == Icons.help) return 'help';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.person) return 'person';
    if (icon == Icons.people) return 'people';
    if (icon == Icons.home) return 'home';
    if (icon == Icons.search) return 'search';
    if (icon == Icons.label) return 'label';
    if (icon == Icons.local_offer) return 'tag';
    if (icon == Icons.bookmark) return 'bookmark';
    if (icon == Icons.flag) return 'flag';
    if (icon == Icons.priority_high) return 'priority_high';
    if (icon == Icons.fiber_new) return 'new';
    if (icon == Icons.trending_up) return 'trending';
    if (icon == Icons.verified) return 'verified';
    if (icon == Icons.thumb_up) return 'thumb_up';
    if (icon == Icons.thumb_down) return 'thumb_down';
    if (icon == Icons.delete) return 'delete';
    if (icon == Icons.edit) return 'edit';
    if (icon == Icons.save) return 'save';
    if (icon == Icons.share) return 'share';
    if (icon == Icons.download) return 'download';
    if (icon == Icons.upload) return 'upload';
    if (icon == Icons.attach_file) return 'attach';
    if (icon == Icons.link) return 'link';
    if (icon == Icons.send) return 'send';
    if (icon == Icons.mail) return 'mail';
    if (icon == Icons.email) return 'email';
    if (icon == Icons.phone) return 'phone';
    if (icon == Icons.message) return 'message';
    if (icon == Icons.chat) return 'chat';
    if (icon == Icons.comment) return 'comment';
    if (icon == Icons.notifications) return 'notifications';
    if (icon == Icons.calendar_today) return 'calendar';
    if (icon == Icons.event) return 'event';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.access_time) return 'clock';
    if (icon == Icons.location_on) return 'location';
    if (icon == Icons.place) return 'place';
    if (icon == Icons.map) return 'map';
    if (icon == Icons.directions) return 'directions';
    if (icon == Icons.camera_alt) return 'camera';
    if (icon == Icons.photo) return 'photo';
    if (icon == Icons.image) return 'image';
    if (icon == Icons.videocam) return 'video';
    if (icon == Icons.music_note) return 'music';
    if (icon == Icons.audiotrack) return 'audio';
    if (icon == Icons.volume_up) return 'volume';
    if (icon == Icons.mic) return 'mic';
    if (icon == Icons.play_arrow) return 'play';
    if (icon == Icons.pause) return 'pause';
    if (icon == Icons.stop) return 'stop';
    if (icon == Icons.skip_next) return 'skip_next';
    if (icon == Icons.skip_previous) return 'skip_previous';
    if (icon == Icons.fast_forward) return 'fast_forward';
    if (icon == Icons.fast_rewind) return 'fast_rewind';
    if (icon == Icons.repeat) return 'repeat';
    if (icon == Icons.shuffle) return 'shuffle';
    if (icon == Icons.playlist_play) return 'playlist';
    if (icon == Icons.album) return 'album';
    if (icon == Icons.category) return 'genre';
    if (icon == Icons.insert_drive_file) return 'file';
    if (icon == Icons.folder) return 'folder';
    if (icon == Icons.cloud) return 'cloud';
    if (icon == Icons.wifi) return 'wifi';
    if (icon == Icons.bluetooth) return 'bluetooth';
    if (icon == Icons.battery_full) return 'battery';
    if (icon == Icons.power_settings_new) return 'power';
    if (icon == Icons.refresh) return 'refresh';
    if (icon == Icons.sync) return 'sync';
    if (icon == Icons.update) return 'update';
    if (icon == Icons.more_horiz) return 'more';
    if (icon == Icons.menu) return 'menu';
    if (icon == Icons.list) return 'list';
    if (icon == Icons.grid_view) return 'grid';
    if (icon == Icons.dashboard) return 'dashboard';
    if (icon == Icons.filter_list) return 'filter';
    if (icon == Icons.sort) return 'sort';
    if (icon == Icons.shopping_cart) return 'shopping_cart';
    if (icon == Icons.shop) return 'shop';
    if (icon == Icons.store) return 'store';
    if (icon == Icons.payment) return 'payment';
    if (icon == Icons.credit_card) return 'credit_card';
    if (icon == Icons.attach_money) return 'money';
    if (icon == Icons.account_balance_wallet) return 'wallet';
    if (icon == Icons.account_circle) return 'account';
    if (icon == Icons.login) return 'login';
    if (icon == Icons.logout) return 'logout';
    if (icon == Icons.security) return 'security';
    if (icon == Icons.lock) return 'lock';
    if (icon == Icons.lock_open) return 'unlock';
    if (icon == Icons.visibility) return 'visibility';
    if (icon == Icons.visibility_off) return 'visibility_off';
    if (icon == Icons.flash_on) return 'flash_on';
    if (icon == Icons.flash_off) return 'flash_off';
    if (icon == Icons.lightbulb) return 'lightbulb';
    if (icon == Icons.dark_mode) return 'dark_mode';
    if (icon == Icons.light_mode) return 'light_mode';
    if (icon == Icons.color_lens) return 'color_lens';
    if (icon == Icons.brush) return 'brush';
    if (icon == Icons.format_paint) return 'format';
    if (icon == Icons.text_fields) return 'text';
    if (icon == Icons.font_download) return 'font';
    if (icon == Icons.format_bold) return 'bold';
    if (icon == Icons.format_italic) return 'italic';
    if (icon == Icons.format_underlined) return 'underline';
    if (icon == Icons.format_align_left) return 'align_left';
    if (icon == Icons.format_align_center) return 'align_center';
    if (icon == Icons.format_align_right) return 'align_right';
    if (icon == Icons.format_align_justify) return 'align_justify';
    if (icon == Icons.format_indent_increase) return 'indent';
    if (icon == Icons.format_indent_decrease) return 'outdent';
    if (icon == Icons.format_list_bulleted) return 'list_bulleted';
    if (icon == Icons.format_list_numbered) return 'list_numbered';
    if (icon == Icons.clear) return 'clear';
    if (icon == Icons.undo) return 'undo';
    if (icon == Icons.redo) return 'redo';
    if (icon == Icons.content_copy) return 'copy';
    if (icon == Icons.content_paste) return 'paste';
    if (icon == Icons.content_cut) return 'cut';
    if (icon == Icons.select_all) return 'select_all';
    if (icon == Icons.print) return 'print';
    if (icon == Icons.scanner) return 'scanner';

    return 'tag'; // Default icon name
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    // Use the color value directly to avoid deprecated properties
    final hex = color.toString().replaceAll('Color(0xff', '').replaceAll(')', '');
    return '#$hex';
  }

  /// Converts the TagWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    // Convert padding to a map
    Map<String, dynamic> paddingMap = {};
    if (padding is EdgeInsets) {
      EdgeInsets p = padding as EdgeInsets;
      paddingMap = {
        'left': p.left,
        'top': p.top,
        'right': p.right,
        'bottom': p.bottom,
      };
    }

    // Convert margin to a map
    Map<String, dynamic> marginMap = {};
    if (margin is EdgeInsets) {
      EdgeInsets m = margin as EdgeInsets;
      marginMap = {
        'left': m.left,
        'top': m.top,
        'right': m.right,
        'bottom': m.bottom,
      };
    }

    // Convert font weight to string
    String fontWeightString = 'normal';
    if (fontWeight == FontWeight.bold) {
      fontWeightString = 'bold';
    } else if (fontWeight == FontWeight.w100) {
      fontWeightString = '100';
    } else if (fontWeight == FontWeight.w200) {
      fontWeightString = '200';
    } else if (fontWeight == FontWeight.w300) {
      fontWeightString = '300';
    } else if (fontWeight == FontWeight.w400) {
      fontWeightString = '400';
    } else if (fontWeight == FontWeight.w500) {
      fontWeightString = '500';
    } else if (fontWeight == FontWeight.w600) {
      fontWeightString = '600';
    } else if (fontWeight == FontWeight.w700) {
      fontWeightString = '700';
    } else if (fontWeight == FontWeight.w800) {
      fontWeightString = '800';
    } else if (fontWeight == FontWeight.w900) {
      fontWeightString = '900';
    }

    // Convert gradient colors to list of strings
    List<String>? gradientColorStrings;
    if (gradientColors != null) {
      gradientColorStrings = gradientColors!.map((color) => _colorToJson(color)).toList();
    }

    // Convert text decoration to string
    String textDecorationString = 'none';
    if (textDecoration == TextDecoration.underline) {
      textDecorationString = 'underline';
    } else if (textDecoration == TextDecoration.overline) {
      textDecorationString = 'overline';
    } else if (textDecoration == TextDecoration.lineThrough) {
      textDecorationString = 'line-through';
    }

    // Convert text overflow to string
    String textOverflowString = 'ellipsis';
    if (textOverflow == TextOverflow.clip) {
      textOverflowString = 'clip';
    } else if (textOverflow == TextOverflow.fade) {
      textOverflowString = 'fade';
    } else if (textOverflow == TextOverflow.visible) {
      textOverflowString = 'visible';
    }

    // Convert text align to string
    String textAlignString = 'center';
    if (textAlign == TextAlign.left) {
      textAlignString = 'left';
    } else if (textAlign == TextAlign.right) {
      textAlignString = 'right';
    } else if (textAlign == TextAlign.justify) {
      textAlignString = 'justify';
    }

    // Convert text direction to string
    String? textDirectionString;
    if (textDirection == TextDirection.ltr) {
      textDirectionString = 'ltr';
    } else if (textDirection == TextDirection.rtl) {
      textDirectionString = 'rtl';
    }

    // Convert transform to map if not null
    Map<String, dynamic>? transformMap;
    if (transform != null) {
      // This is a simplified approach - a full implementation would need to
      // decompose the matrix to extract rotation, scale, and translation
      transformMap = {
        'matrix': transform!.storage.join(','),
        // Include placeholder properties that users might set directly
        'rotation': 0,
        'scaleX': 1.0,
        'scaleY': 1.0,
        'translateX': 0.0,
        'translateY': 0.0,
      };
    }

    // Convert animation curve to string
    String animationCurveString = 'ease_in_out';
    if (animationCurve == Curves.linear) {
      animationCurveString = 'linear';
    } else if (animationCurve == Curves.decelerate) {
      animationCurveString = 'decelerate';
    } else if (animationCurve == Curves.ease) {
      animationCurveString = 'ease';
    } else if (animationCurve == Curves.easeIn) {
      animationCurveString = 'ease_in';
    } else if (animationCurve == Curves.easeOut) {
      animationCurveString = 'ease_out';
    }

    // Build the JSON map
    final Map<String, dynamic> json = {
      // Basic properties
      'text': text,
      'backgroundColor': _colorToJson(backgroundColor),
      'textColor': _colorToJson(textColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'iconSize': iconSize,
      'iconSpacing': iconSpacing,
      'padding': paddingMap,
      'isDismissible': isDismissible,
      'elevation': elevation,
      'fontSize': fontSize,
      'fontWeight': fontWeightString,
      'isOutlined': isOutlined,
      'isFilled': isFilled,
      'hasGradient': hasGradient,
      'gradientBegin': _alignmentToString(gradientBegin),
      'gradientEnd': _alignmentToString(gradientEnd),
      'isClickable': onTap != null,

      // Additional text styling properties
      'textDecoration': textDecorationString,
      'textOverflow': textOverflowString,
      'textAlign': textAlignString,
      'letterSpacing': letterSpacing,
      'wordSpacing': wordSpacing,

      // Additional visual enhancement properties
      'shadowColor': _colorToJson(shadowColor),
      'opacity': opacity,
      'blurRadius': blurRadius,
      'spreadRadius': spreadRadius,

      // Additional layout properties
      'margin': marginMap,

      // Additional animation properties
      'animationDuration': animationDuration.inMilliseconds,
      'animationCurve': animationCurveString,
      'animationType': animationType,

      // Additional interaction properties
      'longPressEnabled': longPressEnabled,
      'doubleTapEnabled': doubleTapEnabled,
    };

    // Add optional properties only if they are not null
    if (borderColor != null) {
      json['borderColor'] = _colorToJson(borderColor!);
    }

    if (icon != null) {
      json['icon'] = _getIconName(icon);
    }

    if (iconColor != null) {
      json['iconColor'] = _colorToJson(iconColor!);
    }

    if (dismissIconColor != null) {
      json['dismissIconColor'] = _colorToJson(dismissIconColor!);
    }

    if (dismissIcon != Icons.close) {
      json['dismissIcon'] = _getIconName(dismissIcon);
    }

    if (gradientColorStrings != null) {
      json['gradientColors'] = gradientColorStrings;
    }

    if (maxLines != null) {
      json['maxLines'] = maxLines;
    }

    if (textDirectionString != null) {
      json['textDirection'] = textDirectionString;
    }

    if (width != null) {
      json['width'] = width;
    }

    if (height != null) {
      json['height'] = height;
    }

    if (alignment != null) {
      json['alignment'] = _alignmentToString(alignment!);
    }

    if (transformMap != null) {
      json['transform'] = transformMap;
    }

    if (hoverColor != null) {
      json['hoverColor'] = _colorToJson(hoverColor!);
    }

    if (splashColor != null) {
      json['splashColor'] = _colorToJson(splashColor!);
    }

    if (highlightColor != null) {
      json['highlightColor'] = _colorToJson(highlightColor!);
    }

    if (focusColor != null) {
      json['focusColor'] = _colorToJson(focusColor!);
    }

    return json;
  }

  @override
  State<TagWidget> createState() => _TagWidgetState();
}

class _TagWidgetState extends State<TagWidget> with SingleTickerProviderStateMixin {
  bool _isVisible = true;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Create animation based on animation type
    switch (widget.animationType.toLowerCase()) {
      case 'fade':
        _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: widget.animationCurve,
          ),
        );
        break;
      case 'scale':
        _animation = Tween<double>(begin: 0.5, end: 1.0).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: widget.animationCurve,
          ),
        );
        break;
      case 'slide':
        _animation = Tween<double>(begin: -50.0, end: 0.0).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: widget.animationCurve,
          ),
        );
        break;
      default:
        _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: widget.animationCurve,
          ),
        );
    }

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) {
      return const SizedBox.shrink();
    }

    // Determine the background decoration based on configuration
    BoxDecoration decoration;
    if (widget.hasGradient && widget.gradientColors != null && widget.gradientColors!.length >= 2) {
      // Gradient background
      decoration = BoxDecoration(
        gradient: LinearGradient(
          colors: widget.gradientColors!,
          begin: widget.gradientBegin,
          end: widget.gradientEnd,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null || widget.isOutlined
            ? Border.all(
                color: widget.borderColor ?? widget.backgroundColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow || widget.elevation > 0
            ? [
                BoxShadow(
                  color: Color.fromRGBO(
                    widget.shadowColor.red,
                    widget.shadowColor.green,
                    widget.shadowColor.blue,
                    0.3,
                  ),
                  blurRadius: widget.blurRadius,
                  spreadRadius: widget.spreadRadius,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      );
    } else if (widget.isOutlined) {
      // Outlined style (transparent background with border)
      decoration = BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(
          color: widget.borderColor ?? Colors.blue,
          width: widget.borderWidth,
        ),
        boxShadow: widget.hasShadow || widget.elevation > 0
            ? [
                BoxShadow(
                  color: Color.fromRGBO(
                    widget.shadowColor.red,
                    widget.shadowColor.green,
                    widget.shadowColor.blue,
                    0.3,
                  ),
                  blurRadius: widget.blurRadius,
                  spreadRadius: widget.spreadRadius,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      );
    } else if (widget.isFilled) {
      // Filled style (solid background)
      decoration = BoxDecoration(
        color: Color(0xFFf0f5ff),
        borderRadius: BorderRadius.circular(4),
        border: widget.borderColor != null
            ? Border.all(
                color: Color(0xFF0058FF),
                width: widget.borderWidth,
              )
            : null,
        // boxShadow: widget.hasShadow || widget.elevation > 0
        //     ? [
        //         BoxShadow(
        //           color: Color.fromRGBO(
        //             widget.shadowColor.red,
        //             widget.shadowColor.green,
        //             widget.shadowColor.blue,
        //             0.3,
        //           ),
        //           blurRadius: widget.blurRadius,
        //           spreadRadius: widget.spreadRadius,
        //           offset: const Offset(0, 2),
        //         ),
        //       ]
        //     : null,
      );
    } else {
      // Default style
      decoration = BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow || widget.elevation > 0
            ? [
                BoxShadow(
                  color: Color.fromRGBO(
                    widget.shadowColor.red,
                    widget.shadowColor.green,
                    widget.shadowColor.blue,
                    0.3,
                  ),
                  blurRadius: widget.blurRadius,
                  spreadRadius: widget.spreadRadius,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      );
    }

    // Create the text style with all the configured properties
    final textStyle = TextStyle(
      color: widget.textColor,
      fontSize: widget.fontSize,
      fontWeight: widget.fontWeight,
      decoration: widget.textDecoration,
      letterSpacing: widget.letterSpacing,
      wordSpacing: widget.wordSpacing,
    );

    // Create the base widget
    Widget tagWidget = Material(
      color: Colors.transparent,
      elevation: widget.elevation,
      shadowColor: widget.shadowColor,
      borderRadius: BorderRadius.circular(widget.borderRadius),  
      child: InkWell(
        onTap: widget.onTap,
        onLongPress: widget.longPressEnabled ? widget.onLongPress : null,
        onDoubleTap: widget.doubleTapEnabled ? widget.onDoubleTap : null,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        hoverColor: widget.hoverColor,
        splashColor: widget.splashColor,
        highlightColor: widget.highlightColor,
        focusColor: widget.focusColor,
        child: Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          decoration: decoration,
          padding:  _getResponsivePadding(context),
          alignment: widget.alignment,
          transform: widget.transform,
          child: Opacity(
            opacity: widget.opacity,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              textDirection: widget.textDirection,
              children: [
                // Icon (if provided)
                // if (widget.icon != null) ...[
                //   Icon(
                //     widget.icon,
                //     size: widget.iconSize,
                //     color: widget.iconColor ?? widget.textColor,
                //   ),
                //   SizedBox(width: widget.iconSpacing),
                // ],

                // Text
                Flexible(
                child: Text(
                  widget.text,
                  overflow: widget.textOverflow,
                  maxLines: widget.maxLines,
                  textAlign: widget.textAlign,
                  style: TextStyle(
                    color: Color(0xFF333333),       // 👈 Text color
                    fontSize: _getResponsiveValueFontSize(context),            // 👈 Font size
                    fontWeight: FontWeight.w500, // 👈 Font weight (w400, w500, w600, etc.)
                  ),
                ),
              ),


                // Dismiss icon (if dismissible)
                if (widget.isDismissible) ...[
                  SizedBox(width: widget.iconSpacing),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _isVisible = false;
                      });
                      if (widget.onDismiss != null) {
                        widget.onDismiss!();
                      }
                    },
                    child: Icon(
                      widget.dismissIcon,
                      size: widget.iconSize,
                      color: widget.dismissIconColor ?? widget.textColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );

    // Apply animation based on animation type
    switch (widget.animationType.toLowerCase()) {
      case 'fade':
        return FadeTransition(
          opacity: _animation,
          child: tagWidget,
        );
      case 'scale':
        return ScaleTransition(
          scale: _animation,
          child: tagWidget,
        );
      case 'slide':
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(_animation.value, 0),
              child: child,
            );
          },
          child: tagWidget,
        );
      default:
        return tagWidget;
    }
  }
}

   double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 20.0,
      vertical: 10.5,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 20.0,
      vertical: 8.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Default for very small screens
  }
}