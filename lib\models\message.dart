import 'base_model.dart';

enum MessageRole { user, assistant, system }

// Define MessageContentType enum for compatibility with repository
enum MessageContentType { text, code, yaml, image, file }

class Message implements BaseModel {
  final String id;
  final String content;
  final MessageRole role;
  final DateTime timestamp;
  final MessageContentType contentType;
  final String? title;
  final String? language;

  Message({
    String? id,
    required this.content,
    required this.role,
    DateTime? timestamp,
    this.contentType = MessageContentType.text,
    this.title,
    this.language,
  })  : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        timestamp = timestamp ?? DateTime.now();

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      content: json['content'] ?? '',
      role: MessageRole.values.firstWhere(
        (e) => e.toString() == 'MessageRole.${json['role']}',
        orElse: () => MessageRole.user,
      ),
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      contentType: _parseContentType(json['content_type']),
      title: json['title'],
      language: json['language'],
    );
  }

  static MessageContentType _parseContentType(String? contentType) {
    switch (contentType) {
      case 'text':
        return MessageContentType.text;
      case 'code':
        return MessageContentType.code;
      case 'yaml':
        return MessageContentType.yaml;
      case 'image':
        return MessageContentType.image;
      case 'file':
        return MessageContentType.file;
      default:
        return MessageContentType.text;
    }
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'role': role.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'content_type': contentType.toString().split('.').last,
      'title': title,
      'language': language,
    };
  }

  @override
  Message copyWith({
    String? id,
    String? content,
    MessageRole? role,
    DateTime? timestamp,
    MessageContentType? contentType,
    String? title,
    String? language,
  }) {
    return Message(
      id: id ?? this.id,
      content: content ?? this.content,
      role: role ?? this.role,
      timestamp: timestamp ?? this.timestamp,
      contentType: contentType ?? this.contentType,
      title: title ?? this.title,
      language: language ?? this.language,
    );
  }

  /// Create a user message
  factory Message.user({required String content, String? id}) {
    return Message(
      id: id,
      content: content,
      role: MessageRole.user,
    );
  }

  /// Create an assistant message
  factory Message.nsl({
    required String content,
    MessageContentType contentType = MessageContentType.text,
    String? title,
    String? language,
    String? id,
  }) {
    return Message(
      id: id,
      content: content,
      role: MessageRole.assistant,
      contentType: contentType,
      title: title,
      language: language,
    );
  }

  /// Create a system message
  factory Message.system({required String content, String? id}) {
    return Message(
      id: id,
      content: content,
      role: MessageRole.system,
    );
  }
}
