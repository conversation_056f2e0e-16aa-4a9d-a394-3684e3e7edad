
import 'dart:convert';

DiscoveryChatHistory discoveryChatHistoryFromJson(String str) => DiscoveryChatHistory.fromJson(json.decode(str));

String discoveryChatHistoryToJson(DiscoveryChatHistory data) => json.encode(data.toJson());

class DiscoveryChatHistory {
    String? message;
    List<History>? history;

    DiscoveryChatHistory({
        this.message,
        this.history,
    });

    factory DiscoveryChatHistory.fromJson(Map<String, dynamic> json) => DiscoveryChatHistory(
        message: json["message"],
        history: json["history"] == null ? [] : List<History>.from(json["history"]!.map((x) => History.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "message": message,
        "history": history == null ? [] : List<dynamic>.from(history!.map((x) => x.toJson())),
    };
}

class History {
    String? role;
    String? content;

    History({
        this.role,
        this.content,
    });

    factory History.fromJson(Map<String, dynamic> json) => History(
        role: json["role"],
        content: json["content"],
    );

    Map<String, dynamic> toJson() => {
        "role": role,
        "content": content,
    };
}
