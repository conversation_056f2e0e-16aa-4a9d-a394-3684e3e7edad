# Flutter UI Component Library

This is a comprehensive UI component library for Flutter applications, designed to provide consistent, reusable, and customizable UI elements that follow a cohesive design system.

## Overview

The library includes various UI components organized by their functionality:

- **Theme**: Design tokens and theme configuration
- **Buttons**: Various button styles and variants
- **Inputs**: Text fields and form elements
- **Cards**: Content containers and cards
- **Chat Components**: Message bubbles and typing indicators
- **Layout Components**: Scaffolds, headers, and navigation elements

## Usage

Import the components you need:

```dart
import 'package:your_app/ui_components/index.dart';
```

Or import specific components:

```dart
import 'package:your_app/ui_components/buttons/app_button.dart';
import 'package:your_app/ui_components/inputs/app_text_field.dart';
```

## Components

### Theme

The `AppTheme` class provides consistent theming for the entire application, including:

- Color palette
- Typography
- Spacing
- Border radius
- Elevation
- Animation durations

```dart
// Use theme constants
Container(
  padding: const EdgeInsets.all(AppTheme.spacingM),
  color: AppTheme.primaryColor,
  child: Text(
    'Hello World',
    style: AppTheme.bodyLarge,
  ),
)

// Apply theme to MaterialApp
MaterialApp(
  theme: AppTheme.getThemeData(),
  // ...
)
```

### Buttons

The `AppButton` component provides various button styles:

```dart
// Primary button
AppButton(
  text: 'Primary Button',
  onPressed: () {
    // Handle press
  },
)

// Secondary button
AppButton(
  text: 'Secondary Button',
  variant: AppButtonVariant.secondary,
  onPressed: () {
    // Handle press
  },
)

// Text button
AppButton(
  text: 'Text Button',
  variant: AppButtonVariant.text,
  onPressed: () {
    // Handle press
  },
)

// Icon button
AppButton(
  icon: Icons.add,
  variant: AppButtonVariant.icon,
  onPressed: () {
    // Handle press
  },
)

// Button with icon and text
AppButton(
  text: 'Button with Icon',
  icon: Icons.star,
  onPressed: () {
    // Handle press
  },
)

// Loading state
AppButton(
  text: 'Loading Button',
  isLoading: true,
  onPressed: () {
    // Handle press
  },
)
```

### Inputs

The `AppTextField` component provides various text input styles:

```dart
// Basic text field
AppTextField(
  label: 'Name',
  placeholder: 'Enter your name',
)

// Email field
AppTextField(
  label: 'Email',
  placeholder: 'Enter your email',
  type: AppTextFieldType.email,
)

// Password field
AppTextField(
  label: 'Password',
  placeholder: 'Enter your password',
  type: AppTextFieldType.password,
)

// Search field
AppTextField(
  placeholder: 'Search...',
  type: AppTextFieldType.search,
)

// Multiline field
AppTextField(
  label: 'Description',
  placeholder: 'Enter a description',
  type: AppTextFieldType.multiline,
)

// Field with error
AppTextField(
  label: 'Username',
  placeholder: 'Enter your username',
  errorText: 'Username is already taken',
)

// Field with helper text
AppTextField(
  label: 'Phone',
  placeholder: 'Enter your phone number',
  helperText: 'Include country code',
)
```

### Cards

The `AppCard` component provides various card styles:

```dart
// Basic card
AppCard(
  child: Text('This is a card'),
)

// Outlined card
AppCard(
  type: AppCardType.outlined,
  child: Text('This is an outlined card'),
)

// Filled card
AppCard(
  type: AppCardType.filled,
  child: Text('This is a filled card'),
)

// Card with custom padding
AppCard(
  padding: const EdgeInsets.all(AppTheme.spacingL),
  child: Text('This card has custom padding'),
)

// Card with tap action
AppCard(
  onTap: () {
    // Handle tap
  },
  child: Text('Tap this card'),
)

// Card with header and footer
AppCardWithHeader(
  header: Text('Card Header'),
  content: Text('Card Content'),
  footer: Text('Card Footer'),
)
```

### Chat Components

The `ChatMessageBubble` and `ChatTypingIndicator` components provide chat UI elements:

```dart
// User message bubble
ChatMessageBubble(
  message: Message(
    content: 'Hello!',
    role: MessageRole.user,
  ),
)

// Assistant message bubble
ChatMessageBubble(
  message: Message(
    content: 'How can I help you today?',
    role: MessageRole.assistant,
  ),
)

// Typing indicator
ChatTypingIndicator()
```

### Layout Components

The `AppScaffold`, `AppHeader`, and other layout components provide consistent app structure:

```dart
// Basic scaffold with header
AppScaffold(
  appBar: AppHeader(
    title: 'My App',
    actions: [
      IconButton(
        icon: const Icon(Icons.settings),
        onPressed: () {
          // Handle press
        },
      ),
    ],
  ),
  body: Center(
    child: Text('Hello World'),
  ),
)

// Bottom navigation
AppScaffold(
  body: Center(
    child: Text('Hello World'),
  ),
  bottomNavigationBar: AppBottomNavigationBar(
    currentIndex: 0,
    onTap: (index) {
      // Handle tap
    },
    items: const [
      AppBottomNavigationItem(
        icon: Icons.home,
        label: 'Home',
      ),
      AppBottomNavigationItem(
        icon: Icons.search,
        label: 'Search',
      ),
      AppBottomNavigationItem(
        icon: Icons.person,
        label: 'Profile',
      ),
    ],
  ),
)

// Floating action button
AppScaffold(
  body: Center(
    child: Text('Hello World'),
  ),
  floatingActionButton: AppFloatingActionButton(
    icon: Icons.add,
    onPressed: () {
      // Handle press
    },
  ),
)

// Responsive Grid
AppResponsiveGrid(
  minItemWidth: 200,
  children: [
    AppResponsiveGridItem(
      child: Text('Grid Item 1'),
      elevation: 1,
      onTap: () {
        // Handle tap
      },
    ),
    AppResponsiveGridItem(
      child: Text('Grid Item 2'),
      elevation: 1,
    ),
    // More items...
  ],
)

// Expandable Panel
AppExpandablePanel(
  title: Text('Expandable Panel'),
  content: Text('This is the content of the panel'),
  initiallyExpanded: true,
)

// Expandable Panel Group
AppExpandablePanelGroup(
  panels: [
    AppExpandablePanelData(
      title: Text('Panel 1'),
      content: Text('Content of panel 1'),
    ),
    AppExpandablePanelData(
      title: Text('Panel 2'),
      content: Text('Content of panel 2'),
    ),
  ],
  initiallyExpandedIndex: 0,
)

// Tab Layout
AppTabLayout(
  tabs: [
    AppTabData(
      label: 'Tab 1',
      icon: Icons.home,
      content: Text('Content of tab 1'),
    ),
    AppTabData(
      label: 'Tab 2',
      icon: Icons.search,
      content: Text('Content of tab 2'),
    ),
  ],
)

// Segmented Tab Layout
AppSegmentedTabLayout(
  tabs: [
    AppTabData(
      label: 'Day',
      content: Text('Day view'),
    ),
    AppTabData(
      label: 'Week',
      content: Text('Week view'),
    ),
    AppTabData(
      label: 'Month',
      content: Text('Month view'),
    ),
  ],
)

// Dividers
const AppDivider()
const AppDivider(style: AppDividerStyle.dashed)
const AppDivider.vertical(height: 40)
AppLabeledDivider(label: Text('OR'))

// Spacers
const AppSpacer.xs() // Extra small vertical spacer
const AppSpacer.s() // Small vertical spacer
const AppSpacer.m() // Medium vertical spacer
const AppSpacer.l() // Large vertical spacer
const AppSpacer.xl() // Extra large vertical spacer
const AppSpacer.horizontalM() // Medium horizontal spacer
const AppSpacer.expanded() // Expanded spacer (fills available space)

// Split View
AppSplitView(
  firstPanel: Text('Left Panel'),
  secondPanel: Text('Right Panel'),
  initialFirstPanelWeight: 0.3, // 30% of the width
  resizable: true,
)

// Multi Split View
AppMultiSplitView(
  panels: [
    AppSplitPanelData(content: Text('Panel 1')),
    AppSplitPanelData(content: Text('Panel 2')),
    AppSplitPanelData(content: Text('Panel 3')),
  ],
)
```

## Customization

Most components accept customization parameters to adapt to specific needs while maintaining design consistency. Common customization options include:

- Colors
- Padding and margins
- Border radius
- Elevation
- Text styles

## Best Practices

1. Use the provided components instead of creating new ones for consistent UI
2. Follow the design system guidelines for spacing, colors, and typography
3. Customize components only when necessary
4. Use the theme constants for any custom widgets
5. Keep the component library updated as the design system evolves
