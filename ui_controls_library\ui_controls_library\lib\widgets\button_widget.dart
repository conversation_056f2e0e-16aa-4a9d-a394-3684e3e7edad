import 'package:flutter/material.dart';

/// A customizable button widget that can be used for calls to action (CTA) and various button types.
///
/// This widget provides a variety of customization options including styles, colors, shapes, icons,
/// and interaction capabilities.
class ButtonWidget extends StatefulWidget {
  /// The text to display on the button.
  final String text;

  /// The background color of the button.
  final Color backgroundColor;

  /// The text color of the button.
  final Color textColor;

  /// The border color of the button. If null, no border is drawn.
  final Color? borderColor;

  /// The width of the border. Only applicable if borderColor is not null.
  final double borderWidth;

  /// The border radius of the button.
  final double borderRadius;

  /// The icon to display before the text. If null, no icon is shown.
  final IconData? icon;

  /// The color of the icon. If null, uses textColor.
  final Color? iconColor;

  /// The size of the icon.
  final double iconSize;

  /// The spacing between the icon and the text.
  final double iconSpacing;

  /// The padding inside the button.
  final EdgeInsetsGeometry padding;

  /// The callback when the button is tapped.
  final VoidCallback? onPressed;

  /// The elevation of the button (shadow).
  final double elevation;

  /// The font size of the text.
  final double fontSize;

  /// The font weight of the text.
  final FontWeight fontWeight;

  /// Whether the button is outlined (transparent background with border).
  final bool isOutlined;

  /// Whether the button is filled (solid background).
  final bool isFilled;

  /// Whether the button has a gradient background.
  final bool hasGradient;

  /// The gradient colors if hasGradient is true.
  final List<Color>? gradientColors;

  /// The gradient begin alignment.
  final Alignment gradientBegin;

  /// The gradient end alignment.
  final Alignment gradientEnd;

  /// Whether the button is disabled.
  final bool isDisabled;

  /// The width of the button. If null, the button will size itself to fit its content.
  final double? width;

  /// The height of the button. If null, the button will size itself to fit its content.
  final double? height;

  /// Whether the button should expand to fill its parent's width.
  final bool isFullWidth;

  /// Whether the button should have a loading indicator.
  final bool isLoading;

  /// The color of the loading indicator.
  final Color? loadingColor;

  /// The size of the loading indicator.
  final double loadingSize;

  /// Whether the button should have a ripple effect when tapped.
  final bool hasRippleEffect;

  /// The color of the ripple effect.
  final Color? rippleColor;

  // Additional Text Styling Properties

  /// The text decoration (underline, line-through, etc.)
  final TextDecoration textDecoration;

  /// How to handle text overflow
  final TextOverflow textOverflow;

  /// Maximum number of lines for the text
  final int? maxLines;

  /// Alignment of the text within the button
  final TextAlign textAlign;

  /// Spacing between letters in the text
  final double letterSpacing;

  /// Spacing between words in the text
  final double wordSpacing;

  /// Direction of the text (LTR, RTL)
  final TextDirection? textDirection;

  // Additional Visual Enhancement Properties

  /// Color of the shadow
  final Color shadowColor;

  /// Offset of the shadow
  final Offset shadowOffset;

  /// Opacity of the entire button (0.0 to 1.0)
  final double opacity;

  /// Brightness adjustment for light/dark themes
  final Brightness? brightness;

  /// Blur radius for the shadow
  final double blurRadius;

  /// Spread radius for the shadow
  final double spreadRadius;

  // Additional Layout Properties

  /// Margin around the button
  final EdgeInsetsGeometry margin;

  /// Alignment within the parent
  final Alignment? alignment;

  /// Transform for rotation, scaling, etc.
  final Matrix4? transform;

  // Additional Animation Properties

  /// Duration for animations
  final Duration animationDuration;

  /// Curve for animations
  final Curve animationCurve;

  /// Type of animation (fade, scale, slide)
  final String animationType;

  // Additional Interaction Properties

  /// Whether long press is enabled
  final bool longPressEnabled;

  /// Callback for long press
  final VoidCallback? onLongPress;

  /// Whether double tap is enabled
  final bool doubleTapEnabled;

  /// Callback for double tap
  final VoidCallback? onDoubleTap;

  /// Color when hovered
  final Color? hoverColor;

  /// Color of the splash effect when tapped
  final Color? splashColor;

  /// Color of the highlight effect when tapped
  final Color? highlightColor;

  /// Color when focused
  final Color? focusColor;

  // Icon Position Properties

  /// Position of the icon (left, right, top, bottom)
  final String iconPosition;

  /// Alignment of the icon
  final Alignment iconAlignment;

  // Additional Loading Properties

  /// Type of loading indicator (circular, linear, custom)
  final String loadingType;

  /// Text to show during loading state
  final String? loadingText;

  // Accessibility Properties

  /// Label for screen readers
  final String? semanticsLabel;

  /// Tooltip for hover
  final String? tooltip;

  /// Whether to enable haptic/audio feedback
  final bool enableFeedback;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the button.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the button gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the button's focus state.
  /// If null, the button will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the button should be focused automatically when it appears
  ///
  /// If true, the button will request focus when it is first built.
  final bool autofocus;

  /// Creates a button widget.
  const ButtonWidget({
    super.key,
    required this.text,
    this.backgroundColor = Colors.blue,
    this.textColor = Colors.white,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.icon,
    this.iconColor,
    this.iconSize = 18.0,
    this.iconSpacing = 8.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
    this.onPressed,
    this.elevation = 0.0,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isOutlined = false,
    this.isFilled = true,
    this.hasGradient = false,
    this.gradientColors,
    this.gradientBegin = Alignment.centerLeft,
    this.gradientEnd = Alignment.centerRight,
    this.isDisabled = false,
    this.width,
    this.height,
    this.isFullWidth = false,
    this.isLoading = false,
    this.loadingColor,
    this.loadingSize = 20.0,
    this.hasRippleEffect = true,
    this.rippleColor,
    // Additional Text Styling Properties
    this.textDecoration = TextDecoration.none,
    this.textOverflow = TextOverflow.clip,
    this.maxLines,
    this.textAlign = TextAlign.center,
    this.letterSpacing = 0.0,
    this.wordSpacing = 0.0,
    this.textDirection,
    // Additional Visual Enhancement Properties
    this.shadowColor = Colors.transparent,
    this.shadowOffset = const Offset(0, 2),
    this.opacity = 1.0,
    this.brightness,
    this.blurRadius = 3.0,
    this.spreadRadius = 0.0,
    // Additional Layout Properties
    this.margin = EdgeInsets.zero,
    this.alignment,
    this.transform,
    // Additional Animation Properties
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.animationType = 'fade',
    // Additional Interaction Properties
    this.longPressEnabled = false,
    this.onLongPress,
    this.doubleTapEnabled = false,
    this.onDoubleTap,
    this.hoverColor,
    this.splashColor,
    this.highlightColor,
    this.focusColor,
    // Icon Position Properties
    this.iconPosition = 'left',
    this.iconAlignment = Alignment.center,
    // Additional Loading Properties
    this.loadingType = 'circular',
    this.loadingText,
    // Accessibility Properties
    this.semanticsLabel,
    this.tooltip,
    this.enableFeedback = true,
    // Advanced Interaction Properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
  });

  /// Creates a ButtonWidget from a JSON map
  factory ButtonWidget.fromJson(Map<String, dynamic> json) {
    // Handle padding
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    if (json['padding'] != null) {
      if (json['padding'] is double) {
        double paddingValue = json['padding'] as double;
        padding = EdgeInsets.all(paddingValue);
      } else if (json['padding'] is Map) {
        Map<String, dynamic> paddingMap = json['padding'] as Map<String, dynamic>;
        padding = EdgeInsets.fromLTRB(
          paddingMap['left'] as double? ?? 12.0,
          paddingMap['top'] as double? ?? 8.0,
          paddingMap['right'] as double? ?? 12.0,
          paddingMap['bottom'] as double? ?? 8.0,
        );
      }
    }

    return ButtonWidget(
      text: json['text'] as String? ?? 'Button',
      backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.blue,
      textColor: _colorFromJson(json['textColor']) ?? Colors.white,
      borderColor: _colorFromJson(json['borderColor']),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      padding: padding,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 0.0,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      isOutlined: json['isOutlined'] as bool? ?? false,
      isFilled: json['isFilled'] as bool? ?? true,
      isDisabled: json['isDisabled'] as bool? ?? false,
      onPressed: json['isClickable'] == true || json['onPressed'] == true ? () {
        debugPrint('Button pressed: ${json['text'] ?? 'Button'}');
      } : null,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'amber': return Colors.amber;
        case 'cyan': return Colors.cyan;
        case 'indigo': return Colors.indigo;
        case 'lime': return Colors.lime;
        case 'teal': return Colors.teal;
        default: return null;
      }
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  @override
  State<ButtonWidget> createState() => _ButtonWidgetState();
}

class _ButtonWidgetState extends State<ButtonWidget> {
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  void _handleHoverChange(bool isHovered) {
    if (isHovered != _isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the background decoration based on configuration
    BoxDecoration decoration;
    if (widget.hasGradient && widget.gradientColors != null && widget.gradientColors!.length >= 2) {
      // Gradient background
      decoration = BoxDecoration(
        gradient: LinearGradient(
          colors: widget.gradientColors!,
          begin: widget.gradientBegin,
          end: widget.gradientEnd,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null || widget.isOutlined
            ? Border.all(
                color: widget.borderColor ?? widget.backgroundColor,
                width: widget.borderWidth,
              )
            : null,
      );
    } else if (widget.isOutlined) {
      // Outlined style (transparent background with border)
      decoration = BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(
          color: widget.borderColor ?? widget.backgroundColor,
          width: widget.borderWidth,
        ),
      );
    } else if (widget.isFilled) {
      // Filled style - matching TagWidget styling
      decoration = BoxDecoration(
        color: widget.isDisabled 
          ? widget.backgroundColor.withAlpha((255 * 0.6).round()) 
          : Color(0xFFf0f5ff), // Light blue background like TagWidget
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Color(0xFF0058FF), // Blue border like TagWidget
          width: widget.borderWidth,
        ),
      );
    } else {
      // Default style
      decoration = BoxDecoration(
        color: widget.isDisabled ?
          widget.backgroundColor.withAlpha((255 * 0.6).round()) : widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
      );
    }

    // Create the button content
    Widget buttonContent = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.isLoading)
          SizedBox(
            width: widget.loadingSize,
            height: widget.loadingSize,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.loadingColor ?? widget.textColor,
              ),
            ),
          )
        else if (widget.icon != null) ...[
          SizedBox(width: 0),
        ],
        Flexible(
          child: Text(
            widget.text,
            overflow: widget.textOverflow,
            maxLines: widget.maxLines,
            textAlign: widget.textAlign,
            style: TextStyle(
              color: Color(0xFF333333), // Dark gray text like TagWidget
              fontSize: _getResponsiveValueFontSize(context),
              fontWeight: FontWeight.w500, // Medium font weight like TagWidget
            ),
          ),
        ),
      ],
    );

    // Wrap in a container with the appropriate size
    if (widget.isFullWidth || widget.width != null || widget.height != null) {
      buttonContent = Container(
        width: widget.isFullWidth ? double.infinity : widget.width,
        height: widget.height,
        alignment: Alignment.center,
        child: buttonContent,
      );
    }

    // Create the final button with Material for elevation and ink effects
    Widget buttonWidget = Material(
      color: Colors.transparent,
      //elevation: widget.isDisabled ? 0 : widget.elevation,
      borderRadius: BorderRadius.circular(widget.borderRadius),
      child: MouseRegion(
        onEnter: (_) => _handleHoverChange(true),
        onExit: (_) => _handleHoverChange(false),
        child: InkWell(
          onTap: widget.isDisabled || widget.isLoading ? null : widget.onPressed,
          onLongPress: widget.longPressEnabled ? widget.onLongPress : null,
          onDoubleTap: widget.doubleTapEnabled ? widget.onDoubleTap : null,
          focusNode: _focusNode,
          focusColor: widget.focusColor,
          hoverColor: widget.hoverColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          splashColor: widget.hasRippleEffect ?
            (widget.rippleColor ?? widget.backgroundColor.withAlpha((255 * 0.3).round())) :
            Colors.transparent,
          highlightColor: widget.hasRippleEffect ?
            (widget.rippleColor ?? widget.backgroundColor.withAlpha((255 * 0.1).round())) :
            Colors.transparent,
          enableFeedback: widget.enableFeedback,
          child: Ink(
            decoration: decoration,
            child: Container(
              padding:  _getResponsivePadding(context),
              child: buttonContent,
            ),
          ),
        ),
      ),
    );

    // Wrap with tooltip if provided
    if (widget.tooltip != null) {
      buttonWidget = Tooltip(
        message: widget.tooltip!,
        child: buttonWidget,
      );
    }

    // Wrap with Semantics for accessibility if semanticsLabel is provided
    if (widget.semanticsLabel != null) {
      buttonWidget = Semantics(
        label: widget.semanticsLabel,
        button: true,
        enabled: !widget.isDisabled,
        child: buttonWidget,
      );
    }

    return buttonWidget;
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 20.0,
      vertical: 10.5,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 20.0,
      vertical: 8.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Default for very small screens
  }
}