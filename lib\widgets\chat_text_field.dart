import 'package:flutter/material.dart';
import '../services/speech_service.dart';
import 'chatgpt_loading_icon.dart';
import 'full_width_typing_indicator.dart';
import '../utils/logger.dart';

class ChatTextField extends StatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final Function(String) onSubmitted;
  final bool isLoading;
  final VoidCallback onCancel;
  final bool enabled;

  const ChatTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.onSubmitted,
    required this.onCancel,
    this.isLoading = false,
    this.enabled = true,
  });

  @override
  State<ChatTextField> createState() => _ChatTextFieldState();
}

class _ChatTextFieldState extends State<ChatTextField> {
  bool _hasText = false;
  final SpeechService _speechService = SpeechService();
  bool _isListening = false;
  bool _isAutoSending = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_updateHasText);
    _initializeSpeechService();
  }

  @override
  void dispose() {
    widget.controller.removeListener(_updateHasText);
    _speechService.dispose();
    super.dispose();
  }

  Future<void> _initializeSpeechService() async {
    await _speechService.initialize();
  }

  void _updateHasText() {
    final hasText = widget.controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  Future<void> _startListening() async {
    Logger.info('Starting speech recognition');

    // Check microphone permission first
    bool hasPermission = await _speechService.checkMicrophonePermission();
    if (!hasPermission) {
      // Request permission
      hasPermission = await _speechService.requestMicrophonePermission();
      if (!hasPermission) {
        // Show error message if permission denied
        _showPermissionDeniedDialog();
        return;
      }
    }

    setState(() {
      _isListening = true;
    });

    await _speechService.startListening(
      onResult: (text) {
        // Update the text field with partial results
        if (text.isNotEmpty) {
          setState(() {
            widget.controller.text = text;
            _hasText = true;
          });
        }
      },
      onFinalResult: (finalText) {
        // Automatically send the message when speech recognition completes with a final result
        Logger.info('Final speech result received, auto-sending: $finalText');

        // Show auto-sending indicator
        setState(() {
          _isAutoSending = true;
        });

        // Add a small delay to allow the UI to update
        Future.delayed(Duration(milliseconds: 800), () {
          widget.onSubmitted(finalText);
          widget.controller.clear();
          setState(() {
            _hasText = false;
            _isAutoSending = false;
          });
        });
      },
      onListeningComplete: () {
        setState(() {
          _isListening = false;
        });
      },
    );
  }

  void _showPermissionDeniedDialog() {
    setState(() {
      _isListening = false;
    });

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Microphone Permission Required'),
          content: Text(
              'This app needs microphone access to convert your speech to text. '
              'Please grant microphone permission in your device settings.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _stopListening() async {
    if (_isListening) {
      await _speechService.stopListening();
      setState(() {
        _isListening = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        widget.isLoading ? FullWidthTypingIndicator() : SizedBox(),
        _isListening || _isAutoSending
            ? Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 8.0),
                color: _isListening
                    ? Theme.of(context).colorScheme.error.withAlpha(25)
                    : Theme.of(context).colorScheme.primary.withAlpha(25),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _isListening ? Icons.mic : Icons.send,
                      color: _isListening
                          ? Theme.of(context).colorScheme.error
                          : Theme.of(context).colorScheme.primary,
                      size: 18,
                    ),
                    SizedBox(width: 8),
                    Text(
                      _isListening ? 'Listening...' : 'Sending message...',
                      style: TextStyle(
                        color: _isListening
                            ? Theme.of(context).colorScheme.error
                            : Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              )
            : SizedBox(),
        Container(
          decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withAlpha(28),
                  blurRadius: 1,
                  offset: Offset(0, -0.5),
                ),
              ],
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30), topRight: Radius.circular(30))),
          padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: widget.controller,
                        enabled: widget.enabled, // Use the enabled parameter
                        decoration: InputDecoration(
                          hintText: widget.hintText,
                          hintStyle: TextStyle(
                            color: widget.enabled
                                ? Theme.of(context).colorScheme.onSurface.withAlpha(153)
                                : Theme.of(context).colorScheme.onSurface.withAlpha(100),
                            fontSize: 16,
                          ),
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 8.0,
                            vertical: 12.0,
                          ),
                        ),
                        style: TextStyle(
                          color: widget.enabled
                              ? Theme.of(context).textTheme.bodyMedium?.color
                              : Theme.of(context).textTheme.bodyMedium?.color?.withAlpha(150),
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        textInputAction: TextInputAction.send,
                        onSubmitted: widget.enabled ? (value) {
                          if (value.trim().isNotEmpty) {
                            widget.onSubmitted(value);
                            widget.controller.clear();
                          }
                        } : null,
                      ),
                    ),
                    // Mic/Send/Loading button
                    Padding(
                      padding: EdgeInsets.only(right: 4.0),
                      child: widget.isLoading
                          ? Container(
                              width: 40,
                              height: 40,
                              padding: EdgeInsets.all(8.0),
                              child: Center(
                                child: ChatGptLoadingIcon(
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 20.0,
                                  onTap: widget.onCancel,
                                ),
                              ),
                            )
                          : IconButton(
                              icon: Icon(
                                _hasText
                                    ? Icons.arrow_upward
                                    : (_isListening
                                        ? Icons.mic_off
                                        : Icons.mic),
                                size: 24,
                                color: !widget.enabled
                                    ? Theme.of(context).colorScheme.onSurface.withAlpha(100)
                                    : _isListening
                                        ? Theme.of(context).colorScheme.error
                                        : null,
                              ),
                              onPressed: widget.enabled ? () {
                                if (_hasText &&
                                    widget.controller.text.trim().isNotEmpty) {
                                  widget.onSubmitted(widget.controller.text);
                                  widget.controller.clear();
                                } else if (_isListening) {
                                  _stopListening();
                                } else {
                                  _startListening();
                                }
                              } : null, // Disable the button when chat is disabled
                              padding: EdgeInsets.zero,
                              constraints: BoxConstraints(
                                minWidth: 40,
                                minHeight: 40,
                              ),
                              color: _hasText
                                  ? Theme.of(context).colorScheme.primary
                                  : _isListening
                                      ? Theme.of(context).colorScheme.error
                                      : Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withAlpha(153),
                            ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
