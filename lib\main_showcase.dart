import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:nsl/ui_components/demo/component_showcase.dart';
import 'ui_components/theme/app_theme.dart';

/// Entry point for the UI component showcase app
void main() {
  runApp(const ShowcaseApp());
}

/// The main app widget for the UI component showcase
class ShowcaseApp extends StatelessWidget {
  const ShowcaseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'UI Component Library',
      debugShowCheckedModeBanner: false,
      localizationsDelegates: [
        FlutterQuillLocalizations.delegate,
      ],
      theme: AppTheme.getThemeData(),
      home: const ComponentShowcase(
        requireAppBar: true,
      ),
      // WorkflowBuilder()
    );
  }
}
