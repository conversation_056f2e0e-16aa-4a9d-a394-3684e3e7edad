import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/file_upload_preview.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/add_button_with_menu.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/hover_button_widget.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/model_selection_dropdown.dart';
import 'package:nsl/theme/spacing.dart';

import '../../../../../utils/logger.dart';
import '../../../../../services/multimedia_service.dart';

class ChatField extends StatefulWidget {
  final double? width;
  final double? height;
  final bool isGeneralLoading; // General chat API loading
  final bool isFileLoading; // File upload/OCR loading
  final bool isSpeechLoading; // Speech-to-text loading
  final Function() onSendMessage;
  final Function()? onCancelRequest;
  final Function(String, String) onFileSelected;
  final Function() onToggleRecording;
  final TextEditingController? controller;
  final MultimediaService? multimediaService;
  final String? initialUploadedFileName;
  final String? initialUploadedFileText;
  final bool initialIsFileUploaded;
  final bool initialIsFileProcessing;
  final VoidCallback? onFileUploadTap;
  final VoidCallback? onFileCloseTap;
  final dynamic parentState;
  Widget? extraWidgets;
  final bool dropdownAbove;
  final bool? isTemp;
  final String? hintText;
  final FocusNode? focusNodeForTextField;

  ChatField({
    super.key,
    this.width,
    this.height,
    this.parentState,
    required this.isGeneralLoading,
    required this.isFileLoading,
    required this.isSpeechLoading,
    required this.onSendMessage,
    this.onCancelRequest,
    required this.onFileSelected,
    required this.onToggleRecording,
    this.controller,
    this.multimediaService,
    this.initialUploadedFileName,
    this.initialUploadedFileText,
    this.initialIsFileUploaded = false,
    this.initialIsFileProcessing = false,
    this.onFileUploadTap,
    this.onFileCloseTap,
    this.extraWidgets,
    this.dropdownAbove = false,
    this.isTemp = false,
    this.hintText,
    this.focusNodeForTextField,
  });

  @override
  State<ChatField> createState() => _ChatFieldState();
}

class _ChatFieldState extends State<ChatField> {
  late TextEditingController chatController;
  late MultimediaService _multimediaService;
  bool isRecording = false;
  bool isFileUploaded = false;
  bool isFileProcessing = false;
  String uploadedFileName = '';
  String uploadedFileText = '';
  final FocusNode _focusNode = FocusNode();
  bool _isShiftPressed = false;

  // Model selection
  late List<ModelInfo> availableModels;
  late ModelInfo selectedModel;

  final FocusNode _focusNodeTextField = FocusNode();

  @override
  void initState() {
    super.initState();
    chatController = widget.controller ?? TextEditingController();
    _multimediaService = widget.multimediaService ?? MultimediaService();

    // Initialize file upload state if provided
    isFileUploaded = widget.initialIsFileUploaded;
    isFileProcessing = widget.initialIsFileProcessing;
    uploadedFileName = widget.initialUploadedFileName ?? '';
    uploadedFileText = widget.initialUploadedFileText ?? '';

    // Initialize model selection
    _initializeModels();

    // Listen for text changes to update hasTextInChatField
    chatController.addListener(_onTextChanged);
  }

  void _initializeModels() {
    availableModels = [
      // ModelInfo(
      //   id: 'NSL 1.0',
      //   name: 'NSL 1.0',
      //   description: 'Powerful, large model for complex challenges',
      //   isPro: true,
      // ),
      // ModelInfo(
      //   id: 'NSL 2.0',
      //   name: 'NSL 2.0',
      //   description: 'Smart, efficient model for everyday use',
      //   isSelected: true,
      // ),
      ModelInfo(
        id: 'GLM 1.0',
        name: 'GLM 1.0',
        description: 'Smart, efficient model for everyday use',
        isSelected: true,
      ),
    ];

    selectedModel = availableModels.firstWhere(
      (model) => model.isSelected,
      orElse: () => availableModels.first,
    );
  }

  @override
  void dispose() {
    // Only dispose the controller if we created it internally
    if (widget.controller == null) {
      chatController.dispose();
    }
    chatController.removeListener(_onTextChanged);
    _focusNode.dispose();
    if(_focusNodeTextField!=null) {
      _focusNodeTextField.dispose();
    }

    super.dispose();
  }

  @override
  void didUpdateWidget(ChatField oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update file upload state when parent widget changes
    // if (widget.initialIsFileUploaded != oldWidget.initialIsFileUploaded ||
    //     widget.initialUploadedFileName != oldWidget.initialUploadedFileName ||
    //     widget.initialUploadedFileText != oldWidget.initialUploadedFileText) {

    // }
    setState(() {
      isFileUploaded = widget.initialIsFileUploaded;
      isFileProcessing = widget.initialIsFileProcessing;
      uploadedFileName = widget.initialUploadedFileName ?? '';
      uploadedFileText = widget.initialUploadedFileText ?? '';
    });
    // // Update the web layout flag in the provider
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (mounted) {
    //     if (isFileUploaded) {
    //       _sendMessage();
    //     }
    //   }
    // });

    Logger.info('ChatField: File upload state updated from parent');
  }

  void _onTextChanged() {
    // Force a rebuild when text changes to update UI
    setState(() {});
  }

  bool get hasTextInChatField => chatController.text.trim().isNotEmpty;

  void _sendMessage() {
    // Guard against duplicate calls - check if general loading is active
    if (widget.isGeneralLoading) {
      Logger.info(
          'ChatField._sendMessage called but general loading is active, ignoring duplicate call');
      return;
    }

    // Clean any trailing newlines or whitespace before sending
    final cleanedText = chatController.text.trim();
    if (widget.isTemp ?? false) {
      widget.onSendMessage();
      return;
    } else {
      if (cleanedText.isNotEmpty) {
        // Update the controller with cleaned text
        chatController.text = cleanedText;
        widget.onSendMessage();
      }
    }
  }

  void _cancelRequest() {
    if (widget.onCancelRequest != null) {
      widget.onCancelRequest!();
    }
  }

  void _toggleRecording() {
    setState(() {
      isRecording = !isRecording;
    });
    widget.onToggleRecording();
  }

// Build a full-width recording UI for web audio recorder
  Widget _buildFullWidthRecordingUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: _multimediaService.createWebAudioRecorderWidget(
        chatController: chatController,
        onCancel: () {
          setState(() {
            isRecording = false;
          });
        },
        onLoadingChanged: (loading) {
          widget.parentState.setState(() {
            widget.parentState.isAudioLoading = loading;
          });
        },
      ),
    );
  }

  Widget hoverButtons({required Widget icon, required Function()? onPressed}) {
    return HoverButton(
      icon: icon,
      onPressed: onPressed,
    );
  }

  @override
  Widget build(BuildContext context) {
    // If recording is active, show a full-width recording UI
    if (isRecording && _multimediaService.shouldUseWebAudioRecorder()) {
      return Container(
        width: widget.width,
        height: widget.height,
        margin: EdgeInsets.only(
          bottom: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSpacing.md),
          border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
        ),
        // constraints: BoxConstraints(
        //   maxHeight: 400,
        //   minHeight: widget.height ?? 110,
        // ),
        child: _buildFullWidthRecordingUI(context),
      );
    }

    Widget icon = Container();
    Function() onPressed = () {};

    if (!hasTextInChatField && widget.isGeneralLoading) {
      icon = Icon(Icons.stop);
      onPressed = _cancelRequest;
    } else if (hasTextInChatField && !widget.isGeneralLoading) {
      icon = Icon(Icons.arrow_upward);
      onPressed = _sendMessage;
    } else if (!hasTextInChatField && widget.isSpeechLoading) {
      icon = Icon(Icons.stop);
      onPressed = _cancelRequest;
    } else if (!hasTextInChatField && !widget.isSpeechLoading) {
      icon = Icon(Icons.mic_none);
      onPressed = _toggleRecording;
    }
    // Otherwise, show the normal chat field
    return Container(
      margin: EdgeInsets.only(
        bottom: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      constraints: BoxConstraints(
        maxHeight: 400,
        minHeight: widget.height ?? 110,
      ),
      padding: EdgeInsets.only(
          top: AppSpacing.xxs, left: AppSpacing.xxs, right: AppSpacing.xxs),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Show file upload preview if a file is uploaded
          if (isFileUploaded)
            FileUploadPreview(
              fileName: uploadedFileName,
              isLoading: widget.isFileLoading || isFileProcessing,
              onFileTap: widget.onFileUploadTap ?? () {},
              onClose: () {
                setState(() {
                  isFileUploaded = false;
                  isFileProcessing = false;
                  uploadedFileName = '';
                  uploadedFileText = '';
                  // Clear the text field if it contains the OCR text
                  if (chatController.text.contains(uploadedFileText)) {
                    chatController.clear();
                  }
                });

                // Clear pending file in parent state for temp mode
                if (widget.isTemp ?? false) {
                  widget.parentState.setState(() {
                    widget.parentState.pendingFile = null;
                    widget.parentState.isFileUploaded = false;
                    widget.parentState.isFileProcessing = false;
                    widget.parentState.uploadedFileName = '';
                    widget.parentState.uploadedFileText = '';
                  });
                }
              },
            ),

          Flexible(
            child: KeyboardListener(
              focusNode: _focusNode,
              onKeyEvent: (KeyEvent event) {
                if (event is KeyDownEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

                  if (event.logicalKey == LogicalKeyboardKey.enter) {
                    if (!_isShiftPressed && !widget.isGeneralLoading) {
                      // Enter alone = send message
                      _sendMessage();
                    }
                    // Shift+Enter = allow new line (don't consume)
                  }
                } else if (event is KeyDownEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
                }
              },
              child: TextField(

                cursorHeight: 14,
                controller: chatController,
                maxLines: null,
                enabled: !widget.isGeneralLoading,
                focusNode: widget.focusNodeForTextField?? _focusNodeTextField,
                inputFormatters: [
                  // Custom formatter to handle newlines properly
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    // If we're not allowing multiline (no shift pressed) and there's a newline
                    if (!_isShiftPressed && newValue.text.contains('\n')) {
                      // Remove the newline but DON'T send message here to avoid duplicate calls
                      // The RawKeyboardListener will handle the send message action
                      final cleanText = newValue.text.replaceAll('\n', '');
                      return TextEditingValue(
                        text: cleanText,
                        selection:
                            TextSelection.collapsed(offset: cleanText.length),
                      );
                    }
                    return newValue;
                  }),
                ],

                decoration: InputDecoration(
                  focusColor: Colors.transparent,
                  hintStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey,
                    fontFamily: "TiemposText",
                  ),
                  hintText: widget.isGeneralLoading
                      ? AppLocalizations.of(context)
                          .translate('home.sendingMessage')
                      :widget.hintText?? AppLocalizations.of(context).translate('home.askNSL'),
                  hoverColor: Colors.transparent,
                  border: OutlineInputBorder(borderSide: BorderSide.none),
                  enabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  focusedBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
                  disabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                ),
                // onSubmitted removed to prevent duplicate calls with RawKeyboardListener
                onSubmitted: null,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  // Add button for file upload
                  AddButtonWithMenu(widget.parentState),
                  SizedBox(width: AppSpacing.xs),
                  if (widget.extraWidgets != null) widget.extraWidgets!,
                ],
              ),
              Row(
                children: [
                  // Model selection dropdown
                  ModelSelectionDropdown(
                    models: availableModels,
                    selectedModel: selectedModel,
                    dropdownAbove: widget.dropdownAbove,
                    onModelSelected: (ModelInfo model) {
                      setState(() {
                        selectedModel = model;
                        // Update the selected state in the models list
                        availableModels = availableModels
                            .map((m) => ModelInfo(
                                  id: m.id,
                                  name: m.name,
                                  description: m.description,
                                  isPro: m.isPro,
                                  isSelected: m.id == model.id,
                                ))
                            .toList();
                      });
                      Logger.info('Selected model: ${model.name}');
                    },
                  ),
                  SizedBox(width: AppSpacing.xs),

                  // if (isRecording &&
                  //     !_multimediaService.shouldUseWebAudioRecorder())
                  //   // Show speech recognition UI when recording (non-web platforms only)
                  //   Row(
                  //     children: [
                  //       SpeechRecognitionWidget(
                  //         height: widget.height,
                  //         width: widget.width,
                  //         recognizedText: _recognizedText,
                  //         onCancel: () {
                  //           _multimediaService.stopSpeechRecognition();
                  //         },
                  //         onConfirm: () {
                  //           // Get the latest recognized text directly from the service
                  //           final latestText =
                  //               _multimediaService.getRecognizedText();

                  //           if (latestText.isNotEmpty) {
                  //             setState(() {
                  //               // Update the chat field with the latest recognized text
                  //               chatController.text = latestText;

                  //               // Also update our local state
                  //               _recognizedText = latestText;
                  //             });
                  //             Logger.info(
                  //                 "Setting chat field text to: $latestText");
                  //           } else {
                  //             Logger.info("No recognized text available");
                  //           }
                  //           _multimediaService.stopSpeechRecognition();
                  //         },
                  //       ),
                  //     ],
                  //   ),
                  // Row(children: [
                  //   // Show mic button - uses speech loading state
                  //   hoverButtons(
                  //       icon: widget.isSpeechLoading
                  //           ? Icon(Icons.stop)
                  //           : Icon(Icons.mic_none),
                  //       onPressed: widget.isSpeechLoading
                  //           ? _cancelRequest
                  //           : _toggleRecording),

                  //   // Show send button only when there's text - uses general loading state
                  //   if (hasTextInChatField)
                  //     hoverButtons(
                  //       icon: widget.isGeneralLoading
                  //           ? Icon(Icons.stop) // Show stop icon when loading
                  //           : Icon(Icons.arrow_upward),
                  //       onPressed: widget.isGeneralLoading
                  //           ? _cancelRequest
                  //           : _sendMessage,
                  //     ),
                  // ]),

                  hoverButtons(icon: icon, onPressed: onPressed),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }
}
