/// Model class for OCR API response
class OcrResponse {
  /// Whether the OCR processing was successful
  final bool success;
  
  /// The extracted text from the image
  final String text;
  
  /// The language of the extracted text
  final String language;
  
  /// Any error message if the OCR processing failed
  final String? errorMessage;

  /// Constructor
  OcrResponse({
    required this.success,
    required this.text,
    required this.language,
    this.errorMessage,
  });

  /// Factory constructor to create an OcrResponse from a JSON map
  factory OcrResponse.fromJson(Map<String, dynamic> json) {
    return OcrResponse(
      success: json['success'] ?? false,
      text: json['text'] ?? '',
      language: json['language'] ?? 'en',
      errorMessage: json['error_message'],
    );
  }

  /// Convert this OcrResponse to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'text': text,
      'language': language,
      if (errorMessage != null) 'error_message': errorMessage,
    };
  }
  
  /// Get a string representation of this OcrResponse
  @override
  String toString() {
    return 'OcrResponse{success: $success, text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}, language: $language, errorMessage: $errorMessage}';
  }
}
