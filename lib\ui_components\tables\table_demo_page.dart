import 'package:flutter/material.dart';
import 'dynamic_table_widget.dart'; // Import the dynamic table widget

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dynamic Table Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: TableDemoPage(),
    );
  }
}

class TableDemoPage extends StatefulWidget {
  const TableDemoPage({super.key});

  @override
  _TableDemoPageState createState() => _TableDemoPageState();
}

class _TableDemoPageState extends State<TableDemoPage> {
  late List<Map<String, dynamic>> _sampleData;
  late List<DynamicTableColumn> _columns;

  @override
  void initState() {
    super.initState();

    // Define sample columns
    _columns = [
      DynamicTableColumn(
        id: 'id',
        title: 'ID',
        type: DynamicColumnType.text,
        initialWidth: 80,
        editable: false,
      ),
      DynamicTableColumn(
        id: 'name',
        title: 'Name',
        type: DynamicColumnType.text,
        description: 'Full name of the person',
        initialWidth: 150,
        summaryType: ColumnSummaryType.count,
      ),
      DynamicTableColumn(
        id: 'age',
        title: 'Age',
        type: DynamicColumnType.number,
        initialWidth: 100,
        summaryType: ColumnSummaryType.average,
      ),
      DynamicTableColumn(
        id: 'email',
        title: 'Email',
        type: DynamicColumnType.text,
        initialWidth: 200,
      ),
      DynamicTableColumn(
        id: 'active',
        title: 'Active',
        type: DynamicColumnType.boolean,
        initialWidth: 80,
        summaryType: ColumnSummaryType.count,
      ),
      DynamicTableColumn(
        id: 'joinDate',
        title: 'Join Date',
        type: DynamicColumnType.date,
        initialWidth: 120,
      ),
      DynamicTableColumn(
        id: 'salary',
        title: 'Salary',
        type: DynamicColumnType.number,
        initialWidth: 120,
        summaryType: ColumnSummaryType.sum,
        conditionalFormatting: [
          ConditionalFormat(
            predicate: (value) => value != null && (value as num) > 70000,
            textColor: Colors.green,
          ),
          ConditionalFormat(
            predicate: (value) => value != null && (value as num) < 50000,
            textColor: Colors.red,
          ),
        ],
      ),
    ];

    // Generate sample data
    _sampleData = List.generate(20, (index) {
      return {
        'id': 'EMP${1000 + index}',
        'name': 'Person ${index + 1}',
        'age': 25 + (index % 30),
        'email': 'person${index + 1}@example.com',
        'active': index % 4 != 0,  // Some inactive users
        'joinDate': DateTime.now().subtract(Duration(days: 30 * (index + 1))),
        'salary': 40000 + (index * 2500),
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Dynamic Table Demo'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Interactive Data Table with Complete User Controls',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
          ),
          Expanded(
            child: DynamicTableWidget(
              data: _sampleData,
              columns: _columns,
              config: DynamicTableConfig(
                allowRowSelection: true,
                allowRowDelete: true,
                allowRowReordering: true,
                allowAddRow: true,
                allowColumnResize: true,
                allowColumnDelete: true,
                allowAddColumn: true,
                allowViewTypeToggle: true,
                allowInlineEdit: true,
                allowPermalink: true,
                showToolbar: true,
                showRowActions: true,
                showColumnSummary: true,
                defaultRowHeight: 45,
                initialViewType: ViewType.table,
                tableId: 'employee-table',
              ),
              onDataChanged: (updatedData) {
                setState(() {
                  _sampleData = updatedData;
                });
                print('Data updated, total rows: ${updatedData.length}');
              },
              onRowExpand: (row) {
                print('Row expanded: ${row['name']}');
              },
              onRowDelete: (row) {
                print('Row deleted: ${row['name']}');
              },
              expandedRowBuilder: (row) {
                // Build expanded row content
                return Card(
                  margin: EdgeInsets.all(8),
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Detailed Information for ${row['name']}',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                        SizedBox(height: 8),
                        Text('ID: ${row['id']}'),
                        Text('Email: ${row['email']}'),
                        Text('Age: ${row['age']}'),
                        Text('Status: ${row['active'] ? 'Active' : 'Inactive'}'),
                        Text('Joined: ${row['joinDate']?.toString().split(' ')[0] ?? 'N/A'}'),
                        Text('Salary: \$${row['salary']}'),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}