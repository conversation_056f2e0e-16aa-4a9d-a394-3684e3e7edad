import 'package:flutter/material.dart';
import '../../ui_components/theme/app_theme.dart';
import '../../widgets/common/nsl_knowledge_loader.dart';
import '../../l10n/app_localizations.dart';

/// A base authentication screen that provides common UI elements and layout
/// for login and registration screens.
class BaseAuthScreen extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget form;
  final bool isLoading;
  final String? errorMessage;

  const BaseAuthScreen({
    super.key,
    required this.title,
    required this.subtitle,
    required this.form,
    required this.isLoading,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return NSLKnowledgeLoaderWrapper(
      isLoading: isLoading,
      
      child: SafeArea(
        child: Scaffold(
            body: Stack(
          children: [
            // Main content
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Logo or App Name
                    Text(
                      'NSL',
                      style: Theme.of(context).textTheme.displayLarge?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
      
                    // Title Text
                    Text(
                      title,
                      style: Theme.of(context).textTheme.headlineMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingXs),
      
                    // Subtitle Text
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withAlpha(178),
                          ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingXl),
      
                    // // Error Message (if any)
                    // if (errorMessage != null) ...[
                    //   Container(
                    //     padding: const EdgeInsets.all(AppTheme.spacingM),
                    //     decoration: BoxDecoration(
                    //       color: AppTheme.errorColor.withAlpha(25),
                    //       borderRadius:
                    //           BorderRadius.circular(AppTheme.borderRadiusM),
                    //     ),
                    //     child: Text(
                    //       errorMessage!,
                    //       style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    //             color: AppTheme.errorColor,
                    //           ),
                    //       textAlign: TextAlign.center,
                    //     ),
                    //   ),
                    //   const SizedBox(height: AppTheme.spacingM),
                    // ],
      
                    // Form content
                    form,
                  ],
                ),
              ),
            ),
      
            // Full-screen loading overlay
            // if (isLoading)
            //   NSLKnowledgeLoaderWrapper(
            //     text: title == 'Welcome Back'
            //         ? context.tr('auth.signingIn')
            //         : context.tr('auth.registering'),
            //     color: Theme.of(context).colorScheme.primary,
            //     indicatorSize: 50,
            //     fontSize: 16,
            //   ),
          ],
        )),
      ),
    );
  }
}
