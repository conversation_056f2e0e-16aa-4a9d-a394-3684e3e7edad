import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/library_container_mobile.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';

/// Mobile version of the Create Book screen
/// This screen allows users to create new books/projects on mobile devices
class CreateBookMobile extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const CreateBookMobile({
    super.key,
    this.initialData,
  });

  @override
  State<CreateBookMobile> createState() => _CreateBookMobileState();
}

class _CreateBookMobileState extends State<CreateBookMobile> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Industry dropdown options
  final List<String> _industries = [
    'E-commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology'
  ];
  String? _selectedIndustry;

  @override
  void initState() {
    super.initState();
    // Initialize with data if provided
    if (widget.initialData != null) {
      _nameController.text = widget.initialData!['name'] ?? '';
      _descriptionController.text = widget.initialData!['description'] ?? '';
      _selectedIndustry = widget.initialData!['industry'];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _showIndustryBottomSheet(FormFieldState<String> field) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => IndustryBottomSheet(
        industries: _industries,
        selectedIndustry: _selectedIndustry,
        onIndustrySelected: (industry) {
          setState(() {
            _selectedIndustry = industry;
          });
          field.didChange(industry);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Always return the create book view directly since navigation is handled by MobileNavigationWrapper
    return _buildCreateBookView();
  }

  // Extract the original build content into a separate method
  Widget _buildCreateBookView() {
    return Scaffold(
      drawer: const CustomDrawer(),
      appBar: AppBar(
        backgroundColor: Color(0xfff6f6f6),
        surfaceTintColor: Colors.transparent,
        foregroundColor: Colors.black,
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        title: Row(
          children: [
            // Hamburger menu icon
            Builder(
              builder: (context) => IconButton(
                icon: const Icon(Icons.menu, color: Colors.black, size: 24),
                onPressed: () => Scaffold.of(context).openDrawer(),
                padding: const EdgeInsets.symmetric(horizontal: 16),
              ),
            ),
            // Expanded widget to center the title
            Expanded(
              child: Text(
                AppLocalizations.of(context)
                    .translate('bookdetails.createYourBook'),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'TiemposText',
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            // Invisible spacer to balance the layout (same width as menu icon)
            const SizedBox(width: 56), // IconButton default width
          ],
        ),
      ),
      backgroundColor: Color(0xfff6f6f6),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(28.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),

                // Project Name Field
                Text(
                  AppLocalizations.of(context).translate('bookdetails.name'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'TiemposText',
                    color: Color(0xff000000),
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 16),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: const BorderSide(
                        color: Color(0xff0058FF),
                        width: 1,
                      ),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a project name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Industry Dropdown Field
                Text(
                  AppLocalizations.of(context)
                      .translate('bookdetails.industry'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'TiemposText',
                    color: Color(0xff000000),
                  ),
                ),
                const SizedBox(height: 8),
                FormField<String>(
                  initialValue: _selectedIndustry,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select an industry';
                    }
                    return null;
                  },
                  builder: (FormFieldState<String> field) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () => _showIndustryBottomSheet(field),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(
                                color: field.hasError
                                    ? Colors.red
                                    : Color(0xffCCCCCC),
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  _selectedIndustry ??
                                      AppLocalizations.of(context)
                                          .translate('bookdetails.industry'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'TiemposText',
                                    color: _selectedIndustry != null
                                        ? Colors.black
                                        : Colors.grey[600],
                                  ),
                                ),
                                const Icon(Icons.arrow_drop_down),
                              ],
                            ),
                          ),
                        ),
                        if (field.hasError)
                          Padding(
                            padding:
                                const EdgeInsets.only(top: 8.0, left: 12.0),
                            child: Text(
                              field.errorText!,
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 12,
                              ),
                            ),
                          ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 24),

                // Description Field
                Text(
                  AppLocalizations.of(context)
                      .translate('bookdetails.descriptionAboutTheProject'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'TiemposText',
                    color: Color(0xff000000),
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 4,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 16),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: const BorderSide(
                        color: Color(0xff0058FF),
                        width: 1,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Start Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        // Create a data object with form values
                        final bookData = {
                          'name': _nameController.text,
                          'description': _descriptionController.text,
                          'industry': _selectedIndustry,
                        };

                        // Navigate to LibraryContainerMobile with Books tab selected
                        debugPrint('Book Data: $bookData');
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LibraryContainerMobile(
                                initialTabIndex: 0),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xff0058FF),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)
                          .translate('bookdetails.start'),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class IndustryBottomSheet extends StatefulWidget {
  final List<String> industries;
  final String? selectedIndustry;
  final Function(String) onIndustrySelected;

  const IndustryBottomSheet({
    super.key,
    required this.industries,
    required this.selectedIndustry,
    required this.onIndustrySelected,
  });

  @override
  State<IndustryBottomSheet> createState() => _IndustryBottomSheetState();
}

class _IndustryBottomSheetState extends State<IndustryBottomSheet> {
  List<String> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.industries);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Select Industry',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Icon(
                    Icons.close,
                    size: 24,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Industry List
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final industry = _filteredItems[index];
                return IndustryBottomSheetMenuItem(
                  text: industry,
                  isSelected: industry == widget.selectedIndustry,
                  onTap: () => widget.onIndustrySelected(industry),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class IndustryBottomSheetMenuItem extends StatefulWidget {
  final String text;
  final VoidCallback onTap;
  final bool isSelected;

  const IndustryBottomSheetMenuItem({
    super.key,
    required this.text,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  State<IndustryBottomSheetMenuItem> createState() =>
      _IndustryBottomSheetMenuItemState();
}

class _IndustryBottomSheetMenuItemState
    extends State<IndustryBottomSheetMenuItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFF0F0F0), width: 0.5),
        ),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        title: Text(
          widget.text,
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'TiemposText',
            color: Colors.black,
            fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        trailing: widget.isSelected
            ? Icon(
                Icons.check,
                color: Color(0xff0058FF),
                size: 20,
              )
            : null,
        onTap: widget.onTap,
      ),
    );
  }
}
