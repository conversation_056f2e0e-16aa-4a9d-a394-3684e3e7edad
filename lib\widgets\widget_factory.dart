import 'package:flutter/material.dart';
import '../models/widget_render_models.dart';
import '../services/widget_renderer_service.dart';
import 'dashboard/kpi_card_group.dart';
import 'dashboard/chart_container.dart';
import 'dashboard/data_table_widget.dart';

/// Factory for creating widgets based on their type and configuration
class WidgetFactory {
  /// Creates a widget based on its type and configuration
  static Widget createWidget(ComponentModel component) {
    switch (component.widget) {
      case 'KpiCardGroup':
        return createKpiCardGroup(component);
      case 'ChartContainer':
        return createChartContainer(component);
      case 'DataTable':
        return createDataTable(component);
      default:
        return Center(
          child: Text('Unknown widget type: ${component.widget}'),
        );
    }
  }

  /// Creates a KPI card group widget
  static Widget createKpiCardGroup(ComponentModel component) {
    final config = component.config;
    final title = config['title'] as String;
    final cards = config['cards'] as List;
    final dataSource =
        WidgetRendererService.getDataForSource('summary_metrics');

    return SizedBox(
      width: 800, // Fixed width for KPI card group
      child: KpiCardGroup(
        title: title,
        cards: cards.map((card) {
          final label = card['label'] as String;
          final dataField = card['data_field'] as String;
          final format = card['format'] as String;
          final icon = card['icon'] as String;
          final comparison = card['comparison'] as Map<String, dynamic>?;

          return KpiCardData(
            label: label,
            value: dataSource[dataField],
            format: format,
            icon: icon,
            comparisonLabel: comparison?['label'],
            comparisonValue: comparison != null
                ? dataSource[comparison['data_field']]
                : null,
          );
        }).toList(),
      ),
    );
  }

  /// Creates a chart container widget
  static Widget createChartContainer(ComponentModel component) {
    final config = component.config;
    final title = config['title'] as String;
    final chartConfig = config['chart_config'] as Map<String, dynamic>;
    final chartType = chartConfig['type'] as String;
    final dataSourceId = chartConfig['data_source'] as String;
    final data = WidgetRendererService.getDataForSource(dataSourceId);

    // Set fixed dimensions for the chart container
    return SizedBox(
      width: 800, // Fixed width for chart (same as other widgets)
      height: 400, // Fixed height for chart
      child: ChartContainer(
        title: title,
        chartType: chartType,
        data: data,
        chartConfig: chartConfig,
      ),
    );
  }

  /// Creates a data table widget
  static Widget createDataTable(ComponentModel component) {
    final config = component.config;
    final title = config['title'] as String;
    final dataSourceId = config['data_source'] as String;
    final data = WidgetRendererService.getDataForSource(dataSourceId);
    final columns = config['columns'] as List;
    final pagination = config['pagination'] as bool;
    final rowsPerPage = config['rows_per_page'] as int;

    return SizedBox(
      width: 800, // Wider to accommodate table content
      height: 500, // Taller to show more rows
      child: DataTableWidget(
        title: title,
        data: data,
        columns:
            columns.map((column) => DataTableColumn.fromJson(column)).toList(),
        pagination: pagination,
        rowsPerPage: rowsPerPage,
      ),
    );
  }
}
