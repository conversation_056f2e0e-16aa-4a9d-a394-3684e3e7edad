import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/screens/web/new_design/widgets/modern_side_panel.dart';
import '../../../models/nsl_hierarchy_model.dart';
import '../../../widgets/resizable_panel.dart';
import 'widgets/unified_organizational_chart.dart';
import 'widgets/nsl_details_panel.dart';
import 'widgets/go_lo_list_panel.dart';

class TreeHierarchyModel extends StatefulWidget {
  const TreeHierarchyModel({super.key});

  @override
  State<TreeHierarchyModel> createState() => _TreeHierarchyModelState();
}

class _TreeHierarchyModelState extends State<TreeHierarchyModel> {
  List<NSLHierarchyData> _nslNodes = [];
  NSLNode? _rootNode;
  NSLNode? _filteredRootNode;
  NSLHierarchyData? _selectedNode;
  String? _selectedNodeId; // Track selected node ID for blue background
  bool _showSidePanel = false;
  int _clearSelectionTrigger = 0; // Trigger to clear selection in ModernSidePanel
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  String _searchQuery = '';
  Map<String, dynamic>? _organizationalStructure;
  Map<String, dynamic>? _systemInfo;
   final ScrollController _horizontalScrollController = ScrollController();

  // New state for multiple GO/LO panels
  List<GoLoPanel> _activeGoLoPanels = [];


  
  // Side panel dimensions - calculated as 48% of screen width
  double _sidePanelWidth = 0.0; // Will be updated in build method
  double _minSidePanelWidth = 0.0; // Will be updated in build method
  double _maxSidePanelWidth = 0.0; // Will be updated in build method

  @override
  void initState() {
    super.initState();
    _loadNSLData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.trim();
      _buildFilteredTree();
    });
  }
  
  Future<void> _loadNSLData() async {
    try {
      // Load JSON data from assets - using the new revised hierarchy file
      final String jsonString = await rootBundle.loadString('assets/data/nsl_revised_hierarchy.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);

      // Store organizational structure for the departments panel
      _organizationalStructure = jsonData['organizational_structure'];
      _systemInfo = jsonData['system_info'];

      // Parse the NSL hierarchy structure
      _rootNode = NSLHierarchyBuilder.buildHierarchyFromNSLData(jsonData);
      
      if (_rootNode != null) {
        _nslNodes = _rootNode!.originalData.getAllNodes(); // Flatten for search
      }

      // Build filtered tree
      _buildFilteredTree();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading NSL data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _buildFilteredTree() {
    if (_searchQuery.isEmpty) {
      _filteredRootNode = _rootNode;
    } else {
      // Find node by title or ID in the flattened list
      final foundNodes = _nslNodes
          .where((node) =>
              node.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              node.id.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();

      if (foundNodes.isNotEmpty) {
        // For nested structure, we need to find the node in the tree and show its subtree
        _filteredRootNode = _findNodeInTree(_rootNode, foundNodes.first.id);
      } else {
        _filteredRootNode = null;
      }
    }
  }

  // Helper method to find a node in the tree and return its subtree
  NSLNode? _findNodeInTree(NSLNode? node, String nodeId) {
    if (node == null) return null;
    
    if (node.id == nodeId) {
      return node;
    }
    
    for (var child in node.children) {
      final found = _findNodeInTree(child, nodeId);
      if (found != null) {
        return found;
      }
    }
    
    return null;
  }

  void _onNodeTitleTap(NSLNode node) {
    // Title tap - opens side panel and sets selection
    setState(() {
      _selectedNode = node.originalData;
      _selectedNodeId = node.id; // Set selected node ID for blue background
      _showSidePanel = true;
      _activeGoLoPanels.clear();
       _clearSelectionTrigger++;
    });
  }

  void _onNodeInfoTap(NSLNode node) {
    // Info tap - handled by the chart widget for expansion
    // This is called when info container is tapped but the expansion
    // logic is handled in the NSLHierarchyChart widget
    print('Info tapped for node: ${node.title}');
  }

  void _hideSidePanel() {
    setState(() {
      _showSidePanel = false;
      _selectedNode = null;
      _selectedNodeId = null; // Clear selection when side panel closes
      _activeGoLoPanels.clear(); // Close ALL GO/LO panels when main panel closes
      _clearSelectionTrigger++; // Increment trigger to clear selection in ModernSidePanel
    });
  }

  // Callback for arrow tap - opens new GO/LO panel
  void _onArrowTap(String metricType) {
    setState(() {
    // CLEAR all existing panels first
    _activeGoLoPanels.clear();
    
    // Then add the new panel
    _activeGoLoPanels.add(GoLoPanel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      metricType: metricType,
    ));
  });
  }

  // Close specific GO/LO panel
  void _closeGoLoPanel(String panelId) {
    setState(() {
      _activeGoLoPanels.removeWhere((panel) => panel.id == panelId);
      _clearSelectionTrigger++; // Increment trigger to clear blue background in ModernSidePanel
    });
  }

  // Clear arrow selection when GO/LO panel is closed
  void _clearArrowSelection() {
    // This will be called when GO/LO panels are closed to clear the blue background
    // The ModernSidePanel will handle clearing its own selection state
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Calculate side panel dimensions as 48% of screen width
    final screenWidth = MediaQuery.of(context).size.width;
    final desiredSidePanelWidth = screenWidth * 0.48;
    
    // Update side panel dimensions
    _sidePanelWidth = desiredSidePanelWidth;
    _minSidePanelWidth = desiredSidePanelWidth * 0.8; // 80% of desired as minimum
    _maxSidePanelWidth = desiredSidePanelWidth * 1.2; // 120% of desired as maximum
  final totalGoLoPanelsWidth = screenWidth * 0.16 * _activeGoLoPanels.length;
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Main content row
          Row(
            children: [
              // Unified organizational chart (combines left panel + hierarchy chart)
              Expanded(
                flex: _showSidePanel ? 3 : 1,
                child: _filteredRootNode != null
                    ? UnifiedOrganizationalChart(
                        organizationalStructure: _organizationalStructure,
                        rootNode: _filteredRootNode!,
                        selectedNodeId: _selectedNodeId, // Pass selected node ID
                        isSidePanelOpen: _showSidePanel, // Pass side panel state
                        sidePanelWidth: _sidePanelWidth, // Pass side panel width
                        onNodeTitleTap: _onNodeTitleTap,
                        onNodeInfoTap: _onNodeInfoTap,
                      )
                    : _buildEmptyState(),
              ),
              // Side panel for NSL node details
              if (_showSidePanel && _selectedNode != null)
                SizedBox(
                   width: _sidePanelWidth, 
                  child: ModernSidePanel(
                    nodeData: _selectedNode,
                    dateRange: _systemInfo?['default_time_range'],
                    onClose: _hideSidePanel,
                    onArrowTap: _onArrowTap, // Pass arrow tap callback
                    onGoLoPanelClosed: _clearArrowSelection, // Pass callback to clear selection
                    clearSelectionTrigger: _clearSelectionTrigger, // Pass trigger to clear selection
                  ),
                ),
              
              // Multiple GO/LO Panels (16% each)
              ..._activeGoLoPanels.map((panel) => 
                Container(
                  width: screenWidth * 0.16,
                  child: GoLoListPanel(
                    panelId: panel.id,
                    metricType: panel.metricType,
                    onClose: () => _closeGoLoPanel(panel.id),
                  ),
                ),
              ).toList(),
            ],
          ),
          
          // Positioned close button - floats over content
          // if (_showSidePanel && _selectedNode != null)
          //   Positioned(
          //     top: 20 ,
          //      right: _sidePanelWidth + totalGoLoPanelsWidth + 8,  // Position it just outside the side panel
          //     child: Container(
              
          //       padding:EdgeInsets.symmetric(horizontal: 5,vertical:1),
          //       decoration: BoxDecoration(
          //           color: Colors.white,
          //              border: Border.all(color: Color(0xFFB4B4B4)),
          //               borderRadius: BorderRadius.circular(4),
          //             ),
          //       child: InkWell(
          //        // borderRadius: BorderRadius.circular(8),
          //         onTap: _hideSidePanel,
          //         child: Container(
          //          padding:EdgeInsets.symmetric(horizontal: 10,vertical:5),
          //           child: Text('Close',
          //           style: TextStyle(
          //           fontSize: 10,
          //       color: Colors.black,
          //       fontFamily: 'TiemposText',)),
          //         ),
          //       ),
          //     ),
          //   ),
       
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Title
          Text(
            'NSL Hierarchy',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: Colors.black,
            ),
          ),

          const Spacer(),

          // Search field
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by title or ID...',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                  fontFamily: 'TiemposText',
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.grey.shade500,
                  size: 20,
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: Colors.grey.shade500,
                          size: 20,
                        ),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty
                ? 'No NSL hierarchy data available'
                : 'No nodes found for "$_searchQuery"',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontFamily: 'TiemposText',
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                _searchController.clear();
              },
              child: Text(
                'Clear search',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
