class GlobalObjective {
  final String objectiveId;
  final String name;
  final String tenantId;
  final String version;
  final String status;

  GlobalObjective({
    required this.objectiveId,
    required this.name,
    required this.tenantId,
    required this.version,
    required this.status,
  });

  // Create an empty GlobalObjective
  factory GlobalObjective.empty() {
    return GlobalObjective(
      objectiveId: '',
      name: 'Empty Objective',
      tenantId: '',
      version: '1.0',
      status: 'draft',
    );
  }

  factory GlobalObjective.fromJson(Map<String, dynamic> json) {
    return GlobalObjective(
      objectiveId: json['objective_id'] ?? '',
      name: json['name'] ?? '',
      tenantId: json['tenant_id'] ?? '',
      version: json['version'] ?? '',
      status: json['status'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'objective_id': objectiveId,
      'name': name,
      'tenant_id': tenantId,
      'version': version,
      'status': status,
    };
  }
}
