/// A base model class that provides common functionality for all models
abstract class BaseModel {
  /// Convert the model to a JSON map
  Map<String, dynamic> toJson();
  
  /// Create a copy of the model with updated fields
  BaseModel copyWith();
  
  /// Check if two models are equal
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BaseModel && toString() == other.toString();
  }
  
  /// Get a hash code for the model
  @override
  int get hashCode => toString().hashCode;
  
  /// Get a string representation of the model
  @override
  String toString() {
    return toJson().toString();
  }
}
