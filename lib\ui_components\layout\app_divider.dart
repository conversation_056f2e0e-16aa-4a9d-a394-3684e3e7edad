import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable divider component
class AppDivider extends StatelessWidget {
  /// The color of the divider
  final Color? color;

  /// The thickness of the divider
  final double thickness;

  /// The height of the divider (including padding)
  final double? height;

  /// The indent on the start of the divider
  final double indent;

  /// The indent on the end of the divider
  final double endIndent;

  /// Whether the divider is vertical
  final bool vertical;

  /// The width of the divider (for vertical dividers)
  final double? width;

  /// The padding around the divider
  final EdgeInsetsGeometry? padding;

  /// The style of the divider
  final AppDividerStyle style;

  /// The dash pattern for dashed dividers
  final List<double>? dashPattern;

  const AppDivider({
    super.key,
    this.color,
    this.thickness = 1.0,
    this.height,
    this.indent = 0.0,
    this.endIndent = 0.0,
    this.vertical = false,
    this.width,
    this.padding,
    this.style = AppDividerStyle.solid,
    this.dashPattern,
  });

  /// Creates a vertical divider
  const AppDivider.vertical({
    super.key,
    this.color,
    this.thickness = 1.0,
    this.width,
    this.height,
    this.indent = 0.0,
    this.endIndent = 0.0,
    this.padding,
    this.style = AppDividerStyle.solid,
    this.dashPattern,
  }) : vertical = true;

  @override
  Widget build(BuildContext context) {
    final Color dividerColor = color ??
        Theme.of(context).dividerTheme.color ??
        AppTheme.textSecondaryColor.withAlpha(51);

    if (style == AppDividerStyle.solid) {
      if (vertical) {
        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: SizedBox(
            width: width,
            child: VerticalDivider(
              color: dividerColor,
              thickness: thickness,
              width: width,
              indent: indent,
              endIndent: endIndent,
            ),
          ),
        );
      } else {
        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Divider(
            color: dividerColor,
            thickness: thickness,
            height: height,
            indent: indent,
            endIndent: endIndent,
          ),
        );
      }
    } else {
      // Dashed or dotted divider
      final List<double> pattern =
          dashPattern ?? (style == AppDividerStyle.dashed ? [4, 4] : [1, 4]);

      return Padding(
        padding: padding ?? EdgeInsets.zero,
        child: CustomPaint(
          size: vertical
              ? Size(thickness, double.infinity)
              : Size(double.infinity, thickness),
          painter: _DashedDividerPainter(
            color: dividerColor,
            dashPattern: pattern,
            vertical: vertical,
            indent: indent,
            endIndent: endIndent,
          ),
        ),
      );
    }
  }
}

/// A painter for dashed dividers
class _DashedDividerPainter extends CustomPainter {
  final Color color;
  final List<double> dashPattern;
  final bool vertical;
  final double indent;
  final double endIndent;

  _DashedDividerPainter({
    required this.color,
    required this.dashPattern,
    required this.vertical,
    required this.indent,
    required this.endIndent,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = vertical ? size.width : size.height
      ..style = PaintingStyle.stroke;

    final Path path = Path();

    if (vertical) {
      final double startY = indent;
      final double endY = size.height - endIndent;

      path.moveTo(size.width / 2, startY);
      path.lineTo(size.width / 2, endY);
    } else {
      final double startX = indent;
      final double endX = size.width - endIndent;

      path.moveTo(startX, size.height / 2);
      path.lineTo(endX, size.height / 2);
    }

    canvas.drawPath(
      dashPath(
        path,
        dashArray: CircularIntervalList<double>(dashPattern),
      ),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  /// Creates a dashed path
  Path dashPath(
    Path source, {
    required CircularIntervalList<double> dashArray,
  }) {
    final Path dest = Path();
    final metrics = source.computeMetrics();

    for (final metric in metrics) {
      double distance = 0.0;
      bool draw = true;

      while (distance < metric.length) {
        final double len = dashArray.next;
        if (draw) {
          dest.addPath(
            metric.extractPath(distance, distance + len),
            Offset.zero,
          );
        }
        distance += len;
        draw = !draw;
      }
    }

    return dest;
  }
}

/// A circular list that repeats its elements
class CircularIntervalList<T> {
  final List<T> _items;
  int _index = 0;

  CircularIntervalList(this._items);

  T get next {
    if (_items.isEmpty) {
      throw Exception('CircularIntervalList is empty');
    }

    final T item = _items[_index];
    _index = (_index + 1) % _items.length;
    return item;
  }
}

/// The style of the divider
enum AppDividerStyle {
  /// A solid line
  solid,

  /// A dashed line
  dashed,

  /// A dotted line
  dotted,
}

/// A divider with a label in the middle
class AppLabeledDivider extends StatelessWidget {
  /// The label widget
  final Widget label;

  /// The color of the divider
  final Color? color;

  /// The thickness of the divider
  final double thickness;

  /// The height of the divider (including padding)
  final double? height;

  /// The spacing between the label and the dividers
  final double labelSpacing;

  /// The style of the divider
  final AppDividerStyle style;

  /// The dash pattern for dashed dividers
  final List<double>? dashPattern;

  /// The alignment of the label
  final AppLabeledDividerAlignment alignment;

  /// The padding around the divider
  final EdgeInsetsGeometry padding;

  const AppLabeledDivider({
    super.key,
    required this.label,
    this.color,
    this.thickness = 1.0,
    this.height,
    this.labelSpacing = AppTheme.spacingM,
    this.style = AppDividerStyle.solid,
    this.dashPattern,
    this.alignment = AppLabeledDividerAlignment.center,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final Widget divider = AppDivider(
      color: color,
      thickness: thickness,
      height: 0,
      style: style,
      dashPattern: dashPattern,
    );

    Widget content;

    switch (alignment) {
      case AppLabeledDividerAlignment.start:
        content = Row(
          children: [
            label,
            SizedBox(width: labelSpacing),
            Expanded(child: divider),
          ],
        );
        break;
      case AppLabeledDividerAlignment.center:
        content = Row(
          children: [
            Expanded(child: divider),
            SizedBox(width: labelSpacing),
            label,
            SizedBox(width: labelSpacing),
            Expanded(child: divider),
          ],
        );
        break;
      case AppLabeledDividerAlignment.end:
        content = Row(
          children: [
            Expanded(child: divider),
            SizedBox(width: labelSpacing),
            label,
          ],
        );
        break;
    }

    return Padding(
      padding: padding,
      child: SizedBox(
        height: height,
        child: Center(child: content),
      ),
    );
  }
}

/// The alignment of the label in a labeled divider
enum AppLabeledDividerAlignment {
  /// The label is at the start of the divider
  start,

  /// The label is in the center of the divider
  center,

  /// The label is at the end of the divider
  end,
}
