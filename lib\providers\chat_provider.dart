import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import '../models/message.dart';
import '../models/conversation.dart';

class ChatProvider extends ChangeNotifier {
  final List<Message> _messages = [];
  final List<Conversation> _conversations = [];
  bool _isLoading = false;
  CancelToken? _cancelToken;
  String _currentConversationId = '';

  List<Message> get messages => _messages;
  List<Conversation> get conversations => _conversations;
  bool get isLoading => _isLoading;
  String get currentConversationId => _currentConversationId;

  ChatProvider() {
    _loadConversations();
    _loadMessages();
  }

  Future<void> _loadConversations() async {
    final prefs = await SharedPreferences.getInstance();
    final conversationsJson = prefs.getStringList('conversations') ?? [];
    final currentId = prefs.getString('current_conversation_id') ?? '';

    _conversations.clear();
    for (final conversationJson in conversationsJson) {
      final conversationMap = jsonDecode(conversationJson);
      _conversations.add(Conversation.fromJson(conversationMap));
    }

    // If no conversations exist, create a default one
    if (_conversations.isEmpty) {
      final defaultConversation = Conversation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: 'New Conversation',
        lastMessagePreview: '',
        timestamp: DateTime.now(),
      );
      _conversations.add(defaultConversation);
      _currentConversationId = defaultConversation.id;
      _saveConversations();
    } else {
      _currentConversationId =
          currentId.isNotEmpty ? currentId : _conversations[0].id;
    }

    notifyListeners();
  }

  Future<void> _saveConversations() async {
    final prefs = await SharedPreferences.getInstance();
    final conversationsJson = _conversations
        .map((conversation) => jsonEncode(conversation.toJson()))
        .toList();

    await prefs.setStringList('conversations', conversationsJson);
    await prefs.setString('current_conversation_id', _currentConversationId);
  }

  Future<void> _loadMessages() async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson =
        prefs.getStringList('messages_$_currentConversationId') ?? [];

    _messages.clear();
    for (final messageJson in messagesJson) {
      final messageMap = jsonDecode(messageJson);
      _messages.add(Message.fromJson(messageMap));
    }

    notifyListeners();
  }

  Future<void> _saveMessages() async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson =
        _messages.map((message) => jsonEncode(message.toJson())).toList();

    await prefs.setStringList('messages_$_currentConversationId', messagesJson);

    // Update the conversation preview
    _updateConversationPreview();
  }

  void _updateConversationPreview() {
    if (_messages.isNotEmpty) {
      final lastMessage = _messages.last;
      final conversationIndex =
          _conversations.indexWhere((c) => c.id == _currentConversationId);

      if (conversationIndex != -1) {
        final updatedConversation = Conversation(
          id: _currentConversationId,
          title: _conversations[conversationIndex].title,
          lastMessagePreview: lastMessage.content.length > 50
              ? '${lastMessage.content.substring(0, 50)}...'
              : lastMessage.content,
          timestamp: lastMessage.timestamp,
        );

        _conversations[conversationIndex] = updatedConversation;
        _saveConversations();
      }
    }
  }

  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    final userMessage = Message(
      content: content,
      role: MessageRole.user,
    );

    _messages.add(userMessage);
    _isLoading = true;
    _cancelToken = CancelToken();
    notifyListeners();
    await _saveMessages();

    try {
      // Simulate a short delay to show the typing indicator
      await Future.delayed(const Duration(milliseconds: 1500));

      // Check if the request was cancelled
      if (_cancelToken?.isCancelled ?? false) {
        return;
      }

      // Instead of calling the API, just respond with "Coming soon"
      final assistantMessage = Message(
        content: "Coming soon",
        role: MessageRole.assistant,
      );

      _messages.add(assistantMessage);
      await _saveMessages();
    } catch (e) {
      // Handle any errors
      if (e is! DioException || (e.type != DioExceptionType.cancel)) {
        final errorMessage = Message(
          content: "Sorry, there was an error processing your request.",
          role: MessageRole.assistant,
        );
        _messages.add(errorMessage);
        await _saveMessages();
      }
    } finally {
      _isLoading = false;
      _cancelToken = null;
      notifyListeners();
    }
  }

  void cancelRequest() {
    if (_isLoading && _cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken?.cancel("User cancelled the request");
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearChat() {
    _messages.clear();
    _saveMessages();
    notifyListeners();
  }

  void createNewConversation() {
    final newConversation = Conversation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'New Conversation',
      lastMessagePreview: '',
      timestamp: DateTime.now(),
    );

    _conversations.add(newConversation);
    _currentConversationId = newConversation.id;
    _saveConversations();

    _messages.clear();
    notifyListeners();
  }

  void selectConversation(String conversationId) {
    if (_currentConversationId != conversationId) {
      _currentConversationId = conversationId;
      _saveConversations();
      _loadMessages();
    }
  }

  void deleteConversation(String conversationId) async {
    final index = _conversations.indexWhere((c) => c.id == conversationId);
    if (index != -1) {
      _conversations.removeAt(index);

      // Delete messages for this conversation
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('messages_$conversationId');

      // If we deleted the current conversation, select another one
      if (_currentConversationId == conversationId) {
        if (_conversations.isNotEmpty) {
          _currentConversationId = _conversations[0].id;
          _loadMessages();
        } else {
          // Create a new conversation if none exist
          createNewConversation();
        }
      }

      _saveConversations();
      notifyListeners();
    }
  }

  void renameConversation(String conversationId, String newTitle) {
    final index = _conversations.indexWhere((c) => c.id == conversationId);
    if (index != -1) {
      final conversation = _conversations[index];
      _conversations[index] = Conversation(
        id: conversation.id,
        title: newTitle,
        lastMessagePreview: conversation.lastMessagePreview,
        timestamp: conversation.timestamp,
      );
      _saveConversations();
      notifyListeners();
    }
  }
}
