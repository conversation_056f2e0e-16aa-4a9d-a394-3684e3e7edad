import 'package:flutter/material.dart';
import 'dart:math';

/// A widget that allows users to quantify data and perform various quantification operations.
///
/// This widget provides functionality for:
/// - Binning/bucketing data into categories
/// - Discretization of continuous values
/// - Quantization of signals
/// - Counting and frequency analysis
/// - Data range mapping
class QuantificationWidget extends StatefulWidget {
  /// Initial data values as comma-separated string
  final String? initialData;

  /// Number of bins/buckets for quantification
  final int? initialBins;

  /// Minimum value for the range
  final double? minValue;

  /// Maximum value for the range
  final double? maxValue;

  /// The quantification method to use
  final QuantificationMethod method;

  /// Whether to show the histogram visualization
  final bool showHistogram;

  /// Whether to show the statistics summary
  final bool showStatistics;

  /// Whether to show the quantification details
  final bool showDetails;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color for the histogram bars
  final Color histogramColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the quantification results change
  final Function(List<double>, List<int>)? onQuantificationChanged;

  /// Creates a quantification widget.
  const QuantificationWidget({
    super.key,
    this.initialData,
    this.initialBins,
    this.minValue,
    this.maxValue,
    this.method = QuantificationMethod.equalWidth,
    this.showHistogram = true,
    this.showStatistics = true,
    this.showDetails = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.histogramColor = Colors.blue,
    this.width,
    this.height,
    this.onQuantificationChanged,
  });

  @override
  State<QuantificationWidget> createState() => _QuantificationWidgetState();
}

/// Enum for quantification methods
enum QuantificationMethod {
  /// Equal width binning (divides the range into equal intervals)
  equalWidth,

  /// Equal frequency binning (each bin contains approximately the same number of elements)
  equalFrequency,

  /// K-means clustering based binning
  kMeans,

  /// Jenks natural breaks optimization
  jenks,

  /// Quantiles (divides data into equal-sized groups)
  quantiles,

  /// Custom breaks (user-defined bin boundaries)
  custom
}

class _QuantificationWidgetState extends State<QuantificationWidget> {
  final TextEditingController _dataController = TextEditingController();
  final TextEditingController _binsController = TextEditingController();
  final TextEditingController _minController = TextEditingController();
  final TextEditingController _maxController = TextEditingController();

  List<double> _data = [];
  List<double> _binBoundaries = [];
  List<int> _binCounts = [];
  int _numBins = 5; // Default number of bins
  double? _minValue;
  double? _maxValue;
  QuantificationMethod _method = QuantificationMethod.equalWidth;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    _method = widget.method;

    // Initialize with provided data
    if (widget.initialData != null) {
      _dataController.text = widget.initialData!;
      _parseData();
    }

    // Initialize number of bins
    if (widget.initialBins != null) {
      _numBins = widget.initialBins!;
      _binsController.text = _numBins.toString();
    } else {
      _binsController.text = _numBins.toString();
    }

    // Initialize min/max values
    if (widget.minValue != null) {
      _minValue = widget.minValue;
      _minController.text = _minValue.toString();
    }

    if (widget.maxValue != null) {
      _maxValue = widget.maxValue;
      _maxController.text = _maxValue.toString();
    }

    // Perform initial quantification if data is available
    if (_data.isNotEmpty) {
      _performQuantification();
    }
  }

  @override
  void dispose() {
    _dataController.dispose();
    _binsController.dispose();
    _minController.dispose();
    _maxController.dispose();
    super.dispose();
  }

  void _parseData() {
    try {
      final input = _dataController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _data = [];
          _errorMessage = 'Please enter data values';
        });
        return;
      }

      // Split by commas, spaces, or semicolons
      final parts = input.split(RegExp(r'[,;\s]+'));
      final parsedData = <double>[];

      for (final part in parts) {
        if (part.trim().isNotEmpty) {
          try {
            parsedData.add(double.parse(part.trim()));
          } catch (e) {
            setState(() {
              _errorMessage = 'Invalid number format: $part';
            });
            return;
          }
        }
      }

      if (parsedData.isEmpty) {
        setState(() {
          _errorMessage = 'No valid data values found';
        });
        return;
      }

      setState(() {
        _data = parsedData;
        _errorMessage = null;

        // Auto-set min/max if not provided
        if (_minValue == null) {
          _minValue = _data.reduce(min);
          _minController.text = _minValue!.toStringAsFixed(2);
        }

        if (_maxValue == null) {
          _maxValue = _data.reduce(max);
          _maxController.text = _maxValue!.toStringAsFixed(2);
        }
      });

      _performQuantification();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error parsing data: ${e.toString()}';
      });
    }
  }

  void _updateBins() {
    try {
      final bins = int.parse(_binsController.text.trim());
      if (bins < 1) {
        setState(() {
          _errorMessage = 'Number of bins must be at least 1';
        });
        return;
      }

      setState(() {
        _numBins = bins;
        _errorMessage = null;
      });

      if (_data.isNotEmpty) {
        _performQuantification();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid number of bins';
      });
    }
  }

  void _updateMinValue() {
    try {
      final min = double.parse(_minController.text.trim());

      if (_maxValue != null && min >= _maxValue!) {
        setState(() {
          _errorMessage = 'Min value must be less than max value';
        });
        return;
      }

      setState(() {
        _minValue = min;
        _errorMessage = null;
      });

      if (_data.isNotEmpty) {
        _performQuantification();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid min value';
      });
    }
  }

  void _updateMaxValue() {
    try {
      final max = double.parse(_maxController.text.trim());

      if (_minValue != null && max <= _minValue!) {
        setState(() {
          _errorMessage = 'Max value must be greater than min value';
        });
        return;
      }

      setState(() {
        _maxValue = max;
        _errorMessage = null;
      });

      if (_data.isNotEmpty) {
        _performQuantification();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid max value';
      });
    }
  }

  void _setQuantificationMethod(QuantificationMethod method) {
    setState(() {
      _method = method;
    });

    if (_data.isNotEmpty) {
      _performQuantification();
    }
  }

  void _performQuantification() {
    if (_data.isEmpty) return;
    if (_minValue == null || _maxValue == null) return;

    try {
      switch (_method) {
        case QuantificationMethod.equalWidth:
          _equalWidthBinning();
          break;
        case QuantificationMethod.equalFrequency:
          _equalFrequencyBinning();
          break;
        case QuantificationMethod.kMeans:
          _kMeansBinning();
          break;
        case QuantificationMethod.jenks:
          _jenksNaturalBreaks();
          break;
        case QuantificationMethod.quantiles:
          _quantilesBinning();
          break;
        case QuantificationMethod.custom:
          // Custom binning would use user-defined boundaries
          // For now, fall back to equal width
          _equalWidthBinning();
          break;
      }

      // Notify callback if provided
      if (widget.onQuantificationChanged != null) {
        widget.onQuantificationChanged!(_binBoundaries, _binCounts);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error performing quantification: ${e.toString()}';
      });
    }
  }

  void _equalWidthBinning() {
    if (_data.isEmpty || _numBins < 1) return;

    final min = _minValue!;
    final max = _maxValue!;
    final range = max - min;
    final binWidth = range / _numBins;

    // Create bin boundaries
    final boundaries = <double>[];
    for (int i = 0; i <= _numBins; i++) {
      boundaries.add(min + i * binWidth);
    }

    // Count data points in each bin
    final counts = List<int>.filled(_numBins, 0);
    for (final value in _data) {
      if (value < min || value > max) continue; // Skip out-of-range values

      int binIndex = ((value - min) / binWidth).floor();
      // Handle edge case for the maximum value
      if (binIndex == _numBins) binIndex = _numBins - 1;

      counts[binIndex]++;
    }

    setState(() {
      _binBoundaries = boundaries;
      _binCounts = counts;
      _errorMessage = null;
    });
  }

  void _equalFrequencyBinning() {
    if (_data.isEmpty || _numBins < 1) return;

    // Sort the data
    final sortedData = List<double>.from(_data)..sort();
    final n = sortedData.length;

    if (n < _numBins) {
      setState(() {
        _errorMessage = 'Not enough data points for the requested number of bins';
      });
      return;
    }

    // Calculate target count per bin
    final targetCount = n / _numBins;

    // Create bin boundaries
    final boundaries = <double>[sortedData.first];
    final counts = <int>[];

    int currentCount = 0;
    int currentBin = 0;

    for (int i = 0; i < n; i++) {
      currentCount++;

      // Check if we need to create a new bin
      if (currentCount >= targetCount * (currentBin + 1) && currentBin < _numBins - 1) {
        boundaries.add(sortedData[i]);
        counts.add(currentCount - counts.fold(0, (sum, count) => sum + count));
        currentBin++;
      }
    }

    // Add the last boundary and count
    boundaries.add(sortedData.last);
    counts.add(n - counts.fold(0, (sum, count) => sum + count));

    setState(() {
      _binBoundaries = boundaries;
      _binCounts = counts;
      _errorMessage = null;
    });
  }

  void _kMeansBinning() {
    if (_data.isEmpty || _numBins < 1) return;

    // For simplicity, we'll implement a basic k-means algorithm
    // Initialize centroids with evenly spaced values
    final minValue = _data.reduce(min);
    final maxValue = _data.reduce(max);
    final range = maxValue - minValue;

    List<double> centroids = List.generate(
      _numBins,
      (i) => minValue + range * (i + 0.5) / _numBins
    );

    // Perform k-means iterations
    const maxIterations = 100;
    const convergenceThreshold = 0.001;

    for (int iter = 0; iter < maxIterations; iter++) {
      // Assign each data point to the nearest centroid
      final clusters = List<List<double>>.generate(_numBins, (_) => []);

      for (final value in _data) {
        int nearestCentroidIndex = 0;
        double minDistance = double.infinity;

        for (int i = 0; i < centroids.length; i++) {
          final distance = (value - centroids[i]).abs();
          if (distance < minDistance) {
            minDistance = distance;
            nearestCentroidIndex = i;
          }
        }

        clusters[nearestCentroidIndex].add(value);
      }

      // Update centroids
      bool converged = true;
      for (int i = 0; i < centroids.length; i++) {
        if (clusters[i].isEmpty) continue;

        final newCentroid = clusters[i].reduce((a, b) => a + b) / clusters[i].length;
        final movement = (newCentroid - centroids[i]).abs();

        if (movement > convergenceThreshold) {
          converged = false;
        }

        centroids[i] = newCentroid;
      }

      if (converged) break;
    }

    // Sort centroids
    centroids.sort();

    // Create bin boundaries
    final boundaries = <double>[minValue];
    for (int i = 0; i < centroids.length - 1; i++) {
      boundaries.add((centroids[i] + centroids[i + 1]) / 2);
    }
    boundaries.add(maxValue);

    // Count data points in each bin
    final counts = List<int>.filled(_numBins, 0);
    for (final value in _data) {
      for (int i = 0; i < boundaries.length - 1; i++) {
        if (value >= boundaries[i] && value <= boundaries[i + 1]) {
          counts[i]++;
          break;
        }
      }
    }

    setState(() {
      _binBoundaries = boundaries;
      _binCounts = counts;
      _errorMessage = null;
    });
  }

  void _jenksNaturalBreaks() {
    if (_data.isEmpty || _numBins < 1) return;

    // For simplicity, we'll implement a simplified version of Jenks algorithm
    // This is not the full algorithm but a simpler approach

    // Sort the data
    final sortedData = List<double>.from(_data)..sort();
    final n = sortedData.length;

    if (n <= _numBins) {
      // Not enough data points for the requested number of bins
      // Just use the data points as boundaries
      final boundaries = List<double>.from(sortedData);
      if (boundaries.length < _numBins + 1) {
        // Pad with extra boundaries if needed
        final lastValue = boundaries.last;
        while (boundaries.length < _numBins + 1) {
          boundaries.add(lastValue);
        }
      }

      final counts = List<int>.filled(_numBins, 0);
      for (int i = 0; i < _numBins; i++) {
        counts[i] = 1;
      }

      setState(() {
        _binBoundaries = boundaries;
        _binCounts = counts;
        _errorMessage = null;
      });
      return;
    }

    // For simplicity, we'll use a quantile-based approach instead of full Jenks
    // This is a reasonable approximation for many datasets
    _quantilesBinning();
  }



  void _quantilesBinning() {
    if (_data.isEmpty || _numBins < 1) return;

    // Sort the data
    final sortedData = List<double>.from(_data)..sort();
    final n = sortedData.length;

    // Create bin boundaries at quantile points
    final boundaries = <double>[sortedData.first];

    for (int i = 1; i < _numBins; i++) {
      final position = (i * n / _numBins).round();
      if (position < n) {
        boundaries.add(sortedData[position]);
      }
    }

    boundaries.add(sortedData.last);

    // Count data points in each bin
    final counts = List<int>.filled(_numBins, 0);
    for (final value in _data) {
      for (int i = 0; i < boundaries.length - 1; i++) {
        if (i == boundaries.length - 2) {
          // Last bin includes the upper boundary
          if (value >= boundaries[i] && value <= boundaries[i + 1]) {
            counts[i]++;
          }
        } else {
          if (value >= boundaries[i] && value < boundaries[i + 1]) {
            counts[i]++;
          }
        }
      }
    }

    setState(() {
      _binBoundaries = boundaries;
      _binCounts = counts;
      _errorMessage = null;
    });
  }



  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Data Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Data Values:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _dataController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter comma-separated values (e.g., 10, 20, 30, 40, 50)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parseData();
                },
                maxLines: 2,
                minLines: 1,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Quantification Parameters
          Row(
            children: [
              // Number of Bins
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Number of Bins:',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _binsController,
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter number of bins',
                        hintStyle: TextStyle(
                          color: effectiveTextColor.withAlpha(128),
                          fontSize: widget.fontSize,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      enabled: !widget.isDisabled && !widget.isReadOnly,
                      onChanged: (value) {
                        _updateBins();
                      },
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Min Value
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Min Value:',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _minController,
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter min value',
                        hintStyle: TextStyle(
                          color: effectiveTextColor.withAlpha(128),
                          fontSize: widget.fontSize,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      enabled: !widget.isDisabled && !widget.isReadOnly,
                      onChanged: (value) {
                        _updateMinValue();
                      },
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Max Value
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Max Value:',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _maxController,
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter max value',
                        hintStyle: TextStyle(
                          color: effectiveTextColor.withAlpha(128),
                          fontSize: widget.fontSize,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      enabled: !widget.isDisabled && !widget.isReadOnly,
                      onChanged: (value) {
                        _updateMaxValue();
                      },
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Quantification Method Selection
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Quantification Method:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMethodChip(QuantificationMethod.equalWidth, 'Equal Width'),
                  _buildMethodChip(QuantificationMethod.equalFrequency, 'Equal Frequency'),
                  _buildMethodChip(QuantificationMethod.kMeans, 'K-Means'),
                  _buildMethodChip(QuantificationMethod.jenks, 'Jenks Natural Breaks'),
                  _buildMethodChip(QuantificationMethod.quantiles, 'Quantiles'),
                  _buildMethodChip(QuantificationMethod.custom, 'Custom'),
                ],
              ),
            ],
          ),

          // Error Message
          if (_errorMessage != null || widget.errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize - 2,
              ),
            ),
          ],

          // Histogram Visualization
          if (widget.showHistogram && _binBoundaries.isNotEmpty && _binCounts.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Histogram:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: _buildHistogram(),
              ),
            ),
          ],

          // Statistics Summary
          if (widget.showStatistics && _data.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Statistics:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Count: ${_data.length}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'Min: ${_data.reduce(min).toStringAsFixed(2)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'Max: ${_data.reduce(max).toStringAsFixed(2)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'Mean: ${(_data.reduce((a, b) => a + b) / _data.length).toStringAsFixed(2)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'Number of Bins: $_numBins',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Quantification Details
          if (widget.showDetails && _binBoundaries.isNotEmpty && _binCounts.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Bin Details:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int i = 0; i < _binCounts.length; i++)
                    Text(
                      'Bin ${i + 1}: [${_binBoundaries[i].toStringAsFixed(2)}, ${_binBoundaries[i + 1].toStringAsFixed(2)}] - Count: ${_binCounts[i]}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                ],
              ),
            ),
          ],

          // Helper Text
          if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMethodChip(QuantificationMethod method, String label) {
    final bool isSelected = _method == method;

    return ChoiceChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : widget.textColor,
          fontSize: widget.fontSize - 2,
        ),
      ),
      selected: isSelected,
      onSelected: widget.isDisabled || widget.isReadOnly
          ? null
          : (selected) {
              if (selected) {
                _setQuantificationMethod(method);
              }
            },
      backgroundColor: widget.backgroundColor,
      selectedColor: widget.histogramColor,
      disabledColor: Colors.grey.withAlpha(30),
    );
  }

  Widget _buildHistogram() {
    if (_binBoundaries.isEmpty || _binCounts.isEmpty) {
      return const Center(child: Text('No data to display'));
    }

    final maxCount = _binCounts.reduce(max);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        for (int i = 0; i < _binCounts.length; i++)
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Count label
                Text(
                  '${_binCounts[i]}',
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize - 4,
                  ),
                ),

                // Bar
                Container(
                  height: maxCount > 0
                      ? 150 * (_binCounts[i] / maxCount)
                      : 0,
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  decoration: BoxDecoration(
                    color: widget.histogramColor,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(widget.borderRadius / 4),
                    ),
                  ),
                ),

                // Bin boundary label
                Text(
                  _binBoundaries[i].toStringAsFixed(1),
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize - 4,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

        // Last boundary
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            const SizedBox(height: 20), // Space for count
            const SizedBox(height: 150), // Space for bar
            Text(
              _binBoundaries.last.toStringAsFixed(1),
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize - 4,
              ),
            ),
          ],
        ),
      ],
    );
  }
}