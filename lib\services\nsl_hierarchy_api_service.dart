import 'dart:convert';
import 'package:http/http.dart' as http;
import '../screens/web/nsl_hierarchy_web/models/nsl_tree_hierarchy_model_new.dart';

class NslHierarchyApiService {
  static const String baseUrl = 'http://***********:9223';
  static const String modulesEndpoint = '/nsl-prescriptor/api/v1/modules';
  
  static Future<NslTreeHierarchy?> fetchModules() async {
    try {
      final url = Uri.parse('$baseUrl$modulesEndpoint');
      print('Fetching NSL hierarchy data from API...');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = NslTreeHierarchy.fromJson(jsonData);
        print('Successfully loaded ${result.result?.nodes?.length ?? 0} nodes from API');
        return result;
      } else {
        print('API Error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching modules: $e');
      return null;
    }
  }
}
