import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:universal_html/html.dart' as html;
import 'package:waveform_recorder/waveform_recorder.dart';
import '../models/multimedia/file_upload_response_model.dart';
import '../models/multimedia/speech_to_text_response_model.dart';
import '../services/dio_client.dart';
import '../utils/logger.dart';

class AudioRecordingService {
  // Singleton instance
  static final AudioRecordingService _instance =
      AudioRecordingService._internal();

  // Factory constructor
  factory AudioRecordingService() => _instance;

  // Internal constructor
  AudioRecordingService._internal();

  // API endpoints
  final String _uploadApiUrl = 'http://**********:8055/api/upload';
  final String _speechToTextApiUrl =
      'http://**********:8055/api/speect_to_text';

  // Dio client
  final Dio _dio = DioClient().client;

  // Waveform recorder instance
  late final WaveformRecorder _recorder;

  // Recording state
  bool _isRecorderInitialized = false;
  bool _isRecording = false;

  // Current recording file path
  String? _currentRecordingPath;

  // Audio data buffer for web
  final List<Uint8List> _audioDataBuffer = [];

  // Audio stream subscription
  StreamSubscription<Uint8List>? _audioStreamSubscription;

  // Initialize the recorder
  Future<void> initialize() async {
    if (_isRecorderInitialized) return;

    // Request microphone permission
    final status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      Logger.error('Microphone permission not granted');
      throw Exception('Microphone permission not granted');
    }

    // Create a controller for the waveform recorder
    final controller = WaveformRecorderController();

    // Initialize the waveform recorder
    _recorder = WaveformRecorder(
      controller: controller,
      height: 100, // Height of the waveform visualization
    );

    _isRecorderInitialized = true;
    Logger.info('Recorder initialized');
  }

  // Start recording
  Future<void> startRecording() async {
    if (!_isRecorderInitialized) {
      await initialize();
    }

    if (_isRecording) {
      Logger.info('Already recording');
      return;
    }

    try {
      // Clear the audio data buffer
      _audioDataBuffer.clear();

      // Generate a unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

      // Set the current recording path
      if (kIsWeb) {
        _currentRecordingPath = 'recording_$timestamp.wav';
      } else {
        try {
          final tempDir = await getTemporaryDirectory();
          _currentRecordingPath = '${tempDir.path}/recording_$timestamp.wav';
        } catch (e) {
          _currentRecordingPath = 'recording_$timestamp.wav';
          Logger.warning(
              'Could not get temporary directory: $e, using fallback');
        }
      }

      // Start recording using the waveform recorder
      // The waveform_recorder package handles the recording differently
      // It automatically captures the audio data and provides it through its controller
      _recorder.controller.startRecording();

      _isRecording = true;
      Logger.info('Recording started: $_currentRecordingPath');
    } catch (e) {
      Logger.error('Error starting recording: $e');
      throw Exception('Error starting recording: $e');
    }
  }

  // Stop recording
  Future<String?> stopRecording() async {
    if (!_isRecording) {
      Logger.info('Not recording');
      return null;
    }

    try {
      // Stop the waveform recorder
      _recorder.controller.stopRecording();

      // Cancel any audio stream subscription
      await _audioStreamSubscription?.cancel();
      _audioStreamSubscription = null;

      // The waveform_recorder package doesn't provide direct access to the audio data
      // We'll use the audio data buffer we've been collecting

      if (kIsWeb) {
        // For web, create a downloadable blob with the actual recorded audio data
        if (_currentRecordingPath != null) {
          try {
            // Get the audio data from the buffer
            Logger.info(
                'Audio data buffer size: ${_audioDataBuffer.length} chunks');

            if (_audioDataBuffer.isNotEmpty) {
              // Calculate total size of audio data
              int totalSize = 0;
              for (final chunk in _audioDataBuffer) {
                totalSize += chunk.length;
              }

              Logger.info('Total audio data size: $totalSize bytes');

              // For PCM data, we need to handle it differently
              // The PCM data from the recorder is already in the correct format (16-bit PCM)
              // We just need to concatenate all chunks

              // Create a buffer for the audio data
              final audioData = Uint8List(totalSize);

              // Copy all chunks into the buffer
              int offset = 0;
              for (final chunk in _audioDataBuffer) {
                audioData.setRange(offset, offset + chunk.length, chunk);
                offset += chunk.length;
              }

              Logger.info('Total PCM data size: $totalSize bytes');

              // Create a WAV file header for mono 16-bit audio at 16kHz
              final header = _createWavHeader(16000, 1, 16);

              // Update the WAV header with the correct data size
              final headerData =
                  ByteData.view(Uint8List.fromList(header).buffer);
              headerData.setUint32(4, 36 + audioData.length,
                  Endian.little); // Update RIFF chunk size
              headerData.setUint32(40, audioData.length,
                  Endian.little); // Update data chunk size

              // Combine the header and audio data
              final wavData = [...header, ...audioData];

              Logger.info(
                  'Created WAV data with size: ${wavData.length} bytes');

              // Create a downloadable blob and trigger download
              _createDownloadableBlob(wavData, _currentRecordingPath!);

              Logger.info(
                  'Created downloadable WAV blob with actual audio data for web');
            } else {
              Logger.warning('No audio data captured, creating empty WAV file');

              // Create a WAV file header for mono 16-bit audio at 16kHz
              final header = _createWavHeader(16000, 1, 16);

              // Create a small amount of silence (1 second)
              // For 16-bit mono audio at 16kHz, we need 16000 * 2 bytes
              final audioData = Uint8List(16000 * 2);

              // Update the WAV header with the correct data size
              final headerData =
                  ByteData.view(Uint8List.fromList(header).buffer);
              headerData.setUint32(4, 36 + audioData.length,
                  Endian.little); // Update RIFF chunk size
              headerData.setUint32(40, audioData.length,
                  Endian.little); // Update data chunk size

              // Combine the header and audio data
              final wavData = [...header, ...audioData];

              // Create a downloadable blob and trigger download
              _createDownloadableBlob(wavData, _currentRecordingPath!);

              Logger.info('Created downloadable empty WAV blob for web');
            }
          } catch (e) {
            Logger.warning('Could not create downloadable blob: $e');
          }
        }
      } else {
        // For mobile, we need to save the audio data to a file
        try {
          // Create a WAV file header for mono 16-bit audio at 16kHz
          final header = _createWavHeader(16000, 1, 16);

          // Create a buffer for the audio data
          int totalSize = 0;
          for (final chunk in _audioDataBuffer) {
            totalSize += chunk.length;
          }

          if (totalSize > 0) {
            // Concatenate all audio data chunks
            final audioData = Uint8List(totalSize);
            int offset = 0;
            for (final chunk in _audioDataBuffer) {
              audioData.setRange(offset, offset + chunk.length, chunk);
              offset += chunk.length;
            }

            // Update the WAV header with the correct data size
            final headerData = ByteData.view(Uint8List.fromList(header).buffer);
            headerData.setUint32(4, 36 + audioData.length,
                Endian.little); // Update RIFF chunk size
            headerData.setUint32(
                40, audioData.length, Endian.little); // Update data chunk size

            // Combine the header and audio data
            final wavData = [...header, ...audioData];

            // Save the WAV file
            if (_currentRecordingPath != null) {
              final file = File(_currentRecordingPath!);
              await file.writeAsBytes(wavData);
              Logger.info('Saved WAV file to: $_currentRecordingPath');
            }
          } else {
            // Create a small amount of silence (1 second)
            final audioData = Uint8List(16000 * 2);

            // Update the WAV header with the correct data size
            final headerData = ByteData.view(Uint8List.fromList(header).buffer);
            headerData.setUint32(4, 36 + audioData.length,
                Endian.little); // Update RIFF chunk size
            headerData.setUint32(
                40, audioData.length, Endian.little); // Update data chunk size

            // Combine the header and audio data
            final wavData = [...header, ...audioData];

            // Save the WAV file
            if (_currentRecordingPath != null) {
              final file = File(_currentRecordingPath!);
              await file.writeAsBytes(wavData);
              Logger.info('Saved empty WAV file to: $_currentRecordingPath');
            }
          }
        } catch (e) {
          Logger.error('Error saving WAV file: $e');
        }
      }

      _isRecording = false;
      Logger.info('Recording stopped: $_currentRecordingPath');

      return _currentRecordingPath;
    } catch (e) {
      Logger.error('Error stopping recording: $e');
      _isRecording = false;
      return null;
    }
  }

  // Upload recording to API and download for testing
  Future<Map<String, dynamic>> uploadRecording(String filePath) async {
    try {
      Logger.info('Uploading recording: $filePath');

      FormData formData;
      String uploadedFilePath = '';

      if (kIsWeb) {
        // For web, we need to create a form data with the actual audio data
        try {
          if (_audioDataBuffer.isNotEmpty) {
            // Calculate total size of audio data
            int totalSize = 0;
            for (final chunk in _audioDataBuffer) {
              totalSize += chunk.length;
            }

            Logger.info('Preparing to upload audio data: $totalSize bytes');

            // For PCM data, we need to handle it differently
            // The PCM data from the recorder is already in the correct format (16-bit PCM)
            // We just need to concatenate all chunks

            // Create a buffer for the audio data
            final audioData = Uint8List(totalSize);

            // Copy all chunks into the buffer
            int offset = 0;
            for (final chunk in _audioDataBuffer) {
              audioData.setRange(offset, offset + chunk.length, chunk);
              offset += chunk.length;
            }

            Logger.info('Total PCM data size: $totalSize bytes');

            // Create a WAV file header for mono 16-bit audio at 16kHz
            final header = _createWavHeader(16000, 1, 16);

            // Update the WAV header with the correct data size
            final headerData = ByteData.view(Uint8List.fromList(header).buffer);
            headerData.setUint32(4, 36 + audioData.length,
                Endian.little); // Update RIFF chunk size
            headerData.setUint32(
                40, audioData.length, Endian.little); // Update data chunk size

            // Combine the header and audio data
            final wavData = [...header, ...audioData];

            Logger.info('Created WAV data with size: ${wavData.length} bytes');

            // Create form data with the actual audio data
            formData = FormData.fromMap({
              'file': MultipartFile.fromBytes(
                wavData,
                filename: filePath,
              ),
            });

            Logger.info(
                'Created form data with actual audio data for web: ${wavData.length} bytes');
          } else {
            Logger.warning('No audio data available, using fallback');

            // Create a WAV file header for mono 16-bit audio at 16kHz
            final header = _createWavHeader(16000, 1, 16);

            // Create a small amount of silence (1 second)
            // For 16-bit mono audio at 16kHz, we need 16000 * 2 bytes
            final audioData = Uint8List(16000 * 2);

            // Update the WAV header with the correct data size
            final headerData = ByteData.view(Uint8List.fromList(header).buffer);
            headerData.setUint32(4, 36 + audioData.length,
                Endian.little); // Update RIFF chunk size
            headerData.setUint32(
                40, audioData.length, Endian.little); // Update data chunk size

            // Combine the header and audio data
            final wavData = [...header, ...audioData];

            Logger.info('Created WAV data with size: ${wavData.length} bytes');

            // Create form data with the fallback audio data
            formData = FormData.fromMap({
              'file': MultipartFile.fromBytes(
                wavData,
                filename: filePath,
              ),
            });

            Logger.info('Created form data with fallback audio data for web');
          }
        } catch (e) {
          // Fallback to dummy data if we can't create the actual audio data
          formData = FormData.fromMap({
            'file': MultipartFile.fromString(
              'Dummy audio content for web testing',
              filename: filePath,
            ),
          });

          Logger.error('Error creating form data with actual audio data: $e');
        }
      } else {
        // For mobile platforms, check if file exists
        final file = File(filePath);
        if (!await file.exists()) {
          Logger.error('File does not exist: $filePath');
          return {
            'success': false,
            'message': 'File does not exist: $filePath',
            'error': 'File not found',
          };
        }

        // Create form data with the actual file
        formData = FormData.fromMap({
          'file':
              await MultipartFile.fromFile(filePath, filename: 'recording.wav'),
        });
      }

      // Make API call to upload the file
      final uploadResponse = await _dio.post(
        _uploadApiUrl,
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      Logger.info('Upload response: ${uploadResponse.statusCode}');

      // Parse the upload response
      if (uploadResponse.statusCode != 200) {
        return {
          'success': false,
          'message':
              'Failed to upload recording: ${uploadResponse.statusMessage}',
          'error': 'HTTP ${uploadResponse.statusCode}',
          'data': {'transcribed_text': "Error occurred during file upload."}
        };
      }

      // Parse the upload response using the model
      final fileUploadResponse = FileUploadResponseModel.fromJson(
          uploadResponse.data is Map
              ? uploadResponse.data as Map<String, dynamic>
              : {'success': false, 'error': 'Invalid response format'});

      if (!fileUploadResponse.success) {
        return {
          'success': false,
          'message': fileUploadResponse.message ?? 'Unknown error',
          'error': fileUploadResponse.error ?? 'Unknown error',
          'data': {'transcribed_text': "Error occurred during file upload."}
        };
      }

      // Get the uploaded file path
      uploadedFilePath = fileUploadResponse.filePath;
      Logger.info('File uploaded successfully: $uploadedFilePath');

      // Extract just the filename from the file path
      final fileName = uploadedFilePath.split('/').last;
      Logger.info('Extracted filename: $fileName');

      // Now call the speech-to-text API with just the filename
      try {
        // Create form data for speech-to-text API
        final speechToTextFormData = FormData.fromMap({
          'audio_file': fileName,
        });

        // Make API call to speech-to-text
        final speechToTextResponse = await _dio.post(
          _speechToTextApiUrl,
          data: speechToTextFormData,
          options: Options(
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          ),
        );

        Logger.info(
            'Speech-to-text response: ${speechToTextResponse.statusCode}');

        // Parse the speech-to-text response
        if (speechToTextResponse.statusCode != 200) {
          return {
            'success': true,
            'data': {
              'file_upload': fileUploadResponse.toJson(),
              'download_url': uploadedFilePath,
              'transcribed_text':
                  "Error occurred during speech-to-text processing."
            },
            'message': 'File uploaded successfully but speech-to-text failed',
          };
        }

        // Parse the speech-to-text response using the model
        final speechToTextResult =
            SpeechToTextResponseModel.fromJson(speechToTextResponse.data is Map
                ? speechToTextResponse.data as Map<String, dynamic>
                : {
                    'success': false,
                    'text': 'Could not transcribe audio',
                    'error': 'Invalid response format'
                  });

        // Return the combined result
        return {
          'success': true,
          'data': {
            'file_upload': fileUploadResponse.toJson(),
            'speech_to_text': speechToTextResult.toJson(),
            'download_url': uploadedFilePath,
            'transcribed_text': speechToTextResult.text
          },
          'message': 'Recording uploaded and transcribed successfully',
        };
      } catch (e) {
        // If speech-to-text fails, still return the file upload success
        Logger.error('Error in speech-to-text processing: $e');
        return {
          'success': true,
          'data': {
            'file_upload': fileUploadResponse.toJson(),
            'download_url': uploadedFilePath,
            'transcribed_text':
                "Error occurred during speech-to-text processing: $e"
          },
          'message': 'File uploaded successfully but speech-to-text failed',
        };
      }
    } catch (e) {
      Logger.error('Error uploading recording: $e');
      return {
        'success': false,
        'message': 'Failed to upload recording: $e',
        'error': e.toString(),
        'data': {
          'transcribed_text': "Error occurred during speech-to-text processing."
        }
      };
    }
  }

  // Check if recording
  bool get isRecording => _isRecording;

  // Dispose the recorder
  Future<void> dispose() async {
    if (_isRecording) {
      await stopRecording();
    }

    // Cancel any active stream subscription
    await _audioStreamSubscription?.cancel();
    _audioStreamSubscription = null;

    // Clear the audio data buffer
    _audioDataBuffer.clear();

    // The waveform_recorder package doesn't have a dispose method
    // We'll just set the recorder to uninitialized
    _isRecorderInitialized = false;
  }

  // Create a downloadable blob from audio data for web
  void _createDownloadableBlob(List<int> audioData, String filename) {
    if (kIsWeb) {
      try {
        Logger.info('Creating downloadable blob for web: $filename');

        // Debug the WAV file structure
        _debugWavFile(audioData);

        // Create a blob from the audio data
        final blob = html.Blob([Uint8List.fromList(audioData)], 'audio/wav');

        // Create a URL for the blob
        final url = html.Url.createObjectUrlFromBlob(blob);

        // Create an anchor element
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', filename)
          ..style.display = 'none';

        // Add the anchor to the document body
        html.document.body?.append(anchor);

        // Trigger a click on the anchor
        anchor.click();

        // Remove the anchor from the document
        anchor.remove();

        // Revoke the URL to free up memory
        html.Url.revokeObjectUrl(url);

        Logger.info('Download triggered successfully');
      } catch (e) {
        Logger.error('Error creating downloadable blob: $e');
      }
    }
  }

  // Debug WAV file structure
  void _debugWavFile(List<int> wavData) {
    try {
      if (wavData.length < 44) {
        Logger.error('WAV file too short: ${wavData.length} bytes');
        return;
      }

      // Check RIFF header
      final riffHeader = String.fromCharCodes(wavData.sublist(0, 4));
      Logger.info('RIFF header: $riffHeader');

      // Check file size
      final fileSize = wavData.length;
      final reportedSize =
          _bytesToInt32(wavData.sublist(4, 8), Endian.little) + 8;
      Logger.info(
          'File size: $fileSize bytes, reported size: $reportedSize bytes');

      // Check WAVE format
      final waveFormat = String.fromCharCodes(wavData.sublist(8, 12));
      Logger.info('WAVE format: $waveFormat');

      // Check fmt chunk
      final fmtChunk = String.fromCharCodes(wavData.sublist(12, 16));
      Logger.info('fmt chunk: $fmtChunk');

      // Check audio format
      final audioFormat = _bytesToInt16(wavData.sublist(20, 22), Endian.little);
      Logger.info('Audio format: $audioFormat (1 = PCM)');

      // Check number of channels
      final numChannels = _bytesToInt16(wavData.sublist(22, 24), Endian.little);
      Logger.info('Number of channels: $numChannels');

      // Check sample rate
      final sampleRate = _bytesToInt32(wavData.sublist(24, 28), Endian.little);
      Logger.info('Sample rate: $sampleRate Hz');

      // Check bits per sample
      final bitsPerSample =
          _bytesToInt16(wavData.sublist(34, 36), Endian.little);
      Logger.info('Bits per sample: $bitsPerSample');

      // Check data chunk
      final dataChunk = String.fromCharCodes(wavData.sublist(36, 40));
      Logger.info('data chunk: $dataChunk');

      // Check data size
      final dataSize = _bytesToInt32(wavData.sublist(40, 44), Endian.little);
      final actualDataSize = wavData.length - 44;
      Logger.info(
          'Data size: $dataSize bytes, actual data size: $actualDataSize bytes');
    } catch (e) {
      Logger.error('Error debugging WAV file: $e');
    }
  }

  // Convert bytes to int16
  int _bytesToInt16(List<int> bytes, Endian endian) {
    final data = ByteData(2);
    data.setUint8(0, bytes[0]);
    data.setUint8(1, bytes[1]);
    return data.getInt16(0, endian);
  }

  // Convert bytes to int32
  int _bytesToInt32(List<int> bytes, Endian endian) {
    final data = ByteData(4);
    data.setUint8(0, bytes[0]);
    data.setUint8(1, bytes[1]);
    data.setUint8(2, bytes[2]);
    data.setUint8(3, bytes[3]);
    return data.getInt32(0, endian);
  }

  // Create a WAV file header
  List<int> _createWavHeader(
      int sampleRate, int numChannels, int bitsPerSample) {
    final byteRate = sampleRate * numChannels * bitsPerSample ~/ 8;
    final blockAlign = numChannels * bitsPerSample ~/ 8;

    // Create a buffer for the header (44 bytes)
    final header = Uint8List(44);
    final headerData = ByteData.view(header.buffer);

    // RIFF chunk descriptor
    // Write 'RIFF' in ASCII - one byte per character
    header[0] = 0x52; // 'R'
    header[1] = 0x49; // 'I'
    header[2] = 0x46; // 'F'
    header[3] = 0x46; // 'F'

    // Chunk size (file size - 8), will be updated later
    headerData.setUint32(4, 36, Endian.little);

    // Write 'WAVE' in ASCII
    header[8] = 0x57; // 'W'
    header[9] = 0x41; // 'A'
    header[10] = 0x56; // 'V'
    header[11] = 0x45; // 'E'

    // 'fmt ' sub-chunk
    // Write 'fmt ' in ASCII
    header[12] = 0x66; // 'f'
    header[13] = 0x6D; // 'm'
    header[14] = 0x74; // 't'
    header[15] = 0x20; // ' ' (space)

    headerData.setUint32(16, 16, Endian.little); // Sub-chunk size (16 for PCM)
    headerData.setUint16(20, 1, Endian.little); // Audio format (1 for PCM)
    headerData.setUint16(22, numChannels, Endian.little); // Number of channels
    headerData.setUint32(24, sampleRate, Endian.little); // Sample rate
    headerData.setUint32(28, byteRate, Endian.little); // Byte rate
    headerData.setUint16(32, blockAlign, Endian.little); // Block align
    headerData.setUint16(34, bitsPerSample, Endian.little); // Bits per sample

    // 'data' sub-chunk
    // Write 'data' in ASCII
    header[36] = 0x64; // 'd'
    header[37] = 0x61; // 'a'
    header[38] = 0x74; // 't'
    header[39] = 0x61; // 'a'

    // Sub-chunk size (data size), will be updated later
    headerData.setUint32(40, 0, Endian.little);

    return header.toList();
  }
}
