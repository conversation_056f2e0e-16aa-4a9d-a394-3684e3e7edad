import 'package:dio/dio.dart';
import '../utils/logger.dart';
import '../utils/environment.dart';
import 'base_api_service.dart';
import 'service_locator.dart';
import 'auth_service.dart';

class WorkflowService extends BaseApiService {
  // Get auth service instance
  final AuthService _authService = ServiceLocator().authService;
  // Get environment instance
  final Environment _env = Environment.instance;

  // Use environment for endpoint URLs
  String get workflowInstancesUrl => _env.workflowInstancesUrl;

  // Create a workflow instance
  Future<Map<String, dynamic>> createWorkflowInstance({
    required String goId,
    required String tenantId,
    String? userId,
    Map<String, dynamic>? initialData,
  }) async {
    try {
      Logger.info('Creating workflow instance for GO: $goId');

      // Get user ID from SharedPreferences if not provided
      String userIdToUse = userId ?? '';
      if (userIdToUse.isEmpty) {
        final storedUserId = await _authService.getUserId();
        if (storedUserId != null) {
          userIdToUse = storedUserId;
          Logger.info('Using user ID from SharedPreferences: $userIdToUse');
        } else {
          Logger.warning('No user ID provided or found in SharedPreferences');
        }
      }

      final data = {
        'go_id': goId,
        'tenant_id': tenantId,
        'user_id': userIdToUse,
        'initial_data': initialData ?? {'use_ai_assistance': true},
      };

      // Get a valid token
      final token = await _authService.getValidToken();

      final response = await dio.post(
        workflowInstancesUrl,
        data: data,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Create workflow instance response status code: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        Logger.info('Workflow instance created successfully');
        return response.data;
      } else {
        Logger.error(
            'Error creating workflow instance: ${response.statusCode}');
        throw Exception(
            'Failed to create workflow instance: ${response.statusCode}');
      }
    } catch (e) {
      Logger.error('Exception while creating workflow instance: $e');
      throw Exception('Failed to create workflow instance: $e');
    }
  }

  // Start a workflow instance
  Future<Map<String, dynamic>> startWorkflowInstance({
    required String instanceId,
    String? userId,
  }) async {
    try {
      Logger.info('Starting workflow instance: $instanceId');

      // Get user ID from SharedPreferences if not provided
      String userIdToUse = userId ?? '';
      if (userIdToUse.isEmpty) {
        final storedUserId = await _authService.getUserId();
        if (storedUserId != null) {
          userIdToUse = storedUserId;
          Logger.info(
              'Using user ID from SharedPreferences for starting workflow: $userIdToUse');
        } else {
          Logger.warning(
              'No user ID provided or found in SharedPreferences for starting workflow');
        }
      }

      final data = {
        'user_id': userIdToUse,
      };

      // Get a valid token
      final token = await _authService.getValidToken();

      final response = await dio.post(
        _env.workflowStartUrl(instanceId),
        data: data,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Start workflow instance response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Workflow instance started successfully');
        return response.data;
      } else {
        Logger.error(
            'Error starting workflow instance: ${response.statusCode}');
        throw Exception(
            'Failed to start workflow instance: ${response.statusCode}');
      }
    } catch (e) {
      Logger.error('Exception while starting workflow instance: $e');
      throw Exception('Failed to start workflow instance: $e');
    }
  }

  // Fetch workflow inputs
  // Execute workflow with input data
  Future<Map<String, dynamic>> executeWorkflow({
    required String instanceId,
    required Map<String, dynamic> inputData,
    String? userId,
  }) async {
    try {
      Logger.info('Executing workflow instance: $instanceId');

      // Get user ID from SharedPreferences if not provided
      String userIdToUse = userId ?? '';
      if (userIdToUse.isEmpty) {
        final storedUserId = await _authService.getUserId();
        if (storedUserId != null) {
          userIdToUse = storedUserId;
          Logger.info(
              'Using user ID from SharedPreferences for executing workflow: $userIdToUse');
        } else {
          Logger.warning(
              'No user ID provided or found in SharedPreferences for executing workflow');
        }
      }

      final data = {
        'input_data': inputData,
        'user_id': userIdToUse,
      };

      // Get a valid token
      final token = await _authService.getValidToken();

      final response = await dio.post(
        _env.workflowExecuteUrl(instanceId),
        data: data,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Execute workflow response status code: ${response.statusCode}');
      Logger.info('Execute workflow response data: ${response.data}');

      if (response.statusCode == 200) {
        Logger.info('Workflow executed successfully');
        return response.data;
      } else {
        Logger.error('Error executing workflow: ${response.statusCode}');
        throw Exception('Failed to execute workflow: ${response.statusCode}');
      }
    } catch (e) {
      Logger.error('Exception while executing workflow: $e');

      // Check if it's a DioException with a response
      if (e is DioException && e.response != null) {
        final response = e.response!;
        Logger.error('Error status code: ${response.statusCode}');
        Logger.error('Error response data: ${response.data}');

        // Check if the response contains a 'detail' field
        if (response.data is Map && response.data.containsKey('detail')) {
          final detail = response.data['detail'];
          Logger.error('Error detail: $detail');
          throw Exception('Server error: $detail');
        }
      }

      throw Exception('Failed to execute workflow: $e');
    }
  }

  Future<dynamic> fetchWorkflowInputs({
    required String instanceId,
    Map<String, dynamic>? queryParams,
  }) async {
    try {
      Logger.info('Fetching inputs for workflow instance: $instanceId');

      // Use the environment helper method
      String url = _env.workflowInputsUrl(instanceId);

      // Add query parameters if provided
      if (queryParams != null && queryParams.isNotEmpty) {
        // Convert query parameters to URL query string
        final queryString = queryParams.entries
            .map((e) =>
                '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
            .join('&');

        // Append query string to URL
        url = '$url?$queryString';
        Logger.info('Fetching from URL with query params: $url');
      } else {
        Logger.info('Fetching from URL: $url');
      }

      // Get a valid token
      final token = await _authService.getValidToken();

      final response = await dio.get(
        url,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Fetch workflow inputs response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Workflow inputs fetched successfully');
        Logger.info('Response data type: ${response.data.runtimeType}');

        // Check if response.data is a Map and has expected keys
        if (response.data is Map) {
          Logger.info(
              'Response is a Map with keys: ${(response.data as Map).keys.toList()}');

          // Check for new format keys
          if (response.data.containsKey('user_inputs')) {
            Logger.info(
                'user_inputs found with type: ${response.data['user_inputs'].runtimeType}');
            Logger.info(
                'user_inputs length: ${response.data['user_inputs'] is List ? (response.data['user_inputs'] as List).length : 'not a list'}');
          }

          if (response.data.containsKey('system_inputs')) {
            Logger.info(
                'system_inputs found with type: ${response.data['system_inputs'].runtimeType}');
            Logger.info(
                'system_inputs length: ${response.data['system_inputs'] is List ? (response.data['system_inputs'] as List).length : 'not a list'}');
          }

          if (response.data.containsKey('info_inputs')) {
            Logger.info(
                'info_inputs found with type: ${response.data['info_inputs'].runtimeType}');
            Logger.info(
                'info_inputs length: ${response.data['info_inputs'] is List ? (response.data['info_inputs'] as List).length : 'not a list'}');
          }

          if (response.data.containsKey('dependent_inputs')) {
            Logger.info(
                'dependent_inputs found with type: ${response.data['dependent_inputs'].runtimeType}');
            Logger.info(
                'dependent_inputs length: ${response.data['dependent_inputs'] is List ? (response.data['dependent_inputs'] as List).length : 'not a list'}');
          }

          if (response.data.containsKey('dependencies')) {
            Logger.info(
                'dependencies found with type: ${response.data['dependencies'].runtimeType}');
            Logger.info(
                'dependencies keys: ${response.data['dependencies'] is Map ? (response.data['dependencies'] as Map).keys.toList() : 'not a map'}');
          }

          // Check for legacy format keys
          if (response.data.containsKey('input_fields')) {
            Logger.info(
                'Legacy format: input_fields found with type: ${response.data['input_fields'].runtimeType}');
            Logger.info(
                'Legacy format: input_fields length: ${response.data['input_fields'] is List ? (response.data['input_fields'] as List).length : 'not a list'}');
          }

          // Check for local_objective key (common to both formats)
          if (response.data.containsKey('local_objective')) {
            Logger.info(
                'local_objective found: ${response.data['local_objective']}');
          } else {
            Logger.warning('local_objective key not found in response');
          }
        }

        Logger.info('Response data: ${response.data}');
        return response.data;
      } else {
        Logger.error('Error fetching workflow inputs: ${response.statusCode}');
        throw Exception(
            'Failed to fetch workflow inputs: ${response.statusCode}');
      }
    } catch (e) {
      Logger.error('Exception while fetching workflow inputs: $e');
      throw Exception('Failed to fetch workflow inputs: $e');
    }
  }
}
