import 'dart:convert';
import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// A customizable avatar image widget that can display an image, asset, or initials.
///
/// This widget is designed to be used for user avatars, profile pictures, etc.
/// It supports various customization options including shape, size, border, shadow, etc.
class AvatarImageWidget extends StatefulWidget {
  /// URL of the image to display
  final String? imageUrl;

  /// Path to an asset image to display
  final String? assetPath;

  /// Initials to display when no image is available
  final String? initials;

  /// Label to display below the avatar
  final String? label;

  /// Whether to show the label
  final bool showLabel;

  /// Style for the label text
  final TextStyle? labelStyle;

  /// Size of the avatar (width and height)
  final double size;

  /// Whether the avatar should be circular
  final bool isCircular;

  /// Border radius for non-circular avatars
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Color of the border
  final Color borderColor;

  /// Width of the border
  final double borderWidth;

  /// Whether to show a shadow
  final bool hasShadow;

  /// Elevation for the shadow
  final double elevation;

  /// Background color for the avatar (used for initials)
  final Color backgroundColor;

  /// Padding around the avatar
  final EdgeInsetsGeometry padding;

  /// Margin around the avatar
  final EdgeInsetsGeometry margin;

  /// Alignment of the avatar within its parent
  final Alignment alignment;

  /// Whether the avatar is tappable
  final bool isTappable;

  /// Callback for when the avatar is tapped
  final VoidCallback? onTap;

  // Advanced interaction properties
  /// Callback for when the avatar is hovered
  final void Function(bool)? onHover;

  /// Callback for when the avatar is focused
  final void Function(bool)? onFocus;

  /// Focus node for the avatar
  final FocusNode? focusNode;

  /// Whether the avatar should autofocus
  final bool autofocus;

  /// Color to use when the avatar is hovered
  final Color? hoverColor;

  /// Color to use when the avatar is focused
  final Color? focusColor;

  /// Whether to enable feedback when the avatar is tapped
  final bool enableFeedback;

  /// Callback for when the avatar is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the avatar is long-pressed
  final VoidCallback? onLongPress;

  // Animation properties
  /// Whether to animate the avatar when it appears
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Avatar-specific properties
  /// Whether to show a badge on the avatar
  final bool showBadge;

  /// Icon to use for the badge
  final IconData? badgeIcon;

  /// Color of the badge
  final Color badgeColor;

  /// Position of the badge
  final Alignment badgePosition;

  /// Size of the badge
  final double badgeSize;

  /// Whether to show a status indicator
  final bool showStatus;

  /// Color of the status indicator
  final Color statusColor;

  /// Position of the status indicator
  final Alignment statusPosition;

  /// Size of the status indicator
  final double statusSize;

  /// Whether to show a placeholder while the image is loading
  final bool showPlaceholder;

  /// Icon to use for the placeholder
  final IconData placeholderIcon;

  /// Color of the placeholder
  final Color placeholderColor;

  /// Whether to show an error indicator if the image fails to load
  final bool showErrorIndicator;

  /// Icon to use for the error indicator
  final IconData errorIcon;

  /// Color of the error indicator
  final Color errorColor;

  /// Text color for the initials
  final Color initialsTextColor;

  /// Font weight for the initials
  final FontWeight initialsFontWeight;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON validation
  final bool useJsonValidation;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Avatar-specific JSON configuration
  /// Whether to use JSON avatar configuration
  final bool useJsonAvatarConfig;

  /// Avatar-specific JSON configuration
  final Map<String, dynamic>? avatarConfig;

  /// Creates a new avatar image widget.
  const AvatarImageWidget({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.initials,
    this.label,
    this.showLabel = false,
    this.labelStyle,
    this.size = 48.0,
    this.isCircular = true,
    this.borderRadius = 24.0,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.backgroundColor = const Color(0xFF0058FF),
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.alignment = Alignment.center,
    this.isTappable = false,
    this.onTap,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Avatar-specific properties
    this.showBadge = false,
    this.badgeIcon = Icons.check_circle,
    this.badgeColor = Colors.green,
    this.badgePosition = Alignment.bottomRight,
    this.badgeSize = 16.0,
    this.showStatus = false,
    this.statusColor = Colors.green,
    this.statusPosition = Alignment.bottomRight,
    this.statusSize = 12.0,
    this.showPlaceholder = true,
    this.placeholderIcon = Icons.person,
    this.placeholderColor = Colors.grey,
    this.showErrorIndicator = true,
    this.errorIcon = Icons.error,
    this.errorColor = Colors.red,
    this.initialsTextColor = Colors.white,
    this.initialsFontWeight = FontWeight.w500,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Avatar-specific JSON configuration
    this.useJsonAvatarConfig = false,
    this.avatarConfig,
  });

  /// Creates an AvatarImageWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the AvatarImageWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "imageUrl": "https://example.com/avatar.jpg",
  ///   "size": 64,
  ///   "isCircular": true,
  ///   "hasBorder": true,
  ///   "borderColor": "#0066cc"
  /// }
  /// ```
  factory AvatarImageWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse alignment
    Alignment parseAlignment(dynamic alignValue) {
      if (alignValue == null) return Alignment.center;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'topleft':
          case 'top_left':
            return Alignment.topLeft;
          case 'topcenter':
          case 'top_center':
          case 'top':
            return Alignment.topCenter;
          case 'topright':
          case 'top_right':
            return Alignment.topRight;
          case 'centerleft':
          case 'center_left':
          case 'left':
            return Alignment.centerLeft;
          case 'center':
            return Alignment.center;
          case 'centerright':
          case 'center_right':
          case 'right':
            return Alignment.centerRight;
          case 'bottomleft':
          case 'bottom_left':
            return Alignment.bottomLeft;
          case 'bottomcenter':
          case 'bottom_center':
          case 'bottom':
            return Alignment.bottomCenter;
          case 'bottomright':
          case 'bottom_right':
            return Alignment.bottomRight;
          default:
            return Alignment.center;
        }
      }

      return Alignment.center;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return EdgeInsets.zero;
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return EdgeInsets.zero;
    }

    // Parse duration
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) {
        return const Duration(milliseconds: 300);
      }

      if (durationValue is int) {
        return Duration(milliseconds: durationValue);
      } else if (durationValue is Map<String, dynamic>) {
        final int milliseconds =
            (durationValue['milliseconds'] as num?)?.toInt() ?? 0;
        final int seconds = (durationValue['seconds'] as num?)?.toInt() ?? 0;
        final int minutes = (durationValue['minutes'] as num?)?.toInt() ?? 0;

        return Duration(
          milliseconds: milliseconds,
          seconds: seconds,
          minutes: minutes,
        );
      } else if (durationValue is String) {
        // Parse strings like "300ms", "2s", "1m"
        final RegExp durationRegExp = RegExp(r'(\d+)(ms|s|m)');
        final match = durationRegExp.firstMatch(durationValue);

        if (match != null) {
          final int value = int.parse(match.group(1)!);
          final String unit = match.group(2)!;

          switch (unit) {
            case 'ms':
              return Duration(milliseconds: value);
            case 's':
              return Duration(seconds: value);
            case 'm':
              return Duration(minutes: value);
            default:
              return const Duration(milliseconds: 300);
          }
        }
      }

      return const Duration(milliseconds: 300);
    }

    // Parse curve
    Curve parseCurve(dynamic curveValue) {
      if (curveValue == null) return Curves.easeInOut;

      if (curveValue is String) {
        switch (curveValue.toLowerCase()) {
          case 'linear':
            return Curves.linear;
          case 'decelerate':
            return Curves.decelerate;
          case 'ease':
            return Curves.ease;
          case 'easein':
          case 'ease_in':
            return Curves.easeIn;
          case 'easeout':
          case 'ease_out':
            return Curves.easeOut;
          case 'easeinout':
          case 'ease_in_out':
            return Curves.easeInOut;
          case 'elasticin':
          case 'elastic_in':
            return Curves.elasticIn;
          case 'elasticout':
          case 'elastic_out':
            return Curves.elasticOut;
          case 'elasticinout':
          case 'elastic_in_out':
            return Curves.elasticInOut;
          case 'bouncein':
          case 'bounce_in':
            return Curves.bounceIn;
          case 'bounceout':
          case 'bounce_out':
            return Curves.bounceOut;
          case 'bounceinout':
          case 'bounce_in_out':
            return Curves.bounceInOut;
          default:
            return Curves.easeInOut;
        }
      }

      return Curves.easeInOut;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'person':
            return Icons.person;
          case 'face':
            return Icons.face;
          case 'account_circle':
            return Icons.account_circle;
          case 'check':
            return Icons.check;
          case 'check_circle':
            return Icons.check_circle;
          case 'verified':
            return Icons.verified;
          case 'verified_user':
            return Icons.verified_user;
          case 'star':
            return Icons.star;
          case 'favorite':
            return Icons.favorite;
          case 'thumb_up':
            return Icons.thumb_up;
          case 'error':
            return Icons.error;
          case 'warning':
            return Icons.warning;
          case 'info':
            return Icons.info;
          case 'help':
            return Icons.help;
          case 'camera':
            return Icons.camera_alt;
          case 'photo':
            return Icons.photo;
          case 'image':
            return Icons.image;
          case 'edit':
            return Icons.edit;
          case 'settings':
            return Icons.settings;
          case 'more':
            return Icons.more_horiz;
          case 'menu':
            return Icons.menu;
          case 'close':
            return Icons.close;
          case 'add':
            return Icons.add;
          case 'remove':
            return Icons.remove;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final color = parseColor(styleValue['color']);
        final fontSize =
            styleValue['fontSize'] != null
                ? (styleValue['fontSize'] as num).toDouble()
                : null;
        final fontWeight =
            styleValue['fontWeight'] != null
                ? (styleValue['fontWeight'] == 'bold'
                    ? FontWeight.bold
                    : styleValue['fontWeight'] == 'normal'
                    ? FontWeight.normal
                    : FontWeight.normal)
                : null;
        final fontStyle =
            styleValue['fontStyle'] != null
                ? (styleValue['fontStyle'] == 'italic'
                    ? FontStyle.italic
                    : FontStyle.normal)
                : null;
        final letterSpacing =
            styleValue['letterSpacing'] != null
                ? (styleValue['letterSpacing'] as num).toDouble()
                : null;
        final wordSpacing =
            styleValue['wordSpacing'] != null
                ? (styleValue['wordSpacing'] as num).toDouble()
                : null;
        final height =
            styleValue['height'] != null
                ? (styleValue['height'] as num).toDouble()
                : null;
        final decoration =
            styleValue['decoration'] != null
                ? (styleValue['decoration'] == 'underline'
                    ? TextDecoration.underline
                    : styleValue['decoration'] == 'lineThrough'
                    ? TextDecoration.lineThrough
                    : styleValue['decoration'] == 'overline'
                    ? TextDecoration.overline
                    : null)
                : null;
        final fontFamily = styleValue['fontFamily'] as String?;

        return TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          wordSpacing: wordSpacing,
          height: height,
          decoration: decoration,
          fontFamily: fontFamily,
        );
      }

      return null;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          case 'medium':
            return FontWeight.w500;
          case 'semibold':
            return FontWeight.w600;
          case 'black':
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is int) {
        // Handle numeric weights (100, 200, ..., 900)
        return FontWeight.values.firstWhere(
          (weight) => weight.index == weightValue ~/ 100 - 1,
          orElse: () => FontWeight.normal,
        );
      }

      return FontWeight.normal;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    // Parse Avatar-specific configuration
    Map<String, dynamic>? avatarConfig;
    bool useJsonAvatarConfig = json['useJsonAvatarConfig'] as bool? ?? false;

    if (json['avatarConfig'] != null) {
      if (json['avatarConfig'] is Map) {
        avatarConfig = Map<String, dynamic>.from(json['avatarConfig'] as Map);
        useJsonAvatarConfig = true;
      } else if (json['avatarConfig'] is String) {
        try {
          avatarConfig =
              jsonDecode(json['avatarConfig'] as String)
                  as Map<String, dynamic>;
          useJsonAvatarConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return AvatarImageWidget(
      // Basic properties
      imageUrl: json['imageUrl'] as String?,
      assetPath: json['assetPath'] as String?,
      initials: json['initials'] as String?,
      label: json['label'] as String?,
      showLabel: json['showLabel'] as bool? ?? false,
      labelStyle: parseTextStyle(json['labelStyle']),
      size: json['size'] != null ? (json['size'] as num).toDouble() : 48.0,
      isCircular: json['isCircular'] as bool? ?? true,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 24.0,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: parseColor(json['borderColor']) ?? Colors.grey,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      backgroundColor:
          parseColor(json['backgroundColor']) ?? const Color(0xFF0058FF),
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      alignment: parseAlignment(json['alignment']),
      isTappable: json['isTappable'] as bool? ?? false,

      // Advanced interaction properties
      onHover: null, // Cannot be created from JSON directly
      onFocus: null, // Cannot be created from JSON directly
      focusNode: null, // Cannot be created from JSON directly
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: null, // Cannot be created from JSON directly
      onLongPress: null, // Cannot be created from JSON directly
      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: parseDuration(json['animationDuration']),
      animationCurve: parseCurve(json['animationCurve']),

      // Avatar-specific properties
      showBadge: json['showBadge'] as bool? ?? false,
      badgeIcon: parseIconData(json['badgeIcon']) ?? Icons.check_circle,
      badgeColor: parseColor(json['badgeColor']) ?? Colors.green,
      badgePosition: parseAlignment(json['badgePosition']),
      badgeSize:
          json['badgeSize'] != null
              ? (json['badgeSize'] as num).toDouble()
              : 16.0,
      showStatus: json['showStatus'] as bool? ?? false,
      statusColor: parseColor(json['statusColor']) ?? Colors.green,
      statusPosition: parseAlignment(json['statusPosition']),
      statusSize:
          json['statusSize'] != null
              ? (json['statusSize'] as num).toDouble()
              : 12.0,
      showPlaceholder: json['showPlaceholder'] as bool? ?? true,
      placeholderIcon: parseIconData(json['placeholderIcon']) ?? Icons.person,
      placeholderColor: parseColor(json['placeholderColor']) ?? Colors.grey,
      showErrorIndicator: json['showErrorIndicator'] as bool? ?? true,
      errorIcon: parseIconData(json['errorIcon']) ?? Icons.error,
      errorColor: parseColor(json['errorColor']) ?? Colors.red,
      initialsTextColor: parseColor(json['initialsTextColor']) ?? Colors.white,
      initialsFontWeight: parseFontWeight(json['initialsFontWeight']),

      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : null,
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,

      // Avatar-specific JSON configuration
      useJsonAvatarConfig: useJsonAvatarConfig,
      avatarConfig: avatarConfig,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'imageUrl': imageUrl,
      'assetPath': assetPath,
      'initials': initials,
      'label': label,
      'showLabel': showLabel,
      'size': size,
      'isCircular': isCircular,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.value.toRadixString(16).padLeft(8, '0')}',
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'backgroundColor':
          '#${backgroundColor.value.toRadixString(16).padLeft(8, '0')}',
      'isTappable': isTappable,

      // Advanced properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,
      'showBadge': showBadge,
      'badgeColor': '#${badgeColor.value.toRadixString(16).padLeft(8, '0')}',
      'badgePosition': badgePosition.toString(),
      'badgeSize': badgeSize,
      'showStatus': showStatus,
      'statusColor': '#${statusColor.value.toRadixString(16).padLeft(8, '0')}',
      'statusPosition': statusPosition.toString(),
      'statusSize': statusSize,
      'showPlaceholder': showPlaceholder,
      'placeholderColor':
          '#${placeholderColor.value.toRadixString(16).padLeft(8, '0')}',
      'showErrorIndicator': showErrorIndicator,
      'errorColor': '#${errorColor.value.toRadixString(16).padLeft(8, '0')}',
      'initialsTextColor':
          '#${initialsTextColor.value.toRadixString(16).padLeft(8, '0')}',

      // JSON configuration
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonAvatarConfig': useJsonAvatarConfig,
    };
  }

  @override
  AvatarImageWidgetState createState() => AvatarImageWidgetState();
}

class AvatarImageWidgetState extends State<AvatarImageWidget>
    with SingleTickerProviderStateMixin {
  bool _isLoaded = false;
  bool _hasError = false;

  // Animation controller for the avatar
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Callback state
  late Map<String, dynamic> _callbackState;

  // Parsed JSON configuration
  Map<String, dynamic>? _parsedJsonConfig;

  // Validation state
  bool _isValid = true;

  @override
  void initState() {
    super.initState();

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Create animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Start animation if needed
    if (widget.hasAnimation) {
      _animationController.forward();
    }

    // Execute onInit callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onInit')) {
      _executeJsonCallback('onInit');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['imageUrl'] = widget.imageUrl;
      _callbackState['assetPath'] = widget.assetPath;
      _callbackState['initials'] = widget.initials;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = widget.imageUrl ?? widget.assetPath ?? widget.initials;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the current value
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply required validation
        if (rules.containsKey('required') && rules['required'] == true) {
          if (widget.imageUrl == null &&
              widget.assetPath == null &&
              widget.initials == null) {
            _isValid = false;
            return;
          }
        }

        // Apply URL validation
        if (rules.containsKey('validateUrl') && rules['validateUrl'] == true) {
          if (widget.imageUrl != null) {
            final urlPattern = RegExp(
              r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
            );
            if (!urlPattern.hasMatch(widget.imageUrl!)) {
              _isValid = false;
              return;
            }
          }
        }
      }
    }

    _isValid = true;
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  @override
  Widget build(BuildContext context) {
    // Apply animation if needed
    Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildAvatar(),
        if (widget.showLabel && widget.label != null)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              widget.label!,
              style:
                  widget.labelStyle ?? Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      content = FadeTransition(opacity: _animation, child: content);
    }

    // Apply margin
    return Padding(padding: widget.margin, child: content);
  }

  Widget _buildAvatar() {
    Widget avatarContent = _buildAvatarContent();

    // Apply border radius
    Widget avatarWithShape = ClipRRect(
      borderRadius: BorderRadius.circular(
        widget.isCircular ? widget.size / 2 : widget.borderRadius,
      ),
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: avatarContent,
      ),
    );

    // Apply border if needed
    if (widget.hasBorder) {
      avatarWithShape = Container(
        decoration: BoxDecoration(
          shape: widget.isCircular ? BoxShape.circle : BoxShape.rectangle,
          borderRadius:
              widget.isCircular
                  ? null
                  : BorderRadius.circular(widget.borderRadius),
          border: Border.all(
            // color: widget.borderColor,
            color: Colors.transparent, // or Color(0xFF0058FF)
            width: widget.borderWidth,
          ),
        ),
        child: avatarWithShape,
      );
    }

    // Apply shadow if needed
    if (widget.hasShadow) {
      avatarWithShape = Container(
        decoration: BoxDecoration(
          shape: widget.isCircular ? BoxShape.circle : BoxShape.rectangle,
          borderRadius:
              widget.isCircular
                  ? null
                  : BorderRadius.circular(widget.borderRadius),
          boxShadow: [
            BoxShadow(
              color: const Color.fromARGB(
                255,
                190,
                188,
                188,
              ).withAlpha(51), // 0.2 opacity
              blurRadius: widget.elevation,
              spreadRadius: 1,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: avatarWithShape,
      );
    }

    // Apply badge if needed
    if (widget.showBadge) {
      avatarWithShape = Stack(
        clipBehavior: Clip.none,
        children: [
          avatarWithShape,
          Positioned.fill(
            child: Align(
              alignment: widget.badgePosition,
              child: Container(
                width: widget.badgeSize,
                height: widget.badgeSize,
                decoration: BoxDecoration(
                  color: widget.badgeColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1.5),
                ),
                child:
                    widget.badgeIcon != null
                        ? Icon(
                          widget.badgeIcon,
                          color: Colors.white,
                          size: widget.badgeSize * 0.7,
                        )
                        : null,
              ),
            ),
          ),
        ],
      );
    }

    // Apply status indicator if needed
    if (widget.showStatus) {
      avatarWithShape = Stack(
        clipBehavior: Clip.none,
        children: [
          avatarWithShape,
          Positioned.fill(
            child: Align(
              alignment: widget.statusPosition,
              child: Container(
                width: widget.statusSize,
                height: widget.statusSize,
                decoration: BoxDecoration(
                  color: widget.statusColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1.5),
                ),
              ),
            ),
          ),
        ],
      );
    }

    // Apply padding
    Widget avatarWithPadding = Padding(
      padding: widget.padding,
      child: avatarWithShape,
    );

    // Apply advanced interaction properties
    if (widget.isTappable ||
        widget.onHover != null ||
        widget.onFocus != null ||
        widget.onDoubleTap != null ||
        widget.onLongPress != null) {
      // Create a widget with all the interaction properties
      return MouseRegion(
        onEnter: widget.onHover != null ? (_) => widget.onHover!(true) : null,
        onExit: widget.onHover != null ? (_) => widget.onHover!(false) : null,
        child: GestureDetector(
          onTap:
              widget.isTappable
                  ? () {
                    // Execute onTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onTap')) {
                      _executeJsonCallback('onTap');
                    }

                    // Call standard callback
                    if (widget.onTap != null) {
                      widget.onTap!();
                    }
                  }
                  : null,
          onDoubleTap:
              widget.onDoubleTap != null
                  ? () {
                    // Execute onDoubleTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                      _executeJsonCallback('onDoubleTap');
                    }

                    // Call standard callback
                    widget.onDoubleTap!();
                  }
                  : null,
          onLongPress:
              widget.onLongPress != null
                  ? () {
                    // Execute onLongPress callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onLongPress')) {
                      _executeJsonCallback('onLongPress');
                    }

                    // Call standard callback
                    widget.onLongPress!();
                  }
                  : null,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: widget.onFocus,
            autofocus: widget.autofocus,
            child: avatarWithPadding,
          ),
        ),
      );
    }

    return avatarWithPadding;
  }

  Widget _buildAvatarContent() {
    // If we have a URL, try to load the image
    if (widget.imageUrl != null) {
      return Image.network(
        widget.imageUrl!,
        fit: BoxFit.cover,
        width: widget.size,
        height: widget.size,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            // Image loaded successfully
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && !_isLoaded) {
                setState(() {
                  _isLoaded = true;
                  _hasError = false;
                });
              }
            });
            return child;
          }
          return _buildLoadingIndicator();
        },
        errorBuilder: (context, error, stackTrace) {
          // Image failed to load
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && !_isLoaded) {
              setState(() {
                _isLoaded = true;
                _hasError = true;
              });
            }
          });
          return _buildFallbackContent();
        },
      );
    }

    // If we have an asset path, try to load the asset
    if (widget.assetPath != null) {
      try {
        return Image.asset(
          widget.assetPath!,
          fit: BoxFit.cover,
          width: widget.size,
          height: widget.size,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackContent();
          },
        );
      } catch (e) {
        return _buildFallbackContent();
      }
    }

    // If we have initials, show them
    return _buildInitialsAvatar();
  }

  Widget _buildInitialsAvatar() {
    return Container(
      width: widget.size,
      height: widget.size,
      //color: widget.backgroundColor,
      color: const Color(0xFF0058FF),
      alignment: Alignment.center,
      child: Padding(
        padding: const EdgeInsets.all(4.0), // <-- You can adjust this value
        child: Text(
          widget.initials ?? '',
          style: TextStyle(
            color: Colors.white,
            fontSize: _getResponsiveFontSize(context),
            //fontWeight: widget.initialsFontWeight,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    if (!widget.showPlaceholder) {
      return Container(
        width: widget.size,
        height: widget.size,
        color: widget.backgroundColor,
      );
    }

    return Container(
      width: widget.size,
      height: widget.size,
      color: widget.backgroundColor,
      child: Center(
        child: SizedBox(
          width: widget.size * 0.5,
          height: widget.size * 0.5,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(widget.placeholderColor),
          ),
        ),
      ),
    );
  }

  Widget _buildFallbackContent() {
    // If we have initials, use them as fallback
    if (widget.initials != null) {
      return _buildInitialsAvatar();
    }

    // If error indicator is disabled, show a blank container
    if (!widget.showErrorIndicator && _hasError) {
      return Container(
        width: widget.size,
        height: widget.size,
        color: widget.backgroundColor,
      );
    }

    // Show a placeholder icon
    return Container(
      width: widget.size,
      height: widget.size,
      color: widget.backgroundColor,
      child: Icon(
        _hasError ? widget.errorIcon : widget.placeholderIcon,
        color: _hasError ? widget.errorColor : widget.placeholderColor,
        size: widget.size * 0.6,
      ),
    );
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large (>1920px) - Reduced for better fit
  } else if (screenWidth >= 1440) {
    return 16.0; // Large (1440-1920px) - Reduced for better fit
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium (1280-1366px) - Standard size
  } else if (screenWidth >= 768) {
    return 12.0; // Small (768-1024px) - Increased for readability
  } else {
    return 14.0; // Default for very small screens - Consistent
  }
}
