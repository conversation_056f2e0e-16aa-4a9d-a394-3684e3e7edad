import 'package:flutter/material.dart';
import '../screens/responsive_form_demo_screen.dart';

/// A responsive builder that shows the responsive form demo screen
class ResponsiveFormDemoBuilder extends StatelessWidget {
  const ResponsiveFormDemoBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // For now, we only have one version of the demo screen
        // In a real app, you might have different versions for different screen sizes
        return const ResponsiveFormDemoScreen();
      },
    );
  }
}
