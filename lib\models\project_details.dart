class ProjectDetails {
  final String projectName;
  final String? description;
  final String? filePath;

  ProjectDetails({
    required this.projectName,
    this.description,
    this.filePath,
  });

  Map<String, dynamic> toJson() {
    return {
      'projectName': projectName,
      'description': description,
      'filePath': filePath,
    };
  }

  factory ProjectDetails.fromJson(Map<String, dynamic> json) {
    return ProjectDetails(
      projectName: json['projectName'] as String,
      description: json['description'] as String?,
      filePath: json['filePath'] as String?,
    );
  }
}
