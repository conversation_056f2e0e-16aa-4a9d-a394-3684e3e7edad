import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/widgets/responsive_chat_builder.dart';
import 'package:nsl/widgets/responsive_components_builder.dart';
import 'package:nsl/widgets/responsive_settings_builder.dart';
import 'package:nsl/widgets/responsive_transact_builder.dart';
import 'package:nsl/widgets/responsive_build_builder.dart';
import 'package:nsl/widgets/responsive_build_new_builder.dart';
import 'package:nsl/widgets/responsive_widget_binder.dart';
import '../screens/dashboard/dashboard_screen.dart';

class AppNavigationDrawer extends StatelessWidget {
  final String currentRoute;

  const AppNavigationDrawer({
    super.key,
    required this.currentRoute,
  });

  // Helper method to handle navigation safely
  void _handleNavigation(BuildContext context, String route, Widget screen) {
    if (currentRoute != route) {
      // Close the drawer first
      Navigator.pop(context);
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => screen),
        (route) => false, // This removes all previous routes
      );

      // Wait for the drawer animation to complete before navigating
      // Future.delayed(const Duration(milliseconds: 300), () {
      //   //if (context.mounted) {
      //     Navigator.pushAndRemoveUntil(
      //       context,
      //       MaterialPageRoute(builder: (context) => screen),
      //       (route) => false, // This removes all previous routes
      //     );
      //  // }
      // });
    } else {
      // If we're already on the current route, just close the drawer
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Text(
              context.tr('navigation.drawer.appName'),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
              ),
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.dashboard_outlined,
            title: context.tr('navigation.dashboard'),
            isSelected: currentRoute == 'dashboard',
            onTap: () => _handleNavigation(
              context,
              'dashboard',
              const DashboardScreen(),
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.chat_outlined,
            title: context.tr('navigation.chat'),
            isSelected: currentRoute == 'chat',
            onTap: () => _handleNavigation(
              context,
              'chat',
              const ResponsiveChatBuilder(), // Use ResponsiveChatBuilder instead of ChatScreen
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.build_outlined,
            title: context.tr('navigation.build'),
            isSelected: currentRoute == 'create',
            onTap: () => _handleNavigation(
              context,
              'create',
              const ResponsiveBuildBuilder(),
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.swap_horiz,
            title: context.tr('navigation.transact'),
            isSelected: currentRoute == 'transact',
            onTap: () => _handleNavigation(
              context,
              'transact',
              const ResponsiveTransactBuilder(),
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.swap_horiz,
            title: context.tr('navigation.build'),
            isSelected: currentRoute == 'build',
            onTap: () => _handleNavigation(
              context,
              'build',
              const ResponsiveBuildNewBuilder(),
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.widgets_outlined,
            title: context.tr('navigation.components'),
            isSelected: currentRoute == 'components',
            onTap: () => _handleNavigation(
              context,
              'components',
              const ResponsiveComponentsBuilder(),
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.widgets_outlined,
            title: 'WidgetBinder',
            isSelected: currentRoute == 'widgetBinder',
            onTap: () => _handleNavigation(
              context,
              'widgetBinder',
              const ResponsiveWidgetBinderBuilder(),
            ),
          ),

          // _buildDrawerItem(
          //   context: context,
          //   icon: Icons.history,
          //   title: 'My Transactions',
          //   isSelected: currentRoute == 'my_transactions',
          //   onTap: () => _handleNavigation(
          //     context,
          //     'my_transactions',
          //     const ResponsiveMyTransactionsBuilder(),
          //   ),
          // ),
          _buildDrawerItem(
            context: context,
            icon: Icons.settings_outlined,
            title: context.tr('navigation.settings'),
            isSelected: currentRoute == 'settings',
            onTap: () => _handleNavigation(
              context,
              'settings',
              const ResponsiveSettingsBuilder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurface,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      onTap: onTap,
      selected: isSelected,
      selectedTileColor: Theme.of(context).colorScheme.primary.withAlpha(25),
    );
  }
}
