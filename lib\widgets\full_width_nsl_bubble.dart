import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:nsl/screens/workflow/screens/workflow-implementation.dart';
import 'package:nsl/screens/workflow/screens/full_node_view_screen.dart';
import '../models/message.dart';
import '../services/build_service.dart';
import '../theme/app_colors.dart';
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';
import '../providers/build_provider.dart';
import 'package:provider/provider.dart';
import '../screens/build_screen.dart';
import 'chat_text_field.dart';
import 'solution_details_panel.dart';

enum ResponseSection {
  agent,
  entity,
  workflows,
}

enum ResponseSectionMenu {
  prescriptiveText,
  javaCode,
  yamlOutput,
  deployRuntime,
  showWorkflows
}

// A wrapper class to hold the selected section
// This allows us to modify it from the bottom sheet
class SectionWrapper {
  ResponseSection section;
  SectionWrapper(this.section);
}

class FullWidthNSLBubble extends StatefulWidget {
  final Message message;
  final BuildResponse? response;
  final VoidCallback? onLongPress;

  const FullWidthNSLBubble({
    super.key,
    required this.message,
    this.response,
    this.onLongPress,
  });

  @override
  State<FullWidthNSLBubble> createState() => _FullWidthNSLBubbleState();
}

class _FullWidthNSLBubbleState extends State<FullWidthNSLBubble>
    with SingleTickerProviderStateMixin {
  ResponseSection _selectedSection = ResponseSection.workflows;
  late BuildResponse _parsedResponse;
  bool _isParsed = false;
  String _title = '';
  bool _isExpanded = false;
  ResponseSectionMenu _selectedMenuSection =
      ResponseSectionMenu.prescriptiveText;
  final TextEditingController _textController = TextEditingController();
  @override
  void initState() {
    super.initState();
    _parseResponse();
  }

  void _parseResponse() {
    if (widget.response != null) {
      _parsedResponse = widget.response!;
      _isParsed = true;
      _extractTitleFromPrescriptiveText(_parsedResponse.prescriptiveText);
      return;
    }

    try {
      // Try to parse the content if we don't have a direct response object
      final content = widget.message.content;

      // Extract prescriptive text
      String prescriptiveText = '';
      final prescriptiveMatch = RegExp(
              r'## Prescriptive Text\n([\s\S]*?)(?=\n##|\n\*\*Validation Status)')
          .firstMatch(content);
      if (prescriptiveMatch != null && prescriptiveMatch.groupCount >= 1) {
        prescriptiveText = prescriptiveMatch.group(1)?.trim() ?? '';

        // Extract title from prescriptive text (first line or first sentence)
        _extractTitleFromPrescriptiveText(prescriptiveText);
      }

      // Extract YAML output
      String yamlOutput = '';
      final yamlMatch = RegExp(r'```yaml\n([\s\S]*?)```').firstMatch(content);
      if (yamlMatch != null && yamlMatch.groupCount >= 1) {
        yamlOutput = yamlMatch.group(1)?.trim() ?? '';
      }

      // Extract Java code
      String javaCode = '';
      final javaMatch = RegExp(r'```java\n([\s\S]*?)```').firstMatch(content);
      if (javaMatch != null && javaMatch.groupCount >= 1) {
        javaCode = javaMatch.group(1)?.trim() ?? '';
      }

      // Extract validation status
      String validationStatus = '';
      final validationMatch =
          RegExp(r'\*\*Validation Status\*\*: (.*?)(?=\n|$)')
              .firstMatch(content);
      if (validationMatch != null && validationMatch.groupCount >= 1) {
        validationStatus = validationMatch.group(1)?.trim() ?? '';
      }

      // Extract validation error if present
      String? validationError;
      final errorMatch = RegExp(r'\*\*Validation Error\*\*: (.*?)(?=\n|$)')
          .firstMatch(content);
      if (errorMatch != null && errorMatch.groupCount >= 1) {
        validationError = errorMatch.group(1)?.trim();
      }

      _parsedResponse = BuildResponse(
        conversationId: '', // No conversation ID for parsed responses
        prescriptiveText: prescriptiveText,
        yamlOutput: yamlOutput,
        javaCode: javaCode,
        validationStatus: validationStatus,
        validationError: validationError,
      );

      // If we have a direct response object, extract the title
      if (_title.isEmpty) {
        _extractTitleFromPrescriptiveText(_parsedResponse.prescriptiveText);
      }

      _isParsed = true;
    } catch (e) {
      // If parsing fails, just show the original content
      _isParsed = false;
    }
  }

  void _extractTitleFromPrescriptiveText(String text) {
    if (text.isEmpty) {
      _title = 'NSL Response';
      return;
    }

    // Look specifically for markdown headings (lines starting with #)
    final lines = text.split('\n');

    // First pass: Look for level 1 headings (# Title)
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;

      // Check for level 1 markdown heading (single #)
      if (trimmedLine.startsWith('# ')) {
        final headingText = trimmedLine.substring(2).trim();
        if (headingText.isNotEmpty) {
          _title = headingText;
          return;
        }
      }
    }

    // Second pass: Look for any level headings (## Title, ### Title, etc.)
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;

      if (trimmedLine.startsWith('#')) {
        final headingText =
            trimmedLine.replaceFirst(RegExp(r'^#+\s*'), '').trim();
        if (headingText.isNotEmpty) {
          _title = headingText;
          return;
        }
      }
    }

    // Third pass: Look for the first non-empty line as a fallback
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isNotEmpty) {
        // If it's a short line (likely a title)
        if (trimmedLine.length <= 100) {
          _title = trimmedLine;
          return;
        }

        // If it's a longer line, try to extract the first sentence
        final sentenceMatch =
            RegExp(r'^([^.!?]+[.!?])').firstMatch(trimmedLine);
        if (sentenceMatch != null && sentenceMatch.groupCount >= 1) {
          final sentence = sentenceMatch.group(1)?.trim() ?? '';
          if (sentence.length <= 100) {
            _title = sentence;
            return;
          }
        }

        // If we can't find a good sentence, just use the first 80 chars
        _title = trimmedLine.length > 80
            ? '${trimmedLine.substring(0, 80)}...'
            : trimmedLine;
        return;
      }
    }

    // Fallback if no good title found
    _title = 'NSL Response';
  }

  void _toggleExpand() {
    // Get device type for responsive behavior
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    if (_isExpanded) {
      // Close the bottom sheet if on mobile
      if (deviceType == DeviceType.mobile || deviceType == DeviceType.tablet) {
        Navigator.of(context).pop();
      }
      setState(() {
        _isExpanded = false;
      });
    } else {
      setState(() {
        _isExpanded = true;
      });

      // Show bottom sheet only on mobile/tablet
      if (deviceType == DeviceType.mobile || deviceType == DeviceType.tablet) {
        _showBottomSheet();
      } else {
        // For desktop/web, the details will be shown in the right panel
        // Update the selected message in the BuildProvider
        final buildProvider =
            Provider.of<BuildProvider>(context, listen: false);
        buildProvider.selectMessage(widget.message);
      }
    }
  }

  void _showBottomSheet() {
    // Create a mutable wrapper around the selected section
    // _selectedSection = ResponseSection.workflows;
    // _selectedMenuSection = ResponseSectionMenu.prescriptiveText;
    final sectionWrapper = SectionWrapper(_selectedSection);
    final menuSectionWrapper = MenuSectionWrapper(_selectedMenuSection);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setSheetState) {
            return _buildBottomSheetContent(
                context, sectionWrapper, setSheetState, menuSectionWrapper);
          },
        );
      },
    ).then((_) {
      // When bottom sheet is closed
      setState(() {
        _isExpanded = false;
      });
    });
  }

  Widget _buildBottomSheetContent(
      BuildContext context,
      SectionWrapper sectionWrapper,
      StateSetter setSheetState,
      MenuSectionWrapper menuSectionWrapper) {
    // Extract the current section from the wrapper
    final ResponseSection currentSection = sectionWrapper.section;
    // Uncomment when needed
    // final ResponseSectionMenu currentMenuSection = menuSectionWrapper.section;
    // final TextEditingController bottomSheetTextController = TextEditingController();

    return Container(
      height: MediaQuery.of(context).size.height * 0.95,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header strip with close button, title, and more button
          Expanded(
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.sm,
                  ),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Handle
                      // Center(
                      //   child: Container(
                      //     width: 40,
                      //     height: 4,
                      //     margin: EdgeInsets.only(bottom: AppSpacing.xs),
                      //     decoration: BoxDecoration(
                      //       color: Theme.of(context).dividerColor,
                      //       borderRadius: BorderRadius.circular(2),
                      //     ),
                      //   ),
                      // ),
                      // Header strip
                      Row(
                        children: [
                          // Close button
                          IconButton(
                            icon: Icon(Icons.close, size: 20),
                            padding: EdgeInsets.all(AppSpacing.xs),
                            constraints: BoxConstraints(),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            color: AppColors.textSecondaryLight,
                          ),
                          // Title (centered and expanded)
                          Expanded(
                            child: Center(
                              child: Text(
                                _title,
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.color, // Use theme text color
                                      fontFamily:
                                          'TiemposText', // Use TiemposText for title
                                    ),
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          // More button
                          IconButton(
                            icon: const Icon(Icons.more_horiz, size: 20),
                            padding: EdgeInsets.all(AppSpacing.xs),
                            constraints: BoxConstraints(),
                            onPressed: () {
                              final RenderBox button =
                                  context.findRenderObject() as RenderBox;
                              final Offset offset =
                                  button.localToGlobal(Offset.zero);

                              showMenu(
                                context: context,
                                position: RelativeRect.fromLTRB(
                                    MediaQuery.of(context).size.width -
                                        offset.dx,
                                    offset.dy + 50, //+ button.size.height,
                                    offset.dx, //+ button.size.width,
                                    offset.dy //+ button.size.height,
                                    ),
                                items: <PopupMenuEntry<ResponseSectionMenu>>[
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.prescriptiveText,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.description_outlined,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Prescriptive'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.javaCode,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.code,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Java'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.yamlOutput,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.data_object_outlined,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('YAML'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.deployRuntime,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.rocket_launch,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Deploy to Runtime'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.showWorkflows,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.account_tree_outlined,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Node View'),
                                      ],
                                    ),
                                  ),
                                ],
                              ).then((ResponseSectionMenu? selected) {
                                if (selected != null) {
                                  setSheetState(() {
                                    // Update the section in the wrapper
                                    // This will be reflected in the bottom sheet
                                    _selectedMenuSection = selected;
                                  });
                                }
                              });
                            },
                            color: AppColors.textSecondaryLight,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Section selector with current section name
                _selectedMenuSection == ResponseSectionMenu.prescriptiveText
                    ? Padding(
                        padding: EdgeInsets.all(AppSpacing.md),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section name
                            _buildSectionSelector(
                                sectionWrapper, setSheetState),
                          ],
                        ),
                      )
                    : Container(),

                // Content
                Expanded(
                  child: _selectedMenuSection ==
                          ResponseSectionMenu.showWorkflows
                      ? Column(
                          children: [
                            Expanded(
                                child: const AdvancedWorkflowBuilder(
                              isFullScreen: false,
                            )),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                _buildFullViewButton(),
                                const SizedBox(
                                  width: AppSpacing.md,
                                ),
                                deployButton(),
                              ],
                            )
                          ],
                        ) // Show the workflow builder widget
                      : SingleChildScrollView(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.md,
                            vertical: AppSpacing.sm,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              MarkdownBody(
                                data: _isParsed
                                    ? _selectedMenuSection ==
                                            ResponseSectionMenu.prescriptiveText
                                        ? _getSectionContent(currentSection)
                                        : _getMenuSectionContent(
                                            _selectedMenuSection)
                                    : widget.message.content,
                                styleSheet: MarkdownStyleSheet(
                                  p: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color, // Use theme text color
                                    fontFamily:
                                        'TiemposText', // Use TiemposText font for NSL responses
                                  ),
                                  h1: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .headlineMedium
                                        ?.color, // Use theme text color
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                  ),
                                  h2: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .headlineSmall
                                        ?.color, // Use theme text color
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                  h3: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.color, // Use theme text color
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                  blockquote: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withAlpha(
                                            204), // 0.8 opacity (204/255)
                                    fontFamily: 'TiemposText',
                                    fontStyle: FontStyle.italic,
                                  ),
                                  em: TextStyle(
                                    fontFamily: 'TiemposText',
                                    fontStyle: FontStyle.italic,
                                  ),
                                  strong: TextStyle(
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                  ),
                                  code: TextStyle(
                                    backgroundColor:
                                        Theme.of(context).colorScheme.surface,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color, // Use theme text color
                                    fontFamily:
                                        'SFProText', // Keep SFProText for code blocks
                                  ),
                                  codeblockDecoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.surface,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                              // if (_isParsed && _parsedResponse.validationStatus.isNotEmpty)

                              //   Padding(
                              //     padding: EdgeInsets.only(top: AppSpacing.sm),
                              //     child: Text(
                              //       "Validation Status: ${_parsedResponse.validationStatus}",
                              //       style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              //         fontWeight: FontWeight.bold,
                              //         color: _parsedResponse.validationStatus.toLowerCase().contains('success')
                              //             ? AppColors.success
                              //             : AppColors.error,
                              //       ),
                              //     ),
                              //   ),
                              // if (_isParsed && _parsedResponse.validationError != null)
                              //   Padding(
                              //     padding: EdgeInsets.only(top: AppSpacing.xxs),
                              //     child: Text(
                              //       "Error: ${_parsedResponse.validationError}",
                              //       style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              //         color: AppColors.error,
                              //       ),
                              //     ),
                              //   ),
                              // Add Deploy to Runtime button if we have a conversation ID
                              deployButton(),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
          Consumer<BuildProvider>(builder: (context, buildProvider, _) {
            return ChatTextField(
              controller: _textController,
              hintText: 'Chat with NSL...',
              isLoading: buildProvider.isLoading,
              onSubmitted: (value) {
                Navigator.of(context).pop();
                buildProvider.sendMessage(value);
              },
              onCancel: () => buildProvider.cancelRequest(),
            );
          })
        ],
      ),
    );
  }

  Widget _buildFullViewButton() {
    return Center(
      child: ElevatedButton.icon(
        icon: Icon(
          Icons.fullscreen,
          color: Colors.red,
        ),
        label: Text('Full View'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.secondary,
          foregroundColor: Theme.of(context).colorScheme.onSecondary,
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
        onPressed: () {
          // Close the bottom sheet first
          Navigator.of(context).pop();

          // Navigate to the full view screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const FullNodeViewScreen(),
            ),
          );
        },
      ),
    );
  }

  Widget deployButton() {
    if (_isParsed && _parsedResponse.conversationId.isNotEmpty) {
      return Padding(
        padding: EdgeInsets.only(top: AppSpacing.xs, bottom: AppSpacing.md),
        child: Center(
          child: ElevatedButton.icon(
            icon: Icon(
              Icons.cloud_upload,
              color: Colors.white,
            ),
            label: Text('Deploy to Runtime'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            onPressed: () => _deployToRuntime(context),
          ),
        ),
      );
    }
    return Container();
  }

  String _getSectionContent([ResponseSection? section]) {
    if (!_isParsed) {
      return widget.message.content;
    }

    // Use provided section or fall back to the selected section
    ResponseSection sectionToUse = section ?? _selectedSection;

    switch (sectionToUse) {
      case ResponseSection.agent:
        // If the prescriptive text starts with the title, remove it to avoid duplication
        if (_title.isNotEmpty &&
            _parsedResponse.prescriptiveText.trim().startsWith(_title)) {
          final remainingText = _parsedResponse.prescriptiveText
              .trim()
              .substring(_title.length)
              .trim();
          return remainingText;
        }
        return _parsedResponse.prescriptiveText;
      case ResponseSection.entity:
        //  return "```java\n${_parsedResponse.javaCode}\n```";
        //return "```java\n${_parsedResponse.prescriptiveText}\n```";

        return _parsedResponse.prescriptiveText;
      case ResponseSection.workflows:
        // return "```yaml\n${_parsedResponse.yamlOutput}\n```";
        // return "```java\n${_parsedResponse.prescriptiveText}\n```";

        return _parsedResponse.prescriptiveText;
    }
  }

  String _getMenuSectionContent([ResponseSectionMenu? section]) {
    if (!_isParsed) {
      return widget.message.content;
    }

    // Use provided section or fall back to the selected section
    ResponseSectionMenu sectionToUse = section ?? _selectedMenuSection;

    switch (sectionToUse) {
      case ResponseSectionMenu.prescriptiveText:
        // If the prescriptive text starts with the title, remove it to avoid duplication
        if (_title.isNotEmpty &&
            _parsedResponse.prescriptiveText.trim().startsWith(_title)) {
          final remainingText = _parsedResponse.prescriptiveText
              .trim()
              .substring(_title.length)
              .trim();
          return remainingText;
        }
        return _parsedResponse.prescriptiveText;
      case ResponseSectionMenu.javaCode:
        return "```java\n${_parsedResponse.javaCode}\n```";
      //return "```java\n${_parsedResponse.prescriptiveText}\n```";

      case ResponseSectionMenu.yamlOutput:
        return "```yaml\n${_parsedResponse.yamlOutput}\n```";

      case ResponseSectionMenu.deployRuntime:
        return "Deploying Solution";
      case ResponseSectionMenu.showWorkflows:
        return "Coming Soon";
    }
  }

  // Removed unused method
  // String _getSectionTitle([ResponseSection? section]) {
  //   // Use provided section or fall back to the selected section
  //   ResponseSection sectionToUse = section ?? _selectedSection;
  //
  //   switch (sectionToUse) {
  //     case ResponseSection.agent:
  //       return "Agent";
  //     case ResponseSection.entity:
  //       return "Entity";
  //     case ResponseSection.workflows:
  //       return "Workflow";
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    // Get device type for responsive spacing
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    return GestureDetector(
        // Only trigger onLongPress if it's provided
        onLongPress: widget.onLongPress != null
            ? () {
                // Check if we're in a bottom sheet or panel by looking at the context
                final isInBottomSheet = context.findAncestorWidgetOfExactType<
                        SolutionDetailsPanel>() !=
                    null;

                // Only trigger the callback if we're not in a bottom sheet
                if (!isInBottomSheet) {
                  widget.onLongPress!();
                }
              }
            : null,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            vertical:
                AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
          ),
          child: Center(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal:
                    AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType),
              ),
              constraints: BoxConstraints(
                maxWidth: 800, // Limit max width for very large screens
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Removed NSL header

                  // Title section (always visible)
                  if (_isParsed && _title.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: AppSpacing.xs),
                      child: InkWell(
                        onTap: _toggleExpand,
                        borderRadius: BorderRadius.circular(8.0),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(
                            vertical: AppSpacing.xs,
                            horizontal: AppSpacing.sm,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .primary
                                .withAlpha(20),
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withAlpha(50),
                              width: 1.0,
                            ),
                          ),
                          child: Text(
                            _title,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.color, // Use theme text color
                                  fontFamily:
                                      'TiemposText', // Use TiemposText for title
                                ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ));
  }

  Widget _buildSectionSelector(
      [SectionWrapper? sectionWrapper, StateSetter? setSheetState]) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          _buildSectionButton(
            ResponseSection.workflows,
            "Workflow",
            Icons.data_object_outlined,
            sectionWrapper,
            setSheetState,
          ),
          _buildSectionButton(
            ResponseSection.agent,
            "Agent",
            Icons.description_outlined,
            sectionWrapper,
            setSheetState,
          ),
          _buildSectionButton(
            ResponseSection.entity,
            "Entity",
            Icons.code,
            sectionWrapper,
            setSheetState,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionButton(
      ResponseSection section, String label, IconData icon,
      [SectionWrapper? sectionWrapper, StateSetter? setSheetState]) {
    // Determine which section to use for comparison
    ResponseSection sectionToUse;

    if (sectionWrapper != null) {
      // If we're in a bottom sheet, use the section from the wrapper
      sectionToUse = sectionWrapper.section;
    } else {
      // Otherwise, use the widget's selected section
      sectionToUse = _selectedSection;
    }

    final isSelected = sectionToUse == section;

    return Expanded(
      flex: 1, // section == ResponseSection.agent ? 3 : 2,
      child: InkWell(
        onTap: () {
          // If we're in a bottom sheet, use the provided StateSetter
          if (setSheetState != null && sectionWrapper != null) {
            setSheetState(() {
              // Update the section in the wrapper
              // This will be reflected in the bottom sheet
              sectionWrapper.section = section;
            });
          } else {
            // Otherwise, use the widget's setState
            setState(() {
              _selectedSection = section;
            });
          }
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          height: 40,
          padding: EdgeInsets.only(left: 0),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: AppSpacing.xxs),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected
                          ? Colors.white
                          : Theme.of(context).colorScheme.onSurface,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  void _showCreateAnotherSolutionDialog(BuildContext context) {
    if (!mounted) return;

    // Use a local variable to avoid using context across async gaps
    final localContext = context;

    showDialog(
      context: localContext,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('Deployment Successful'),
          content: Text('Do you want to create another solution?'),
          actions: [
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                // Close the bottom sheet
                if (mounted) {
                  Navigator.of(localContext).pop();
                }

                // Reset the expanded state
                if (mounted) {
                  setState(() {
                    _isExpanded = false;
                  });
                }

                // Clear the chat and go back to build screen initial view
                if (mounted) {
                  // Use the static method from BuildScreen to toggle the view
                  BuildScreen.toggleView(localContext);
                }
              },
              child: Text('No'),
            ),
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                // Close the bottom sheet
                if (mounted) {
                  Navigator.of(localContext).pop();
                }

                // Reset the expanded state
                if (mounted) {
                  setState(() {
                    _isExpanded = false;
                  });
                }

                // Don't clear the chat - just allow the user to continue with the current chat
              },
              style: TextButton.styleFrom(
                backgroundColor: Theme.of(localContext).colorScheme.primary,
                foregroundColor: Theme.of(localContext).colorScheme.onPrimary,
              ),
              child: Text('Yes'),
            ),
          ],
        );
      },
    );
  }

  void _showRetryDialog(
      BuildContext context, String conversationId, bool isRetry) {
    // Check if the widget is still mounted before showing dialog
    if (!mounted) return;

    // Use a local variable to avoid using context across async gaps
    final localContext = context;

    showDialog(
      context: localContext,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('Deployment Failed'),
          content: Text('Something went wrong, please try again'),
          actions: [
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                if (isRetry) {
                  // This was already a retry attempt, go back to build initial view
                  // Close the bottom sheet
                  if (mounted) {
                    Navigator.of(localContext).pop();
                  }

                  // Reset the expanded state
                  if (mounted) {
                    setState(() {
                      _isExpanded = false;
                    });
                  }

                  // Get back to build initial view
                  if (mounted) {
                    final buildProvider =
                        Provider.of<BuildProvider>(localContext, listen: false);
                    buildProvider.clearChat();
                  }
                } else {
                  // Try again
                  if (mounted) {
                    // Use Future.microtask to avoid calling setState during build
                    Future.microtask(() {
                      if (mounted) {
                        // Create a new method call that doesn't use the context from the closure
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted) {
                            _retryDeployment(localContext, conversationId);
                          }
                        });
                      }
                    });
                  }
                }
              },
              style: TextButton.styleFrom(
                backgroundColor: Theme.of(localContext).colorScheme.primary,
                foregroundColor: Theme.of(localContext).colorScheme.onPrimary,
              ),
              child: Text('Try Again'),
            ),
          ],
        );
      },
    );
  }

  void _retryDeployment(BuildContext context, String conversationId) async {
    // Use a local variable to avoid using context across async gaps
    final localContext = context;
    // Store the scaffold messenger for later use
    final scaffoldMessenger = ScaffoldMessenger.of(localContext);

    // Show loading indicator
    BuildContext? dialogContext;
    if (mounted) {
      showDialog(
        context: localContext,
        barrierDismissible: false,
        builder: (BuildContext ctx) {
          dialogContext = ctx;
          return AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('Retrying deployment...'),
              ],
            ),
          );
        },
      );
    }

    try {
      final buildService = BuildService();
      final success = await buildService.deployToRuntime(conversationId);

      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null && mounted) {
        // Store in local variable to avoid using across async gap
        final localDialogContext = dialogContext;
        if (localDialogContext != null) {
          Navigator.of(localDialogContext).pop();
        }
      }

      if (success) {
        // Show success message
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Deployed successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Show the confirmation dialog if still mounted
        if (mounted) {
          // Use Future.microtask to avoid calling setState during build
          Future.microtask(() {
            if (mounted) {
              // Create a new method call that doesn't use the context from the closure
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _showCreateAnotherSolutionDialog(localContext);
                }
              });
            }
          });
        }
      } else {
        // This was a retry and it failed again, go back to build initial view
        if (mounted) {
          _showRetryDialog(localContext, conversationId, true);
        }
      }
    } catch (e) {
      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null && mounted) {
        // Store in local variable to avoid using across async gap
        final localDialogContext = dialogContext;
        if (localDialogContext != null) {
          Navigator.of(localDialogContext).pop();
        }
      }

      // This was a retry and it failed again, go back to build initial view
      if (mounted) {
        // Store context in local variable to avoid using across async gap
        final localContext = context;
        _showRetryDialog(localContext, conversationId, true);
      }
    }
  }

  void _deployToRuntime(BuildContext context) async {
    // Use a local variable to avoid using context across async gaps
    final localContext = context;
    if (!_isParsed || _parsedResponse.conversationId.isEmpty) {
      ScaffoldMessenger.of(localContext).showSnackBar(
        SnackBar(content: Text('No conversation ID available for deployment')),
      );
      return;
    }

    // Store the conversation ID for later use
    final String conversationId = _parsedResponse.conversationId;

    // Store the scaffold messenger for later use
    final scaffoldMessenger = ScaffoldMessenger.of(localContext);

    // Show loading indicator
    BuildContext? dialogContext;
    if (mounted) {
      showDialog(
        context: localContext,
        barrierDismissible: false,
        builder: (BuildContext ctx) {
          dialogContext = ctx;
          return AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('Deploying to runtime...'),
              ],
            ),
          );
        },
      );
    }

    try {
      final buildService = BuildService();
      final success = await buildService.deployToRuntime(conversationId);

      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null && mounted) {
        // Store in local variable to avoid using across async gap
        final localDialogContext = dialogContext;
        if (localDialogContext != null) {
          Navigator.of(localDialogContext).pop();
        }
      }

      if (success) {
        // Show success message
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Deployed successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Show the confirmation dialog if still mounted
        if (mounted) {
          // Use Future.microtask to avoid calling setState during build
          Future.microtask(() {
            if (mounted) {
              // Create a new method call that doesn't use the context from the closure
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _showCreateAnotherSolutionDialog(localContext);
                }
              });
            }
          });
        }
      } else {
        // Show error dialog for retry
        if (mounted) {
          // Store context in local variable to avoid using across async gap
          final localContext = context;
          _showRetryDialog(localContext, conversationId, false);
        }
      }
    } catch (e) {
      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null && mounted) {
        // Store in local variable to avoid using across async gap
        final localDialogContext = dialogContext;
        if (localDialogContext != null) {
          Navigator.of(localDialogContext).pop();
        }
      }

      // Show error dialog for retry
      if (mounted) {
        // Store context in local variable to avoid using across async gap
        final localContext = context;
        _showRetryDialog(localContext, conversationId, false);
      }
    }
  }
}

class MenuSectionWrapper {
  ResponseSectionMenu section;
  MenuSectionWrapper(this.section);
}
