class SpeechToTextResponseModel {
  final bool success;
  final String text;
  final String? language;
  final double? confidence;
  final String? message;
  final String? error;

  SpeechToTextResponseModel({
    required this.success,
    required this.text,
    this.language,
    this.confidence,
    this.message,
    this.error,
  });

  factory SpeechToTextResponseModel.fromJson(Map<String, dynamic> json) {
    return SpeechToTextResponseModel(
      success: json['success'] ?? false,
      text: json['text'] ?? '',
      language: json['language'],
      confidence: json['confidence'] != null ? 
        (json['confidence'] is double ? json['confidence'] : double.tryParse(json['confidence'].toString())) : null,
      message: json['message'],
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'text': text,
      if (language != null) 'language': language,
      if (confidence != null) 'confidence': confidence,
      if (message != null) 'message': message,
      if (error != null) 'error': error,
    };
  }
}
