import 'package:flutter/material.dart';
import 'package:nsl/widgets/responsive_builder.dart';

enum MessageType { user, assistant }

class ChatBubble extends StatelessWidget {
  final String message;
  final MessageType type;
  final String? timestamp;

  final DeviceType deviceType;

  const ChatBubble({
    super.key,
    required this.message,
    required this.type,
    this.timestamp,
    this.deviceType = DeviceType.desktop,
    bool? showTail,
  });

  @override
  Widget build(BuildContext context) {
    // Get the current theme brightness
    // final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Align(
      alignment: type == MessageType.user
          ? Alignment.centerRight
          : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width *
              (deviceType == DeviceType.mobile ? 0.8 : 0.75),
        ),
        child: Container(
          margin: EdgeInsets.symmetric(
              vertical: deviceType == DeviceType.mobile ? 6 : 8,
              horizontal: deviceType == DeviceType.mobile ? 12 : 16),
          padding: EdgeInsets.all(deviceType == DeviceType.mobile ? 10 : 12),
          // Rest of the code remains the same
        ),
      ),
    );
  }
}

// ... rest of the code for _getBubbleColor and _getBubbleRadius
// List<Widget> getWelcomeSuggestions() {
//   return [
//     SuggestionChip(
//       label: "What can you do?",
//       onTap: () => _handleSuggestionTap("What can you do?"),
//       deviceType: deviceType,
//     ),
//     SuggestionChip(
//       label: "Help me with Flutter",
//       onTap: () => _handleSuggestionTap("Help me with Flutter"),
//       deviceType: deviceType,
//     ),
//     SuggestionChip(
//       label: "Write some code",
//       onTap: () => _handleSuggestionTap("Write some code"),
//       deviceType: deviceType,
//     ),
//   ];
// }

// // Add code block with copy button
// List<Widget> getCodeComponents(String code, String language) {
//   return [
//     InteractiveCodeBlock(
//       code: code,
//       language: language,
//       onCopy: () => _copyToClipboard(code),
//       deviceType: deviceType,
//     ),
//   ];
// }

// // Add interactive buttons below certain responses
// List<Widget> getResponseActions() {
//   return [
//     InlineButton(
//       label: "Regenerate",
//       icon: Icons.refresh,
//       onTap: () => _regenerateResponse(),
//       deviceType: deviceType,
//     ),
//     InlineButton(
//       label: "Copy",
//       icon: Icons.content_copy,
//       onTap: () => _copyToClipboard(_messages.last['text']),
//       deviceType: deviceType,
//     ),
//     InlineButton(
//       label: "Save",
//       icon: Icons.bookmark_border,
//       onTap: () => _saveResponse(),
//       deviceType: deviceType,
//     ),
//   ];
// }
