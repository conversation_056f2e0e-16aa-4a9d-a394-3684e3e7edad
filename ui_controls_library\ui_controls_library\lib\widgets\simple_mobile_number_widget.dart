import 'package:flutter/material.dart';

class SimpleMobileNumberWidget extends StatefulWidget {
  final String? initialValue;
  final String countryCode;
  final bool showCountryCode;
  final String? labelText;
  final String? hintText;
  final Color? borderColor;
  final double borderRadius;
  final Function(String)? onChanged;

  const SimpleMobileNumberWidget({
    super.key,
    this.initialValue,
    this.countryCode = '+1',
    this.showCountryCode = true,
    this.labelText,
    this.hintText = 'Enter mobile number',
    this.borderColor,
    this.borderRadius = 4.0,
    this.onChanged,
  });

  @override
  SimpleMobileNumberWidgetState createState() => SimpleMobileNumberWidgetState();
}

class SimpleMobileNumberWidgetState extends State<SimpleMobileNumberWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.labelText != null) ...[
          Text(
            widget.labelText!,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
        ],
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.borderColor ?? theme.dividerColor,
            ),
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: Row(
            children: [
              if (widget.showCountryCode) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: widget.borderColor ?? theme.dividerColor,
                      ),
                    ),
                  ),
                  child: Text(
                    widget.countryCode,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
              Expanded(
                child: TextField(
                  controller: _controller,
                  decoration: InputDecoration(
                    hintText: widget.hintText,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                  ),
                  keyboardType: TextInputType.phone,
                  onChanged: widget.onChanged,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
