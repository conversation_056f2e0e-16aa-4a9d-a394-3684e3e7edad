import 'package:flutter/material.dart';

class CustomEntityVersionDropdown extends StatefulWidget {
  final String selectedVersion;
  final List<String> versions;
  final Function(String) onVersionSelected;

  const CustomEntityVersionDropdown({
    Key? key,
    required this.selectedVersion,
    required this.versions,
    required this.onVersionSelected,
  }) : super(key: key);

  @override
  _CustomEntityVersionDropdownState createState() =>
      _CustomEntityVersionDropdownState();
}

class _CustomEntityVersionDropdownState
    extends State<CustomEntityVersionDropdown> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    } else {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    }
    setState(() {
      _isDropdownOpen = !_isDropdownOpen;
    });
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: () {
          // Close dropdown when tapping outside
          _toggleDropdown();
        },
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: Colors.transparent,
          child: Stack(
            children: [
              Positioned(
                left: offset.dx,
                top: offset.dy + renderBox.size.height,
                width: renderBox.size.width,
                child: CompositedTransformFollower(
                  link: _layerLink,
                  showWhenUnlinked: false,
                  offset: Offset(0.0, 0.0),
                  child: GestureDetector(
                    onTap: () {
                      // Prevent closing when tapping on the dropdown itself
                    },
                    child: Material(
                      elevation: 4.0,
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Top section showing selected version and folder icon
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 3, vertical: 3),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(8)),
                              color: Colors.white,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.folder_outlined,
                                    size: 16, color: Colors.black),
                                Icon(Icons.keyboard_arrow_down,
                                    size: 16, color: Colors.black),
                                SizedBox(width: 4),
                                Text(
                                  widget.selectedVersion,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[700],
                                    fontFamily: 'TiemposText',
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Divider(height: 1, color: Colors.grey[300]),

                          // List of selectable versions
                          ...widget.versions.map((version) {
                            return InkWell(
                              onTap: () {
                                widget.onVersionSelected(version);
                                _toggleDropdown();
                              },
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Text(
                                      version,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[800],
                                        fontFamily: 'TiemposText',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleDropdown,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            border: _isDropdownOpen
                ? Border.all(color: Colors.grey.shade300)
                : Border.all(color: Colors.transparent),
            borderRadius: BorderRadius.circular(6),
            // color: Colors.white,
            boxShadow: _isDropdownOpen
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    )
                  ]
                : [],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.folder_outlined, size: 16, color: Colors.black),
              Icon(Icons.keyboard_arrow_down, size: 16, color: Colors.black),
              SizedBox(width: 4),
              Text(
                widget.selectedVersion,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[800],
                  fontFamily: 'TiemposText',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _overlayEntry?.remove();
    super.dispose();
  }
}
