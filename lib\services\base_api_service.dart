import 'package:dio/dio.dart';
import 'dio_client.dart';
import '../utils/error_handler.dart';
import '../utils/logger.dart';

/// A base API service that provides common functionality for all API services
abstract class BaseApiService {
  // Dio client for making API requests
  final Dio _dio = DioClient().client;

  /// Get the Dio client
  Dio get dio => _dio;

  /// Make a GET request to the specified endpoint
  Future<dynamic> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    String? baseUrl,
    bool requiresAuth = false,
  }) async {
    try {
      Logger.info('GET request to $endpoint');

      // If authentication is required and no options provided, get auth options
      if (requiresAuth && options == null) {
        options = await getAuthOptions();
      }

      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );

      Logger.info('GET response from $endpoint: ${response.statusCode}');
      return response.data;
    } catch (e) {
      final errorMessage =
          ErrorHandler.handleError(e, context: 'GET $endpoint');
      Logger.error('GET request failed: $errorMessage');
      rethrow;
    }
  }

  /// Make a POST request to the specified endpoint
  Future<dynamic> post(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    String? baseUrl,
    bool requiresAuth = false,
  }) async {
    try {
      Logger.info('POST request to $endpoint');

      // If authentication is required and no options provided, get auth options
      if (requiresAuth && options == null) {
        options = await getAuthOptions();
      }

      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );

      Logger.info('POST response from $endpoint: ${response.statusCode}');
      return response.data;
    } catch (e) {
      final errorMessage =
          ErrorHandler.handleError(e, context: 'POST $endpoint');
      Logger.error('POST request failed: $errorMessage');
      rethrow;
    }
  }

  /// Make a PUT request to the specified endpoint
  Future<dynamic> put(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    String? baseUrl,
    bool requiresAuth = false,
  }) async {
    try {
      Logger.info('PUT request to $endpoint');

      // If authentication is required and no options provided, get auth options
      if (requiresAuth && options == null) {
        options = await getAuthOptions();
      }

      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );

      Logger.info('PUT response from $endpoint: ${response.statusCode}');
      return response.data;
    } catch (e) {
      final errorMessage =
          ErrorHandler.handleError(e, context: 'PUT $endpoint');
      Logger.error('PUT request failed: $errorMessage');
      rethrow;
    }
  }

  /// Make a DELETE request to the specified endpoint
  Future<dynamic> delete(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    String? baseUrl,
    bool requiresAuth = false,
  }) async {
    try {
      Logger.info('DELETE request to $endpoint');

      // If authentication is required and no options provided, get auth options
      if (requiresAuth && options == null) {
        options = await getAuthOptions();
      }

      final response = await _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      Logger.info('DELETE response from $endpoint: ${response.statusCode}');
      return response.data;
    } catch (e) {
      final errorMessage =
          ErrorHandler.handleError(e, context: 'DELETE $endpoint');
      Logger.error('DELETE request failed: $errorMessage');
      rethrow;
    }
  }

  /// Make a PATCH request to the specified endpoint
  Future<dynamic> patch(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    String? baseUrl,
    bool requiresAuth = false,
  }) async {
    try {
      Logger.info('PATCH request to $endpoint');

      // If authentication is required and no options provided, get auth options
      if (requiresAuth && options == null) {
        options = await getAuthOptions();
      }

      final response = await _dio.patch(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );

      Logger.info('PATCH response from $endpoint: ${response.statusCode}');
      return response.data;
    } catch (e) {
      final errorMessage =
          ErrorHandler.handleError(e, context: 'PATCH $endpoint');
      Logger.error('PATCH request failed: $errorMessage');
      rethrow;
    }
  }

  /// Handle API response
  Map<String, dynamic> handleResponse(Response response) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      return {
        'success': true,
        'data': response.data,
      };
    } else {
      return {
        'success': false,
        'message': response.data['message'] ?? 'Unknown error',
      };
    }
  }

  /// Create options with authorization header
  Options createAuthOptions(String token) {
    return Options(
      headers: {
        'Authorization': 'Bearer $token',
      },
    );
  }

  /// Get authenticated options with a valid token
  Future<Options?> getAuthOptions() async {
    try {
      // This method should be implemented in AuthService, but we can access it through service locator
      // For now, we'll use a placeholder implementation
      final token = await getValidToken();

      if (token != null) {
        return createAuthOptions(token);
      }
      return null;
    } catch (e) {
      Logger.error('Error getting auth options: $e');
      return null;
    }
  }

  /// This method should be overridden in AuthService to provide a valid token
  Future<String?> getValidToken() async {
    // This is a placeholder that should be overridden
    return null;
  }

  /// This method should be overridden in AuthService to provide the user ID
  Future<String?> getUserId() async {
    // This is a placeholder that should be overridden
    return null;
  }
}
