import 'package:flutter/material.dart';
import '../routes/app_routes.dart';
import 'logger.dart';

/// A service that provides navigation capabilities throughout the app
/// without requiring a BuildContext.
class NavigationService {
  // Private constructor to prevent instantiation
  NavigationService._();

  // Navigator key for accessing the navigator state
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  /// Get the navigator state
  static NavigatorState? get navigator => navigatorKey.currentState;

  /// Get the current context
  static BuildContext? get context => navigatorKey.currentContext;

  /// Navigate to a named route
  static Future<dynamic>? navigateTo(String routeName, {Object? arguments}) {
    Logger.info('Navigating to $routeName with arguments: $arguments');
    return navigator?.pushNamed(routeName, arguments: arguments);
  }

  /// Navigate to a route and remove all previous routes
  static Future<dynamic>? navigateToReplacement(String routeName,
      {Object? arguments}) {
    Logger.info(
        'Navigating to $routeName with replacement and arguments: $arguments');
    return navigator?.pushNamedAndRemoveUntil(routeName, (route) => false,
        arguments: arguments);
  }

  /// Navigate to a route using MaterialPageRoute
  static Future<dynamic>? push(Widget page) {
    Logger.info('Pushing page: ${page.runtimeType}');
    return navigator?.push(
      MaterialPageRoute(builder: (context) => page),
    );
  }

  /// Navigate to a route using MaterialPageRoute and remove all previous routes
  static Future<dynamic>? pushReplacement(Widget page) {
    Logger.info('Pushing page with replacement: ${page.runtimeType}');
    return navigator?.pushReplacement(
      MaterialPageRoute(builder: (context) => page),
    );
  }

  /// Pop the current route
  static void pop<T>([T? result]) {
    Logger.info('Popping current route with result: $result');
    return navigator?.pop(result);
  }

  /// Pop until a specific route
  static void popUntil(String routeName) {
    Logger.info('Popping until route: $routeName');
    navigator?.popUntil(ModalRoute.withName(routeName));
  }

  /// Check if can pop
  static bool canPop() {
    return navigator?.canPop() ?? false;
  }

  /// Navigate to a named route using the AppRoutes class
  static Future<dynamic>? navigateToRoute(String routeName,
      {Object? arguments}) {
    Logger.info('Navigating to route: $routeName with arguments: $arguments');
    return navigateTo(routeName, arguments: arguments);
  }

  /// Navigate to the login screen
  static Future<dynamic>? navigateToLogin() {
    return navigateToRoute(AppRoutes.login);
  }

  /// Navigate to the register screen
  static Future<dynamic>? navigateToRegister() {
    return navigateToRoute(AppRoutes.register);
  }

  /// Navigate to the chat screen
  static Future<dynamic>? navigateToChat() {
    return navigateToRoute(AppRoutes.chat);
  }

  /// Navigate to the build screen
  static Future<dynamic>? navigateToBuild() {
    return navigateToRoute(AppRoutes.build);
  }

  /// Navigate to the build screen
  static Future<dynamic>? navigateToBuildNew() {
    return navigateToRoute(AppRoutes.buildNew);
  }

  /// Navigate to the settings screen
  static Future<dynamic>? navigateToSettings() {
    return navigateToRoute(AppRoutes.setting);
  }

  /// Navigate to the profile screen
  static Future<dynamic>? navigateToProfile() {
    return navigateToRoute(AppRoutes.profile);
  }

  /// Navigate to the transaction screen
  static Future<dynamic>? navigateToTransaction() {
    return navigateToRoute(AppRoutes.transact);
  }

  /// Navigate to the workflow screen
  static Future<dynamic>? navigateToWorkflow(
      {String? objectiveId, String? objectiveTitle}) {
    return navigateToRoute(AppRoutes.workflow, arguments: {
      'objectiveId': objectiveId,
      'objectiveTitle': objectiveTitle,
    });
  }

  static Future? navigateToWorkflowView() {
    return navigateToRoute(AppRoutes.workflowView);
  }

  /// Navigate to the components screen
  static Future<dynamic>? navigateToComponents() {
    return navigateToRoute(AppRoutes.components);
  }

  static Future<dynamic>? navigateToWidgetBinder() {
    return navigateToRoute(AppRoutes.widgetBinder);
  }

  /// Navigate to the home screen
  static Future<dynamic>? navigateToHome() {
    return navigateToRoute(AppRoutes.home);
  }
}
