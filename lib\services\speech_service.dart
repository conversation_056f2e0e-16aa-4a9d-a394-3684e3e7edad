import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';

class SpeechService {
  static final SpeechService _instance = SpeechService._internal();
  factory SpeechService() => _instance;
  SpeechService._internal();

  final stt.SpeechToText _speech = stt.SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  bool _isInitialized = false;
  bool _isListening = false;

  // Check if microphone permission is granted
  Future<bool> checkMicrophonePermission() async {
    if (kIsWeb) return true; // Web handles permissions differently

    final status = await Permission.microphone.status;
    Logger.info('Microphone permission status: $status');
    return status == PermissionStatus.granted;
  }

  // Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    if (kIsWeb) return true; // Web handles permissions differently

    try {
      final status = await Permission.microphone.request();
      Logger.info('Microphone permission request result: $status');
      return status == PermissionStatus.granted;
    } catch (e) {
      Logger.error('Error requesting microphone permission: $e');
      return false;
    }
  }

  // Speech-to-text methods
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Check and request microphone permission
      if (!kIsWeb) {
        bool hasPermission = await checkMicrophonePermission();

        if (!hasPermission) {
          hasPermission = await requestMicrophonePermission();
          if (!hasPermission) {
            Logger.error('Microphone permission denied');
            return false;
          }
        }
      }

      // Initialize speech recognition
      _isInitialized = await _speech.initialize(
        onError: (error) => Logger.error('Speech recognition error: $error'),
        onStatus: (status) => Logger.info('Speech recognition status: $status'),
      );

      // Initialize text-to-speech
      await _flutterTts.setLanguage("en-US");
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);

      Logger.info('Speech service initialized: $_isInitialized');
      return _isInitialized;
    } catch (e) {
      Logger.error('Error initializing speech service: $e');
      _isInitialized = false;
      return false;
    }
  }

  Future<void> startListening({
    required Function(String) onResult,
    required VoidCallback onListeningComplete,
    Function(String)? onFinalResult,
  }) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) {
        Logger.error('Failed to initialize speech recognition');
        return;
      }
    }

    if (_speech.isAvailable && !_isListening) {
      Logger.info('Starting speech recognition');
      _isListening = true;

      await _speech.listen(
        onResult: (result) {
          // Always provide partial results
          onResult(result.recognizedWords);

          // If this is a final result, handle it specially
          if (result.finalResult) {
            Logger.info(
                'Final speech recognition result: ${result.recognizedWords}');
            _isListening = false;

            // Call the onFinalResult callback if provided
            if (onFinalResult != null && result.recognizedWords.isNotEmpty) {
              onFinalResult(result.recognizedWords);
            }

            // Always call the onListeningComplete callback
            onListeningComplete();
          }
        },
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 10),
        localeId: "en_US",
      );
    } else {
      Logger.error('Speech recognition not available or already listening');
    }
  }

  Future<void> stopListening() async {
    if (_isListening) {
      await _speech.stop();
      _isListening = false;
      Logger.info('Speech recognition stopped');
    }
  }

  bool get isListening => _isListening;

  // Text-to-speech methods
  Future<void> speak(String text) async {
    if (text.isEmpty) return;

    try {
      await _flutterTts.speak(text);
      Logger.info('Speaking: $text');
    } catch (e) {
      Logger.error('Error speaking text: $e');
    }
  }

  Future<void> stop() async {
    try {
      await _flutterTts.stop();
      Logger.info('TTS stopped');
    } catch (e) {
      Logger.error('Error stopping TTS: $e');
    }
  }

  void dispose() {
    _speech.cancel();
    _flutterTts.stop();
    _isInitialized = false;
    _isListening = false;
  }
}
