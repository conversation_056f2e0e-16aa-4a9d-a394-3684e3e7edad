import 'package:flutter/material.dart';
import '../models/widget_render_models.dart';
import '../services/widget_renderer_service.dart';
import '../widgets/dashboard/layout_renderer.dart';

class WidgetBinderScreen extends StatefulWidget {
  const WidgetBinderScreen({super.key});

  @override
  State<WidgetBinderScreen> createState() => WidgetBinderScreenState();
}

class WidgetBinderScreenState extends State<WidgetBinderScreen> {
  bool _isLoading = true;
  String? _error;
  WidgetRenderModel? _widgetRenderConfig;

  @override
  void initState() {
    super.initState();
    _loadWidgetRenderConfig();
  }

  Future<void> _loadWidgetRenderConfig() async {
    try {
      final config = await WidgetRendererService.loadWidgetRenderConfig(
        'assets/data/widget_render.json',
      );

      if (mounted) {
        setState(() {
          _widgetRenderConfig = config;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error loading widget render configuration: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dynamic Widget Renderer'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
                _error = null;
              });
              _loadWidgetRenderConfig();
            },
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48.0,
              ),
              const SizedBox(height: 16.0),
              Text(
                'Error',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8.0),
              Text(
                _error!,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16.0),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                  _loadWidgetRenderConfig();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_widgetRenderConfig == null) {
      return const Center(
        child: Text('No widget render configuration found.'),
      );
    }

    // For mobile, we'll use a simpler layout
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with intent information
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _widgetRenderConfig!.intent.query,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 4.0),
                Text(
                  'Type: ${_widgetRenderConfig!.intent.type}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),

          // Global filters if available
          if (_widgetRenderConfig!.globalFilters != null &&
              _widgetRenderConfig!.globalFilters!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: _buildGlobalFilters(),
            ),

          // Main content with layout renderer
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.7,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: LayoutRenderer(
                layout: _widgetRenderConfig!.layout,
                components: _widgetRenderConfig!.components,
              ),
            ),
          ),

          // Global actions if available
          if (_widgetRenderConfig!.globalActions != null &&
              _widgetRenderConfig!.globalActions!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: _buildGlobalActions(),
            ),
        ],
      ),
    );
  }

  Widget _buildGlobalFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Wrap(
          spacing: 16.0,
          runSpacing: 16.0,
          children: _widgetRenderConfig!.globalFilters!.map((filter) {
            return _buildFilterWidget(filter);
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFilterWidget(GlobalFilterModel filter) {
    // For now, we'll just show a dropdown for all filter types
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          filter.label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 8.0),
        DropdownButton<String>(
          value: filter.defaultPreset,
          onChanged: (value) {
            // In a real implementation, this would update the filter
          },
          items: filter.presets.map((preset) {
            return DropdownMenuItem<String>(
              value: preset['value'] as String,
              child: Text(preset['label'] as String),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildGlobalActions() {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      alignment: WrapAlignment.end,
      children: _widgetRenderConfig!.globalActions!.map((action) {
        return ElevatedButton.icon(
          icon: Icon(_getIconData(action.icon)),
          label: Text(action.label),
          onPressed: () {
            // In a real implementation, this would trigger the action
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Action: ${action.callback}'),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  IconData _getIconData(String icon) {
    switch (icon) {
      case 'refresh':
        return Icons.refresh;
      case 'download':
        return Icons.download;
      case 'filter_list':
        return Icons.filter_list;
      case 'settings':
        return Icons.settings;
      default:
        return Icons.touch_app;
    }
  }
}
