import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import '../models/message.dart';
import '../providers/build_provider.dart';
import '../screens/build_screen.dart';
import '../screens/workflow/screens/workflow-implementation.dart';
import '../screens/workflow/screens/full_node_view_screen.dart';
import '../services/build_service.dart';
import '../theme/app_colors.dart';
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';

// A wrapper class to hold the selected section
// This allows us to modify it from the bottom sheet
class SectionWrapper {
  ResponseSection section;
  SectionWrapper(this.section);
}

class MenuSectionWrapper {
  ResponseSectionMenu section;
  MenuSectionWrapper(this.section);
}

enum ResponseSection {
  agent,
  entity,
  workflows,
}

enum ResponseSectionMenu {
  prescriptiveText,
  javaCode,
  yamlOutput,
  deployRuntime,
  showWorkflows
}

class SolutionDetailsPanel extends StatefulWidget {
  final Message message;
  final BuildResponse? response;
  final bool isBottomSheet;
  final VoidCallback onClose;

  const SolutionDetailsPanel({
    super.key,
    required this.message,
    this.response,
    this.isBottomSheet = false,
    required this.onClose,
  });

  @override
  State<SolutionDetailsPanel> createState() => _SolutionDetailsPanelState();
}

class _SolutionDetailsPanelState extends State<SolutionDetailsPanel> {
  late BuildResponse _parsedResponse;
  bool _isParsed = false;
  String _title = '';
  ResponseSection _selectedSection = ResponseSection.agent;
  ResponseSectionMenu _selectedMenuSection =
      ResponseSectionMenu.prescriptiveText;

  @override
  void initState() {
    super.initState();
    _parseResponse();
  }

  void _parseResponse() {
    if (widget.response != null) {
      _parsedResponse = widget.response!;
      _isParsed = true;
      _extractTitleFromPrescriptiveText(_parsedResponse.prescriptiveText);
      return;
    }

    try {
      // Try to parse the content if we don't have a direct response object
      final content = widget.message.content;

      // Extract prescriptive text
      String prescriptiveText = '';
      final prescriptiveMatch = RegExp(
              r'## Prescriptive Text\n([\s\S]*?)(?=\n##|\n\*\*Validation Status)')
          .firstMatch(content);
      if (prescriptiveMatch != null && prescriptiveMatch.groupCount >= 1) {
        prescriptiveText = prescriptiveMatch.group(1)?.trim() ?? '';

        // Extract title from prescriptive text (first line or first sentence)
        _extractTitleFromPrescriptiveText(prescriptiveText);
      }

      // Extract YAML output
      String yamlOutput = '';
      final yamlMatch = RegExp(r'```yaml\n([\s\S]*?)```').firstMatch(content);
      if (yamlMatch != null && yamlMatch.groupCount >= 1) {
        yamlOutput = yamlMatch.group(1)?.trim() ?? '';
      }

      // Extract Java code
      String javaCode = '';
      final javaMatch = RegExp(r'```java\n([\s\S]*?)```').firstMatch(content);
      if (javaMatch != null && javaMatch.groupCount >= 1) {
        javaCode = javaMatch.group(1)?.trim() ?? '';
      }

      // Extract validation status
      String validationStatus = '';
      final validationMatch =
          RegExp(r'\*\*Validation Status\*\*: (.*?)(?=\n|$)')
              .firstMatch(content);
      if (validationMatch != null && validationMatch.groupCount >= 1) {
        validationStatus = validationMatch.group(1)?.trim() ?? '';
      }

      // Extract validation error if present
      String? validationError;
      final errorMatch = RegExp(r'\*\*Validation Error\*\*: (.*?)(?=\n|$)')
          .firstMatch(content);
      if (errorMatch != null && errorMatch.groupCount >= 1) {
        validationError = errorMatch.group(1)?.trim();
      }

      _parsedResponse = BuildResponse(
        conversationId: '', // No conversation ID for parsed responses
        prescriptiveText: prescriptiveText,
        yamlOutput: yamlOutput,
        javaCode: javaCode,
        validationStatus: validationStatus,
        validationError: validationError,
      );

      _isParsed = true;
    } catch (e) {
      // If parsing fails, just show the original content
      _isParsed = false;
    }
  }

  void _extractTitleFromPrescriptiveText(String text) {
    if (text.isEmpty) {
      _title = 'Solution Details';
      return;
    }

    // Look specifically for markdown headings (lines starting with #)
    final lines = text.split('\n');

    // First pass: Look for level 1 headings (# Title)
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;

      // Check for level 1 markdown heading (single #)
      if (trimmedLine.startsWith('# ')) {
        final headingText = trimmedLine.substring(2).trim();
        if (headingText.isNotEmpty) {
          _title = headingText;
          return;
        }
      }
    }

    // Second pass: Look for any level headings (## Title, ### Title, etc.)
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;

      if (trimmedLine.startsWith('#')) {
        final headingText =
            trimmedLine.replaceFirst(RegExp(r'^#+\s*'), '').trim();
        if (headingText.isNotEmpty) {
          _title = headingText;
          return;
        }
      }
    }

    // Fallback if no good title found
    _title = 'Solution Details';
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isBottomSheet) {
      return _buildBottomSheetContent(context, null, null, null);
    } else {
      return _buildPanelContent();
    }
  }

  Widget _buildPanelContent() {
    return Column(
      children: [
        Expanded(
          child: _buildTabContent(),
        ),
      ],
    );
  }

  Widget _buildBottomSheetContent(
      BuildContext context,
      SectionWrapper? sectionWrapper,
      StateSetter? setSheetState,
      MenuSectionWrapper? menuSectionWrapper) {
    // Extract the current section from the wrapper
    final ResponseSection currentSection =
        sectionWrapper?.section ?? _selectedSection;
    final ResponseSectionMenu currentMenuSection =
        menuSectionWrapper?.section ?? _selectedMenuSection;

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.95,
      // decoration: BoxDecoration(
      //   color: Theme.of(context).colorScheme.surface,
      //   borderRadius: BorderRadius.only(
      //     topLeft: Radius.circular(16),
      //     topRight: Radius.circular(16),
      //   ),
      //   boxShadow: [
      //     BoxShadow(
      //       color: Colors.black.withAlpha(26),
      //       blurRadius: 10,
      //       offset: Offset(0, -2),
      //     ),
      //   ],
      // ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header strip with close button, title, and more button
          Expanded(
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.sm,
                  ),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Header strip
                      Row(
                        children: [
                          // Close button
                          IconButton(
                            icon: Icon(Icons.close, size: 20),
                            padding: EdgeInsets.all(AppSpacing.xs),
                            constraints: BoxConstraints(),
                            onPressed: widget.onClose,
                            color: AppColors.textSecondaryLight,
                          ),
                          // Title (centered and expanded)
                          Expanded(
                            child: Center(
                              child: Text(
                                _title,
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.color, // Use theme text color
                                      fontFamily:
                                          'TiemposText', // Use TiemposText for title
                                    ),
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          // More button
                          IconButton(
                            icon: const Icon(Icons.more_horiz, size: 20),
                            padding: EdgeInsets.all(AppSpacing.xs),
                            constraints: BoxConstraints(),
                            onPressed: () {
                              // Find the render box of the icon button
                              final RenderBox button =
                                  context.findRenderObject() as RenderBox;
                              // Get the overlay so we can position relative to the whole screen
                              final RenderBox overlay = Overlay.of(context)
                                  .context
                                  .findRenderObject() as RenderBox;
                              // Calculate position for the menu to appear directly below the icon
                              final RelativeRect position =
                                  RelativeRect.fromRect(
                                // This rect represents the icon button's position and size
                                Rect.fromPoints(
                                  button.localToGlobal(Offset.zero,
                                      ancestor: overlay),
                                  button.localToGlobal(
                                      button.size.bottomRight(Offset.zero),
                                      ancestor: overlay),
                                ),
                                // This is the full screen size
                                Offset.zero & overlay.size,
                              );

                              showMenu(
                                context: context,
                                position: position,
                                items: <PopupMenuEntry<ResponseSectionMenu>>[
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.prescriptiveText,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.description_outlined,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Prescriptive'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.javaCode,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.code,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Java'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.yamlOutput,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.data_object_outlined,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('YAML'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.deployRuntime,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.rocket_launch,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Deploy to Runtime'),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<ResponseSectionMenu>(
                                    value: ResponseSectionMenu.showWorkflows,
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.account_tree_outlined,
                                          size: 20,
                                          color:
                                              Theme.of(context).iconTheme.color,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Node View'),
                                      ],
                                    ),
                                  ),
                                ],
                              ).then((ResponseSectionMenu? selected) {
                                if (selected != null) {
                                  if (setSheetState != null) {
                                    setSheetState(() {
                                      // Update the section in the wrapper

                                      _selectedMenuSection = selected;
                                    });
                                  } else {
                                    setState(() {
                                      _selectedMenuSection = selected;
                                    });
                                  }
                                }
                              });
                            },
                            color: AppColors.textSecondaryLight,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Section selector with current section name
                Padding(
                  padding: EdgeInsets.all(AppSpacing.md),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Section name
                      currentMenuSection == ResponseSectionMenu.prescriptiveText
                          ? _buildSectionSelector(sectionWrapper, setSheetState)
                          : Container(),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: _selectedMenuSection ==
                          ResponseSectionMenu.showWorkflows
                      ? Column(
                          children: [
                            Expanded(
                              child: SizedBox(
                                width: double.infinity,
                                height: double.infinity,
                                child: const AdvancedWorkflowBuilder(
                                    isFullScreen: false),
                              ),
                            ),
                            SizedBox(height: 16),
                            Container(
                            //  color: Colors.red,
                              padding: EdgeInsets.only(top: 5,bottom: 5),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  _buildFullViewButton(),
                                  const SizedBox(
                                    width: AppSpacing.md,
                                  ),
                                  deployButton(),
                                ],
                              ),
                            ),
                          ],
                        ) // Show the workflow builder widget
                      : SingleChildScrollView(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.md,
                            vertical: AppSpacing.sm,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              MarkdownBody(
                                data: _isParsed
                                    ? _selectedMenuSection ==
                                            ResponseSectionMenu.prescriptiveText
                                        ? _getSectionContent(currentSection)
                                        : _getMenuSectionContent(
                                            _selectedMenuSection)
                                    : widget.message.content,
                                styleSheet: MarkdownStyleSheet(
                                  p: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color, // Use theme text color
                                    fontFamily:
                                        'TiemposText', // Use TiemposText font for NSL responses
                                  ),
                                  h1: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .headlineMedium
                                        ?.color, // Use theme text color
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                  ),
                                  h2: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .headlineSmall
                                        ?.color, // Use theme text color
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                  h3: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.color, // Use theme text color
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                  blockquote: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withAlpha(
                                            204), // 0.8 opacity (204/255)
                                    fontFamily: 'TiemposText',
                                    fontStyle: FontStyle.italic,
                                  ),
                                  em: TextStyle(
                                    fontFamily: 'TiemposText',
                                    fontStyle: FontStyle.italic,
                                  ),
                                  strong: TextStyle(
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.bold,
                                  ),
                                  code: TextStyle(
                                    backgroundColor:
                                        Theme.of(context).colorScheme.surface,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color, // Use theme text color
                                    fontFamily:
                                        'SFProText', // Keep SFProText for code blocks
                                  ),
                                  codeblockDecoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.surface,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),

                              // Add Deploy to Runtime button if we have a conversation ID
                              deployButton(),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullViewButton() {
    // Get device type for responsive behavior
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);
    final isDesktop = deviceType == DeviceType.desktop;

    return Center(
      child: ElevatedButton.icon(
        icon: Icon(Icons.fullscreen,color: Colors.white,),
        label: Text('Full View'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.secondary,
          foregroundColor: Theme.of(context).colorScheme.onSecondary,
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
        onPressed: () {
          // For web desktop, we want to close any existing side panel
          if (kIsWeb && isDesktop) {
            // Close the bottom sheet or panel first
            widget.onClose();

            // Navigate to the full view screen
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const FullNodeViewScreen(),
                fullscreenDialog:
                    true, // Use fullscreen dialog for better web experience
              ),
            );
          } else {
            // For mobile, just close the bottom sheet and navigate
            widget.onClose();

            // Navigate to the full view screen
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const FullNodeViewScreen(),
              ),
            );
          }
        },
      ),
    );
  }

  Widget deployButton() {
    if (_isParsed && _parsedResponse.conversationId.isNotEmpty) {
      return Center(
        child: ElevatedButton.icon(
          icon: Icon(Icons.cloud_upload,color: Colors.white,),
          label: Text('Deploy to Runtime'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          onPressed: () => _deployToRuntime(context),
        ),
      );
    }
    return SizedBox();
  }

  Widget _buildSectionSelector(
      [SectionWrapper? sectionWrapper, StateSetter? setSheetState]) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          _buildSectionButton(
            ResponseSection.agent,
            "Agent",
            Icons.description_outlined,
            sectionWrapper,
            setSheetState,
          ),
          _buildSectionButton(
            ResponseSection.entity,
            "Entity",
            Icons.code,
            sectionWrapper,
            setSheetState,
          ),
          _buildSectionButton(
            ResponseSection.workflows,
            "Workflow",
            Icons.data_object_outlined,
            sectionWrapper,
            setSheetState,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionButton(
      ResponseSection section, String label, IconData icon,
      [SectionWrapper? sectionWrapper, StateSetter? setSheetState]) {
    // Determine which section to use for comparison
    ResponseSection sectionToUse;

    if (sectionWrapper != null) {
      // If we're in a bottom sheet, use the section from the wrapper
      sectionToUse = sectionWrapper.section;
    } else {
      // Otherwise, use the widget's selected section
      sectionToUse = _selectedSection;
    }

    final isSelected = sectionToUse == section;

    return Expanded(
      flex: 1,
      child: InkWell(
        onTap: () {
          // If we're in a bottom sheet, use the provided StateSetter
          if (setSheetState != null && sectionWrapper != null) {
            setSheetState(() {
              // Update the section in the wrapper
              // This will be reflected in the bottom sheet
              sectionWrapper.section = section;
            });
          } else {
            // Otherwise, use the widget's setState
            setState(() {
              _selectedSection = section;
            });
          }
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          height: 40,
          padding: EdgeInsets.only(left: 0),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: AppSpacing.xxs),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected
                          ? Colors.white
                          : Theme.of(context).colorScheme.onSurface,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getSectionContent([ResponseSection? section]) {
    if (!_isParsed) {
      return widget.message.content;
    }

    // Use provided section or fall back to the selected section
    ResponseSection sectionToUse = section ?? _selectedSection;

    switch (sectionToUse) {
      case ResponseSection.agent:
        // If the prescriptive text starts with the title, remove it to avoid duplication
        if (_title.isNotEmpty &&
            _parsedResponse.prescriptiveText.trim().startsWith(_title)) {
          final remainingText = _parsedResponse.prescriptiveText
              .trim()
              .substring(_title.length)
              .trim();
          return remainingText;
        }
        return _parsedResponse.prescriptiveText;
      case ResponseSection.entity:
        //  return "```java\n${_parsedResponse.javaCode}\n```";
        //return "```java\n${_parsedResponse.prescriptiveText}\n```";

        return _parsedResponse.prescriptiveText;
      case ResponseSection.workflows:
        // return "```yaml\n${_parsedResponse.yamlOutput}\n```";
        // return "```java\n${_parsedResponse.prescriptiveText}\n```";

        return _parsedResponse.prescriptiveText;
    }
  }

  String _getMenuSectionContent([ResponseSectionMenu? section]) {
    if (!_isParsed) {
      return widget.message.content;
    }

    // Use provided section or fall back to the selected section
    ResponseSectionMenu sectionToUse = section ?? _selectedMenuSection;

    switch (sectionToUse) {
      case ResponseSectionMenu.prescriptiveText:
        // If the prescriptive text starts with the title, remove it to avoid duplication
        if (_title.isNotEmpty &&
            _parsedResponse.prescriptiveText.trim().startsWith(_title)) {
          final remainingText = _parsedResponse.prescriptiveText
              .trim()
              .substring(_title.length)
              .trim();
          return remainingText;
        }
        return _parsedResponse.prescriptiveText;
      case ResponseSectionMenu.javaCode:
        return "```java\n${_parsedResponse.javaCode}\n```";
      //return "```java\n${_parsedResponse.prescriptiveText}\n```";

      case ResponseSectionMenu.yamlOutput:
        return "```yaml\n${_parsedResponse.yamlOutput}\n```";

      case ResponseSectionMenu.showWorkflows:
        // For showWorkflows, we'll return a placeholder message
        // The actual widget will be shown in the _buildContentArea method
        return "_show_workflow_builder_";

      case ResponseSectionMenu.deployRuntime:
        return "Deploying Solution";
    }
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  void _deployToRuntime(BuildContext context) async {
    if (!_isParsed || _parsedResponse.conversationId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('No conversation ID available for deployment')),
      );
      return;
    }

    // Store the conversation ID for later use
    final String conversationId = _parsedResponse.conversationId;

    // Store the scaffold messenger for later use
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show loading indicator
    BuildContext? dialogContext;
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext ctx) {
          dialogContext = ctx;
          return AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('Deploying to runtime...'),
              ],
            ),
          );
        },
      );
    }

    try {
      final buildService = BuildService();
      final success = await buildService.deployToRuntime(conversationId);

      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null && mounted) {
        Navigator.of(dialogContext!).pop();
      }

      if (success) {
        // Show success message
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Deployed successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Show the confirmation dialog if still mounted
        if (mounted) {
          // Use Future.microtask to avoid calling setState during build
          Future.microtask(() {
            if (mounted) {
              // Create a new method call that doesn't use the context from the closure
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _showCreateAnotherSolutionDialog(context);
                }
              });
            }
          });
        }
      } else {
        // Show error dialog for retry
        if (mounted) {
          _showRetryDialog(context, conversationId, false);
        }
      }
    } catch (e) {
      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null && mounted) {
        Navigator.of(dialogContext!).pop();
      }

      // Show error dialog for retry
      if (mounted) {
        _showRetryDialog(context, conversationId, false);
      }
    }
  }

  void _showCreateAnotherSolutionDialog(BuildContext context) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('Deployment Successful'),
          content: Text('Do you want to create another solution?'),
          actions: [
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                // Close the bottom sheet
                if (mounted) {
                  Navigator.of(context).pop();
                }

                // Reset the expanded state
                if (mounted) {
                  setState(() {
                    // Reset any state if needed
                  });
                }

                // Clear the chat and go back to build screen initial view
                if (mounted) {
                  // Use the static method from BuildScreen to toggle the view
                  BuildScreen.toggleView(context);
                }
              },
              child: Text('No'),
            ),
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                // Close the bottom sheet
                if (mounted) {
                  Navigator.of(context).pop();
                }

                // Reset the expanded state
                if (mounted) {
                  setState(() {
                    // Reset any state if needed
                  });
                }

                // Don't clear the chat - just allow the user to continue with the current chat
              },
              style: TextButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              child: Text('Yes'),
            ),
          ],
        );
      },
    );
  }

  void _showRetryDialog(
      BuildContext context, String conversationId, bool isRetry) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('Deployment Failed'),
          content: Text('Something went wrong, please try again'),
          actions: [
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                if (isRetry) {
                  // This was already a retry attempt, go back to build initial view
                  // Close the bottom sheet
                  if (mounted) {
                    Navigator.of(context).pop();
                  }

                  // Reset the expanded state
                  if (mounted) {
                    setState(() {
                      // Reset any state if needed
                    });
                  }

                  // Get back to build initial view
                  if (mounted) {
                    final buildProvider =
                        Provider.of<BuildProvider>(context, listen: false);
                    buildProvider.clearChat();
                  }
                } else {
                  // Try again
                  if (mounted) {
                    // Use Future.microtask to avoid calling setState during build
                    Future.microtask(() {
                      if (mounted) {
                        // Create a new method call that doesn't use the context from the closure
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted) {
                            _retryDeployment(context, conversationId);
                          }
                        });
                      }
                    });
                  }
                }
              },
              style: TextButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              child: Text('Try Again'),
            ),
          ],
        );
      },
    );
  }

  void _retryDeployment(BuildContext context, String conversationId) async {
    // Store the scaffold messenger for later use
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show loading indicator
    BuildContext? dialogContext;
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext ctx) {
          dialogContext = ctx;
          return AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('Retrying deployment...'),
              ],
            ),
          );
        },
      );
    }

    try {
      final buildService = BuildService();
      final success = await buildService.deployToRuntime(conversationId);

      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null) {
        Navigator.of(dialogContext!).pop();
      }

      if (success) {
        // Show success message
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Deployed successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Show the confirmation dialog if still mounted
        if (mounted) {
          // Use Future.microtask to avoid calling setState during build
          Future.microtask(() {
            if (mounted) {
              // Create a new method call that doesn't use the context from the closure
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _showCreateAnotherSolutionDialog(context);
                }
              });
            }
          });
        }
      } else {
        // Show error dialog for retry
        if (mounted) {
          _showRetryDialog(context, conversationId, true);
        }
      }
    } catch (e) {
      // Check if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog if context is still valid
      if (dialogContext != null) {
        Navigator.of(dialogContext!).pop();
      }

      // Show error dialog for retry
      if (mounted) {
        _showRetryDialog(context, conversationId, true);
      }
    }
  }

  String _selectedTab = 'prescriptive';

  Widget _buildTabContent() {
    return Column(
      children: [
        // Tab bar
        Row(
          children: [
            _buildTabButton('prescriptive', 'Prescriptive'),
            _buildTabButton('yaml', 'YAML'),
            _buildTabButton('java', 'Java'),
          ],
        ),
        // Tab content
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: _buildSelectedTabContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildTabButton(String tabId, String label) {
    final isSelected = _selectedTab == tabId;
    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedTab = tabId;
          });
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.0),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).textTheme.bodyMedium?.color,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedTabContent() {
    if (!_isParsed) {
      return Text(widget.message.content);
    }

    switch (_selectedTab) {
      case 'prescriptive':
        return SelectableText(_parsedResponse.prescriptiveText);
      case 'yaml':
        return SelectableText(_parsedResponse.yamlOutput);
      case 'java':
        return SelectableText(_parsedResponse.javaCode);
      default:
        return SelectableText(_parsedResponse.prescriptiveText);
    }
  }
}
