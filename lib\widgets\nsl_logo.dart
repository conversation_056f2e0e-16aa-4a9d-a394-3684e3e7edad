import 'package:flutter/material.dart';

class NSLLogo extends StatelessWidget {
  final double size;
  final Color? color;
  final Color? textColor;

  const NSLLogo({
    super.key,
    this.size = 80.0,
    this.color,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final themeColor = color ?? Theme.of(context).colorScheme.primary;
    final logoTextColor = textColor ?? themeColor;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: themeColor,
          width: 2.0,
        ),
      ),
      child: Center(
        child: Text(
          'NSL',
          style: TextStyle(
            color: logoTextColor,
            fontSize: size * 0.35,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.0,
          ),
        ),
      ),
    );
  }
}
