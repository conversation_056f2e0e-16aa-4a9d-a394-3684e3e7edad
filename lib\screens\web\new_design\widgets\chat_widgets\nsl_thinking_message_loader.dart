import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';

class NSLThinkingMessageLoader extends StatelessWidget {
  const NSLThinkingMessageLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: AppSpacing.md),
      padding: EdgeInsets.all(AppSpacing.md),
      // decoration: BoxDecoration(
      //   color: Colors.white,
      //   borderRadius:
      //       BorderRadius.circular(AppSpacing.md),
      //   border: Border.all(
      //       color: Color(0xffD0D0D0), width: 1),
      // ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          SizedBox(width: AppSpacing.sm),
          Text(
            'NSL Knowledge...',
            style: TextStyle(
              fontFamily: 'TiemposText',
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
