import 'auth_service.dart';
import 'build_service.dart';
import 'chat_service.dart';
import 'transaction_service.dart';
import 'workflow_service.dart';
import 'dio_client.dart';
import 'preferences_service.dart';
import '../repositories/user_repository.dart';
import '../repositories/message_repository.dart';
import '../utils/logger.dart';
import '../utils/error_handler.dart';

/// A service locator that provides access to all services in the application
class ServiceLocator {
  // Singleton instance
  static final ServiceLocator _instance = ServiceLocator._internal();

  // Factory constructor to return the same instance
  factory ServiceLocator() => _instance;

  // Private constructor
  ServiceLocator._internal();

  // Services
  late final AuthService authService;
  late final BuildService buildService;
  late final ChatService chatService;
  late final TransactionService transactionService;
  late final WorkflowService workflowService;
  late final DioClient dioClient;
  late final PreferencesService preferencesService;

  // Repositories
  late final UserRepository userRepository;
  late final MessageRepository messageRepository;

  // Initialization flag
  bool _isInitialized = false;

  /// Initialize all services
  Future<void> init() async {
    if (_isInitialized) {
      Logger.info('ServiceLocator already initialized');
      return;
    }

    Logger.info('Initializing ServiceLocator');

    try {
      // Initialize PreferencesService
      preferencesService = PreferencesService();
      await preferencesService.init();
      Logger.info('PreferencesService initialized');

      // Initialize Dio client
      dioClient = DioClient();
      Logger.info('DioClient initialized');

      // Initialize services
      authService = AuthService();
      buildService = BuildService();
      chatService = ChatService();
      transactionService = TransactionService();
      workflowService = WorkflowService();

      // Initialize repositories
      userRepository = UserRepository(authService);
      messageRepository = MessageRepository(authService);

      _isInitialized = true;
      Logger.info('ServiceLocator initialized successfully');
    } catch (e) {
      final errorMessage =
          ErrorHandler.handleError(e, context: 'ServiceLocator.init');
      Logger.error('Error initializing ServiceLocator: $errorMessage');
      rethrow;
    }
  }

  /// Check if the service locator is initialized
  bool get isInitialized => _isInitialized;
}
