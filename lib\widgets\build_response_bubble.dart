import 'package:flutter/material.dart';
import '../models/response_section.dart';
import '../models/build_response.dart' as models;
import '../screens/workflow/workflow_screen.dart';

import '../utils/logger.dart';

class BuildResponseBubble extends StatefulWidget {
  final models.BuildResponse response;
  final bool isSelected;
  final VoidCallback? onTap;

  const BuildResponseBubble({
    super.key,
    required this.response,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<BuildResponseBubble> createState() => _BuildResponseBubbleState();
}

class _BuildResponseBubbleState extends State<BuildResponseBubble> {
  ResponseSection _selectedSection = ResponseSection.prescriptiveText;
  bool _isDeploying = false;

  Widget _buildContent() {
    switch (_selectedSection) {
      case ResponseSection.prescriptiveText:
        return _buildPrescriptiveText();
      case ResponseSection.javaCode:
        return _buildJavaCode();
      case ResponseSection.yamlOutput:
        return _buildYamlOutput();
      case ResponseSection.deployRuntime:
        return _buildDeploySection();
      case ResponseSection.showWorkflows:
        return _buildWorkflowsSection();
    }
  }

  Widget _buildPrescriptiveText() {
    return SelectableText(
      widget.response.prescriptiveText,
      style: Theme.of(context).textTheme.bodyMedium,
    );
  }

  Widget _buildJavaCode() {
    return SelectableText(
      widget.response.javaCode,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontFamily: 'monospace',
          ),
    );
  }

  Widget _buildYamlOutput() {
    return SelectableText(
      widget.response.yamlOutput,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontFamily: 'monospace',
          ),
    );
  }

  Widget _buildDeploySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Deploy Configuration',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: _isDeploying ? null : _handleDeployToRuntime,
          icon: _isDeploying
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.rocket_launch),
          label: Text(_isDeploying ? 'Deploying...' : 'Deploy to Runtime'),
        ),
      ],
    );
  }

  Widget _buildWorkflowsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workflows',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: () => _handleShowWorkflows(),
          icon: const Icon(Icons.account_tree_outlined),
          label: const Text('View Workflows'),
        ),
      ],
    );
  }

  void _handleDeployToRuntime() async {
    try {
      setState(() => _isDeploying = true);

      // Show confirmation dialog
      final bool? shouldDeploy = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Deploy to Runtime'),
            content: const Text(
                'Are you sure you want to deploy this configuration to runtime?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Deploy'),
              ),
            ],
          );
        },
      );

      if (shouldDeploy == true) {
        // Implement your deployment logic here
        await Future.delayed(const Duration(seconds: 2)); // Simulate deployment

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Deployment completed successfully')),
        );
      }
    } catch (e) {
      Logger.error('Error during deployment: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Deployment failed: $e')),
      );
    } finally {
      if (mounted) {
        setState(() => _isDeploying = false);
      }
    }
  }

  void _handleShowWorkflows() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WorkflowScreen(),
      ),
    );
  }

  Widget _buildSectionButton(
      ResponseSection section, String label, IconData icon) {
    return ElevatedButton.icon(
      onPressed: () {
        setState(() {
          _selectedSection = section;
        });
      },
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor:
            _selectedSection == section ? Colors.blue : Colors.grey[300],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedSection.title,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Row(
                  children: [
                    _buildSectionButton(
                      ResponseSection.prescriptiveText,
                      "Prescriptive",
                      Icons.description_outlined,
                    ),
                    _buildSectionButton(
                      ResponseSection.javaCode,
                      "Java",
                      Icons.code,
                    ),
                    _buildSectionButton(
                      ResponseSection.yamlOutput,
                      "YAML",
                      Icons.data_object_outlined,
                    ),
                    _buildSectionButton(
                      ResponseSection.deployRuntime,
                      "Deploy",
                      Icons.rocket_launch,
                    ),
                    _buildSectionButton(
                      ResponseSection.showWorkflows,
                      "Workflows",
                      Icons.account_tree_outlined,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildContent(),
          ),
        ],
      ),
    );
  }
}
