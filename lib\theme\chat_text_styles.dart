
import 'package:flutter/material.dart';

import '../widgets/responsive_builder.dart';

class ChatTextStyles {
  // User Message Styles (Sans-serif - SF Pro Text)
  // static TextStyle userMessageText(BuildContext context) {
  //   return TextStyle(
  //     fontFamily: 'SFProText',
  //     fontSize: 16,
  //     fontWeight: FontWeight.w400,
  //     color: Theme.of(context).brightness == Brightness.dark
  //         ? Colors.white
  //         : Colors.white, // White text on both themes as user bubble is colored
  //     height: 1.4,
  //   );
  // }

  // Assistant Message Styles (Serif - Tiempos Text)
  // static TextStyle assistantMessageText(BuildContext context) {
  //   return TextStyle(
  //     fontFamily: 'TiemposText',
  //     fontSize: 16,
  //     fontWeight: FontWeight.w400,
  //     color: Theme.of(context).brightness == Brightness.dark
  //         ? Color(0xFFF9FAFB) // Gray 50
  //         : Color(0xFF1F2937), // Gray 800
  //     height: 1.5, // Slightly more line height for serif
  //   );
  // }

  // Date/Time Separator
  static TextStyle timeStamp(BuildContext context) {
    return TextStyle(
      fontFamily: 'SFProText',
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: Theme.of(context).brightness == Brightness.dark
          ? Color(0xFF9CA3AF) // Gray 400
          : Color(0xFF6B7280), // Gray 500
    );
  }

  // Typing Indicator
  static TextStyle typingIndicator(BuildContext context) {
    return TextStyle(
      fontFamily: 'SFProText',
      fontSize: 14,
      fontWeight: FontWeight.w400,
      fontStyle: FontStyle.italic,
      color: Theme.of(context).brightness == Brightness.dark
          ? Color(0xFFD1D5DB) // Gray 300
          : Color(0xFF6B7280), // Gray 500
    );
  }

  // Message Input Field
  static TextStyle inputField(BuildContext context) {
    return TextStyle(
      fontFamily: 'SFProText',
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: Theme.of(context).brightness == Brightness.dark
          ? Colors.white
          : Color(0xFF1F2937), // Gray 800
    );
  }

  // Code Blocks
  static TextStyle codeBlock(BuildContext context) {
    return TextStyle(
      fontFamily: 'SFProText', // Monospace would be better but we're sticking with our fonts
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: Theme.of(context).brightness == Brightness.dark
          ? Color(0xFFD1D5DB) // Gray 300
          : Color(0xFF1F2937), // Gray 800
      height: 1.5,
    );
  }
  // Add device type parameter to your text style methods
static TextStyle userMessageText(BuildContext context, {DeviceType deviceType = DeviceType.desktop}) {
  return TextStyle(
    fontFamily: 'SFProText',
    fontSize: deviceType == DeviceType.mobile ? 15 : 16,
    fontWeight: FontWeight.w400,
    color: Colors.white,
    height: 1.4,
  );
}

static TextStyle assistantMessageText(BuildContext context, {DeviceType deviceType = DeviceType.desktop}) {
  return TextStyle(
    fontFamily: 'TiemposText',
    fontSize: deviceType == DeviceType.mobile ? 15 : 16,
    fontWeight: FontWeight.w400,
    color: Theme.of(context).brightness == Brightness.dark
        ? Color(0xFFF9FAFB) // Gray 50
        : Color(0xFF1F2937), // Gray 800
    height: 1.5,
  );
}
}
