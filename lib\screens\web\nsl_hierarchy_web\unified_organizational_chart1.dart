import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/root_node_top_bar1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/simplified_nsl_card1.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';



class UnifiedOrganizationalChart1 extends StatefulWidget {
  final NSLNode rootNode;
  final Map<String, dynamic>? organizationalStructure;
  final String? selectedNodeId; // Add selected node ID parameter
  final bool isSidePanelOpen; // Add side panel state
  final bool isLoPanelOpen;
  final double sidePanelWidth; // Add side panel width
  final Function(NSLNode)? onNodeTitleTap;
  final Function(NSLNode)? onNodeInfoTap;
  final double horizontalSpacing;
  final double verticalSpacing;
  final double lineThickness;
  final Color lineColor;
  const UnifiedOrganizationalChart1({
    super.key,
    required this.rootNode,
    this.organizationalStructure,
    this.selectedNodeId, // Add to constructor
    this.isSidePanelOpen = false, // Add side panel state
    this.isLoPanelOpen = false,
    this.sidePanelWidth = 0.0, // Add side panel width
    this.onNodeTitleTap,
    this.onNodeInfoTap,
    this.horizontalSpacing = 120.0,
    this.verticalSpacing = 60.0,
    this.lineThickness = 1.0,
    this.lineColor = const Color(0xFF797676),
  });
  @override
  State<UnifiedOrganizationalChart1> createState() =>
      _UnifiedOrganizationalChartState();
}

class _UnifiedOrganizationalChartState
    extends State<UnifiedOrganizationalChart1> {
  late NSLNode _rootNode;
  NSLNode? _selectedNode;
  List<String> _selectedPathIds = [];
  Set<String> _infoTappedNodes = {};
  final TransformationController _transformationController =
      TransformationController();
  final ScrollController _horizontalScrollController = ScrollController();
  final Map<String, double> _subtreeWidths = {};
  // Level configuration - each level has fixed height for alignment
  static const double levelHeight = 114.0;
  // Calculate exact header height: AppSpacing.md (16) + AppSpacing.xs (8) = 24 for padding, plus text height ~20 = ~44
  static const double headerHeight =
      44.0; // Height of the left panel header (calculated)
  // Dynamic level positions - calculated based on available levels
  Map<String, double> _levelCenterY = {};
  Map<String, double> _dynamicSeparatorPositions = {};
  Map<String, double> _actualSeparatorPositions = {};
  List<String> _availableLevels = [];
  // GlobalKeys for tracking actual separator positions
  final Map<String, GlobalKey> _separatorKeys = {};
  bool _positionsInitialized = false;
  // Card dimensions
  double _getCardWidth(BuildContext context) => 123.0;
  double _getCardHeight(BuildContext context) => 60.0;
  late double _cardWidth;
  late double _cardHeight;
  @override
  void initState() {
    super.initState();
    _rootNode = widget.rootNode;
    _cardWidth = 123.0;
    _cardHeight = 60.0;
    _initializeDynamicLevels();
    _calculateAllSubtreeWidths(_rootNode);
    // Initialize selected path if a node is already selected
    if (widget.selectedNodeId != null) {
      _selectedPathIds = _getPathToNode(widget.selectedNodeId!);
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _transformationController.value = Matrix4.identity()
        ..scale(0.8, 0.8)
        ..translate(25.0, 25.0);
      // Measure actual separator positions after layout
      _updateActualSeparatorPositions();
      // Auto-scroll to center the tree
      _scrollToCenter();
    });
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(UnifiedOrganizationalChart1 oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.rootNode != widget.rootNode) {
      _rootNode = widget.rootNode;
      setState(() {});
    }
    // Re-center tree when side panel state changes
    if (oldWidget.isSidePanelOpen != widget.isSidePanelOpen ||
        oldWidget.sidePanelWidth != widget.sidePanelWidth) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCenter();
      });
    }
    // Update selected path when selectedNodeId changes
    if (oldWidget.selectedNodeId != widget.selectedNodeId) {
       _initializeDynamicLevels();
      setState(() {
        if (widget.selectedNodeId != null) {
          _selectedPathIds = _getPathToNode(widget.selectedNodeId!);
        } else {
          _selectedPathIds = [];
        }
      });
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCenter();
      });
    }
  }

  // Initialize dynamic level calculations
  void _initializeDynamicLevels() {
    _availableLevels = _getAvailableLevels();
    _levelCenterY = _calculateDynamicLevelCenterY();
    _dynamicSeparatorPositions = _calculateDynamicSeparatorPositions();
    _initializeSeparatorKeys();
  }

  // Initialize GlobalKeys for separator position tracking
  void _initializeSeparatorKeys() {
    _separatorKeys.clear();
    for (int i = 0; i < _availableLevels.length - 1; i++) {
      final parentLevel = _availableLevels[i];
      final childLevel = _availableLevels[i + 1];
      final separatorKey = '$parentLevel-$childLevel';
      _separatorKeys[separatorKey] = GlobalKey();
    }
  }

  // Get actual rendered position of separator
  double? _getActualSeparatorPosition(String separatorKey) {
    final key = _separatorKeys[separatorKey];
    if (key?.currentContext != null) {
      final RenderBox? renderBox =
          key!.currentContext!.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final position = renderBox.localToGlobal(Offset.zero);
        return position.dy;
      }
    }
    return null;
  }

  // Update actual separator positions after layout
  void _updateActualSeparatorPositions() {
    _actualSeparatorPositions.clear();
    for (final separatorKey in _separatorKeys.keys) {
      final actualPosition = _getActualSeparatorPosition(separatorKey);
      if (actualPosition != null) {
        _actualSeparatorPositions[separatorKey] = actualPosition;
      }
    }
    if (_actualSeparatorPositions.isNotEmpty && !_positionsInitialized) {
      _positionsInitialized = true;
      // Trigger a repaint with actual positions
      setState(() {});
    }
  }

  // Auto-detect all levels from organizational structure
  List<String> _getAvailableLevels() {
    var levels = widget.organizationalStructure?.keys.toList() ?? [];
    // If direct access doesn't work, try nested access
    if (levels.isEmpty) {
      final orgStructure =
          widget.organizationalStructure?['organizational_structure']
              as Map<String, dynamic>?;
      levels = orgStructure?.keys.toList() ?? [];
    }
    // Sort levels in descending order (M4, M3, M2, M1)
    levels.sort((a, b) {
      final aNum = _extractLevelNumber(a);
      final bNum = _extractLevelNumber(b);
      return bNum.compareTo(aNum);
    });
    return levels;
  }

  // Extract level number from level string (M4 -> 4, M5 -> 5)
  int _extractLevelNumber(String level) {
    return int.tryParse(level.substring(1)) ?? 0;
  }

  // Calculate dynamic level center positions
  Map<String, double> _calculateDynamicLevelCenterY() {
    final levelCenters = <String, double>{};
    for (int i = 0; i < _availableLevels.length; i++) {
      final level = _availableLevels[i];
      final centerY = effectiveHeaderHeight + (i * levelHeight) + (levelHeight / 2);
      levelCenters[level] = centerY;
    }
    return levelCenters;
  }
double get effectiveHeaderHeight {
  // Only account for left panel header when node is selected
  final baseHeaderHeight = widget.selectedNodeId != null ? 44.0 : 0.0;
  
  // Add top bar height only when it's visible (when no node is selected)
  final topBarHeight = widget.selectedNodeId == null ? 36.5 : 0.0;
  
  return baseHeaderHeight + topBarHeight;
}

  // Generate separator positions for any number of levels
  Map<String, double> _calculateDynamicSeparatorPositions() {
    final separators = <String, double>{};
    for (int i = 0; i < _availableLevels.length - 1; i++) {
      final parentLevel = _availableLevels[i];
      final childLevel = _availableLevels[i + 1];
      final separatorKey = '$parentLevel-$childLevel';
      // Calculate position: header + (level_index * levelHeight)
      final separatorY =
          effectiveHeaderHeight + ((i + 1) * levelHeight); // -10 for fine-tuning
      separators[separatorKey] = separatorY;
    }
    return separators;
  }

  // Auto-scroll to center the tree horizontally or ensure selected node is visible
  void _scrollToCenter() {
    if (!_horizontalScrollController.hasClients) return;
    // If side panel is open and a node is selected, ensure visibility
    if (widget.isSidePanelOpen && widget.selectedNodeId != null) {
      _ensureSelectedNodeVisible(widget.selectedNodeId!);
    } else {
      _scrollToCenterNormally();
    }
  }

  // Normal centering behavior - center the root node in viewport
  void _scrollToCenterNormally() {
    // Use the same centering logic as expansion for consistency
    _ensureNodeVisibleAfterExpansion(_rootNode.id);
  }

  // Ensure selected node is visible in the available space
  // In unified_organizational_chart.dart, replace the _ensureSelectedNodeVisible method:
  void _ensureSelectedNodeVisible(String selectedNodeId) {
    // First, calculate node positions to get the selected node's position
    final nodePositions = _calculateNodePositions(_rootNode);
    final selectedPosition = nodePositions[selectedNodeId];
    if (selectedPosition == null) return;
    final screenWidth = MediaQuery.of(context).size.width;
    final leftPanelWidth = screenWidth * 0.0688;
    final sidePanelWidth = widget.isSidePanelOpen ? widget.sidePanelWidth : 0.0;
    final availableRightPanelWidth =
        screenWidth - leftPanelWidth - sidePanelWidth;
    // Get current scroll position
    final currentScrollOffset = _horizontalScrollController.offset;
    // Calculate the viewport boundaries in the scrollable content coordinate system
    final viewportLeft = currentScrollOffset;
    final viewportRight = currentScrollOffset + availableRightPanelWidth;
    // Calculate node boundaries in the same coordinate system
    // Note: selectedPosition.x is already in the scrollable content coordinate system
    final nodeLeft = selectedPosition.x;
    final nodeRight = selectedPosition.x + selectedPosition.width;
    // Add some padding for better visibility
    const double padding = 50.0;
    double? newScrollOffset;
    if (nodeLeft < viewportLeft + padding) {
      // Node is too far left or too close to left edge - scroll left to show it with padding
      newScrollOffset = math.max(0, nodeLeft - padding);
    } else if (nodeRight > viewportRight - padding) {
      // Node is too far right or too close to right edge - scroll right to show it with padding
      newScrollOffset = nodeRight - availableRightPanelWidth + padding;
    }
    // Only scroll if needed and ensure we don't scroll to negative values
    if (newScrollOffset != null) {
      newScrollOffset = math.max(0, newScrollOffset);
      _horizontalScrollController.animateTo(
        newScrollOffset,
        duration: Duration(milliseconds: 1),
        curve: Curves.easeInOut,
      );
    }
  }

  // Center the selected node in the viewport after expansion/collapse
  void _ensureNodeVisibleAfterExpansion(String nodeId) {
    if (!_horizontalScrollController.hasClients) return;
    
    // Calculate node positions after the tree has been updated
    final nodePositions = _calculateNodePositions(_rootNode);
    final nodePosition = nodePositions[nodeId];
    if (nodePosition == null) return;
    
    final screenWidth = MediaQuery.of(context).size.width;
    final leftPanelWidth = screenWidth * 0.0688;
    final sidePanelWidth = widget.isSidePanelOpen ? widget.sidePanelWidth : 0.0;
    final availableRightPanelWidth = screenWidth - leftPanelWidth - sidePanelWidth;
    
    // Calculate the center of the selected node (in scrollable container coordinates)
    final nodeCenterX = nodePosition.x + (nodePosition.width / 2);
    
    // Debug logging
    print('=== CENTERING DEBUG ===');
    print('Screen width: $screenWidth');
    print('Left panel width: $leftPanelWidth');
    print('Side panel width: $sidePanelWidth');
    print('Available right panel width: $availableRightPanelWidth');
    print('Node position X: ${nodePosition.x}');
    print('Node width: ${nodePosition.width}');
    print('Node center X: $nodeCenterX');
    
    // The true center should be at the middle of the available right panel
    // But we need to account for where the scrollable content starts
    final viewportCenterX = availableRightPanelWidth / 2;
    print('Viewport center X: $viewportCenterX');
    
    // Calculate scroll offset to center the node
    final scrollOffset = math.max(0.0, nodeCenterX - viewportCenterX);
    print('Calculated scroll offset: $scrollOffset');
    print('======================');
    
    // Animate to center the selected node
    _horizontalScrollController.animateTo(
      scrollOffset,
      duration: Duration(milliseconds: 1),
      curve: Curves.easeInOut,
    );
  }

// Also, make sure to add the math import at the top of the file if it's not already there:
  // void _ensureSelectedNodeVisible(String selectedNodeId) {
  //   // First, calculate node positions to get the selected node's position
  //   final nodePositions = _calculateNodePositions(_rootNode);
  //   final selectedPosition = nodePositions[selectedNodeId];
  //   if (selectedPosition == null) return;
  //   final screenWidth = MediaQuery.of(context).size.width;
  //   final leftPanelWidth = screenWidth * 0.0688;
  //   final sidePanelWidth = widget.isSidePanelOpen ? widget.sidePanelWidth : 0.0;
  //   final availableRightPanelWidth = screenWidth - leftPanelWidth - sidePanelWidth;
  //   // Calculate visible area boundaries
  //   final currentScrollOffset = _horizontalScrollController.offset;
  //   final visibleLeftBoundary = currentScrollOffset + leftPanelWidth;
  //   final visibleRightBoundary = currentScrollOffset + leftPanelWidth + availableRightPanelWidth;
  //   // Check if selected node is already visible
  //   final nodeLeft = selectedPosition.x ;
  //   final nodeRight = selectedPosition.x + selectedPosition.width;
  //   double? newScrollOffset;
  //   if (nodeLeft < visibleLeftBoundary) {
  //     // Node is too far left - scroll left to show it
  //     newScrollOffset = nodeLeft - leftPanelWidth - 20; // 20px padding
  //   } else if (nodeRight > visibleRightBoundary) {
  //     // Node is too far right - scroll right to show it
  //     newScrollOffset = nodeRight - leftPanelWidth - availableRightPanelWidth + 20; // 20px padding
  //   }
  //   // Only scroll if needed
  //   if (newScrollOffset != null) {
  //     _horizontalScrollController.animateTo(
  //       newScrollOffset,
  //       duration: Duration(milliseconds: 500),
  //       curve: Curves.easeInOut,
  //     );
  //   }
  // }
  void _calculateAllSubtreeWidths(NSLNode node) {
    _getSubtreeWidth(node);
  }

  double _getSubtreeWidth(NSLNode node) {
    if (_subtreeWidths.containsKey(node.id)) {
      return _subtreeWidths[node.id]!;
    }
    double width;
    if (!node.isExpanded || node.children.isEmpty) {
      width = _cardWidth;
    } else {
      double childrenCombinedWidth = node.children.length * _cardWidth;
      if (node.children.length > 1) {
        childrenCombinedWidth +=
            (node.children.length - 1) * widget.horizontalSpacing;
      }
      width = childrenCombinedWidth;
      if (width < _cardWidth) {
        width = _cardWidth;
      }
    }
    _subtreeWidths[node.id] = width;
    return width;
  }

  // Calculate the total width needed for the entire tree
  double _calculateTotalTreeWidth(NSLNode rootNode) {
    // Get all node positions to find the leftmost and rightmost nodes
    final nodePositions = _calculateNodePositions(rootNode);
    
    double minX = double.infinity;
    double maxX = 0;
    
    for (final position in nodePositions.values) {
      final leftEdge = position.x;
      final rightEdge = position.x + position.width;
      
      if (leftEdge < minX) {
        minX = leftEdge;
      }
      if (rightEdge > maxX) {
        maxX = rightEdge;
      }
    }
    
    // Calculate dynamic padding based on actual node positions
    // Ensure leftmost node has at least 200px from container edge
    final leftPadding = math.max(200.0, -minX + 200.0);
    final rightPadding = 200.0; // Fixed right padding
    
    // Total width: actual tree span + dynamic left padding + right padding
    return (maxX - minX) + leftPadding + rightPadding;
  }

  @override
  Widget build(BuildContext context) {
    final newCardWidth = _getCardWidth(context);
    final newCardHeight = _getCardHeight(context);
    if (_cardWidth != newCardWidth || _cardHeight != newCardHeight) {
      _cardWidth = newCardWidth;
      _cardHeight = newCardHeight;
      _subtreeWidths.clear();
      _calculateAllSubtreeWidths(_rootNode);
    }
    return Container(
      decoration: BoxDecoration(
          //// border: Border.all(color: Color(0xffD0D0D0), width: 1),
          // borderRadius: BorderRadius.circular(4),
          ),
      child: Stack(
        children: [
          // Main content - always takes full space
          Row(
            children: [
              _buildLeftPanel(),
              // Right Panel - Organizational Chart
              Expanded(
                child: _buildRightPanel(),
              ),
            ],
          ),
          // Top bar overlay - positioned absolutely at top
          if (widget.selectedNodeId == null)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: RootNodeTopBar1(rootNode: _rootNode),
            ),
        ],
      ),
    );
  }

  Widget _buildLeftPanel() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.0695,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          right: BorderSide(color: Color(0xffD0D0D0), width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Add top padding when RootNodeTopBar is visible to account for overlay
          if (widget.selectedNodeId == null)
            SizedBox(height: 44), // Height of RootNodeTopBar
          // Header
          if (widget.selectedNodeId != null)
            Container(
              padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.md, vertical: AppSpacing.xs),
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                border: Border(
                  bottom: BorderSide(color: Color(0xFF797676), width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Organisation Departments',
                          maxLines: 2,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          // Department levels - use fixed height instead of Expanded
          ..._buildDepartmentLevels(),
        ],
      ),
    );
  }

  List<Widget> _buildDepartmentLevels() {
    final List<Widget> levels = [];
    // Try both approaches - direct access and nested access
    var availableLevels = widget.organizationalStructure?.keys.toList() ?? [];
    // If direct access doesn't work, try nested access
    if (availableLevels.isEmpty) {
      final orgStructure =
          widget.organizationalStructure?['organizational_structure']
              as Map<String, dynamic>?;
      availableLevels = orgStructure?.keys.toList() ?? [];
    }
    // Sort levels (M4, M3, M2, M1, etc.)
    availableLevels.sort((a, b) {
      // Extract number from level (M4 -> 4, M3 -> 3, etc.)
      final aNum = int.tryParse(a.substring(1)) ?? 0;
      final bNum = int.tryParse(b.substring(1)) ?? 0;
      return bNum.compareTo(aNum); // Descending order (M4, M3, M2, M1)
    });
    // Get the selected node's level
    final selectedNodeLevel = _getSelectedNodeLevel();
    for (int i = 0; i < availableLevels.length; i++) {
      final levelKey = availableLevels[i];
      // Try both data access approaches
      var levelData = widget.organizationalStructure![levelKey];
      if (levelData == null) {
        final orgStructure =
            widget.organizationalStructure?['organizational_structure']
                as Map<String, dynamic>?;
        levelData = orgStructure?[levelKey];
      }
      if (levelData != null) {
        final levelName = levelData['level_name'] ?? levelKey;
        levels.add(
          _buildDepartmentLevel(
            level: levelKey,
            title: levelName,
            isSelected: selectedNodeLevel == levelKey,
          ),
        );
        // Add separator line between levels with GlobalKey for position tracking
        if (i < availableLevels.length - 1) {
          final nextLevelKey = availableLevels[i + 1];
          final separatorKey = '$levelKey-$nextLevelKey';
          final globalKey = _separatorKeys[separatorKey];
          levels.add(
            Container(
              key: globalKey,
              height: 0.5,
              color: Color(0xff797676),
            ),
          );
        }
        if (i == availableLevels.length - 1) {
          levels.add(
            Container(
              height: 0.5, // Same thin height as others
              color: Color(0xff797676), // Same lighter color
            ),
          );
        }
      }
    }
    return levels;
  }

  Widget _buildDepartmentLevel({
    required String level,
    required String title,
    required bool isSelected,
  }) {
    return Container(
      width: double.infinity,
      height: levelHeight,
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md, vertical: AppSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: isSelected
            ? Border(
                right: BorderSide(color: Color(0xFF0058FF), width: 3),
              )
            : null,
      ),
      child: InkWell(
        onTap: () {
          // Handle level selection if needed
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              level,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.bold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            // SizedBox(height: AppSpacing.xs),
            // Text(
            //   title,
            //   style: FontManager.getCustomStyle(
            //     fontSize: FontManager.s10,
            //     fontWeight: FontManager.regular,
            //     fontFamily: FontManager.fontFamilyTiemposText,
            //     color: Colors.grey.shade700,
            //   ),
            //   maxLines: 2,
            //   overflow: TextOverflow.ellipsis,
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildRightPanel() {
    // Calculate the total width needed for the tree
    final totalTreeWidth = _calculateTotalTreeWidth(_rootNode);
    final screenWidth = MediaQuery.of(context).size.width;
    final minWidth = screenWidth * 2; // Minimum 2x screen width for scrolling
    final containerWidth = math.max(totalTreeWidth, minWidth);
    
    return SingleChildScrollView(
      controller: _horizontalScrollController,
      scrollDirection: Axis.horizontal,
      child: Container(
        width: containerWidth,
        height: MediaQuery.of(context).size.height,
        color: Colors.white,
        child: _buildOrganizationTree(_rootNode),
      ),
    );
  }

  Widget _buildZoomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              spreadRadius: 1,
            )
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.add, color: Colors.black54),
              onPressed: () => _zoom(1.2),
              tooltip: 'Zoom In',
            ),
            SizedBox(
              height: 30,
              width: 2,
              child: Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                        height: 30, width: 1.5, color: Colors.grey.shade400),
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: const Color(0xFFA5D6A7),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.remove, color: Colors.black54),
              onPressed: () => _zoom(0.8),
              tooltip: 'Zoom Out',
            ),
          ],
        ),
      ),
    );
  }

  void _zoom(double zoomFactor) {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    double newScale = currentScale * zoomFactor;
    newScale = newScale.clamp(0.1, 2.0);
    final relativeScaleFactor = newScale / currentScale;
    final Matrix4 newMatrix = _transformationController.value.clone()
      ..scale(relativeScaleFactor, relativeScaleFactor);
    _transformationController.value = newMatrix;
  }

  Widget _buildOrganizationTree(NSLNode rootNode) {
    final nodesInfo = _calculateNodePositions(rootNode);
    final nodeWidgets = _buildAllNodes(rootNode, nodesInfo);
    return Stack(
      children: [
        CustomPaint(
          painter: UnifiedConnectionsPainter(
            rootNode: _rootNode,
            nodePositions: nodesInfo,
            selectedPathIds: _selectedPathIds,
            defaultLineColor: widget.lineColor,
            lineThickness: widget.lineThickness,
            verticalSpacing: widget.verticalSpacing,
            dynamicSeparatorPositions: _dynamicSeparatorPositions,
            actualSeparatorPositions: _actualSeparatorPositions,
          ),
          child: Container(),
        ),
        ...nodeWidgets,
      ],
    );
  }

  List<Widget> _buildAllNodes(
      NSLNode node, Map<String, UnifiedNodePosition> nodesInfo) {
    final List<Widget> allWidgets = [];
    final position = nodesInfo[node.id];
    if (position != null) {
      allWidgets.add(
        Positioned(
          left: position.x,
          top: position.y,
          child: _buildNodeWidget(node),
        ),
      );
      if (node.isExpanded) {
        for (final child in node.children) {
          allWidgets.addAll(_buildAllNodes(child, nodesInfo));
        }
      }
    }
    return allWidgets;
  }

  Widget _buildNodeWidget(NSLNode node) {
    // Check if this node is selected based on the selectedNodeId from parent
    final isNodeSelected = widget.selectedNodeId == node.id;
    return SimplifiedNSLCard1(
      node: node,
      isSelected: isNodeSelected, // Use the selectedNodeId from parent
      isHighlightedInPath: _selectedPathIds.contains(node.id),
      hasInfoTapped: _infoTappedNodes.contains(node.id),
      onTitleTap: () {
        setState(() {
          if (_selectedNode?.id == node.id) {
            _selectedNode = null;
            _selectedPathIds = [];
          } else {
            _selectedNode = node;
            _selectedPathIds = _getPathToNode(node.id);
          }
        });
        widget.onNodeTitleTap?.call(node);
      },
      onInfoTap: () {
        setState(() {
          if (_infoTappedNodes.contains(node.id)) {
            _infoTappedNodes.remove(node.id);
          } else {
            _infoTappedNodes.add(node.id);
          }
          // Use the new sibling collapse functionality
          final newRoot = _toggleNodeWithSiblingCollapse(_rootNode, node.id);
          _subtreeWidths.clear();
          _calculateAllSubtreeWidths(newRoot);
          _rootNode = newRoot;
        });
        
        // After expanding/collapsing, ensure the tapped node remains visible
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _ensureNodeVisibleAfterExpansion(node.id);
        });
        
        widget.onNodeInfoTap?.call(node);
      },
    );
  }

  NSLNode _toggleNodeExpansionRecursive(
      NSLNode currentNode, String targetNodeId) {
    if (currentNode.id == targetNodeId) {
      return currentNode.copyWith(isExpanded: !currentNode.isExpanded);
    }
    final List<NSLNode> updatedChildren = [];
    bool childChanged = false;
    for (final child in currentNode.children) {
      final updatedChild = _toggleNodeExpansionRecursive(child, targetNodeId);
      if (updatedChild != child) {
        childChanged = true;
      }
      updatedChildren.add(updatedChild);
    }
    if (childChanged) {
      return currentNode.copyWith(children: updatedChildren);
    }
    return currentNode;
  }

  // New method to implement sibling collapse functionality
  NSLNode _toggleNodeWithSiblingCollapse(
      NSLNode rootNode, String targetNodeId) {
    // First, find the target node and its parent
    final targetNodeInfo = _findNodeWithParent(rootNode, targetNodeId);
    if (targetNodeInfo == null) return rootNode;
    final targetNode = targetNodeInfo['node'] as NSLNode;
    final parentNode = targetNodeInfo['parent'] as NSLNode?;

     // Special handling for root node
  if (targetNode.id == rootNode.id) {
    if (targetNode.isExpanded) {
      // Root is being collapsed - collapse everything
      return _collapseAllChildren(rootNode.copyWith(isExpanded: false));
    } else {
      // Root is being expanded - expand only to default level (M3)
      return _expandToDefaultLevel(rootNode);
    }
  }
    // Toggle the target node's expansion state
    NSLNode updatedRoot = _toggleNodeExpansionRecursive(rootNode, targetNodeId);
    // If the target node is being expanded and has a parent, collapse siblings' children
    if (!targetNode.isExpanded && parentNode != null) {
      // The node is being expanded (was collapsed, now expanding)
      updatedRoot =
          _collapseSiblingsChildren(updatedRoot, parentNode.id, targetNodeId);
    }
    return updatedRoot;
  }

  // Helper method to find a node and its parent
  Map<String, NSLNode?>? _findNodeWithParent(
      NSLNode currentNode, String targetNodeId,
      [NSLNode? parent]) {
    if (currentNode.id == targetNodeId) {
      return {'node': currentNode, 'parent': parent};
    }
    for (final child in currentNode.children) {
      final result = _findNodeWithParent(child, targetNodeId, currentNode);
      if (result != null) return result;
    }
    return null;
  }

  // Method to collapse all sibling nodes (and their children) except the expanded one
  NSLNode _collapseSiblingsChildren(
      NSLNode currentNode, String parentNodeId, String expandedNodeId) {
    if (currentNode.id == parentNodeId) {
      // Found the parent node, now collapse all siblings except the expanded one
      final List<NSLNode> updatedChildren = [];
      for (final child in currentNode.children) {
        if (child.id == expandedNodeId) {
          // Keep the expanded node as is
          updatedChildren.add(child);
        } else {
          // Collapse this sibling node AND all its children recursively
          final collapsedSibling =
              _collapseAllChildren(child.copyWith(isExpanded: false));
          updatedChildren.add(collapsedSibling);
        }
      }
      return currentNode.copyWith(children: updatedChildren);
    }
    // Continue searching in children
    final List<NSLNode> updatedChildren = [];
    bool childChanged = false;
    for (final child in currentNode.children) {
      final updatedChild =
          _collapseSiblingsChildren(child, parentNodeId, expandedNodeId);
      if (updatedChild != child) {
        childChanged = true;
      }
      updatedChildren.add(updatedChild);
    }
    if (childChanged) {
      return currentNode.copyWith(children: updatedChildren);
    }
    return currentNode;
  }

  // Method to recursively collapse all children of a node
  NSLNode _collapseAllChildren(NSLNode node) {
    if (node.children.isEmpty) {
      return node;
    }
    // Recursively collapse all children
    final List<NSLNode> collapsedChildren = node.children.map((child) {
      return _collapseAllChildren(child.copyWith(isExpanded: false));
    }).toList();
    return node.copyWith(children: collapsedChildren);
  }

  // Helper method to calculate preliminary node positions without dynamic padding
  Map<String, UnifiedNodePosition> _calculatePreliminaryNodePositions(
      NSLNode rootNode, double leftPanelWidth, double availableRightPanelWidth) {
    final Map<String, UnifiedNodePosition> positions = {};
    
    // Use a basic centering approach for preliminary calculation
    final rightPanelCenterX = leftPanelWidth + (availableRightPanelWidth / 2);
    
    // Get the Y position based on the node's level
    final rootY = _levelCenterY[rootNode.level] ?? 0.0;
    final rootCardY = rootY - (_cardHeight / 2);
    
    // Position the root node at the center
    final rootCardX = rightPanelCenterX - (_cardWidth / 2);
    positions[rootNode.id] = UnifiedNodePosition(
      id: rootNode.id,
      x: rootCardX,
      y: rootCardY,
      width: _cardWidth,
      height: _cardHeight,
      level: rootNode.level,
    );
    
    _calculateChildPositions(
        rootNode, positions, rootCardX, rootCardY, _cardWidth, _cardHeight);
    return positions;
  }

  Map<String, UnifiedNodePosition> _calculateNodePositions(NSLNode rootNode) {
    final Map<String, UnifiedNodePosition> positions = {};
    // Calculate available space for centering the tree
    final totalWidth = MediaQuery.of(context).size.width;
    final leftPanelWidth = totalWidth * 0.0688;
    final sidePanelWidth = widget.isSidePanelOpen ? widget.sidePanelWidth : 0.0;
    final availableRightPanelWidth =
        totalWidth - leftPanelWidth - sidePanelWidth;
    
    // Calculate dynamic left padding based on actual tree positions
    // First, do a preliminary calculation to find the leftmost position
    final preliminaryPositions = _calculatePreliminaryNodePositions(rootNode, leftPanelWidth, availableRightPanelWidth);
    double minX = double.infinity;
    for (final position in preliminaryPositions.values) {
      if (position.x < minX) {
        minX = position.x;
      }
    }
    
    // Calculate dynamic left padding to ensure leftmost node has at least 200px from container edge
    final dynamicLeftPadding = math.max(200.0, -minX + 200.0);
    
    // Center the tree horizontally in the right panel, accounting for dynamic left padding
    final rightPanelCenterX = dynamicLeftPadding + leftPanelWidth +
        (availableRightPanelWidth / 2) ;
        //  +
        // (widget.isSidePanelOpen ? (getTotalLevels() * 40.0) : 0) - 40.0;
    
    // Get the Y position based on the node's level
    final rootY = _levelCenterY[rootNode.level] ?? 0.0;
    final rootCardY =
        rootY - (_cardHeight / 2); // Center the card vertically in the level
    // Position the root node at the center of the right panel
    final rootSubtreeWidth = _subtreeWidths[rootNode.id] ?? _cardWidth;
    final rootCardX = rightPanelCenterX - (_cardWidth / 2);
    positions[rootNode.id] = UnifiedNodePosition(
      id: rootNode.id,
      x: rootCardX,
      y: rootCardY,
      width: _cardWidth,
      height: _cardHeight,
      level: rootNode.level,
    );
    _calculateChildPositions(
        rootNode, positions, rootCardX, rootCardY, _cardWidth, _cardHeight);
    return positions;
  }

  void _calculateChildPositions(
    NSLNode parent,
    Map<String, UnifiedNodePosition> positions,
    double parentCardX,
    double parentCardY,
    double parentCardWidth,
    double parentCardHeight,
  ) {
    if (!parent.isExpanded || parent.children.isEmpty) return;
    final numberOfChildren = parent.children.length;
    // Calculate Y position based on children's level
    final firstChild = parent.children.first;
    final childLevelY = _levelCenterY[firstChild.level] ?? 0.0;
    final childY =
        childLevelY - (_cardHeight / 2); // Center cards in their level
    // Universal child centering algorithm - works for any number of children
    _positionChildrenUnderParent(
      parent: parent,
      children: parent.children,
      parentPosition: positions[parent.id]!,
      childY: childY,
      positions: positions,
    );
    // Recursively position grandchildren for each child
    for (final child in parent.children) {
      final childPosition = positions[child.id];
      if (childPosition != null) {
        _calculateChildPositions(
          child,
          positions,
          childPosition.x,
          childPosition.y,
          childPosition.width,
          childPosition.height,
        );
      }
    }
  }

  // Universal algorithm to center any number of children under their parent
  void _positionChildrenUnderParent({
    required NSLNode parent,
    required List<NSLNode> children,
    required UnifiedNodePosition parentPosition,
    required double childY,
    required Map<String, UnifiedNodePosition> positions,
  }) {
    if (children.isEmpty) return;
    final numberOfChildren = children.length;
    if (numberOfChildren == 1) {
      // Single child: position directly under parent center
      final child = children[0];
      final parentCenterX = parentPosition.x + (parentPosition.width / 2);
      final childX = parentCenterX - (_cardWidth / 2);
      positions[child.id] = UnifiedNodePosition(
        id: child.id,
        x: childX,
        y: childY,
        width: _cardWidth,
        height: _cardHeight,
        parentId: parent.id,
        level: child.level,
      );
    } else {
      // Multiple children: use reduced spacing to keep them closer to parent center
      final reducedSpacing = widget.horizontalSpacing; // Reduce spacing by 50%
      final totalChildrenWidth = (numberOfChildren * _cardWidth) +
          ((numberOfChildren - 1) * reducedSpacing);
      // Get parent center point for perfect alignment
      final parentCenterX = parentPosition.x + (parentPosition.width / 2);
      // Calculate starting X position to center the children block, shifted left
      final startXForChildrenBlock = parentCenterX - (totalChildrenWidth / 2);
      // Position each child with reduced spacing to stay closer to parent arrow
      for (int i = 0; i < numberOfChildren; i++) {
        final child = children[i];
        final childX =
            startXForChildrenBlock + (i * (_cardWidth + reducedSpacing));
        positions[child.id] = UnifiedNodePosition(
          id: child.id,
          x: childX,
          y: childY,
          width: _cardWidth,
          height: _cardHeight,
          parentId: parent.id,
          level: child.level,
        );
      }
    }
  }

  List<String> _getPathToNode(String targetNodeId) {
    List<String> currentPath = [];
    bool findPathRecursive(NSLNode currentNode) {
      currentPath.add(currentNode.id);
      if (currentNode.id == targetNodeId) {
        return true;
      }
      for (final child in currentNode.children) {
        if (findPathRecursive(child)) {
          return true;
        }
      }
      currentPath.removeLast();
      return false;
    }

    findPathRecursive(_rootNode);
    return currentPath;
  }

  // Helper method to get the selected node's level
  String? _getSelectedNodeLevel() {
    if (widget.selectedNodeId == null) return null;
    NSLNode? findNodeById(NSLNode currentNode, String targetId) {
      if (currentNode.id == targetId) return currentNode;
      for (final child in currentNode.children) {
        final found = findNodeById(child, targetId);
        if (found != null) return found;
      }
      return null;
    }

    final selectedNode = findNodeById(_rootNode, widget.selectedNodeId!);
    return selectedNode?.level;
  }

  int getTotalLevels() {
    return _availableLevels.length; // This already exists in your code
  }
  
NSLNode _expandToDefaultLevel(NSLNode node) {
  // Only expand the root node by default
  if (node.id == _rootNode.id) {
    // This is the root node - expand it and collapse all its children
    final collapsedChildren = node.children.map((child) {
      return _collapseAllChildren(child.copyWith(isExpanded: false));
    }).toList();
    
    return node.copyWith(
      isExpanded: true,
      children: collapsedChildren,
    );
  } else {
    // This is not the root node - collapse it and all its children
    final collapsedChildren = node.children.map((child) {
      return _collapseAllChildren(child.copyWith(isExpanded: false));
    }).toList();
    
    return node.copyWith(
      isExpanded: false,
      children: collapsedChildren,
    );
  }
}
}

class UnifiedNodePosition {
  final String id;
  final double x;
  final double y;
  final double width;
  final double height;
  final String? parentId;
  final String level;
  UnifiedNodePosition({
    required this.id,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.parentId,
    required this.level,
  });
  Offset get bottomCenter => Offset(x + width / 2, y + height);
  Offset get topCenter => Offset(x + width / 2, y);
  Offset get greenDotPosition {
    // Special handling for M1 level
    // debugPrint('Positioning dot for $id at level $level');
    // if (level == 'M1') {
    //   return Offset(x + 5, y - 90); // Increased offset for M1
    // }
    // Default for all other levels
    return Offset(x + 5, y - 5);
  }
}

class UnifiedConnectionsPainter extends CustomPainter {
  final NSLNode rootNode;
  final Map<String, UnifiedNodePosition> nodePositions;
  final List<String> selectedPathIds;
  final Color defaultLineColor;
  final double lineThickness;
  final double verticalSpacing;
  final Map<String, double> dynamicSeparatorPositions;
  final Map<String, double> actualSeparatorPositions;
  // Level separator positions - these should match the black lines in left panel
  static const double levelHeight = 114.0;
  static const double headerHeight = 44.0;
  // static const Map<String, double> levelSeparatorY = {
  //   'M4-M3': headerHeight + levelHeight - 10, // 148px (moved up)
  //   'M3-M2': headerHeight + (levelHeight * 2) - 10, // 262px (moved up)
  //   'M2-M1': headerHeight + (levelHeight * 3) - 10, // 376px (moved up)
  // };
  UnifiedConnectionsPainter({
    required this.rootNode,
    required this.nodePositions,
    required this.selectedPathIds,
    required this.defaultLineColor,
    required this.dynamicSeparatorPositions,
    required this.actualSeparatorPositions,
    this.lineThickness = 1.0,
    this.verticalSpacing = 80.0,
  });
  NSLNode? _findNodeById(NSLNode currentNode, String id) {
    if (currentNode.id == id) return currentNode;
    for (final child in currentNode.children) {
      final found = _findNodeById(child, id);
      if (found != null) return found;
    }
    return null;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = lineThickness
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;
    for (final position in nodePositions.values) {
      if (position.parentId != null) {
        final parentPosition = nodePositions[position.parentId!];
        if (parentPosition != null) {
          final parentNode = _findNodeById(rootNode, position.parentId!);
          // Check if this connection is part of the selected path
          bool isPathHighlighted = selectedPathIds.contains(position.id) &&
              selectedPathIds.contains(position.parentId!);
          // Use blue color and thicker line for highlighted path
          if (isPathHighlighted) {
            paint.color = Color(0xFF0058FF); // Blue color for selected path
            paint.strokeWidth =
                lineThickness * 2; // Make it thicker for visibility
          } else {
            paint.color = defaultLineColor; // Default gray color
            paint.strokeWidth = lineThickness;
          }
          _drawConnection(canvas, paint, parentPosition, position);
        }
      }
    }
  }

  void _drawConnection(Canvas canvas, Paint paint, UnifiedNodePosition parent,
      UnifiedNodePosition child) {
    // Use the helper methods for consistent connection points
    final parentConnectionPoint = parent.bottomCenter;
    final childConnectionPoint = child.topCenter;
    // Determine the separator line Y position based on parent and child levels
    final parentNode = _findNodeById(rootNode, parent.id);
    final childNode = _findNodeById(rootNode, child.id);
    double separatorY;
    if (parentNode?.level != null && childNode?.level != null) {
      final separatorKey = '${parentNode!.level}-${childNode!.level}';
      // Priority 1: Use actual measured positions (most accurate)
      if (actualSeparatorPositions.containsKey(separatorKey)) {
        separatorY = actualSeparatorPositions[separatorKey]!;
      }
      // Priority 2: Use dynamic calculated positions
      else if (dynamicSeparatorPositions.containsKey(separatorKey)) {
        separatorY = dynamicSeparatorPositions[separatorKey]!;
      }
      // Priority 3: Use static fallback positions
      // else if (levelSeparatorY.containsKey(separatorKey)) {
      //   separatorY = levelSeparatorY[separatorKey]!;
      // }
      // Priority 4: Calculate midpoint as last resort
      else {
        separatorY = parentConnectionPoint.dy +
            (childConnectionPoint.dy - parentConnectionPoint.dy) / 2;
      }
    } else {
      // Fallback to midpoint if levels are null
      separatorY = parentConnectionPoint.dy +
          (childConnectionPoint.dy - parentConnectionPoint.dy) / 2;
    }
    if (parentConnectionPoint.dx == childConnectionPoint.dx) {
      canvas.drawLine(parentConnectionPoint, childConnectionPoint, paint);
    } else {
      // Draw vertical line from parent to separator line
      canvas.drawLine(parentConnectionPoint,
          Offset(parentConnectionPoint.dx, separatorY), paint);
      // Draw horizontal line at separator level
      canvas.drawLine(Offset(parentConnectionPoint.dx, separatorY),
          Offset(childConnectionPoint.dx, separatorY), paint);
      // Draw vertical line from separator to child
      canvas.drawLine(Offset(childConnectionPoint.dx, separatorY),
          childConnectionPoint, paint);
    }
    _drawArrowhead(canvas, paint, childConnectionPoint, math.pi / 2);
  }

  void _drawArrowhead(Canvas canvas, Paint paint, Offset point, double angle) {
    const double arrowSize = 10.0;
    const double arrowWidth = 10.0;
    final Path trianglePath = Path();
    trianglePath.moveTo(point.dx, point.dy);
    trianglePath.lineTo(point.dx - arrowWidth / 2, point.dy - arrowSize);
    trianglePath.lineTo(point.dx + arrowWidth / 2, point.dy - arrowSize);
    trianglePath.close();
    final fillPaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.fill;
    canvas.drawPath(trianglePath, fillPaint);
  }

  @override
  bool shouldRepaint(UnifiedConnectionsPainter oldDelegate) =>
      oldDelegate.nodePositions != nodePositions ||
      oldDelegate.rootNode != rootNode ||
      oldDelegate.selectedPathIds != selectedPathIds ||
      oldDelegate.defaultLineColor != defaultLineColor ||
      oldDelegate.lineThickness != lineThickness;
}
