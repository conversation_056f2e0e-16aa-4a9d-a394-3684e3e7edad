class InputAttribute {
  final String attributeId;
  final String attributeDisplayName;
  final dynamic value;

  InputAttribute({
    required this.attributeId,
    required this.attributeDisplayName,
    required this.value,
  });

  factory InputAttribute.fromJson(Map<String, dynamic> json) {
    return InputAttribute(
      attributeId: json['attribute_id'] ?? '',
      attributeDisplayName: json['attribute_display_name'] ?? '',
      value: json['value'],
    );
  }
}

class TransactionDetail {
  final String workflowInstanceId;
  final String goId;
  final String tenantId;
  final String userId;
  final String goName;
  final String loId;
  final String? loName;
  final String status;
  final dynamic inputStack;
  final dynamic outputStack;
  final String createdAt;
  final String updatedAt;

  TransactionDetail({
    required this.workflowInstanceId,
    required this.goId,
    required this.tenantId,
    required this.userId,
    required this.goName,
    required this.loId,
    this.loName,
    required this.status,
    required this.inputStack,
    required this.outputStack,
    required this.createdAt,
    required this.updatedAt,
  });

  // Helper getter to get a formatted display name
  String get displayName =>
      loName != null && loName!.isNotEmpty ? loName! : goName;

  // Helper getter to get a formatted description
  String get description => 'Workflow ID: $workflowInstanceId';

  // Helper getter to get a formatted timestamp
  String get formattedTimestamp => createdAt;

  // Helper method to get input attributes as a list
  List<InputAttribute> getInputAttributes() {
    if (inputStack is List) {
      return (inputStack as List)
          .map((item) => InputAttribute.fromJson(item))
          .toList();
    }
    return [];
  }

  factory TransactionDetail.fromJson(Map<String, dynamic> json) {
    return TransactionDetail(
      workflowInstanceId: json['workflow_instance_id'] ?? '',
      goId: json['go_id'] ?? '',
      tenantId: json['tenant_id'] ?? '',
      userId: json['user_id'] ?? '',
      goName: json['go_name'] ?? '',
      loId: json['lo_id'] ?? '',
      loName: json['lo_name'],
      status: json['status'] ?? '',
      inputStack: json['input_stack'] ?? {},
      outputStack: json['output_stack'],
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }
}
