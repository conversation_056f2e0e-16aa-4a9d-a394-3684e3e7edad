import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable split view component that divides the screen into two panels
class AppSplitView extends StatefulWidget {
  /// The first panel content
  final Widget firstPanel;

  /// The second panel content
  final Widget secondPanel;

  /// The initial weight of the first panel (0.0 to 1.0)
  final double initialFirstPanelWeight;

  /// The minimum weight of the first panel (0.0 to 1.0)
  final double minFirstPanelWeight;

  /// The maximum weight of the first panel (0.0 to 1.0)
  final double maxFirstPanelWeight;

  /// Whether the split is vertical (horizontal divider)
  final bool isVertical;

  /// The color of the divider
  final Color? dividerColor;

  /// The width of the divider
  final double dividerWidth;

  /// The handle size for dragging the divider
  final double handleSize;

  /// The handle color
  final Color? handleColor;

  /// The background color of the split view
  final Color? backgroundColor;

  /// The border radius of the split view
  final BorderRadius? borderRadius;

  /// The elevation of the split view
  final double elevation;

  /// The border of the split view
  final Border? border;

  /// Whether the divider can be dragged to resize the panels
  final bool resizable;

  /// Callback when the divider position changes
  final Function(double)? onWeightChanged;

  const AppSplitView({
    super.key,
    required this.firstPanel,
    required this.secondPanel,
    this.initialFirstPanelWeight = 0.5,
    this.minFirstPanelWeight = 0.1,
    this.maxFirstPanelWeight = 0.9,
    this.isVertical = false,
    this.dividerColor,
    this.dividerWidth = 1.0,
    this.handleSize = 20.0,
    this.handleColor,
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 0,
    this.border,
    this.resizable = true,
    this.onWeightChanged,
  })  : assert(initialFirstPanelWeight >= 0.0 && initialFirstPanelWeight <= 1.0,
            'initialFirstPanelWeight must be between 0.0 and 1.0'),
        assert(minFirstPanelWeight >= 0.0 && minFirstPanelWeight <= 1.0,
            'minFirstPanelWeight must be between 0.0 and 1.0'),
        assert(maxFirstPanelWeight >= 0.0 && maxFirstPanelWeight <= 1.0,
            'maxFirstPanelWeight must be between 0.0 and 1.0'),
        assert(minFirstPanelWeight <= maxFirstPanelWeight,
            'minFirstPanelWeight must be less than or equal to maxFirstPanelWeight'),
        assert(
            initialFirstPanelWeight >= minFirstPanelWeight &&
                initialFirstPanelWeight <= maxFirstPanelWeight,
            'initialFirstPanelWeight must be between minFirstPanelWeight and maxFirstPanelWeight');

  @override
  State<AppSplitView> createState() => _AppSplitViewState();
}

class _AppSplitViewState extends State<AppSplitView> {
  late double _firstPanelWeight;
  double? _dragStartWeight;
  double? _dragStartPos;

  @override
  void initState() {
    super.initState();
    _firstPanelWeight = widget.initialFirstPanelWeight;
  }

  void _handleDragStart(DragStartDetails details) {
    if (!widget.resizable) return;

    setState(() {
      _dragStartWeight = _firstPanelWeight;
      _dragStartPos = widget.isVertical
          ? details.globalPosition.dy
          : details.globalPosition.dx;
    });
  }

  void _handleDragUpdate(
      DragUpdateDetails details, BoxConstraints constraints) {
    if (!widget.resizable ||
        _dragStartWeight == null ||
        _dragStartPos == null) {
      return;
    }

    final currentPos = widget.isVertical
        ? details.globalPosition.dy
        : details.globalPosition.dx;
    final totalSize =
        widget.isVertical ? constraints.maxHeight : constraints.maxWidth;

    if (totalSize <= 0) return;

    final delta = (currentPos - _dragStartPos!) / totalSize;
    final newWeight = _dragStartWeight! + delta;

    setState(() {
      _firstPanelWeight = newWeight.clamp(
        widget.minFirstPanelWeight,
        widget.maxFirstPanelWeight,
      );
    });

    if (widget.onWeightChanged != null) {
      widget.onWeightChanged!(_firstPanelWeight);
    }
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!widget.resizable) return;

    setState(() {
      _dragStartWeight = null;
      _dragStartPos = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final Color dividerColor = widget.dividerColor ??
        Theme.of(context).dividerTheme.color ??
        AppTheme.textSecondaryColor.withAlpha(51);
    final Color handleColor =
        widget.handleColor ?? AppTheme.primaryColor.withAlpha(128);

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? AppTheme.backgroundColor,
            borderRadius: widget.borderRadius,
            border: widget.border,
            boxShadow: widget.elevation > 0
                ? [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: widget.elevation * 2,
                      offset: Offset(0, widget.elevation),
                    ),
                  ]
                : null,
          ),
          child: widget.isVertical
              ? Column(
                  children: [
                    // First panel
                    SizedBox(
                      height: constraints.maxHeight * _firstPanelWeight -
                          (widget.dividerWidth / 2),
                      width: constraints.maxWidth,
                      child: widget.firstPanel,
                    ),

                    // Divider
                    GestureDetector(
                      onVerticalDragStart: _handleDragStart,
                      onVerticalDragUpdate: (details) =>
                          _handleDragUpdate(details, constraints),
                      onVerticalDragEnd: _handleDragEnd,
                      child: Container(
                        height: widget.dividerWidth,
                        width: constraints.maxWidth,
                        color: dividerColor,
                        child: widget.resizable
                            ? Center(
                                child: Container(
                                  width: widget.handleSize,
                                  height: widget.dividerWidth * 3,
                                  decoration: BoxDecoration(
                                    color: handleColor,
                                    borderRadius: BorderRadius.circular(
                                        widget.dividerWidth),
                                  ),
                                ),
                              )
                            : null,
                      ),
                    ),

                    // Second panel
                    SizedBox(
                      height: constraints.maxHeight * (1 - _firstPanelWeight) -
                          (widget.dividerWidth / 2),
                      width: constraints.maxWidth,
                      child: widget.secondPanel,
                    ),
                  ],
                )
              : Row(
                  children: [
                    // First panel
                    SizedBox(
                      width: constraints.maxWidth * _firstPanelWeight -
                          (widget.dividerWidth / 2),
                      height: constraints.maxHeight,
                      child: widget.firstPanel,
                    ),

                    // Divider
                    GestureDetector(
                      onHorizontalDragStart: _handleDragStart,
                      onHorizontalDragUpdate: (details) =>
                          _handleDragUpdate(details, constraints),
                      onHorizontalDragEnd: _handleDragEnd,
                      child: Container(
                        width: widget.dividerWidth,
                        height: constraints.maxHeight,
                        color: dividerColor,
                        child: widget.resizable
                            ? Center(
                                child: Container(
                                  height: widget.handleSize,
                                  width: widget.dividerWidth * 3,
                                  decoration: BoxDecoration(
                                    color: handleColor,
                                    borderRadius: BorderRadius.circular(
                                        widget.dividerWidth),
                                  ),
                                ),
                              )
                            : null,
                      ),
                    ),

                    // Second panel
                    SizedBox(
                      width: constraints.maxWidth * (1 - _firstPanelWeight) -
                          (widget.dividerWidth / 2),
                      height: constraints.maxHeight,
                      child: widget.secondPanel,
                    ),
                  ],
                ),
        );
      },
    );
  }
}

/// A customizable multi-panel split view component
class AppMultiSplitView extends StatefulWidget {
  /// The list of panel data
  final List<AppSplitPanelData> panels;

  /// The list of initial panel weights (must sum to 1.0)
  final List<double>? initialPanelWeights;

  /// The minimum weight for each panel
  final double minPanelWeight;

  /// Whether the split is vertical (horizontal divider)
  final bool isVertical;

  /// The color of the dividers
  final Color? dividerColor;

  /// The width of the dividers
  final double dividerWidth;

  /// The handle size for dragging the dividers
  final double handleSize;

  /// The handle color
  final Color? handleColor;

  /// The background color of the split view
  final Color? backgroundColor;

  /// The border radius of the split view
  final BorderRadius? borderRadius;

  /// The elevation of the split view
  final double elevation;

  /// The border of the split view
  final Border? border;

  /// Whether the dividers can be dragged to resize the panels
  final bool resizable;

  /// Callback when a divider position changes
  final Function(List<double>)? onWeightsChanged;

  const AppMultiSplitView({
    super.key,
    required this.panels,
    this.initialPanelWeights,
    this.minPanelWeight = 0.05,
    this.isVertical = false,
    this.dividerColor,
    this.dividerWidth = 1.0,
    this.handleSize = 20.0,
    this.handleColor,
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 0,
    this.border,
    this.resizable = true,
    this.onWeightsChanged,
  })  : assert(panels.length >= 2, 'At least two panels must be provided'),
        assert(
            initialPanelWeights == null ||
                initialPanelWeights.length == panels.length,
            'initialPanelWeights must have the same length as panels'),
        assert(minPanelWeight >= 0.0 && minPanelWeight <= 1.0 / panels.length,
            'minPanelWeight must be between 0.0 and 1.0 / panels.length');

  @override
  State<AppMultiSplitView> createState() => _AppMultiSplitViewState();
}

class _AppMultiSplitViewState extends State<AppMultiSplitView> {
  late List<double> _panelWeights;
  int? _dragIndex;
  double? _dragStartPos;
  late List<double> _dragStartWeights;

  @override
  void initState() {
    super.initState();

    if (widget.initialPanelWeights != null) {
      _panelWeights = List.from(widget.initialPanelWeights!);
    } else {
      // Equal distribution
      final equalWeight = 1.0 / widget.panels.length;
      _panelWeights = List.filled(widget.panels.length, equalWeight);
    }

    _dragStartWeights = List.from(_panelWeights);
  }

  void _handleDragStart(int index, DragStartDetails details) {
    if (!widget.resizable) return;

    setState(() {
      _dragIndex = index;
      _dragStartPos = widget.isVertical
          ? details.globalPosition.dy
          : details.globalPosition.dx;
      _dragStartWeights = List.from(_panelWeights);
    });
  }

  void _handleDragUpdate(
      DragUpdateDetails details, BoxConstraints constraints) {
    if (!widget.resizable || _dragIndex == null || _dragStartPos == null) {
      return;
    }

    final currentPos = widget.isVertical
        ? details.globalPosition.dy
        : details.globalPosition.dx;
    final totalSize =
        widget.isVertical ? constraints.maxHeight : constraints.maxWidth;

    if (totalSize <= 0) return;

    final delta = (currentPos - _dragStartPos!) / totalSize;

    // Adjust the weights of the panels on either side of the divider
    final newWeights = List<double>.from(_dragStartWeights);

    // Ensure minimum weights
    final leftIndex = _dragIndex!;
    final rightIndex = _dragIndex! + 1;

    final leftWeight = _dragStartWeights[leftIndex] + delta;
    final rightWeight = _dragStartWeights[rightIndex] - delta;

    if (leftWeight < widget.minPanelWeight ||
        rightWeight < widget.minPanelWeight) {
      return;
    }

    newWeights[leftIndex] = leftWeight;
    newWeights[rightIndex] = rightWeight;

    setState(() {
      _panelWeights = newWeights;
    });

    if (widget.onWeightsChanged != null) {
      widget.onWeightsChanged!(_panelWeights);
    }
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!widget.resizable) return;

    setState(() {
      _dragIndex = null;
      _dragStartPos = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final Color dividerColor = widget.dividerColor ??
        Theme.of(context).dividerTheme.color ??
        AppTheme.textSecondaryColor.withAlpha(51);
    final Color handleColor =
        widget.handleColor ?? AppTheme.primaryColor.withAlpha(128);

    return LayoutBuilder(
      builder: (context, constraints) {
        final List<Widget> children = [];

        for (int i = 0; i < widget.panels.length; i++) {
          // Add panel
          final panel = widget.panels[i];
          final panelSize = widget.isVertical
              ? constraints.maxHeight * _panelWeights[i] -
                  (i < widget.panels.length - 1 ? widget.dividerWidth / 2 : 0) -
                  (i > 0 ? widget.dividerWidth / 2 : 0)
              : constraints.maxWidth * _panelWeights[i] -
                  (i < widget.panels.length - 1 ? widget.dividerWidth / 2 : 0) -
                  (i > 0 ? widget.dividerWidth / 2 : 0);

          children.add(
            SizedBox(
              width: widget.isVertical ? constraints.maxWidth : panelSize,
              height: widget.isVertical ? panelSize : constraints.maxHeight,
              child: panel.content,
            ),
          );

          // Add divider after all panels except the last one
          if (i < widget.panels.length - 1) {
            final dividerGesture = widget.isVertical
                ? GestureDetector(
                    onVerticalDragStart: (details) =>
                        _handleDragStart(i, details),
                    onVerticalDragUpdate: (details) =>
                        _handleDragUpdate(details, constraints),
                    onVerticalDragEnd: _handleDragEnd,
                    child: Container(
                      height: widget.dividerWidth,
                      width: constraints.maxWidth,
                      color: dividerColor,
                      child: widget.resizable
                          ? Center(
                              child: Container(
                                width: widget.handleSize,
                                height: widget.dividerWidth * 3,
                                decoration: BoxDecoration(
                                  color: handleColor,
                                  borderRadius: BorderRadius.circular(
                                      widget.dividerWidth),
                                ),
                              ),
                            )
                          : null,
                    ),
                  )
                : GestureDetector(
                    onHorizontalDragStart: (details) =>
                        _handleDragStart(i, details),
                    onHorizontalDragUpdate: (details) =>
                        _handleDragUpdate(details, constraints),
                    onHorizontalDragEnd: _handleDragEnd,
                    child: Container(
                      width: widget.dividerWidth,
                      height: constraints.maxHeight,
                      color: dividerColor,
                      child: widget.resizable
                          ? Center(
                              child: Container(
                                height: widget.handleSize,
                                width: widget.dividerWidth * 3,
                                decoration: BoxDecoration(
                                  color: handleColor,
                                  borderRadius: BorderRadius.circular(
                                      widget.dividerWidth),
                                ),
                              ),
                            )
                          : null,
                    ),
                  );

            children.add(dividerGesture);
          }
        }

        return Container(
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? AppTheme.backgroundColor,
            borderRadius: widget.borderRadius,
            border: widget.border,
            boxShadow: widget.elevation > 0
                ? [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: widget.elevation * 2,
                      offset: Offset(0, widget.elevation),
                    ),
                  ]
                : null,
          ),
          child: widget.isVertical
              ? Column(children: children)
              : Row(children: children),
        );
      },
    );
  }
}

/// Data for a split panel
class AppSplitPanelData {
  /// The content of the panel
  final Widget content;

  /// The minimum weight of the panel
  final double? minWeight;

  /// The maximum weight of the panel
  final double? maxWeight;

  const AppSplitPanelData({
    required this.content,
    this.minWeight,
    this.maxWeight,
  });
}
