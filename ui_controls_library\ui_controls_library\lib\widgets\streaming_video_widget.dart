import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
// Removed BetterPlayer import to fix compatibility issues

/// A widget that provides streaming video playback functionality.
///
/// This widget allows users to play streaming videos from various sources and protocols.
/// It supports various configuration options such as autoplay, looping, custom controls,
/// adaptive streaming, and custom styling.
class StreamingVideoWidget extends StatefulWidget {
  /// URL of the streaming video
  final String? url;

  /// Type of streaming (HLS, DASH, RTMP, etc.)
  final StreamingType streamingType;

  /// Whether to autoplay the video when the widget is loaded
  final bool autoplay;

  /// Whether to loop the video
  final bool loop;

  /// Whether to show video controls
  final bool showControls;

  /// Whether to show fullscreen button
  final bool showFullscreenButton;

  /// Whether to show playback speed controls
  final bool showPlaybackSpeedButton;

  /// Whether to show volume control
  final bool showVolumeButton;

  /// Whether to show progress bar
  final bool showProgressBar;

  /// Whether to show duration
  final bool showDuration;

  /// Whether to show title
  final bool showTitle;

  /// Title of the video
  final String? title;

  /// Subtitle or description of the video
  final String? subtitle;

  /// Initial volume (0.0 to 1.0)
  final double initialVolume;

  /// Theme color for controls
  final Color? themeColor;

  /// Background color
  final Color? backgroundColor;

  /// Text color
  final Color? textColor;

  /// Icon color
  final Color? iconColor;

  /// Progress bar color
  final Color? progressBarColor;

  /// Buffer bar color
  final Color? bufferBarColor;

  /// Custom play icon
  final IconData playIcon;

  /// Custom pause icon
  final IconData pauseIcon;

  /// Custom fullscreen icon
  final IconData fullscreenIcon;

  /// Custom exit fullscreen icon
  final IconData exitFullscreenIcon;

  /// Custom volume icon
  final IconData volumeIcon;

  /// Custom mute icon
  final IconData muteIcon;

  /// Whether to use a compact layout
  final bool compact;

  /// Whether to show a thumbnail before playing
  final bool showThumbnail;

  /// URL for the thumbnail image
  final String? thumbnailUrl;

  /// Whether to allow picture-in-picture mode
  final bool allowPip;

  /// Aspect ratio of the video (null for original aspect ratio)
  final double? aspectRatio;

  /// Initial playback position
  final Duration? startPosition;

  /// Whether to mute the video initially
  final bool initiallyMuted;

  /// Whether to show closed captions
  final bool showClosedCaptions;

  /// URL for closed captions file
  final String? closedCaptionsUrl;

  /// Whether to allow seeking by tapping on the progress bar
  final bool allowSeeking;

  /// Whether to show overlay controls on tap
  final bool showOverlayOnTap;

  /// Whether to hide controls after a period of inactivity
  final bool hideControlsOnInactivity;

  /// Duration after which controls should hide
  final Duration controlsHideDuration;

  /// Whether to show a replay button when video ends
  final bool showReplayButton;

  /// Whether to show a play/pause button in the center
  final bool showCenterPlayButton;

  /// Whether to show a loading indicator
  final bool showLoadingIndicator;

  /// Color of the loading indicator
  final Color? loadingIndicatorColor;

  /// Whether to show error messages
  final bool showErrorMessages;

  /// Custom error message
  final String? errorMessage;

  /// Whether to enable adaptive streaming
  final bool enableAdaptiveStreaming;

  /// List of available stream qualities
  final List<StreamQuality>? availableQualities;

  /// Initial stream quality
  final StreamQuality? initialQuality;

  /// Whether to automatically select the best quality based on network conditions
  final bool autoSelectQuality;

  /// Buffer configuration in milliseconds
  final int bufferDuration;

  /// Whether this is a live stream
  final bool isLiveStream;

  /// Whether to show a live indicator for live streams
  final bool showLiveIndicator;

  /// Text to display for the live indicator
  final String liveText;

  /// Color of the live indicator
  final Color liveIndicatorColor;

  /// Whether to show stream health indicator
  final bool showStreamHealth;

  /// Whether to retry automatically on stream errors
  final bool autoRetry;

  /// Number of retry attempts
  final int maxRetryAttempts;

  /// Delay between retry attempts in seconds
  final int retryDelaySeconds;

  /// Whether to show stream statistics (bitrate, resolution, etc.)
  final bool showStreamStats;

  /// Whether to enable DRM protection
  final bool enableDrm;

  /// DRM license URL
  final String? drmLicenseUrl;

  /// DRM headers
  final Map<String, String>? drmHeaders;

  /// Callback when video starts playing
  final VoidCallback? onPlay;

  /// Callback when video is paused
  final VoidCallback? onPause;

  /// Callback when video ends
  final VoidCallback? onComplete;

  /// Callback when video is seeked
  final Function(Duration)? onSeek;

  /// Callback when volume changes
  final Function(double)? onVolumeChange;

  /// Callback when fullscreen mode changes
  final Function(bool)? onFullscreenChange;

  /// Callback when an error occurs
  final Function(String)? onError;

  /// Callback when video is loaded
  final VoidCallback? onLoaded;

  /// Callback when video buffer changes
  final Function(double)? onBufferChange;

  /// Callback when stream quality changes
  final Function(StreamQuality)? onQualityChange;

  /// Callback when stream health changes
  final Function(StreamHealth)? onStreamHealthChange;

  /// JSON configuration for the widget
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON configuration
  final bool useJsonConfig;

  /// JSON callbacks
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// Creates a streaming video widget.
  const StreamingVideoWidget({
    super.key,
    this.url,
    this.streamingType = StreamingType.hls,
    this.autoplay = false,
    this.loop = false,
    this.showControls = true,
    this.showFullscreenButton = true,
    this.showPlaybackSpeedButton = false,
    this.showVolumeButton = true,
    this.showProgressBar = true,
    this.showDuration = true,
    this.showTitle = true,
    this.title,
    this.subtitle,
    this.initialVolume = 0.7,
    this.themeColor,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.progressBarColor,
    this.bufferBarColor,
    this.playIcon = Icons.play_arrow,
    this.pauseIcon = Icons.pause,
    this.fullscreenIcon = Icons.fullscreen,
    this.exitFullscreenIcon = Icons.fullscreen_exit,
    this.volumeIcon = Icons.volume_up,
    this.muteIcon = Icons.volume_off,
    this.compact = false,
    this.showThumbnail = false,
    this.thumbnailUrl,
    this.allowPip = false,
    this.aspectRatio,
    this.startPosition,
    this.initiallyMuted = false,
    this.showClosedCaptions = false,
    this.closedCaptionsUrl,
    this.allowSeeking = true,
    this.showOverlayOnTap = true,
    this.hideControlsOnInactivity = true,
    this.controlsHideDuration = const Duration(seconds: 3),
    this.showReplayButton = true,
    this.showCenterPlayButton = true,
    this.showLoadingIndicator = true,
    this.loadingIndicatorColor,
    this.showErrorMessages = true,
    this.errorMessage,
    this.enableAdaptiveStreaming = true,
    this.availableQualities,
    this.initialQuality,
    this.autoSelectQuality = true,
    this.bufferDuration = 30000,
    this.isLiveStream = false,
    this.showLiveIndicator = true,
    this.liveText = "LIVE",
    this.liveIndicatorColor = Colors.red,
    this.showStreamHealth = false,
    this.autoRetry = true,
    this.maxRetryAttempts = 3,
    this.retryDelaySeconds = 5,
    this.showStreamStats = false,
    this.enableDrm = false,
    this.drmLicenseUrl,
    this.drmHeaders,
    this.onPlay,
    this.onPause,
    this.onComplete,
    this.onSeek,
    this.onVolumeChange,
    this.onFullscreenChange,
    this.onError,
    this.onLoaded,
    this.onBufferChange,
    this.onQualityChange,
    this.onStreamHealthChange,
    this.jsonConfig,
    this.useJsonConfig = false,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
  });

  /// Helper method to parse a color from a string or map
  static Color _parseColor(dynamic colorValue, [Color defaultColor = Colors.blue]) {
    if (colorValue == null) return defaultColor;

    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return defaultColor;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return defaultColor;
  }

  /// Helper method to convert a color to a string
  static String _colorToString(Color color) {
    final r = color.toString().split('(0x')[1].substring(2, 4);
    final g = color.toString().split('(0x')[1].substring(4, 6);
    final b = color.toString().split('(0x')[1].substring(6, 8);
    return '#$r$g$b';
  }

  /// Helper method to parse a Duration from a string or map
  static Duration? _parseDuration(dynamic durationValue) {
    if (durationValue == null) return null;

    if (durationValue is String) {
      // Parse from string format "HH:MM:SS" or "MM:SS"
      final parts = durationValue.split(':');
      if (parts.length == 3) {
        final hours = int.tryParse(parts[0]) ?? 0;
        final minutes = int.tryParse(parts[1]) ?? 0;
        final seconds = int.tryParse(parts[2]) ?? 0;
        return Duration(hours: hours, minutes: minutes, seconds: seconds);
      } else if (parts.length == 2) {
        final minutes = int.tryParse(parts[0]) ?? 0;
        final seconds = int.tryParse(parts[1]) ?? 0;
        return Duration(minutes: minutes, seconds: seconds);
      } else if (parts.length == 1) {
        final seconds = int.tryParse(parts[0]) ?? 0;
        return Duration(seconds: seconds);
      }
    } else if (durationValue is Map) {
      final hours = durationValue['hours'] as int? ?? 0;
      final minutes = durationValue['minutes'] as int? ?? 0;
      final seconds = durationValue['seconds'] as int? ?? 0;
      final milliseconds = durationValue['milliseconds'] as int? ?? 0;
      return Duration(
        hours: hours,
        minutes: minutes,
        seconds: seconds,
        milliseconds: milliseconds,
      );
    } else if (durationValue is int) {
      return Duration(seconds: durationValue);
    }
    return null;
  }

  /// Helper method to convert a Duration to a string
  static String _durationToString(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Helper method to parse a StreamingType from a string
  static StreamingType _parseStreamingType(dynamic typeValue) {
    if (typeValue is String) {
      switch (typeValue.toLowerCase()) {
        case 'hls': return StreamingType.hls;
        case 'dash': return StreamingType.dash;
        case 'rtmp': return StreamingType.rtmp;
        case 'other': return StreamingType.other;
        default: return StreamingType.hls;
      }
    }
    return StreamingType.hls;
  }

  /// Helper method to convert a StreamingType to a string
  static String _streamingTypeToString(StreamingType type) {
    return type.toString().split('.').last;
  }

  /// Helper method to parse a StreamQuality from a map
  static StreamQuality? _parseStreamQuality(dynamic qualityValue) {
    if (qualityValue == null) return null;

    if (qualityValue is Map) {
      final name = qualityValue['name'] as String? ?? 'Default';
      final url = qualityValue['url'] as String? ?? '';
      final width = qualityValue['width'] as int?;
      final height = qualityValue['height'] as int?;
      final bitrate = qualityValue['bitrate'] as int?;

      return StreamQuality(
        name: name,
        url: url,
        width: width,
        height: height,
        bitrate: bitrate,
      );
    }
    return null;
  }

  /// Helper method to convert a StreamQuality to a map
  static Map<String, dynamic> _streamQualityToJson(StreamQuality quality) {
    return {
      'name': quality.name,
      'url': quality.url,
      'width': quality.width,
      'height': quality.height,
      'bitrate': quality.bitrate,
    };
  }

  /// Helper method to parse a list of StreamQuality from a list
  static List<StreamQuality>? _parseStreamQualities(dynamic qualitiesValue) {
    if (qualitiesValue == null) return null;

    if (qualitiesValue is List) {
      return qualitiesValue
          .map((quality) => _parseStreamQuality(quality))
          .whereType<StreamQuality>()
          .toList();
    }
    return null;
  }

  /// Creates a StreamingVideoWidget from a JSON map
  ///
  /// This factory constructor allows for creating a fully configured streaming video widget
  /// from a JSON object, making it easy to load configurations from external sources.
  factory StreamingVideoWidget.fromJson(dynamic jsonData) {
    // Handle string JSON input
    Map<String, dynamic> json;
    if (jsonData is String) {
      json = jsonDecode(jsonData) as Map<String, dynamic>;
    } else if (jsonData is Map<String, dynamic>) {
      json = jsonData;
    } else {
      throw ArgumentError('Invalid JSON data: $jsonData');
    }

    // Handle the specific format: {url, type, title, thumbnail, duration, autoplay}
    if (json.containsKey('url') && json.containsKey('type')) {
      final url = json['url'] as String?;
      final type = json['type'] as String?;
      final title = json['title'] as String?;
      final thumbnail = json['thumbnail'] as String?;
      final duration = json['duration'] as int?;
      final autoplay = json['autoplay'] as bool? ?? false;

      // Determine streaming type from the type field
      StreamingType streamingType = StreamingType.hls;
      if (type != null) {
        if (type.contains('hls')) {
          streamingType = StreamingType.hls;
        } else if (type.contains('dash')) {
          streamingType = StreamingType.dash;
        } else if (type.contains('rtmp')) {
          streamingType = StreamingType.rtmp;
        } else {
          streamingType = StreamingType.other;
        }
      }

      // Create a new widget with the parsed values
      return StreamingVideoWidget(
        url: url,
        streamingType: streamingType,
        title: title,
        thumbnailUrl: thumbnail,
        showThumbnail: thumbnail != null,
        autoplay: autoplay,
        startPosition: duration != null ? Duration(seconds: 0) : null,
      );
    }

    // Parse basic properties
    final url = json['url'] as String?;
    final streamingType = _parseStreamingType(json['streamingType']);
    final autoplay = json['autoplay'] as bool? ?? false;
    final loop = json['loop'] as bool? ?? false;

    // Parse control properties
    final showControls = json['showControls'] as bool? ?? true;
    final showFullscreenButton = json['showFullscreenButton'] as bool? ?? true;
    final showPlaybackSpeedButton = json['showPlaybackSpeedButton'] as bool? ?? false;
    final showVolumeButton = json['showVolumeButton'] as bool? ?? true;
    final showProgressBar = json['showProgressBar'] as bool? ?? true;
    final showDuration = json['showDuration'] as bool? ?? true;

    // Parse title properties
    final showTitle = json['showTitle'] as bool? ?? true;
    final title = json['title'] as String?;
    final subtitle = json['subtitle'] as String?;

    // Parse volume properties
    final initialVolume = json['initialVolume'] != null
        ? (json['initialVolume'] as num).toDouble()
        : 0.7;
    final initiallyMuted = json['initiallyMuted'] as bool? ?? false;

    // Parse color properties
    final themeColor = json['themeColor'] != null
        ? _parseColor(json['themeColor'])
        : null;
    final backgroundColor = json['backgroundColor'] != null
        ? _parseColor(json['backgroundColor'])
        : null;
    final textColor = json['textColor'] != null
        ? _parseColor(json['textColor'])
        : null;
    final iconColor = json['iconColor'] != null
        ? _parseColor(json['iconColor'])
        : null;
    final progressBarColor = json['progressBarColor'] != null
        ? _parseColor(json['progressBarColor'])
        : null;
    final bufferBarColor = json['bufferBarColor'] != null
        ? _parseColor(json['bufferBarColor'])
        : null;

    // Parse icon properties
    final playIcon = json['playIcon'] != null
        ? Icons.play_arrow //IconData(json['playIcon'] as int, fontFamily: 'MaterialIcons')
        : Icons.play_arrow;
    final pauseIcon = json['pauseIcon'] != null
        ? Icons.pause //IconData(json['pauseIcon'] as int, fontFamily: 'MaterialIcons')
        : Icons.pause;
    final fullscreenIcon = json['fullscreenIcon'] != null
        ? Icons.fullscreen //IconData(json['fullscreenIcon'] as int, fontFamily: 'MaterialIcons')
        : Icons.fullscreen;
    final exitFullscreenIcon = json['exitFullscreenIcon'] != null
        ? Icons.fullscreen_exit //IconData(json['exitFullscreenIcon'] as int, fontFamily: 'MaterialIcons')
        : Icons.fullscreen_exit;
    final volumeIcon = json['volumeIcon'] != null
        ? Icons.volume_up // IconData(json['volumeIcon'] as int, fontFamily: 'MaterialIcons')
        : Icons.volume_up;
    final muteIcon = json['muteIcon'] != null
        ?Icons.volume_off // IconData(json['muteIcon'] as int, fontFamily: 'MaterialIcons')
        : Icons.volume_off;

    // Parse layout properties
    final compact = json['compact'] as bool? ?? false;
    final showThumbnail = json['showThumbnail'] as bool? ?? false;
    final thumbnailUrl = json['thumbnailUrl'] as String?;
    final allowPip = json['allowPip'] as bool? ?? false;
    final aspectRatio = json['aspectRatio'] != null
        ? (json['aspectRatio'] as num).toDouble()
        : null;

    // Parse playback properties
    final startPosition = json['startPosition'] != null
        ? _parseDuration(json['startPosition'])
        : null;
    final showClosedCaptions = json['showClosedCaptions'] as bool? ?? false;
    final closedCaptionsUrl = json['closedCaptionsUrl'] as String?;
    final allowSeeking = json['allowSeeking'] as bool? ?? true;

    // Parse control behavior properties
    final showOverlayOnTap = json['showOverlayOnTap'] as bool? ?? true;
    final hideControlsOnInactivity = json['hideControlsOnInactivity'] as bool? ?? true;
    final controlsHideDuration = json['controlsHideDuration'] != null
        ? _parseDuration(json['controlsHideDuration']) ?? const Duration(seconds: 3)
        : const Duration(seconds: 3);

    // Parse button properties
    final showReplayButton = json['showReplayButton'] as bool? ?? true;
    final showCenterPlayButton = json['showCenterPlayButton'] as bool? ?? true;

    // Parse loading properties
    final showLoadingIndicator = json['showLoadingIndicator'] as bool? ?? true;
    final loadingIndicatorColor = json['loadingIndicatorColor'] != null
        ? _parseColor(json['loadingIndicatorColor'])
        : null;

    // Parse error properties
    final showErrorMessages = json['showErrorMessages'] as bool? ?? true;
    final errorMessage = json['errorMessage'] as String?;

    // Parse streaming properties
    final enableAdaptiveStreaming = json['enableAdaptiveStreaming'] as bool? ?? true;
    final availableQualities = _parseStreamQualities(json['availableQualities']);
    final initialQuality = json['initialQuality'] != null
        ? _parseStreamQuality(json['initialQuality'])
        : null;
    final autoSelectQuality = json['autoSelectQuality'] as bool? ?? true;
    final bufferDuration = json['bufferDuration'] as int? ?? 30000;

    // Parse live stream properties
    final isLiveStream = json['isLiveStream'] as bool? ?? false;
    final showLiveIndicator = json['showLiveIndicator'] as bool? ?? true;
    final liveText = json['liveText'] as String? ?? "LIVE";
    final liveIndicatorColor = json['liveIndicatorColor'] != null
        ? _parseColor(json['liveIndicatorColor'])
        : Colors.red;

    // Parse stream health properties
    final showStreamHealth = json['showStreamHealth'] as bool? ?? false;
    final autoRetry = json['autoRetry'] as bool? ?? true;
    final maxRetryAttempts = json['maxRetryAttempts'] as int? ?? 3;
    final retryDelaySeconds = json['retryDelaySeconds'] as int? ?? 5;
    final showStreamStats = json['showStreamStats'] as bool? ?? false;

    // Parse DRM properties
    final enableDrm = json['enableDrm'] as bool? ?? false;
    final drmLicenseUrl = json['drmLicenseUrl'] as String?;
    Map<String, String>? drmHeaders;
    if (json['drmHeaders'] != null && json['drmHeaders'] is Map) {
      drmHeaders = Map<String, String>.from(json['drmHeaders'] as Map);
    }

    // Parse JSON configuration
    final jsonConfig = json['jsonConfig'] as Map<String, dynamic>?;
    final useJsonConfig = json['useJsonConfig'] as bool? ?? false;
    final jsonCallbacks = json['jsonCallbacks'] as Map<String, dynamic>?;
    final useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    // Create and return the widget
    return StreamingVideoWidget(
      url: url,
      streamingType: streamingType,
      autoplay: autoplay,
      loop: loop,
      showControls: showControls,
      showFullscreenButton: showFullscreenButton,
      showPlaybackSpeedButton: showPlaybackSpeedButton,
      showVolumeButton: showVolumeButton,
      showProgressBar: showProgressBar,
      showDuration: showDuration,
      showTitle: showTitle,
      title: title,
      subtitle: subtitle,
      initialVolume: initialVolume,
      themeColor: themeColor,
      backgroundColor: backgroundColor,
      textColor: textColor,
      iconColor: iconColor,
      progressBarColor: progressBarColor,
      bufferBarColor: bufferBarColor,
      playIcon: playIcon,
      pauseIcon: pauseIcon,
      fullscreenIcon: fullscreenIcon,
      exitFullscreenIcon: exitFullscreenIcon,
      volumeIcon: volumeIcon,
      muteIcon: muteIcon,
      compact: compact,
      showThumbnail: showThumbnail,
      thumbnailUrl: thumbnailUrl,
      allowPip: allowPip,
      aspectRatio: aspectRatio,
      startPosition: startPosition,
      initiallyMuted: initiallyMuted,
      showClosedCaptions: showClosedCaptions,
      closedCaptionsUrl: closedCaptionsUrl,
      allowSeeking: allowSeeking,
      showOverlayOnTap: showOverlayOnTap,
      hideControlsOnInactivity: hideControlsOnInactivity,
      controlsHideDuration: controlsHideDuration,
      showReplayButton: showReplayButton,
      showCenterPlayButton: showCenterPlayButton,
      showLoadingIndicator: showLoadingIndicator,
      loadingIndicatorColor: loadingIndicatorColor,
      showErrorMessages: showErrorMessages,
      errorMessage: errorMessage,
      enableAdaptiveStreaming: enableAdaptiveStreaming,
      availableQualities: availableQualities,
      initialQuality: initialQuality,
      autoSelectQuality: autoSelectQuality,
      bufferDuration: bufferDuration,
      isLiveStream: isLiveStream,
      showLiveIndicator: showLiveIndicator,
      liveText: liveText,
      liveIndicatorColor: liveIndicatorColor,
      showStreamHealth: showStreamHealth,
      autoRetry: autoRetry,
      maxRetryAttempts: maxRetryAttempts,
      retryDelaySeconds: retryDelaySeconds,
      showStreamStats: showStreamStats,
      enableDrm: enableDrm,
      drmLicenseUrl: drmLicenseUrl,
      drmHeaders: drmHeaders,
      jsonConfig: jsonConfig,
      useJsonConfig: useJsonConfig,
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
    );
  }

  /// Converts the StreamingVideoWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'streamingType': _streamingTypeToString(streamingType),
      'autoplay': autoplay,
      'loop': loop,
      'showControls': showControls,
      'showFullscreenButton': showFullscreenButton,
      'showPlaybackSpeedButton': showPlaybackSpeedButton,
      'showVolumeButton': showVolumeButton,
      'showProgressBar': showProgressBar,
      'showDuration': showDuration,
      'showTitle': showTitle,
      'title': title,
      'subtitle': subtitle,
      'initialVolume': initialVolume,
      'themeColor': themeColor != null ? _colorToString(themeColor!) : null,
      'backgroundColor': backgroundColor != null ? _colorToString(backgroundColor!) : null,
      'textColor': textColor != null ? _colorToString(textColor!) : null,
      'iconColor': iconColor != null ? _colorToString(iconColor!) : null,
      'progressBarColor': progressBarColor != null ? _colorToString(progressBarColor!) : null,
      'bufferBarColor': bufferBarColor != null ? _colorToString(bufferBarColor!) : null,
      'compact': compact,
      'showThumbnail': showThumbnail,
      'thumbnailUrl': thumbnailUrl,
      'allowPip': allowPip,
      'aspectRatio': aspectRatio,
      'startPosition': startPosition != null ? _durationToString(startPosition!) : null,
      'initiallyMuted': initiallyMuted,
      'showClosedCaptions': showClosedCaptions,
      'closedCaptionsUrl': closedCaptionsUrl,
      'allowSeeking': allowSeeking,
      'showOverlayOnTap': showOverlayOnTap,
      'hideControlsOnInactivity': hideControlsOnInactivity,
      'controlsHideDuration': _durationToString(controlsHideDuration),
      'showReplayButton': showReplayButton,
      'showCenterPlayButton': showCenterPlayButton,
      'showLoadingIndicator': showLoadingIndicator,
      'loadingIndicatorColor': loadingIndicatorColor != null ? _colorToString(loadingIndicatorColor!) : null,
      'showErrorMessages': showErrorMessages,
      'errorMessage': errorMessage,
      'enableAdaptiveStreaming': enableAdaptiveStreaming,
      'availableQualities': availableQualities?.map((quality) => _streamQualityToJson(quality)).toList(),
      'autoSelectQuality': autoSelectQuality,
      'bufferDuration': bufferDuration,
      'isLiveStream': isLiveStream,
      'showLiveIndicator': showLiveIndicator,
      'liveText': liveText,
      'liveIndicatorColor': _colorToString(liveIndicatorColor),
      'showStreamHealth': showStreamHealth,
      'autoRetry': autoRetry,
      'maxRetryAttempts': maxRetryAttempts,
      'retryDelaySeconds': retryDelaySeconds,
      'showStreamStats': showStreamStats,
      'enableDrm': enableDrm,
      'drmLicenseUrl': drmLicenseUrl,
      'drmHeaders': drmHeaders,
      'useJsonConfig': useJsonConfig,
      'useJsonCallbacks': useJsonCallbacks,
    };
  }

  @override
  State<StreamingVideoWidget> createState() => _StreamingVideoWidgetState();
}

class _StreamingVideoWidgetState extends State<StreamingVideoWidget> {
  bool _isLoading = true;
  bool _isMuted = false;
  double _volume = 0.7;
  String? _errorMessage;
  bool _isShowingThumbnail = false;
  Timer? _hideControlsTimer;
  StreamHealth _streamHealth = StreamHealth.good;
  int _retryCount = 0;
  Timer? _retryTimer;
  // These fields are used in event listeners
  bool _isPlaying = false;
  bool _isFullScreen = false;
  StreamQuality? _currentQuality;

  @override
  void initState() {
    super.initState();
    _volume = widget.initialVolume;
    _isMuted = widget.initiallyMuted;
    _isShowingThumbnail = widget.showThumbnail;
    _currentQuality = widget.initialQuality;

    // Initialize the player state
    _initializePlayer();
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _retryTimer?.cancel();
    super.dispose();
  }

  void _initializePlayer([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;

    if (cfg.url == null || cfg.url!.isEmpty) {
      setState(() {
        _errorMessage = 'No streaming URL provided';
        _isLoading = false;
      });
      return;
    }

    // In a real implementation, we would initialize a video player here
    // For now, we'll just simulate loading
    Future.delayed(Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isLoading = false;

          // If autoplay is enabled and we're not showing a thumbnail,
          // simulate starting playback
          if (cfg.autoplay && !_isShowingThumbnail) {
            _isPlaying = true;
            if (cfg.onPlay != null) {
              cfg.onPlay!();
            }
            if (cfg.onLoaded != null) {
              cfg.onLoaded!();
            }
          }
        });
      }
    });
  }

  // Simplified methods for the mock implementation

  void _scheduleRetry([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;
    _retryTimer?.cancel();
    _retryTimer = Timer(Duration(seconds: cfg.retryDelaySeconds), () {
      if (mounted) {
        setState(() {
          _retryCount++;
          _errorMessage = null;
          _isLoading = true;
        });
        _initializePlayer();
      }
    });
  }

  void _checkStreamHealth([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;
    if (!cfg.showStreamHealth) return;

    // This is a simplified version that just randomly changes health status
    // In a real implementation, this would check actual streaming metrics
    final random = DateTime.now().millisecondsSinceEpoch % 3;

    StreamHealth newHealth;
    if (random == 0) {
      newHealth = StreamHealth.poor;
    } else if (random == 1) {
      newHealth = StreamHealth.fair;
    } else {
      newHealth = StreamHealth.good;
    }

    if (newHealth != _streamHealth) {
      setState(() {
        _streamHealth = newHealth;
      });

      if (cfg.onStreamHealthChange != null) {
        cfg.onStreamHealthChange!(_streamHealth);
      }
    }
  }

  void _play() {
    if (_isShowingThumbnail) {
      setState(() {
        _isShowingThumbnail = false;
      });
    }

    setState(() {
      _isPlaying = true;
    });

    if (widget.onPlay != null) {
      widget.onPlay!();
    }
  }

  Widget _buildThumbnail([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;
    if (cfg.thumbnailUrl == null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Icon(
            Icons.video_library,
            size: 80,
            color: Colors.white.withAlpha(128),
          ),
        ),
      );
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        Image.network(
          cfg.thumbnailUrl!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.black,
              child: Center(
                child: Icon(
                  Icons.broken_image,
                  size: 80,
                  color: Colors.white.withAlpha(128),
                ),
              ),
            );
          },
        ),
        Container(
          color: Colors.black.withAlpha(77), // 0.3 opacity
        ),
        IconButton(
          icon: Icon(
            cfg.playIcon,
            size: 60,
            color: Colors.white,
          ),
          onPressed: () {
            setState(() {
              _isShowingThumbnail = false;
            });
            _play();
          },
        ),
      ],
    );
  }

  Widget _buildErrorMessage([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;
    final errorText = cfg.errorMessage ?? _errorMessage ?? 'An error occurred';

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            errorText,
            style: TextStyle(
              color: cfg.textColor ?? Colors.white,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          if (cfg.autoRetry && _retryCount < cfg.maxRetryAttempts)
            Text(
              'Retrying in ${cfg.retryDelaySeconds} seconds...\nAttempt ${_retryCount + 1} of ${cfg.maxRetryAttempts}',
              style: TextStyle(
                color: cfg.textColor ?? Colors.white,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            )
          else
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _retryCount = 0;
                  _errorMessage = null;
                  _isLoading = true;
                });
                _initializePlayer();
              },
              child: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;
    return Center(
      child: CircularProgressIndicator(
        color: cfg.loadingIndicatorColor ?? cfg.themeColor ?? Colors.red,
      ),
    );
  }

  Widget _buildStreamHealthIndicator([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;
    if (!cfg.showStreamHealth) return const SizedBox.shrink();

    Color healthColor;
    String healthText;

    switch (_streamHealth) {
      case StreamHealth.good:
        healthColor = Colors.green;
        healthText = "Stream Quality: Good";
        break;
      case StreamHealth.fair:
        healthColor = Colors.orange;
        healthText = "Stream Quality: Fair";
        break;
      case StreamHealth.poor:
        healthColor = Colors.red;
        healthText = "Stream Quality: Poor";
        break;
    }

    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: healthColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              healthText,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiveIndicator([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;
    if (!cfg.isLiveStream || !cfg.showLiveIndicator) return const SizedBox.shrink();

    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: cfg.liveIndicatorColor,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          cfg.liveText,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildCompactView([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;

    if (_errorMessage != null && cfg.showErrorMessages) {
      return _buildErrorMessage(cfg);
    }

    if (_isLoading && cfg.showLoadingIndicator) {
      return _buildLoadingIndicator(cfg);
    }

    if (_isShowingThumbnail) {
      return _buildThumbnail(cfg);
    }

    // Simplified video player UI
    return Stack(
      children: [
        AspectRatio(
          aspectRatio: cfg.aspectRatio ?? 16 / 9,
          child: Container(
            color: Colors.black,
            child: Center(
              child: _isPlaying
                ? Icon(
                    Icons.pause_circle_outline,
                    size: 64,
                    color: Colors.white.withOpacity(0.7),
                  )
                : IconButton(
                    icon: Icon(
                      Icons.play_circle_outline,
                      size: 64,
                      color: Colors.white.withOpacity(0.7),
                    ),
                    onPressed: _play,
                  ),
            ),
          ),
        ),
        _buildStreamHealthIndicator(cfg),
        _buildLiveIndicator(cfg),
        // Show quality indicator if available
        if (_currentQuality != null)
          Positioned(
            bottom: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                "Quality: ${_currentQuality!.name}",
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        // Show URL
        if (cfg.url != null)
          Positioned(
            bottom: 40,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  "URL: ${cfg.url}",
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
        // Controls
        if (cfg.showControls)
          Positioned(
            bottom: 8,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(_isPlaying ? cfg.pauseIcon : cfg.playIcon, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _isPlaying = !_isPlaying;
                    });
                    if (_isPlaying) {
                      if (cfg.onPlay != null) cfg.onPlay!();
                    } else {
                      if (cfg.onPause != null) cfg.onPause!();
                    }
                  },
                ),
                IconButton(
                  icon: Icon(Icons.stop, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _isPlaying = false;
                    });
                    if (cfg.onComplete != null) cfg.onComplete!();
                  },
                ),
                if (cfg.showFullscreenButton)
                  IconButton(
                    icon: Icon(_isFullScreen ? cfg.exitFullscreenIcon : cfg.fullscreenIcon, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _isFullScreen = !_isFullScreen;
                      });
                      if (cfg.onFullscreenChange != null) {
                        cfg.onFullscreenChange!(_isFullScreen);
                      }
                    },
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildFullView([StreamingVideoWidget? config]) {
    final cfg = config ?? widget;

    if (_errorMessage != null && cfg.showErrorMessages) {
      return _buildErrorMessage(cfg);
    }

    if (_isLoading && cfg.showLoadingIndicator) {
      return _buildLoadingIndicator(cfg);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and subtitle
        if (cfg.showTitle && (cfg.title != null || cfg.subtitle != null))
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (cfg.title != null)
                  Text(
                    cfg.title!,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: cfg.textColor ?? Colors.black,
                    ),
                  ),
                if (cfg.subtitle != null)
                  Text(
                    cfg.subtitle!,
                    style: TextStyle(
                      fontSize: 14,
                      color: cfg.textColor != null
                          ? cfg.textColor!.withAlpha(179) // 0.7 opacity
                          : Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),

        // Video player
        Expanded(
          child: _isShowingThumbnail
              ? _buildThumbnail(cfg)
              : Stack(
                  children: [
                    Container(
                      color: Colors.black,
                      child: Center(
                        child: _isPlaying
                          ? Icon(
                              Icons.pause_circle_outline,
                              size: 64,
                              color: Colors.white.withOpacity(0.7),
                            )
                          : IconButton(
                              icon: Icon(
                                Icons.play_circle_outline,
                                size: 64,
                                color: Colors.white.withOpacity(0.7),
                              ),
                              onPressed: _play,
                            ),
                      ),
                    ),
                    _buildStreamHealthIndicator(cfg),
                    _buildLiveIndicator(cfg),
                    // Show URL
                    if (cfg.url != null)
                      Positioned(
                        bottom: 40,
                        left: 0,
                        right: 0,
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.black54,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              "URL: ${cfg.url}",
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                    // Controls
                    if (cfg.showControls)
                      Positioned(
                        bottom: 8,
                        left: 0,
                        right: 0,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            IconButton(
                              icon: Icon(_isPlaying ? cfg.pauseIcon : cfg.playIcon, color: Colors.white),
                              onPressed: () {
                                setState(() {
                                  _isPlaying = !_isPlaying;
                                });
                                if (_isPlaying) {
                                  if (cfg.onPlay != null) cfg.onPlay!();
                                } else {
                                  if (cfg.onPause != null) cfg.onPause!();
                                }
                              },
                            ),
                            IconButton(
                              icon: Icon(Icons.stop, color: Colors.white),
                              onPressed: () {
                                setState(() {
                                  _isPlaying = false;
                                });
                                if (cfg.onComplete != null) cfg.onComplete!();
                              },
                            ),
                            if (cfg.showFullscreenButton)
                              IconButton(
                                icon: Icon(_isFullScreen ? cfg.exitFullscreenIcon : cfg.fullscreenIcon, color: Colors.white),
                                onPressed: () {
                                  setState(() {
                                    _isFullScreen = !_isFullScreen;
                                  });
                                  if (cfg.onFullscreenChange != null) {
                                    cfg.onFullscreenChange!(_isFullScreen);
                                  }
                                },
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Apply JSON configuration if available
    if (widget.useJsonConfig && widget.jsonConfig != null) {
      // Create a new widget with the JSON configuration applied
      final jsonWidget = StreamingVideoWidget.fromJson(widget.jsonConfig!);

      // Use the JSON widget's properties for rendering, but keep the current state
      return _buildWidgetWithConfig(context, jsonWidget);
    }

    return _buildWidgetWithConfig(context, widget);
  }

  /// Builds the widget with the given configuration
  Widget _buildWidgetWithConfig(BuildContext context, StreamingVideoWidget config) {
    // Apply theme color if provided
    final effectiveBackgroundColor = config.backgroundColor ?? Colors.black;

    return GestureDetector(
      onTap: () {
        // Just show controls on tap, no need to toggle
      },
      child: Container(
        color: effectiveBackgroundColor,
        child: config.compact ? _buildCompactView(config) : _buildFullView(config),
      ),
    );
  }
}

/// Enum representing different streaming types
enum StreamingType {
  /// HTTP Live Streaming
  hls,

  /// Dynamic Adaptive Streaming over HTTP
  dash,

  /// Real-Time Messaging Protocol
  rtmp,

  /// Other streaming types
  other,
}

/// Class representing stream quality options
class StreamQuality {
  /// Quality name (e.g., "720p", "1080p", "Auto")
  final String name;

  /// URL for this quality
  final String url;

  /// Resolution width
  final int? width;

  /// Resolution height
  final int? height;

  /// Bitrate in kbps
  final int? bitrate;

  /// Creates a stream quality option
  const StreamQuality({
    required this.name,
    required this.url,
    this.width,
    this.height,
    this.bitrate,
  });
}

/// Enum representing stream health status
enum StreamHealth {
  /// Good streaming quality
  good,

  /// Fair streaming quality
  fair,

  /// Poor streaming quality
  poor,
}