import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../ui_components/inputs/app_text_field.dart';

/// An enhanced text field that supports validation and input formatters
class ValidatedTextField extends StatelessWidget {
  final String? label;
  final String? placeholder;
  final TextEditingController? controller;
  final AppTextFieldType type;
  final bool enabled;
  final TextInputAction? textInputAction;
  final Widget? prefix;
  final Widget? suffix;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final ValueChanged<String>? onChanged;
  final bool? obscureText;
  final bool isRequired;
  final bool noBorder;

  const ValidatedTextField({
    super.key,
    this.label,
    this.placeholder,
    this.controller,
    this.type = AppTextFieldType.text,
    this.enabled = true,
    this.textInputAction,
    this.prefix,
    this.suffix,
    this.validator,
    this.inputFormatters,
    this.onChanged,
    this.obscureText,
    this.isRequired = false,
    this.noBorder = false,
  });

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      validator: validator,
      initialValue: controller?.text,
      enabled: enabled,
      builder: (FormFieldState<String> state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppTextField(
              noBorder: noBorder,
              label: isRequired ? '$label *' : label,
              placeholder: placeholder,
              controller: controller,
              type: type,
              enabled: enabled,
              textInputAction: textInputAction,
              prefix: prefix,
              suffix: suffix,
              errorText: state.hasError ? state.errorText : null,
              obscureText: obscureText,
              onChanged: (value) {
                state.didChange(value);
                if (onChanged != null) {
                  onChanged!(value);
                }
              },
            ),
          ],
        );
      },
    );
  }
}
