import 'dart:math';

import 'package:flutter/material.dart';

import '../model/workflow_connection.dart';
import '../model/workflow_node.dart';

class WorkflowBuilder extends StatefulWidget {
  const WorkflowBuilder({super.key});

  @override
  WorkflowBuilderState createState() => WorkflowBuilderState();
}

class WorkflowBuilderState extends State<WorkflowBuilder> {
  // Store workflow nodes and connections
  List<WorkflowNode> nodes = [];
  List<WorkflowConnection> connections = [];

  @override
  void initState() {
    super.initState();
    // Initialize with sample workflow
    _initializeSampleWorkflow();
  }

  void _initializeSampleWorkflow() {
    // Create nodes
    final startNode = WorkflowNode(
      id: '1',
      title: 'Start Task',
      type: NodeType.start,
      position: Offset(50.0, 100.0),
      actionLabel: 'Start',
    );

    final approvalNode = WorkflowNode(
      id: '2',
      title: 'Marketing Manager Approval',
      type: NodeType.approval,
      position: Offset(290.0, 60.0),
      actionLabel: 'Manager Approval',
    );

    final emailNode = WorkflowNode(
      id: '3',
      title: 'Email Task',
      type: NodeType.email,
      position: Offset(290.0, 290.0),
      actionLabel: 'Mail',
    );

    final endNode = WorkflowNode(
      id: '4',
      title: 'EndTask',
      type: NodeType.end,
      position: Offset(530.0, 290.0),
      actionLabel: 'End',
    );

    // Add nodes to state
    nodes = [startNode, approvalNode, emailNode, endNode];

    // Create connections
    connections = [
      WorkflowConnection(sourceId: '1', targetId: '2'),
      WorkflowConnection(sourceId: '2', targetId: '3'),
      WorkflowConnection(sourceId: '3', targetId: '4'),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Workflow Builder')),
      body: Stack(
        children: [
          // Background grid
          Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/grid_bg.png'),
                repeat: ImageRepeat.repeat,
              ),
            ),
          ),

          // Connections painter
          CustomPaint(
            size: Size.infinite,
            painter: ConnectionPainter(
              nodes: nodes,
              connections: connections,
            ),
          ),

          // Nodes
          ...nodes.map((node) => _buildDraggableNode(node)),

          // Toolbar
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: _buildToolbar(),
          ),
        ],
      ),
    );
  }

  Widget _buildDraggableNode(WorkflowNode node) {
    return Positioned(
      left: node.position.dx,
      top: node.position.dy,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            final index = nodes.indexWhere((n) => n.id == node.id);
            if (index >= 0) {
              nodes[index] = nodes[index].copyWith(
                position: Offset(
                  nodes[index].position.dx + details.delta.dx,
                  nodes[index].position.dy + details.delta.dy,
                ),
              );
            }
          });
        },
        child: _buildNodeCard(node),
      ),
    );
  }

  Widget _buildNodeCard(WorkflowNode node) {
    // Build the node card UI based on type
    return Container(
      width: 164,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withAlpha(25),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: Row(
              children: [
                _buildNodeIcon(node.type),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    node.title,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),

          // Action button
          Padding(
            padding: EdgeInsets.all(12),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).dividerColor),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(child: Text(node.actionLabel)),
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(Icons.check,
                      color: Theme.of(context).colorScheme.onPrimary, size: 16),
                ),
              ],
            ),
          ),

          // Connection Label
          Padding(
            padding: EdgeInsets.only(bottom: 12),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(25),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withAlpha(50)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Connection Task',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(width: 4),
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNodeIcon(NodeType type) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final secondaryColor = theme.colorScheme.secondary;
    final errorColor = theme.colorScheme.error;
    final successColor = theme.colorScheme.primary;
    final onPrimaryColor = theme.colorScheme.onPrimary;

    Color getNodeColor(NodeType type) {
      switch (type) {
        case NodeType.start:
          return successColor;
        case NodeType.approval:
          return primaryColor;
        case NodeType.email:
          return secondaryColor;
        case NodeType.end:
          return errorColor;
      }
    }

    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: getNodeColor(type),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getNodeIconData(type),
        color: onPrimaryColor,
        size: 16,
      ),
    );
  }

  IconData _getNodeIconData(NodeType type) {
    switch (type) {
      case NodeType.start:
        return Icons.play_arrow;
      case NodeType.approval:
        return Icons.check_circle_outline;
      case NodeType.email:
        return Icons.email;
      case NodeType.end:
        return Icons.stop;
    }
  }

  Widget _buildToolbar() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withAlpha(25),
            blurRadius: 8,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildToolbarButton(
            icon: Icons.play_arrow,
            label: 'Start',
            color: Theme.of(context).colorScheme.primary,
            onPressed: () => _addNode(NodeType.start),
          ),
          _buildToolbarButton(
            icon: Icons.check_circle_outline,
            label: 'Approval',
            color: Theme.of(context).colorScheme.secondary,
            onPressed: () => _addNode(NodeType.approval),
          ),
          _buildToolbarButton(
            icon: Icons.email,
            label: 'Email',
            color: Theme.of(context).colorScheme.tertiary,
            onPressed: () => _addNode(NodeType.email),
          ),
          _buildToolbarButton(
            icon: Icons.stop,
            label: 'End',
            color: Theme.of(context).colorScheme.error,
            onPressed: () => _addNode(NodeType.end),
          ),
          _buildToolbarButton(
            icon: Icons.delete,
            label: 'Delete',
            color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
            onPressed: _deleteSelectedNode,
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    final theme = Theme.of(context);
    final textColor = theme.colorScheme.onSurface;

    return InkWell(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color),
          ),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  void _addNode(NodeType type) {
    // Implementation to add a new node
    final id = (nodes.length + 1).toString();
    final newNode = WorkflowNode(
      id: id,
      title: _getNodeTitle(type),
      type: type,
      position: Offset(100.0, 100.0), // Default position
      actionLabel: _getActionLabel(type),
    );

    setState(() {
      nodes.add(newNode);
    });
  }

  void _deleteSelectedNode() {
    // Implementation to delete selected node
  }

  String _getNodeTitle(NodeType type) {
    switch (type) {
      case NodeType.start:
        return 'Start Task';
      case NodeType.approval:
        return 'Approval Task';
      case NodeType.email:
        return 'Email Task';
      case NodeType.end:
        return 'End Task';
    }
  }

  String _getActionLabel(NodeType type) {
    switch (type) {
      case NodeType.start:
        return 'Start';
      case NodeType.approval:
        return 'Approve';
      case NodeType.email:
        return 'Mail';
      case NodeType.end:
        return 'End';
    }
  }
}

class ConnectionPainter extends CustomPainter {
  final List<WorkflowNode> nodes;
  final List<WorkflowConnection> connections;

  ConnectionPainter({required this.nodes, required this.connections});

  @override
  void paint(Canvas canvas, Size size) {
    // Use a theme-aware color if available, or fallback to blue
    final connectionColor = Colors.blue;

    final paint = Paint()
      ..color = connectionColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    for (var connection in connections) {
      final sourceNode = nodes.firstWhere(
        (node) => node.id == connection.sourceId,
        orElse: () => nodes.first,
      );
      final targetNode = nodes.firstWhere(
        (node) => node.id == connection.targetId,
        orElse: () => nodes.first,
      );

      // Calculate center points of nodes
      final sourcePoint = Offset(
        sourceNode.position.dx + 82, // Half of node width
        sourceNode.position.dy + 50, // Approximate middle point
      );
      final targetPoint = Offset(
        targetNode.position.dx, // Left side of target
        targetNode.position.dy + 50, // Approximate middle point
      );

      // Draw line
      final path = Path();
      path.moveTo(sourcePoint.dx, sourcePoint.dy);

      // If nodes are vertically aligned, draw straight line
      if ((sourcePoint.dy - targetPoint.dy).abs() < 20) {
        path.lineTo(targetPoint.dx, targetPoint.dy);
      } else {
        // Draw bezier curve for better appearance
        final controlPoint1 = Offset(
          sourcePoint.dx + 50,
          sourcePoint.dy,
        );
        final controlPoint2 = Offset(
          targetPoint.dx - 50,
          targetPoint.dy,
        );
        path.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          targetPoint.dx,
          targetPoint.dy,
        );
      }

      canvas.drawPath(path, paint);

      // Draw arrow at target
      final arrowSize = 10.0;
      final angle = atan2(
        targetPoint.dy - sourcePoint.dy,
        targetPoint.dx - sourcePoint.dx,
      );

      final arrowPath = Path();
      arrowPath.moveTo(targetPoint.dx, targetPoint.dy);
      arrowPath.lineTo(
        targetPoint.dx - arrowSize * cos(angle - pi / 6),
        targetPoint.dy - arrowSize * sin(angle - pi / 6),
      );
      arrowPath.lineTo(
        targetPoint.dx - arrowSize * cos(angle + pi / 6),
        targetPoint.dy - arrowSize * sin(angle + pi / 6),
      );
      arrowPath.close();

      canvas.drawPath(arrowPath, Paint()..color = connectionColor);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Data models
