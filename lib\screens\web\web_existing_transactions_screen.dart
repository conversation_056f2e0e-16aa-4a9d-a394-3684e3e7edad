import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/global_objective.dart';
import '../workflow_detail_screen_fixed.dart';
import '../../utils/input_value_store.dart';

class WebExistingTransactionsScreen extends StatelessWidget {
  final GlobalObjective objective;
  final List<dynamic> transactions;

  const WebExistingTransactionsScreen({
    super.key,
    required this.objective,
    required this.transactions,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('transaction.existingTransactions')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Text(
              context.tr('transaction.existingTransactionsFor',
                  args: {'name': objective.name}),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
          ),
          Expanded(
            child: transactions.isEmpty
                ? _buildEmptyState(context)
                : _buildTransactionsList(context),
          ),
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Clear the input store before navigating to workflow detail screen
                  final inputStore = InputValueStore();
                  inputStore.clear();

                  // Navigate to workflow detail screen to start a fresh transaction
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          WorkflowDetailScreen(objective: objective),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(context.tr('transaction.startNewTransaction')),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 64,
            color: Theme.of(context).colorScheme.primary.withAlpha(100),
          ),
          const SizedBox(height: 24),
          Text(
            context.tr('transaction.noExistingTransactions'),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('transaction.startNewTransactionPrompt'),
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
      // Always show 2 items per row as requested
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: MediaQuery.of(context).size.width > 1700
            ? 4
            : MediaQuery.of(context).size.width > 1300
                ? 3
                : 2,
        crossAxisSpacing: 16.0,
        mainAxisSpacing: 16.0,
        childAspectRatio: 1.3, // Adjust this value to control card height
      ),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];

        // Format dates if they exist
        String formattedCreatedAt = 'N/A';
        String formattedUpdatedAt = 'N/A';

        if (transaction['created_at'] != null) {
          try {
            final createdAt = DateTime.parse(transaction['created_at']);
            formattedCreatedAt =
                '${createdAt.day}/${createdAt.month}/${createdAt.year} ${createdAt.hour}:${createdAt.minute.toString().padLeft(2, '0')}';
          } catch (e) {
            formattedCreatedAt = transaction['created_at'];
          }
        }

        if (transaction['updated_at'] != null) {
          try {
            final updatedAt = DateTime.parse(transaction['updated_at']);
            formattedUpdatedAt =
                '${updatedAt.day}/${updatedAt.month}/${updatedAt.year} ${updatedAt.hour}:${updatedAt.minute.toString().padLeft(2, '0')}';
          } catch (e) {
            formattedUpdatedAt = transaction['updated_at'];
          }
        }

        return Card(
          elevation: 2,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row with GO name and status
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        transaction['go_name'] ?? objective.name,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    _buildStatusChip(
                        context, transaction['status'] ?? 'pending'),
                  ],
                ),
                const SizedBox(height: 16),

                // Transaction details in a compact layout
                Wrap(
                  spacing: 16,
                  runSpacing: 12,
                  children: [
                    // Workflow Instance ID
                    _buildDetailItem(
                      context,
                      context.tr('transaction.instanceId'),
                      transaction['workflow_instance_id'] ??
                          transaction['id'] ??
                          context.tr('common.notAvailable'),
                    ),

                    // Created At
                    _buildDetailItem(
                      context,
                      context.tr('transaction.created'),
                      formattedCreatedAt,
                    ),

                    // Updated At
                    _buildDetailItem(
                      context,
                      context.tr('transaction.updated'),
                      formattedUpdatedAt,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        // Clear the input store before navigating to workflow detail screen
                        final inputStore = InputValueStore();
                        inputStore.clear();

                        // Navigate to workflow detail screen with the transaction ID
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => WorkflowDetailScreen(
                              objective: objective,
                              transactionId: transaction['id'],
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                      ),
                      child: Text(context.tr('transaction.resume')),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper method to build a detail item with label and value
  Widget _buildDetailItem(BuildContext context, String label, String value) {
    return SizedBox(
      width: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Helper method to build status chip with different colors for different status
  Widget _buildStatusChip(BuildContext context, String status) {
    Color chipColor;
    String statusText = status.toUpperCase();

    switch (status.toLowerCase()) {
      case 'completed':
        chipColor = Colors.green;
        break;
      case 'pending':
        chipColor = Colors.orange;
        break;
      case 'failed':
        chipColor = Colors.red;
        break;
      default:
        chipColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: chipColor.withAlpha(25),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: chipColor, width: 1),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
}
