import '../models/user.dart';
import '../models/api_response.dart';
import '../models/auth/auth_response_models.dart';
import '../services/auth_service.dart';
import '../utils/logger.dart';
import 'base_repository.dart';

/// A repository for user data
class UserRepository extends BaseRepository<User> {
  final AuthService _authService;

  UserRepository(this._authService) : super(_authService);

  @override
  Future<List<User>> getAll() async {
    try {
      // This is a mock implementation
      return [];
    } catch (e) {
      logError('getAll', e);
      return [];
    }
  }

  @override
  Future<User?> getById(String id) async {
    try {
      // This is a mock implementation
      return null;
    } catch (e) {
      logError('getById', e);
      return null;
    }
  }

  @override
  Future<User?> create(User item) async {
    try {
      // This is a mock implementation
      return item;
    } catch (e) {
      logError('create', e);
      return null;
    }
  }

  @override
  Future<User?> update(User item) async {
    try {
      // This is a mock implementation
      return item;
    } catch (e) {
      logError('update', e);
      return null;
    }
  }

  @override
  Future<bool> delete(String id) async {
    try {
      // This is a mock implementation
      return true;
    } catch (e) {
      logError('delete', e);
      return false;
    }
  }

  @override
  User fromJson(Map<String, dynamic> json) {
    return User.fromJson(json);
  }

  /// Register a new user
  Future<ApiResponse<AuthData>> register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String username,
    required String role,
    required String organization,
    String? profilePicture,
  }) async {
    try {
      Logger.info('Registering user: $email');
      return await _authService.register(
        name: name,
        email: email,
        mobile: mobile,
        password: password,
        username: username,
        role: role,
        organization: organization,
        profilePicture: profilePicture,
      );
    } catch (e) {
      logError('register', e);
      return ApiResponse.error(
        message: 'An error occurred during registration',
      );
    }
  }

  /// Login a user
  Future<ApiResponse<AuthData>> login(String email, String password) async {
    try {
      Logger.info('Logging in user: $email');
      final result = await _authService.login(email, password);

      // Log the result for debugging
      Logger.info('Login result: $result');

      return result;
    } catch (e) {
      logError('login', e);
      return ApiResponse.error(
        message: 'An error occurred during login: ${e.toString()}',
      );
    }
  }

  /// Logout a user
  Future<ApiResponse<LogoutData>> logout() async {
    try {
      Logger.info('Logging out user');
      return await _authService.logout();
    } catch (e) {
      logError('logout', e);
      return ApiResponse.error(
        message: 'An error occurred during logout',
      );
    }
  }

  /// Get saved auth data
  Future<ApiResponse<SavedAuthData>> getSavedAuthData() async {
    try {
      Logger.info('Getting saved auth data');
      return await _authService.getSavedAuthData();
    } catch (e) {
      logError('getSavedAuthData', e);
      final defaultSavedAuthData = SavedAuthData(
        isLoggedIn: false,
        rememberMe: false,
      );
      return ApiResponse.success(data: defaultSavedAuthData);
    }
  }

  /// Save auth data
  Future<void> saveAuthData(
    User user,
    String token,
    bool rememberMe,
    String email,
    String password, {
    String? mobile,
  }) async {
    try {
      Logger.info('Saving auth data for user: ${user.email}');
      await _authService.saveAuthData(
        user,
        token,
        rememberMe,
        email,
        password,
        mobile: mobile,
      );
    } catch (e) {
      logError('saveAuthData', e);
    }
  }

  /// Get current user profile information
  Future<ApiResponse<UserProfileData>> getUserProfile() async {
    try {
      Logger.info('Getting user profile information');
      return await _authService.getUserProfile();
    } catch (e) {
      logError('getUserProfile', e);
      return ApiResponse.error(
        message: 'An error occurred while fetching user profile',
      );
    }
  }

  /// Refresh authentication token
  Future<ApiResponse<AuthData>> refreshToken(String refreshToken) async {
    try {
      Logger.info('Refreshing authentication token');
      return await _authService.refreshToken(refreshToken);
    } catch (e) {
      logError('refreshToken', e);
      return ApiResponse.error(
        message: 'An error occurred while refreshing token',
      );
    }
  }
}
