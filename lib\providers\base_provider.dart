import 'package:flutter/foundation.dart';
import '../utils/error_handler.dart';

/// A base provider class that provides common functionality for all providers
abstract class BaseProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  bool _isDisposed = false;

  /// Whether the provider is currently loading
  bool get isLoading => _isLoading;

  /// The current error message, if any
  String? get error => _error;

  /// Whether the provider has been disposed
  bool get isDisposed => _isDisposed;

  /// Set the loading state
  @protected
  void setLoading(bool value) {
    if (_isDisposed) return;

    if (_isLoading != value) {
      _isLoading = value;
      notifyListeners();
    }
  }

  /// Set an error message
  @protected
  void setError(String? message) {
    if (_isDisposed) return;

    _error = message;
    notifyListeners();
  }

  /// Clear the current error message
  void clearError() {
    if (_isDisposed) return;

    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  /// Handle an error and return a user-friendly message
  @protected
  String handleError(dynamic error, {String? context}) {
    final errorMessage = ErrorHandler.handleError(error, context: context);
    setError(errorMessage);
    return errorMessage;
  }

  /// Run an async operation with loading state and error handling
  @protected
  Future<T?> runWithLoadingAndErrorHandling<T>(
    Future<T> Function() operation, {
    String? context,
    bool showLoading = true,
  }) async {
    if (_isDisposed) return null;

    if (showLoading) {
      setLoading(true);
    }

    clearError();

    try {
      final result = await operation();
      return result;
    } catch (e) {
      handleError(e, context: context);
      return null;
    } finally {
      if (showLoading && !_isDisposed) {
        setLoading(false);
      }
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
