import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// An expandable panel component that can be collapsed or expanded
class AppExpandablePanel extends StatefulWidget {
  /// The title of the panel
  final Widget title;

  /// The content of the panel
  final Widget content;

  /// Whether the panel is initially expanded
  final bool initiallyExpanded;

  /// The background color of the panel
  final Color? backgroundColor;

  /// The background color of the header
  final Color? headerBackgroundColor;

  /// The border radius of the panel
  final BorderRadius? borderRadius;

  /// The padding inside the panel
  final EdgeInsetsGeometry contentPadding;

  /// The padding inside the header
  final EdgeInsetsGeometry headerPadding;

  /// The elevation of the panel
  final double elevation;

  /// The border of the panel
  final Border? border;

  /// The icon to show when the panel is expanded
  final IconData expandedIcon;

  /// The icon to show when the panel is collapsed
  final IconData collapsedIcon;

  /// The animation duration
  final Duration animationDuration;

  /// Callback when the panel is expanded or collapsed
  final Function(bool)? onExpansionChanged;

  const AppExpandablePanel({
    super.key,
    required this.title,
    required this.content,
    this.initiallyExpanded = false,
    this.backgroundColor,
    this.headerBackgroundColor,
    this.borderRadius,
    this.contentPadding = const EdgeInsets.all(AppTheme.spacingM),
    this.headerPadding = const EdgeInsets.all(AppTheme.spacingM),
    this.elevation = 1,
    this.border,
    this.expandedIcon = Icons.keyboard_arrow_up,
    this.collapsedIcon = Icons.keyboard_arrow_down,
    this.animationDuration = AppTheme.durationMedium,
    this.onExpansionChanged,
  });

  @override
  State<AppExpandablePanel> createState() => _AppExpandablePanelState();
}

class _AppExpandablePanelState extends State<AppExpandablePanel>
    with SingleTickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _controller;
  late Animation<double> _heightFactor;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeInOut));

    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }

      if (widget.onExpansionChanged != null) {
        widget.onExpansionChanged!(_isExpanded);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final BorderRadius borderRadius =
        widget.borderRadius ?? BorderRadius.circular(AppTheme.borderRadiusM);

    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? AppTheme.backgroundColor,
        borderRadius: borderRadius,
        border: widget.border,
        boxShadow: widget.elevation > 0
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation * 2,
                  offset: Offset(0, widget.elevation),
                ),
              ]
            : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: BorderRadius.only(
              topLeft: borderRadius.topLeft,
              topRight: borderRadius.topRight,
              bottomLeft: _isExpanded ? Radius.zero : borderRadius.bottomLeft,
              bottomRight: _isExpanded ? Radius.zero : borderRadius.bottomRight,
            ),
            child: Container(
              padding: widget.headerPadding,
              decoration: BoxDecoration(
                color: widget.headerBackgroundColor ??
                    widget.backgroundColor ??
                    AppTheme.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: borderRadius.topLeft,
                  topRight: borderRadius.topRight,
                  bottomLeft:
                      _isExpanded ? Radius.zero : borderRadius.bottomLeft,
                  bottomRight:
                      _isExpanded ? Radius.zero : borderRadius.bottomRight,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(child: widget.title),
                  Icon(
                    _isExpanded ? widget.expandedIcon : widget.collapsedIcon,
                    color: AppTheme.textSecondaryColor,
                  ),
                ],
              ),
            ),
          ),

          // Content
          ClipRect(
            child: AnimatedBuilder(
              animation: _controller.view,
              builder: (context, child) {
                return Align(
                  alignment: Alignment.topCenter,
                  heightFactor: _heightFactor.value,
                  child: child,
                );
              },
              child: _isExpanded
                  ? Container(
                      width: double.infinity,
                      padding: widget.contentPadding,
                      child: widget.content,
                    )
                  : Container(),
            ),
          ),
        ],
      ),
    );
  }
}

/// A group of expandable panels where only one can be expanded at a time
class AppExpandablePanelGroup extends StatefulWidget {
  /// The list of panel data
  final List<AppExpandablePanelData> panels;

  /// The index of the initially expanded panel
  final int? initiallyExpandedIndex;

  /// The background color of the panels
  final Color? backgroundColor;

  /// The background color of the headers
  final Color? headerBackgroundColor;

  /// The border radius of the panels
  final BorderRadius? borderRadius;

  /// The padding inside the panels
  final EdgeInsetsGeometry contentPadding;

  /// The padding inside the headers
  final EdgeInsetsGeometry headerPadding;

  /// The elevation of the panels
  final double elevation;

  /// The border of the panels
  final Border? border;

  /// The spacing between panels
  final double spacing;

  /// The icon to show when a panel is expanded
  final IconData expandedIcon;

  /// The icon to show when a panel is collapsed
  final IconData collapsedIcon;

  /// The animation duration
  final Duration animationDuration;

  /// Callback when a panel is expanded or collapsed
  final Function(int)? onPanelChanged;

  const AppExpandablePanelGroup({
    super.key,
    required this.panels,
    this.initiallyExpandedIndex,
    this.backgroundColor,
    this.headerBackgroundColor,
    this.borderRadius,
    this.contentPadding = const EdgeInsets.all(AppTheme.spacingM),
    this.headerPadding = const EdgeInsets.all(AppTheme.spacingM),
    this.elevation = 1,
    this.border,
    this.spacing = AppTheme.spacingM,
    this.expandedIcon = Icons.keyboard_arrow_up,
    this.collapsedIcon = Icons.keyboard_arrow_down,
    this.animationDuration = AppTheme.durationMedium,
    this.onPanelChanged,
  });

  @override
  State<AppExpandablePanelGroup> createState() =>
      _AppExpandablePanelGroupState();
}

class _AppExpandablePanelGroupState extends State<AppExpandablePanelGroup> {
  int? _expandedIndex;

  @override
  void initState() {
    super.initState();
    _expandedIndex = widget.initiallyExpandedIndex;
  }

  void _handleExpansionChanged(int index, bool isExpanded) {
    if (isExpanded) {
      setState(() {
        _expandedIndex = index;
      });

      if (widget.onPanelChanged != null) {
        widget.onPanelChanged!(index);
      }
    } else if (_expandedIndex == index) {
      setState(() {
        _expandedIndex = null;
      });

      if (widget.onPanelChanged != null) {
        widget.onPanelChanged!(-1);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        widget.panels.length * 2 - 1,
        (index) {
          if (index.isOdd) {
            // Spacer
            return SizedBox(height: widget.spacing);
          }

          final panelIndex = index ~/ 2;
          final panel = widget.panels[panelIndex];

          return AppExpandablePanel(
            title: panel.title,
            content: panel.content,
            initiallyExpanded: _expandedIndex == panelIndex,
            backgroundColor: widget.backgroundColor,
            headerBackgroundColor: widget.headerBackgroundColor,
            borderRadius: widget.borderRadius,
            contentPadding: widget.contentPadding,
            headerPadding: widget.headerPadding,
            elevation: widget.elevation,
            border: widget.border,
            expandedIcon: widget.expandedIcon,
            collapsedIcon: widget.collapsedIcon,
            animationDuration: widget.animationDuration,
            onExpansionChanged: (isExpanded) {
              _handleExpansionChanged(panelIndex, isExpanded);
            },
          );
        },
      ),
    );
  }
}

/// Data for an expandable panel
class AppExpandablePanelData {
  /// The title of the panel
  final Widget title;

  /// The content of the panel
  final Widget content;

  const AppExpandablePanelData({
    required this.title,
    required this.content,
  });
}
