import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../models/user.dart';
import '../../providers/auth_provider.dart';
import '../../ui_components/theme/app_theme.dart';
import '../../theme/spacing.dart';
import '../../widgets/profile/profile_detail_card.dart';
import '../../widgets/common/nsl_knowledge_loader.dart';

/// A screen that displays the user's profile information.
///
/// This screen can be used standalone or embedded in other screens like the settings screen.
/// When embedded, it displays 2 ProfileDetailCards in a row for better space utilization.
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch user profile when screen loads
    _fetchUserProfile();
  }

  /// Fetches the user profile from the AuthProvider
  Future<void> _fetchUserProfile() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.getUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're embedded in the settings screen
    final bool isEmbedded = kIsWeb;

    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        return NSLKnowledgeLoaderWrapper(
          isLoading: authProvider.isLoadingProfile,
          text: context.tr('auth.loadingProfile'),
          circularProgressColor: Theme.of(context).colorScheme.primary,
          child: SafeArea(
            child: isEmbedded
                ? _buildProfileContent(context, isEmbedded: true)
                : Scaffold(
                    appBar: AppBar(
                      title: Text(context.tr('profile.title')),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    body: _buildProfileContent(context, isEmbedded: false),
                  ),
          ),
        );
      },
    );
  }

  /// Builds the main profile content without the Scaffold
  ///
  /// This is used both in standalone mode and when embedded in other screens.
  /// It ensures proper width constraints to avoid layout issues.
  /// When isEmbedded is true, it displays 2 ProfileDetailCards in a row.
  Widget _buildProfileContent(BuildContext context,
      {required bool isEmbedded}) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        final User? user = authProvider.user;

        // Show error message
        if (authProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  color: Theme.of(context).colorScheme.error,
                  size: 48,
                ),
                SizedBox(height: AppSpacing.md),
                Text(
                  context.tr('common.error'),
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                SizedBox(height: AppSpacing.sm),
                Text(authProvider.error!),
                SizedBox(height: AppSpacing.lg),
                ElevatedButton(
                  onPressed: _fetchUserProfile,
                  child: Text(context.tr('common.retry')),
                ),
              ],
            ),
          );
        }

        if (user == null) {
          return Center(
            child: Text(context.tr('profile.userInfoNotAvailable')),
          );
        }

        // Main profile content with proper width constraints
        return LayoutBuilder(
          builder: (context, constraints) {
            return Container(
              width: double.infinity,
              constraints: BoxConstraints(
                maxWidth: constraints.maxWidth,
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile header with avatar
                    _buildProfileHeader(context, user),

                    const SizedBox(height: AppSpacing.xl),

                    // Profile details section
                    Text(
                      context.tr('profile.profileInformation'),
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: AppSpacing.md),

                    // Profile details cards - either in rows of 2 or single column
                    if (isEmbedded)
                      _buildProfileDetailsInRows(context, user, authProvider)
                    else
                      _buildProfileDetailsInColumn(context, user, authProvider),

                    const SizedBox(height: AppSpacing.xl),

                    // Edit profile button
                    _buildEditProfileButton(context),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Builds profile details in a single column (for standalone mode)
  Widget _buildProfileDetailsInColumn(
      BuildContext context, User user, AuthProvider authProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Profile Information section
        ProfileDetailCard(
          icon: Icons.person_outline,
          title: context.tr('profile.fullName'),
          value: user.name,
        ),

        if (user.username != null && user.username!.isNotEmpty)
          ProfileDetailCard(
            icon: Icons.account_circle_outlined,
            title: context.tr('profile.username'),
            value: user.username!,
          ),

        ProfileDetailCard(
          icon: Icons.email_outlined,
          title: context.tr('profile.emailAddress'),
          value: user.email,
        ),

        ProfileDetailCard(
          icon: Icons.phone_outlined,
          title: context.tr('profile.mobileNumber'),
          value: user.mobileNumber ??
              authProvider.savedMobile ??
              context.tr('profile.notProvided'),
        ),

        if (user.role != null && user.role!.isNotEmpty)
          ProfileDetailCard(
            icon: Icons.assignment_ind_outlined,
            title: context.tr('profile.role'),
            value: user.role!,
          ),

        if (user.organization != null && user.organization!.isNotEmpty)
          ProfileDetailCard(
            icon: Icons.business_outlined,
            title: context.tr('profile.organization'),
            value: user.organization!,
          ),

        // Account Information section
        const SizedBox(height: AppSpacing.xl),
        Text(
          context.tr('profile.accountInformation'),
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: AppSpacing.md),

        ProfileDetailCard(
          icon: Icons.fingerprint,
          title: context.tr('profile.userId'),
          value: _formatUserId(user.id),
        ),

        ProfileDetailCard(
          icon: Icons.verified_user_outlined,
          title: context.tr('profile.accountStatus'),
          value: user.status ?? 'Active',
        ),

        if (user.tenantId != null && user.tenantId!.isNotEmpty)
          ProfileDetailCard(
            icon: Icons.domain_outlined,
            title: context.tr('profile.tenantId'),
            value: user.tenantId!,
          ),

        if (user.roles != null && user.roles!.isNotEmpty)
          ProfileDetailCard(
            icon: Icons.assignment_ind_outlined,
            title: context.tr('profile.roles'),
            value: user.roles!.join(', '),
          ),

        if (user.orgUnits != null && user.orgUnits!.isNotEmpty)
          ProfileDetailCard(
            icon: Icons.business_outlined,
            title: context.tr('profile.organizationUnits'),
            value: user.orgUnits!.join(', '),
          ),

        ProfileDetailCard(
          icon: Icons.security_outlined,
          title: context.tr('profile.authProvider'),
          value: _getAuthProvider(user.id),
        ),
      ],
    );
  }

  /// Builds profile details in rows of 2 cards (for embedded mode)
  Widget _buildProfileDetailsInRows(
      BuildContext context, User user, AuthProvider authProvider) {
    // Create a list of all profile detail cards
    final List<Widget> allCards = [
      // Profile Information section
      ProfileDetailCard(
        icon: Icons.person_outline,
        title: 'Full Name',
        value: user.name,
      ),

      if (user.username != null && user.username!.isNotEmpty)
        ProfileDetailCard(
          icon: Icons.account_circle_outlined,
          title: 'Username',
          value: user.username!,
        ),

      ProfileDetailCard(
        icon: Icons.email_outlined,
        title: 'Email Address',
        value: user.email,
      ),

      ProfileDetailCard(
        icon: Icons.phone_outlined,
        title: 'Mobile Number',
        value: user.mobileNumber ?? authProvider.savedMobile ?? 'Not provided',
      ),

      if (user.role != null && user.role!.isNotEmpty)
        ProfileDetailCard(
          icon: Icons.assignment_ind_outlined,
          title: 'Role',
          value: user.role!,
        ),

      if (user.organization != null && user.organization!.isNotEmpty)
        ProfileDetailCard(
          icon: Icons.business_outlined,
          title: 'Organization',
          value: user.organization!,
        ),

      // Account Information cards
      ProfileDetailCard(
        icon: Icons.fingerprint,
        title: 'User ID',
        value: _formatUserId(user.id),
      ),

      ProfileDetailCard(
        icon: Icons.verified_user_outlined,
        title: 'Account Status',
        value: user.status ?? 'Active',
      ),

      if (user.tenantId != null && user.tenantId!.isNotEmpty)
        ProfileDetailCard(
          icon: Icons.domain_outlined,
          title: 'Tenant ID',
          value: user.tenantId!,
        ),

      if (user.roles != null && user.roles!.isNotEmpty)
        ProfileDetailCard(
          icon: Icons.assignment_ind_outlined,
          title: 'Roles',
          value: user.roles!.join(', '),
        ),

      if (user.orgUnits != null && user.orgUnits!.isNotEmpty)
        ProfileDetailCard(
          icon: Icons.business_outlined,
          title: 'Organization Units',
          value: user.orgUnits!.join(', '),
        ),

      ProfileDetailCard(
        icon: Icons.security_outlined,
        title: 'Authentication Provider',
        value: _getAuthProvider(user.id),
      ),
    ];

    // Group cards into rows of 2
    final List<Widget> rows = [];

    // First add the Profile Information section header
    // rows.add(
    //   Column(
    //     crossAxisAlignment: CrossAxisAlignment.start,
    //     children: [
    //       Text(
    //         'Profile Information',
    //         style: Theme.of(context).textTheme.titleLarge,
    //       ),
    //       const SizedBox(height: AppSpacing.md),
    //     ],
    //   ),
    // );

    // Add profile information cards (first 6 cards or fewer)
    final int profileInfoCount = min(6, allCards.length);
    for (int i = 0; i < profileInfoCount; i += 2) {
      if (i + 1 < profileInfoCount) {
        // Add a row with 2 cards
        rows.add(
          Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.md),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: allCards[i]),
                const SizedBox(width: AppSpacing.md),
                Expanded(child: allCards[i + 1]),
              ],
            ),
          ),
        );
      } else {
        // Add a row with 1 card and an empty space
        rows.add(
          Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.md),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: allCards[i]),
                const SizedBox(width: AppSpacing.md),
                const Expanded(child: SizedBox()),
              ],
            ),
          ),
        );
      }
    }

    // Add the Account Information section header
    rows.add(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.md),
          Text(
            'Account Information',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppSpacing.md),
        ],
      ),
    );

    // Add account information cards (remaining cards)
    for (int i = profileInfoCount; i < allCards.length; i += 2) {
      if (i + 1 < allCards.length) {
        // Add a row with 2 cards
        rows.add(
          Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.md),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: allCards[i]),
                const SizedBox(width: AppSpacing.md),
                Expanded(child: allCards[i + 1]),
              ],
            ),
          ),
        );
      } else {
        // Add a row with 1 card and an empty space
        rows.add(
          Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.md),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: allCards[i]),
                const SizedBox(width: AppSpacing.md),
                const Expanded(child: SizedBox()),
              ],
            ),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rows,
    );
  }

  /// Builds the profile header with avatar
  Widget _buildProfileHeader(BuildContext context, User user) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          children: [
            // Avatar
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withAlpha(50),
                  width: 2,
                ),
              ),
              child: ClipOval(
                child: user.profilePicture != null &&
                        user.profilePicture!.isNotEmpty
                    ? Image.network(
                        user.profilePicture!,
                        fit: BoxFit.cover,
                        width: 120,
                        height: 120,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildInitialsAvatar(context, user);
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                      )
                    : _buildInitialsAvatar(context, user),
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // User name
            Text(
              user.name,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),

            // Email
            Text(
              user.email,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color:
                        Theme.of(context).colorScheme.onSurface.withAlpha(178),
                  ),
              textAlign: TextAlign.center,
            ),

            // Username if available
            if (user.username != null && user.username!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  '@${user.username}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Builds an initials avatar as fallback when no profile picture is available
  Widget _buildInitialsAvatar(BuildContext context, User user) {
    return Container(
      color: Theme.of(context).colorScheme.primary.withAlpha(50),
      child: Center(
        child: Text(
          _getInitials(user.name),
          style: TextStyle(
            fontSize: 40,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ),
    );
  }

  /// Builds the edit profile button
  Widget _buildEditProfileButton(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _fetchUserProfile,
                icon: Icon(Icons.refresh,
                    color: Theme.of(context).colorScheme.onPrimary),
                label: Text(context.tr('profile.refreshProfile'),
                    style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary)),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.lg,
                    vertical: AppSpacing.md,
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // TODO: Implement edit profile functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content:
                          Text(context.tr('profile.editProfileComingSoon')),
                    ),
                  );
                },
                icon: Icon(Icons.edit,
                    color: Theme.of(context).colorScheme.onPrimary),
                label: Text(context.tr('profile.editProfile'),
                    style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary)),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.lg,
                    vertical: AppSpacing.md,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Helper method to get user initials for avatar
  String _getInitials(String name) {
    if (name.isEmpty) return '';

    final nameParts = name.trim().split(' ');
    if (nameParts.length == 1) {
      return nameParts[0][0].toUpperCase();
    }

    return '${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}'
        .toUpperCase();
  }

  /// Helper method to format user ID for display
  String _formatUserId(String userId) {
    if (userId.isEmpty) return context.tr('profile.notAvailable');

    // For Auth0 IDs (format: 'auth0|**********')
    if (userId.contains('|')) {
      final parts = userId.split('|');
      if (parts.length > 1) {
        return '${parts[1].substring(0, min(parts[1].length, 12))}...'; // Show first 12 chars
      }
    }

    // For other ID formats, show first 12 chars
    return '${userId.substring(0, min(userId.length, 12))}...';
  }

  /// Helper method to determine authentication provider
  String _getAuthProvider(String userId) {
    if (userId.isEmpty) return context.tr('profile.notAvailable');

    if (userId.startsWith('auth0|')) {
      return 'Auth0';
    } else if (userId.startsWith('google-oauth2|')) {
      return 'Google';
    } else if (userId.startsWith('facebook|')) {
      return 'Facebook';
    } else if (userId.contains('@')) {
      return 'Email';
    }

    return context.tr('auth.localAccount');
  }
}
