class TreeNode {
  final String id;
  final String type;
  final String label;
  final String? parent;
  final List<String> children;
  final Map<String, dynamic>? metadata;

  TreeNode({
    required this.id,
    required this.type,
    required this.label,
    this.parent,
    required this.children,
    this.metadata,
  });

  factory TreeNode.fromJson(Map<String, dynamic> json) {
    return TreeNode(
      id: json['id'],
      type: json['type'],
      label: json['label'],
      parent: json['parent'],
      children: List<String>.from(json['children'] ?? []),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'label': label,
      'parent': parent,
      'children': children,
      'metadata': metadata,
    };
  }
}

class TreeStructure {
  final List<TreeNode> nodes;
  final Map<String, dynamic>? metadata;

  TreeStructure({
    required this.nodes,
    this.metadata,
  });

  factory TreeStructure.fromJson(Map<String, dynamic> json) {
    return TreeStructure(
      nodes: (json['nodes'] as List)
          .map((node) => TreeNode.fromJson(node))
          .toList(),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nodes': nodes.map((node) => node.toJson()).toList(),
      'metadata': metadata,
    };
  }

  // Helper method to get a node by its ID
  TreeNode? getNodeById(String id) {
    try {
      return nodes.firstWhere((node) => node.id == id);
    } catch (e) {
      return null;
    }
  }

  // Helper method to get children of a node
  List<TreeNode> getChildrenOf(String nodeId) {
    final node = getNodeById(nodeId);
    if (node == null) return [];

    return node.children
        .map((childId) => getNodeById(childId))
        .where((child) => child != null)
        .cast<TreeNode>()
        .toList();
  }

  // Helper method to get the root node
  TreeNode? getRootNode() {
    try {
      return nodes.firstWhere((node) => node.type == 'root');
    } catch (e) {
      return null;
    }
  }
}
