import 'dart:convert';
import 'package:flutter/material.dart';

/// A utility class for interpreting and executing callbacks defined in JSON
class CallbackInterpreter {
  /// Executes a callback defined in JSON
  ///
  /// The callback definition can be:
  /// - A simple action string (e.g., "log", "alert", "navigate")
  /// - A JSON object with action and parameters
  ///
  /// Example:
  /// ```json
  /// {
  ///   "action": "log",
  ///   "message": "Option selected: {value}"
  /// }
  /// ```
  ///
  /// Supported actions:
  /// - "log": Logs a message to the console
  /// - "alert": Shows a snackbar with a message
  /// - "navigate": Navigates to a route (requires context)
  /// - "update": Updates a value in a provided state map
  /// - "toggle": Toggles a boolean value in a provided state map
  /// - "increment": Increments a numeric value in a provided state map
  /// - "decrement": Decrements a numeric value in a provided state map
  /// - "custom": Executes a custom function provided in the customHandlers map
  static void executeCallback(
    dynamic callbackDefinition,
    BuildContext? context, {
    dynamic value,
    Map<String, dynamic>? state,
    Map<String, Function>? customHandlers,
  }) {
    if (callbackDefinition == null) return;

    try {
      // Parse the callback definition
      Map<String, dynamic> callbackMap;
      if (callbackDefinition is String) {
        // Try to parse as JSON
        try {
          callbackMap = jsonDecode(callbackDefinition) as Map<String, dynamic>;
        } catch (e) {
          // If not valid JSON, treat as a simple action
          callbackMap = {'action': callbackDefinition};
        }
      } else if (callbackDefinition is Map) {
        callbackMap = Map<String, dynamic>.from(callbackDefinition);
      } else {
        debugPrint('Invalid callback definition: $callbackDefinition');
        return;
      }

      // Get the action
      final action = callbackMap['action']?.toString().toLowerCase();
      if (action == null) {
        debugPrint('No action specified in callback: $callbackMap');
        return;
      }

      // Process template strings in parameters
      Map<String, dynamic> processedParams = {};
      callbackMap.forEach((key, val) {
        if (val is String) {
          // Replace {value} with the actual value
          processedParams[key] = val.replaceAll('{value}', value?.toString() ?? 'null');
        } else {
          processedParams[key] = val;
        }
      });

      // Execute the action
      switch (action) {
        case 'log':
          _handleLogAction(processedParams);
          break;
        case 'alert':
          if (context != null) {
            _handleAlertAction(context, processedParams);
          } else {
            debugPrint('Context is required for alert action');
          }
          break;
        case 'navigate':
          if (context != null) {
            _handleNavigateAction(context, processedParams);
          } else {
            debugPrint('Context is required for navigate action');
          }
          break;
        case 'update':
          if (state != null) {
            _handleUpdateAction(state, processedParams, value);
          } else {
            debugPrint('State map is required for update action');
          }
          break;
        case 'toggle':
          if (state != null) {
            _handleToggleAction(state, processedParams);
          } else {
            debugPrint('State map is required for toggle action');
          }
          break;
        case 'increment':
          if (state != null) {
            _handleIncrementAction(state, processedParams);
          } else {
            debugPrint('State map is required for increment action');
          }
          break;
        case 'decrement':
          if (state != null) {
            _handleDecrementAction(state, processedParams);
          } else {
            debugPrint('State map is required for decrement action');
          }
          break;
        case 'custom':
          if (customHandlers != null) {
            _handleCustomAction(customHandlers, processedParams, value);
          } else {
            debugPrint('Custom handlers are required for custom action');
          }
          break;
        default:
          debugPrint('Unknown action: $action');
      }
    } catch (e) {
      debugPrint('Error executing callback: $e');
    }
  }

  /// Handles the "log" action
  static void _handleLogAction(Map<String, dynamic> params) {
    final message = params['message'] ?? 'Callback executed';
    final level = params['level']?.toString().toLowerCase() ?? 'info';

    switch (level) {
      case 'error':
        debugPrint('ERROR: $message');
        break;
      case 'warning':
        debugPrint('WARNING: $message');
        break;
      case 'info':
      default:
        debugPrint('INFO: $message');
        break;
    }
  }

  /// Handles the "alert" action
  static void _handleAlertAction(BuildContext context, Map<String, dynamic> params) {
    final message = params['message'] ?? 'Action completed';
    final duration = params['duration'] != null
        ? Duration(milliseconds: (params['duration'] as num).toInt())
        : const Duration(seconds: 2);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration,
      ),
    );
  }

  /// Handles the "navigate" action
  static void _handleNavigateAction(BuildContext context, Map<String, dynamic> params) {
    final route = params['route']?.toString();
    if (route == null) {
      debugPrint('No route specified for navigate action');
      return;
    }

    Navigator.of(context).pushNamed(route);
  }

  /// Handles the "update" action
  static void _handleUpdateAction(
    Map<String, dynamic> state,
    Map<String, dynamic> params,
    dynamic value,
  ) {
    final key = params['key']?.toString();
    if (key == null) {
      debugPrint('No key specified for update action');
      return;
    }

    // Use the provided value or the value from params
    final newValue = value ?? params['value'];
    state[key] = newValue;
  }

  /// Handles the "toggle" action
  static void _handleToggleAction(
    Map<String, dynamic> state,
    Map<String, dynamic> params,
  ) {
    final key = params['key']?.toString();
    if (key == null) {
      debugPrint('No key specified for toggle action');
      return;
    }

    if (state.containsKey(key) && state[key] is bool) {
      state[key] = !(state[key] as bool);
    } else {
      // If the key doesn't exist or isn't a boolean, set it to true
      state[key] = true;
    }
  }

  /// Handles the "increment" action
  static void _handleIncrementAction(
    Map<String, dynamic> state,
    Map<String, dynamic> params,
  ) {
    final key = params['key']?.toString();
    if (key == null) {
      debugPrint('No key specified for increment action');
      return;
    }

    final step = params['step'] != null ? (params['step'] as num).toDouble() : 1.0;

    if (state.containsKey(key)) {
      if (state[key] is num) {
        state[key] = (state[key] as num) + step;
      } else {
        debugPrint('Value for key $key is not a number');
      }
    } else {
      // If the key doesn't exist, initialize it with the step value
      state[key] = step;
    }
  }

  /// Handles the "decrement" action
  static void _handleDecrementAction(
    Map<String, dynamic> state,
    Map<String, dynamic> params,
  ) {
    final key = params['key']?.toString();
    if (key == null) {
      debugPrint('No key specified for decrement action');
      return;
    }

    final step = params['step'] != null ? (params['step'] as num).toDouble() : 1.0;

    if (state.containsKey(key)) {
      if (state[key] is num) {
        state[key] = (state[key] as num) - step;
      } else {
        debugPrint('Value for key $key is not a number');
      }
    } else {
      // If the key doesn't exist, initialize it with the negative step value
      state[key] = -step;
    }
  }

  /// Handles the "custom" action
  static void _handleCustomAction(
    Map<String, Function> customHandlers,
    Map<String, dynamic> params,
    dynamic value,
  ) {
    final handlerName = params['handler']?.toString();
    if (handlerName == null) {
      debugPrint('No handler specified for custom action');
      return;
    }

    if (customHandlers.containsKey(handlerName)) {
      final handler = customHandlers[handlerName];
      if (handler != null) {
        // Call the handler with the value and params
        handler(value, params);
      }
    } else {
      debugPrint('Custom handler not found: $handlerName');
    }
  }
}
