import 'package:flutter/material.dart';
import 'package:nsl/models/global_objective.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/workflow_provider.dart';
import 'package:nsl/screens/web_transaction/web_solution_widgets.dart';
import 'package:nsl/screens/web_transaction/workflow_transaction_screen.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/flex_mapper.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class WebTransactionWidgetsDemo extends StatefulWidget {
  const WebTransactionWidgetsDemo({super.key});

  @override
  State<WebTransactionWidgetsDemo> createState() =>
      _WebTransactionWidgetsDemoState();
}

class _WebTransactionWidgetsDemoState extends State<WebTransactionWidgetsDemo> {
  // Sample JSON data for the tree structure
  final List<Map<String, dynamic>> treeData = [
    {
      "name": "Product Catalog",
      "isExpanded": false,
      "isSelected": false,
      "children": [],
      "image": "assets/images/my_business/box_add.svg",
      "dropdownImage": "assets/images/my_business/dropdown_collection.svg"
    },
    {
      "name": "Cart",
      "isExpanded": false,
      "isSelected": false,
      "children": [],
      "image": "assets/images/my_business/box.svg",
      "dropdownImage": "assets/images/my_business/dropdown_collection.svg",
      "collectionControls": {
        "toggle": "assets/images/my_business/toggle_collection.svg",
        "expand": "assets/images/my_business/expand_collection.svg"
      }
    },
    {
      "name": "CRM",
      "isExpanded": false,
      "isSelected": false,
      "children": [],
      "image": "assets/images/my_business/box_add.svg",
      "dropdownImage": "assets/images/my_business/dropdown_collection.svg"
    },
    {
      "name": "Marketing Automation",
      "isExpanded": false,
      "isSelected": false,
      "children": [],
      "image": "assets/images/my_business/box_add.svg",
      "dropdownImage": "assets/images/my_business/dropdown_collection.svg"
    },
    {
      "name": "Order Tracking",
      "isExpanded": false,
      "isSelected": false,
      "children": [],
      "image": "assets/images/my_business/box_add.svg",
      "dropdownImage": "assets/images/my_business/dropdown_collection.svg"
    }
  ];

  // Widget to display in the content area
  Widget? _contentWidget;

  // Collection data for the back navigation
  final Map<String, dynamic> _collectionData = {
    "name": "Collection Name",
    "backImage": "assets/images/my_business/back_arrow.svg"
  };

  // Flag to show collection controls (toggle and expand)
  bool _showCollectionControls = false;

  // Flag to track expanded/fullscreen mode
  bool _isExpanded = false;

  // Flag to track if chat is enabled
  bool _isChatEnabled = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tree structure fixed to the left side - only visible when not expanded
            if (!_isExpanded) ...[
              Expanded(
                flex: 1,
                child: Container(
                  // width: 200,
                  color: Color(0xffF7F9FB),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Back navigation element (outside the box)
                      // _buildBackNavigation(),
                      SizedBox(
                          height:
                              40), // Add some space between back navigation and tree box
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border(
                            top: BorderSide(
                                color: Color(0xffD0D0D0), width: 1.0),
                            bottom: BorderSide(
                                color: Color(0xffD0D0D0), width: 1.0),
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Build tree from JSON data
                            ...treeData.asMap().entries.map((entry) {
                              final index = entry.key;
                              final item = entry.value;
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  _buildTreeItem(item, 0),
                                  // Add divider after each item except the last one
                                  if (index < treeData.length - 1)
                                    Divider(
                                        height: 1,
                                        // thickness: 2,
                                        color: Color(0xffD0D0D0)),
                                ],
                              );
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Add a small gap between tree items and vertical divider
              // SizedBox(width: 1),

              // Stack to position the vertical divider at the right level
              Stack(
                children: [
                  // Invisible spacer to maintain layout
                  Container(
                    width: 1,
                  ),
                  // Vertical divider that starts from the tree items level
                  Container(
                    width: 1,
                    color: Color(0xffD0D0D0),
                  ),
                ],
              ),
            ],
            // Main content area
            Expanded(
              flex: 6,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Fixed header row containing collection controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBackNavigation(),

                      // Collection controls (toggle and expand buttons) - positioned at the same level as back navigation
                      if (_showCollectionControls)
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Expand Collection button - Fullscreen
                            Container(
                              width: 32,
                              height: 32,
                              padding: EdgeInsets.only(top: AppSpacing.sm),
                              decoration: BoxDecoration(
                                color:
                                    Color(0xFFF7F9FB), // Light gray background
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  onTap: () {
                                    // Toggle expanded/fullscreen mode
                                    setState(() {
                                      _isExpanded = !_isExpanded;

                                      // Change the number of widgets per row based on expanded state
                                      if (_isExpanded) {
                                        // In expanded mode, show 5 widgets per row
                                        FlexMapper.setTotalRowFlex(5);
                                      } else {
                                        // In normal mode, show 4 widgets per row
                                        FlexMapper.setTotalRowFlex(4);
                                      }
                                    });
                                  },
                                  child: Center(
                                    child: CustomImage.asset(
                                      // Always use the expand image from JSON
                                      treeData.firstWhere(
                                              (item) => item['name'] == 'Cart')[
                                          'collectionControls']['expand'],
                                      width: 18,
                                      height: 18,
                                    ).toWidget(),
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(width: AppSpacing.xs),
                            // Smaller gap between buttons
                            Container(
                              padding: EdgeInsets.only(top: AppSpacing.sm),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    height: 20,
                                    width: 25,
                                    child: Transform.scale(
                                      scale: 0.6,
                                      child: Switch.adaptive(
                                        value: _isChatEnabled,
                                        activeColor: Colors.white,
                                        activeTrackColor: Color(0xff0058FF),
                                        inactiveThumbColor: Colors.white,
                                        inactiveTrackColor: Colors.grey[300],
                                        trackOutlineColor:
                                            WidgetStateProperty.all(
                                                Colors.transparent),
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                        onChanged: (value) {
                                          setState(() {
                                            _isChatEnabled = value;
                                          });

                                          // 👇 Navigate based on value
                                          if (value) {
                                            Provider.of<WebHomeProvider>(
                                                        context,
                                                        listen: false)
                                                    .currentScreenIndex =
                                                ScreenConstants
                                                    .webCollectionModuleDemo;
                                          } else {
                                            Provider.of<WebHomeProvider>(
                                                        context,
                                                        listen: false)
                                                    .currentScreenIndex =
                                                ScreenConstants
                                                    .webTransactionWidgetsDemo;
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                  Center(
                                    child: Text(
                                      _isChatEnabled ? "Normal" : "NSL",
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: AppSpacing.sm),
                          ],
                        ),
                    ],
                  ),

                  // Content widget
                  Expanded(
                    child: Container(
                      color: Color(0xffF7F9FB),
                      child: _contentWidget ??
                          Center(
                            child:
                                Text('Select an item from the tree navigation'),
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build back navigation element with arrow and text
  Widget _buildBackNavigation() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // Handle back navigation
          // You can add navigation logic here
          Provider.of<WebHomeProvider>(context, listen: false)
              .currentScreenIndex = ScreenConstants.myBusinessCollections;
        },
        child: Center(
          child: Container(
            // width: double.infinity,
            padding: EdgeInsets.only(
              left: AppSpacing.lg,
              // right: AppSpacing.sm,
              top: AppSpacing.sm,
              bottom: AppSpacing.xxs,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Back arrow icon from JSON
                CustomImage.asset(
                  _collectionData['backImage'],
                  width: 12,
                  height: 12,
                  color: Colors.black,
                ).toWidget(),
                SizedBox(width: AppSpacing.sm),

                // Collection name text from JSON
                Text(
                  _collectionData['name'],
                  style: TextStyle(
                    // fontFamily: 'TiemposText',
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Recursive function to build tree items
  Widget _buildTreeItem(Map<String, dynamic> item, int level) {
    return _TreeItem(
      item: item,
      level: level,
      onToggle: () {
        setState(() {
          item['isExpanded'] = !item['isExpanded'];
        });
      },
      onSelect: (selectedItem) {
        setState(() {
          // Reset all items' selection state
          for (var item in treeData) {
            item['isSelected'] = false;
          }

          // Set the selected item
          selectedItem['isSelected'] = true;

          // Reset collection controls flag
          _showCollectionControls = false;

          // Check if the selected item is Cart
          if (selectedItem['name'] == 'Cart') {
            // Show collection controls for Cart
            _showCollectionControls = true;

            // Create a GlobalObjective for Cart
            final objective = GlobalObjective(
              objectiveId: 'cart_workflow',
              name: 'Cart Management',
              tenantId: 'default_tenant',
              version: '1.0',
              status: 'active',
            );

            // Set the content widget to display the WorkflowTransactionScreen
            _contentWidget = ChangeNotifierProvider(
              create: (_) => WorkflowProvider(),
              child: WorkflowTransactionScreen(
                objective: objective,
                chatEnabled: _isChatEnabled, // Pass the chat enabled state
              ),
            );
          } else {
            // For other items, display a simple message
            _contentWidget = Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Selected: ${selectedItem['name']}',
                    style: TextStyle(
                      fontFamily: 'TiemposText',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }
        });
      },
    );
  }
}

// Separate stateful widget for tree items to handle hover state properly
class _TreeItem extends StatefulWidget {
  final Map<String, dynamic> item;
  final int level;
  final VoidCallback onToggle;
  final Function(Map<String, dynamic>) onSelect;

  const _TreeItem({
    required this.item,
    required this.level,
    required this.onToggle,
    required this.onSelect,
  });

  @override
  State<_TreeItem> createState() => _TreeItemState();
}

class _TreeItemState extends State<_TreeItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: InkWell(
            onTap: () {
              // Toggle expansion
              widget.onToggle();

              // Select this item
              widget.onSelect(widget.item);
            },
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 10.0,
              ),
              decoration: BoxDecoration(
                color: widget.item['isSelected'] == true
                    ? Color(
                        0xffE6F0FF) // Light blue background for selected item
                    : isHovered
                        ? Color(0xffF0F0F0)
                        : Colors.white,
              ),
              child: Row(
                children: [
                  // Item icon (box or box_add based on item name)
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CustomImage.asset(
                      widget.item['image'] ??
                          'assets/images/my_business/box_add.svg',
                      width: 12,
                      height: 12,
                      color: isHovered ? Color(0xff0058FF) : Colors.grey[700],
                    ).toWidget(),
                  ),
                  SizedBox(width: 8),

                  // Item name
                  Expanded(
                    child: Text(
                      widget.item['name'],
                      style: TextStyle(
                        fontFamily: 'TiemposText',
                        fontSize: 12,
                        fontWeight: widget.item['isSelected'] == true
                            ? FontWeight.bold
                            : FontWeight.w500,
                        color: isHovered ? Color(0xff0058FF) : Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Show dropdown arrow if item has children
                  Transform.rotate(
                    angle: widget.item['isExpanded']
                        ? 3.14159
                        : 0, // 180 degrees when expanded
                    child: CustomImage.asset(
                      // Get dropdown image from JSON
                      widget.item['dropdownImage'] ??
                          'assets/images/my_business/dropdown_collection.svg',
                      width: 20,
                      height: 20,
                    ).toWidget(),
                  )
                ],
              ),
            ),
          ),
        ),

        // Recursively build children if expanded
        if (widget.item['isExpanded'] && widget.item['children'] != null)
          Padding(
            padding: const EdgeInsets.only(left: 24.0), // Indent child items
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widget.item['children']
                  .map<Widget>((child) => _TreeItem(
                        item: child,
                        level: widget.level + 1,
                        onToggle: () {
                          setState(() {
                            child['isExpanded'] = !child['isExpanded'];
                          });
                        },
                        onSelect: widget
                            .onSelect, // Pass the onSelect callback to children
                      ))
                  .toList(),
            ),
          ),
      ],
    );
  }
}
