import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// A provider that maintains state across responsive layout changes and content switches.
///
/// This provider stores the current screen state when:
/// 1. The layout changes from web to mobile or vice versa
/// 2. Content changes within a single page without navigation
///
/// This ensures that the app maintains the correct state during transitions
/// and when switching content in a single-page application approach.
class ResponsiveStateProvider extends ChangeNotifier {
  // Map to store the current route/screen for each responsive builder
  final Map<String, String> _currentRoutes = {};

  // Map to store the current content state for each page
  final Map<String, String> _contentStates = {};

  // Previous content state before layout change
  String? _lastContentState;

  // Flag to track if we're currently in web layout
  bool _isWebLayout = true;

  // Timer for debouncing notifications
  Timer? _notificationTimer;

  // Getter for web layout flag
  bool get isWebLayout => _isWebLayout;

  // Constructor - load saved states
  ResponsiveStateProvider() {
    _loadSavedStates();
  }

  @override
  void dispose() {
    _disposed = true;
    _notificationTimer?.cancel();
    super.dispose();
  }

  // Debounced notification to prevent rapid successive calls
  void _debouncedNotifyListeners() {
    _notificationTimer?.cancel();
    _notificationTimer = Timer(const Duration(milliseconds: 50), () {
      // Check if the provider is still valid before notifying
      if (!_disposed) {
        notifyListeners();
      }
    });
  }

  // Flag to track if the provider has been disposed
  bool _disposed = false;

  // Load saved states from SharedPreferences
  Future<void> _loadSavedStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load content states if available
      final contentStatesJson = prefs.getString('content_states');
      if (contentStatesJson != null) {
        // Parse the JSON string back to a Map
        final Map<String, dynamic> savedContentStates =
            Map<String, dynamic>.from(json.decode(contentStatesJson));

        // Convert dynamic values to strings
        _contentStates.clear();
        savedContentStates.forEach((key, value) {
          if (value is String) {
            _contentStates[key] = value;
          }
        });

        Logger.info('Loaded content states: $_contentStates');
      }
    } catch (e) {
      Logger.error('Error loading saved states: $e');
    }
  }

  // Save content states to SharedPreferences
  Future<void> _saveContentStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert the map to a JSON string
      final contentStatesJson = json.encode(_contentStates);

      // Save to SharedPreferences
      await prefs.setString('content_states', contentStatesJson);
      Logger.info('Saved content states: $_contentStates');
    } catch (e) {
      Logger.error('Error saving content states: $e');
    }
  }

  // Update the web layout flag
  void setWebLayout(bool isWeb) {
    if (_isWebLayout != isWeb) {
      _isWebLayout = isWeb;
      _debouncedNotifyListeners();
      Logger.info('Layout changed to ${isWeb ? "web" : "mobile"}');
    }
  }

  // Get the current route for a specific builder
  String? getCurrentRoute(String builderKey) {
    return _currentRoutes[builderKey];
  }

  // Set the current route for a specific builder
  void setCurrentRoute(String builderKey, String route) {
    // Only update and notify if the route has actually changed
    if (_currentRoutes[builderKey] != route) {
      _currentRoutes[builderKey] = route;
      Logger.info('Set current route for $builderKey to $route');
      _debouncedNotifyListeners();
    }
  }

  // Get the current content state for a specific page
  String? getContentState(String pageKey) {
    return _contentStates[pageKey];
  }

  // Set the current content state for a specific page
  void setContentState(String pageKey, String contentState) {
    // Only update and notify if the content state has actually changed
    if (_contentStates[pageKey] != contentState) {
      _contentStates[pageKey] = contentState;
      Logger.info('Set content state for $pageKey to $contentState');
      _saveContentStates(); // Save to persistent storage
      _debouncedNotifyListeners();
    }
  }

  // Store the current content state before layout change
  void storeContentStateBeforeLayoutChange(String contentState) {
    _lastContentState = contentState;
    Logger.info('Stored content state before layout change: $contentState');
  }

  // Get and clear the last content state
  String? getAndClearLastContentState() {
    final state = _lastContentState;
    _lastContentState = null;
    return state;
  }

  // Clear the current route for a specific builder
  void clearCurrentRoute(String builderKey) {
    _currentRoutes.remove(builderKey);
    notifyListeners();
  }

  // Clear the content state for a specific page
  void clearContentState(String pageKey) {
    _contentStates.remove(pageKey);
    _saveContentStates(); // Save to persistent storage
    notifyListeners();
  }

  // Clear all routes
  void clearAllRoutes() {
    _currentRoutes.clear();
    notifyListeners();
  }

  // Clear all content states
  void clearAllContentStates() {
    _contentStates.clear();
    _saveContentStates(); // Save to persistent storage
    notifyListeners();
  }
}
