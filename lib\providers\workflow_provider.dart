import 'package:flutter/material.dart';
import '../models/workflow.dart';
import '../models/global_objective.dart';
import '../models/input_value_store.dart';
import '../services/workflow_service.dart';
import '../utils/logger.dart';

class WorkflowProvider extends ChangeNotifier {
  final WorkflowService _workflowService = WorkflowService();

  bool _isLoading = false;
  String? _error;
  bool _hasNewInputs = false;

  WorkflowInstance? _workflowInstance;
  WorkflowInputs? _workflowInputs;
  List<WorkflowInputs> _allWorkflowInputs = [];
  GlobalObjective? _selectedObjective;

  // Store for input values
  final InputValueStore inputValueStore = InputValueStore();

  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasNewInputs => _hasNewInputs;
  WorkflowInstance? get workflowInstance => _workflowInstance;
  WorkflowInputs? get workflowInputs => _workflowInputs;
  List<WorkflowInputs> get allWorkflowInputs => _allWorkflowInputs;
  GlobalObjective? get selectedObjective => _selectedObjective;

  // Reset the new inputs flag
  void resetNewInputsFlag() {
    _hasNewInputs = false;
    notifyListeners();
  }

  // Set the selected objective
  void setSelectedObjective(GlobalObjective objective) {
    // Only notify listeners if the objective has changed
    if (_selectedObjective?.objectiveId != objective.objectiveId) {
      _selectedObjective = objective;
      notifyListeners();
    } else {
      // Just update the reference without notification
      _selectedObjective = objective;
    }
  }

  // Set the workflow instance directly (for resuming workflows)
  void setWorkflowInstance(WorkflowInstance instance) {
    _workflowInstance = instance;
    notifyListeners();
  }

  // Fetch workflow inputs directly (for resuming workflows)
  Future<dynamic> fetchWorkflowInputsDirectly(String instanceId) async {
    try {
      // First, clear all previous data except the workflow instance
      // We need to preserve the workflow instance for resuming
      WorkflowInstance? tempInstance = _workflowInstance;
      GlobalObjective? tempObjective = _selectedObjective;

      // Clear everything else
      _workflowInputs = null;
      _allWorkflowInputs = [];
      _error = null;
      _hasNewInputs = false;

      // Clear the input value store
      inputValueStore.clear();
      Logger.info(
          'Input value store and workflow data cleared in WorkflowProvider.fetchWorkflowInputsDirectly');

      // Restore the workflow instance and objective
      _workflowInstance = tempInstance;
      _selectedObjective = tempObjective;

      // Set loading state
      _isLoading = true;
      notifyListeners();

      // Fetch the workflow inputs
      Logger.info('Fetching workflow inputs for instance: $instanceId');
      await _fetchWorkflowInputs(instanceId);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  // Clear all data
  void clearWorkflowData() {
    _workflowInstance = null;
    _workflowInputs = null;
    _allWorkflowInputs = [];
    _selectedObjective = null;
    _error = null;
    _isLoading = false;
    _hasNewInputs = false;

    // Clear the input value store
    inputValueStore.clear();
    Logger.info(
        'Input value store cleared in WorkflowProvider.clearWorkflowData');

    // Debug: print stack trace to identify where clearWorkflowData is called from
    try {
      throw Exception('Stack trace for WorkflowProvider.clearWorkflowData()');
    } catch (e, stackTrace) {
      Logger.info(
          'WorkflowProvider.clearWorkflowData() called from:\n$stackTrace');
    }

    notifyListeners();
  }

  // Process workflow - calls all three APIs in sequence
  Future<bool> processWorkflow(GlobalObjective objective) async {
    // Always clear all data before starting a new workflow
    clearWorkflowData();

    // Set loading state
    _isLoading = true;
    // Set the selected objective
    _selectedObjective = objective;

    notifyListeners();

    Logger.info(
        'Starting new workflow for objective: ${objective.objectiveId}');

    try {
      // Step 1: Create workflow instance
      final instanceData = await _createWorkflowInstance(
        objective.objectiveId,
        objective.tenantId,
      );

      // Step 2: Start workflow instance
      final startedInstance = await _startWorkflowInstance(
        instanceData['instance_id'],
      );

      // Step 3: Fetch workflow inputs
      await _fetchWorkflowInputs(
        startedInstance['instance_id'],
      );

      return true;
    } catch (e) {
      Logger.error('Error in processWorkflow: $e');
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Create workflow instance
  Future<Map<String, dynamic>> _createWorkflowInstance(
      String goId, String tenantId) async {
    try {
      final result = await _workflowService.createWorkflowInstance(
        goId: goId,
        tenantId: tenantId,
        // userId: 'user-001', // Hardcoded for now
      );

      _workflowInstance = WorkflowInstance.fromJson(result);
      notifyListeners();

      return result;
    } catch (e) {
      Logger.error('Error in _createWorkflowInstance: $e');
      _error = 'Failed to create workflow: $e';
      throw Exception('Failed to create workflow: $e');
    }
  }

  // Start workflow instance
  Future<Map<String, dynamic>> _startWorkflowInstance(String instanceId) async {
    try {
      final result = await _workflowService.startWorkflowInstance(
        instanceId: instanceId,
        // userId: 'user-001', // Hardcoded for now
      );

      _workflowInstance = WorkflowInstance.fromJson(result);
      notifyListeners();

      return result;
    } catch (e) {
      Logger.error('Error in _startWorkflowInstance: $e');
      _error = 'Failed to start workflow: $e';
      throw Exception('Failed to start workflow: $e');
    }
  }

  // Execute workflow with input data
  Future<Map<String, dynamic>> executeWorkflow({
    required String instanceId,
    required Map<String, dynamic> inputData,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final result = await _workflowService.executeWorkflow(
        instanceId: instanceId,
        inputData: inputData,
        // userId: 'user-001', // Hardcoded for now
      );

      _isLoading = false;
      notifyListeners();

      // Check if there's a next local objective to fetch
      if (result.containsKey('next_lo_id') &&
          result['next_lo_id'] != null &&
          result['next_lo_id'].toString().isNotEmpty) {
        // Fetch inputs for the next local objective
        await _fetchWorkflowInputs(instanceId);

        // Set the flag to indicate new inputs are available
        _hasNewInputs = true;
        notifyListeners();
      }

      return result;
    } catch (e) {
      Logger.error('Error in executeWorkflow: $e');
      _error = 'Failed to execute workflow: $e';
      _isLoading = false;
      notifyListeners();
      throw Exception('Failed to execute workflow: $e');
    }
  }

  // Fetch workflow inputs
  Future<dynamic> _fetchWorkflowInputs(String instanceId) async {
    try {
      final result = await _workflowService.fetchWorkflowInputs(
        instanceId: instanceId,
      );

      Logger.info(
          'WorkflowProvider received result type: ${result.runtimeType}');
      Logger.info('WorkflowProvider received result: $result');

      // Create new workflow inputs from the result
      final newInputs = WorkflowInputs.fromJson(result);

      // Log the new inputs for debugging
      Logger.info('New workflow inputs created:');
      Logger.info('- User inputs: ${newInputs.userInputs.length} fields');
      Logger.info('- System inputs: ${newInputs.systemInputs.length} fields');
      Logger.info('- Info inputs: ${newInputs.infoInputs.length} fields');
      Logger.info(
          '- Dependent inputs: ${newInputs.dependentInputs.length} fields');

      // Log user inputs
      for (var field in newInputs.userInputs) {
        Logger.info(
            'User Field: ${field.displayName}, Type: ${field.dataType}, Required: ${field.required}');
      }

      // Log system inputs
      for (var field in newInputs.systemInputs) {
        Logger.info(
            'System Field: ${field.displayName}, Type: ${field.dataType}, Value: ${field.inputValue}');
      }

      // Log info inputs
      for (var field in newInputs.infoInputs) {
        Logger.info(
            'Info Field: ${field.displayName}, Type: ${field.dataType}');
      }

      // Log dependent inputs
      for (var field in newInputs.dependentInputs) {
        Logger.info(
            'Dependent Field: ${field.displayName}, Type: ${field.dataType}, Dependency Type: ${field.dependencyType}');
      }

      // Update the current inputs
      _workflowInputs = newInputs;

      // Add to the list of all inputs (don't replace, add to the list)
      _allWorkflowInputs.add(newInputs);
      Logger.info('Total input sections: ${_allWorkflowInputs.length}');

      // Set flag to indicate new inputs are available
      _hasNewInputs = true;
      _isLoading = false;
      // notifyListeners();

      return result;
    } catch (e) {
      Logger.error('Error in _fetchWorkflowInputs: $e');
      _error = 'Failed to fetch workflow inputs: $e';
      _isLoading = false;
      notifyListeners();
      throw Exception('Failed to fetch workflow inputs: $e');
    }
  }

  void setWorkFlowInputs(WorkflowInputs newInputs) {
    _workflowInputs = newInputs;
    // notifyListeners();
  }

  // Update a dependent input field with new data from API
  void updateDependentInput(Map<String, dynamic> inputData) {
    if (_workflowInputs == null) {
      Logger.warning('Cannot update dependent input: workflowInputs is null');
      return;
    }

    // Get the attribute ID from the input data
    final attributeId = inputData['attribute_id'];
    if (attributeId == null) {
      Logger.warning('Cannot update dependent input: attribute_id is missing');
      return;
    }

    Logger.info('Updating dependent input with attribute ID: $attributeId');

    // Find the dependent input in the current inputs
    final index = _workflowInputs!.dependentInputs
        .indexWhere((field) => field.attributeId == attributeId);

    if (index >= 0) {
      // Update the existing dependent input
      final updatedField = InputField.fromJson(inputData);
      _workflowInputs!.dependentInputs[index] = updatedField;
      Logger.info('Updated existing dependent input: $attributeId');
    } else {
      // Add as a new dependent input
      final newField = InputField.fromJson(inputData);
      _workflowInputs!.dependentInputs.add(newField);
      Logger.info('Added new dependent input: $attributeId');
    }

    // Also update the field in allWorkflowInputs if it exists
    if (_allWorkflowInputs.isNotEmpty) {
      for (var inputs in _allWorkflowInputs) {
        final index = inputs.dependentInputs
            .indexWhere((field) => field.attributeId == attributeId);

        if (index >= 0) {
          // Update the existing dependent input
          final updatedField = InputField.fromJson(inputData);
          inputs.dependentInputs[index] = updatedField;
          Logger.info(
              'Updated dependent input in allWorkflowInputs: $attributeId');
        }
      }
    }

    // No need to call notifyListeners() here as the UI will be rebuilt by the calling code
  }
}
