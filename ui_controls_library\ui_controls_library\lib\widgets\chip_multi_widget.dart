import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class ChipMultiWidget extends StatefulWidget {
  // Configurable properties
  final List<String> options;
  final List<String> initialSelected;
  final Color selectedColor;
  final Color unselectedColor;
  final Color backgroundColor;
  final Color labelColor;
  final Color textColor;
  final double chipSize;
  final bool isRounded;
  final bool isPillShaped;
  final bool hasBorder;
  final double borderWidth;
  final Color selectedBorderColor;
  final Color unselectedBorderColor;
  final bool hasShadow;
  final bool isVertical;
  final bool isScrollable;
  final bool isWrapped;
  final List<String> disabledOptions;
  final bool isReadOnly;
  final bool showIcons;
  final bool showLeadingIcons;
  final bool showTrailingIcons;
  final bool useMaterial3;
  final bool useCupertinoStyle;
  final bool isDarkTheme;
  final bool hasAnimation;
  final bool hasFadeEffect;
  final bool hasBounceEffect;
  final Function(List<String>)? onSelectionChanged;
  final Function(List<String>)? onSubmit;
  final List<String>? testSelected; // For testing purposes only

  const ChipMultiWidget({
    super.key,
    this.options = const ["Apple", "Banana", "Orange", "Mango", "Grapes"],
    this.initialSelected = const [],
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.backgroundColor = Colors.white,
    this.labelColor = Colors.black,
    this.textColor = Colors.black,
    this.chipSize = 32.0,
    this.isRounded = true,
    this.isPillShaped = false,
    this.hasBorder = false,
    this.borderWidth = 1.0,
    this.selectedBorderColor = const Color(0xFF0058FF),
    this.unselectedBorderColor = const Color(0xFFCCCCCC),
    this.hasShadow = false,
    this.isVertical = false,
    this.isScrollable = false,
    this.isWrapped = true,
    this.disabledOptions = const [],
    this.isReadOnly = false,
    this.showIcons = false,
    this.showLeadingIcons = false,
    this.showTrailingIcons = false,
    this.useMaterial3 = false,
    this.useCupertinoStyle = false,
    this.isDarkTheme = false,
    this.hasAnimation = false,
    this.hasFadeEffect = false,
    this.hasBounceEffect = false,
    this.onSelectionChanged,
    this.onSubmit,
    this.testSelected,
  });

  /// Creates a ChipMultiWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the ChipMultiWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "options": ["Red", "Green", "Blue", "Yellow", "Purple"],
  ///   "initialSelected": ["Red", "Blue"],
  ///   "selectedColor": "blue",
  ///   "unselectedColor": "grey",
  ///   "backgroundColor": "white",
  ///   "chipSize": 32.0,
  ///   "isRounded": true,
  ///   "isPillShaped": false,
  ///   "hasBorder": true,
  ///   "isVertical": false,
  ///   "isScrollable": true,
  ///   "showIcons": true
  /// }
  /// ```
  factory ChipMultiWidget.fromJson(Map<String, dynamic> json) {
    // Parse options
    List<String> options = ["Apple", "Banana", "Orange", "Mango", "Grapes"];
    if (json['options'] != null) {
      if (json['options'] is List) {
        options = List<String>.from(json['options']);
      }
    }

    // Parse initially selected options
    List<String> initialSelected = [];
    if (json['initialSelected'] != null) {
      if (json['initialSelected'] is List) {
        initialSelected = List<String>.from(json['initialSelected']);
      }
    }

    // Parse disabled options
    List<String> disabledOptions = [];
    if (json['disabledOptions'] != null) {
      if (json['disabledOptions'] is List) {
        disabledOptions = List<String>.from(json['disabledOptions']);
      }
    }

    return ChipMultiWidget(
      options: options,
      initialSelected: initialSelected,
      selectedColor: _colorFromJson(json['selectedColor']) ?? Color(0xFFf0f5ff),
      unselectedColor: _colorFromJson(json['unselectedColor']) ?? Colors.white,
      backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.white,
      labelColor: _colorFromJson(json['labelColor']) ?? Colors.black,
      textColor: _colorFromJson(json['textColor']) ?? Color(0xFF333333),
      chipSize: (json['chipSize'] as num?)?.toDouble() ?? 32.0,
      isRounded: json['isRounded'] ?? true,
      isPillShaped: json['isPillShaped'] ?? false,
      hasBorder: json['hasBorder'] ?? false,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      selectedBorderColor: _colorFromJson(json['selectedBorderColor']) ?? Color(0xFF0058FF),
      unselectedBorderColor: _colorFromJson(json['unselectedBorderColor']) ?? const Color(0xFFCCCCCC),
      hasShadow: json['hasShadow'] ?? false,
      isVertical: json['isVertical'] ?? false,
      isScrollable: json['isScrollable'] ?? false,
      isWrapped: json['isWrapped'] ?? true,
      disabledOptions: disabledOptions,
      isReadOnly: json['isReadOnly'] ?? false,
      showIcons: json['showIcons'] ?? false,
      showLeadingIcons: json['showLeadingIcons'] ?? false,
      showTrailingIcons: json['showTrailingIcons'] ?? false,
      useMaterial3: json['useMaterial3'] ?? false,
      useCupertinoStyle: json['useCupertinoStyle'] ?? false,
      isDarkTheme: json['isDarkTheme'] ?? false,
      hasAnimation: json['hasAnimation'] ?? false,
      hasFadeEffect: json['hasFadeEffect'] ?? false,
      hasBounceEffect: json['hasBounceEffect'] ?? false,
      onSelectionChanged: (selected) {
        // This would be handled by the app in a real implementation
      },
      onSubmit: json['hasSubmitButton'] == true ? (selected) {
        // This would be handled by the app in a real implementation
      } : null,
    );
  }

  /// Converts the ChipMultiWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      'options': options,
      'initialSelected': initialSelected,
      'selectedColor': _colorToJson(selectedColor),
      'unselectedColor': _colorToJson(unselectedColor),
      'backgroundColor': _colorToJson(backgroundColor),
      'labelColor': _colorToJson(labelColor),
      'textColor': _colorToJson(textColor),
      'chipSize': chipSize,
      'isRounded': isRounded,
      'isPillShaped': isPillShaped,
      'hasBorder': hasBorder,
      'borderWidth': borderWidth,
      'selectedBorderColor': _colorToJson(selectedBorderColor),
      'unselectedBorderColor': _colorToJson(unselectedBorderColor),
      'hasShadow': hasShadow,
      'isVertical': isVertical,
      'isScrollable': isScrollable,
      'isWrapped': isWrapped,
      'disabledOptions': disabledOptions,
      'isReadOnly': isReadOnly,
      'showIcons': showIcons,
      'showLeadingIcons': showLeadingIcons,
      'showTrailingIcons': showTrailingIcons,
      'useMaterial3': useMaterial3,
      'useCupertinoStyle': useCupertinoStyle,
      'isDarkTheme': isDarkTheme,
      'hasAnimation': hasAnimation,
      'hasFadeEffect': hasFadeEffect,
      'hasBounceEffect': hasBounceEffect,
      'hasSubmitButton': onSubmit != null,
    };
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors - return MaterialColor for standard colors
      // to ensure round-trip conversion works correctly
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'amber': return Colors.amber;
        case 'cyan': return Colors.cyan;
        case 'indigo': return Colors.indigo;
        case 'lime': return Colors.lime;
        case 'teal': return Colors.teal;
        default: return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability and test compatibility
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      // We'll keep the original MaterialColor in the round-trip conversion
      // by using a special format that our fromJson method can recognize
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final r = color.toString().substring(10, 12);
    final g = color.toString().substring(12, 14);
    final b = color.toString().substring(14, 16);

    return '#$r$g$b';
  }

  @override
  State<ChipMultiWidget> createState() => _ChipMultiWidgetState();
}

class _ChipMultiWidgetState extends State<ChipMultiWidget> with SingleTickerProviderStateMixin {
  // List to store selected chip values
  late List<String> _selectedTags;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    // Use test selected values if provided (for testing purposes)
    if (widget.testSelected != null) {
      _selectedTags = List.from(widget.testSelected!);
    } else {
      _selectedTags = List.from(widget.initialSelected);
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.hasBounceEffect ? 1.1 : 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Get icon for a specific option
  Widget? _getIconForOption(String option) {
    if (!widget.showIcons) return null;

    // Map common options to icons
    final Map<String, IconData> iconMap = {
      'apple': Icons.apple,
      'banana': Icons.breakfast_dining,
      'orange': Icons.circle,
      'mango': Icons.star,
      'grapes': Icons.grain,
      'mumbai': Icons.location_city,
      'delhi': Icons.location_city,
      'bangalore': Icons.location_city,
      'a': Icons.filter_1,
      'b': Icons.filter_2,
      'c': Icons.filter_3,
      'd': Icons.filter_4,
    };

    final lowerOption = option.toLowerCase();
    if (iconMap.containsKey(lowerOption)) {
      return Icon(
        iconMap[lowerOption],
        size: _getResponsiveIconSize(context),
        color: _selectedTags.contains(option) ? Colors.white : widget.textColor,
      );
    }

    // Default icon if no match
    return Icon(
      Icons.label,
      size: _getResponsiveIconSize(context),
      color: _selectedTags.contains(option) ? Colors.white : widget.textColor,
    );
  }

  // Build a single chip
  Widget _buildChip(String tag) {
    final bool isSelected = _selectedTags.contains(tag);
    final bool isDisabled = widget.disabledOptions.contains(tag) || widget.isReadOnly;

    // Apply animation if enabled
    Widget chipWidget;

    if (widget.useCupertinoStyle) {
      // Cupertino style chip
      chipWidget = Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? widget.selectedColor : widget.unselectedColor,
          borderRadius: BorderRadius.circular(widget.isPillShaped ? 50 : (widget.isRounded ? 8 : 0)),
          border: widget.hasBorder ? Border.all(
            color: isSelected ? widget.selectedBorderColor : widget.unselectedBorderColor,
            width: widget.borderWidth
          ) : null,
          boxShadow: widget.hasShadow ? [
            BoxShadow(color: Colors.black26, blurRadius: 4, offset: Offset(0, 2))
          ] : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showLeadingIcons) ...[_getIconForOption(tag) ?? Container(), SizedBox(width: 4)],
            Text(
              tag,
              style: TextStyle(
                color: isSelected ? Colors.white : widget.textColor,
                fontSize: widget.chipSize / 2.5,
              ),
            ),
            if (widget.showTrailingIcons && isSelected) ...[SizedBox(width: 4), Icon(CupertinoIcons.check_mark, size: widget.chipSize / 2, color: Colors.white)],
          ],
        ),
      );
    } else {
      // Material style chip
      chipWidget = IconTheme(
        data: IconThemeData(size: _getResponsiveIconSize(context)),
        child: FilterChip(
          label: Text(tag),
          labelStyle: TextStyle(
            color: isSelected ? Color(0xFF333333) : widget.textColor,
            fontSize: _getResponsiveValueFontSize(context),
          ),
          selected: isSelected,
          onSelected: isDisabled ? null : (selected) {
            setState(() {
              if (selected) {
                _selectedTags.add(tag);
              } else {
                _selectedTags.remove(tag);
              }
            });
        
            if (widget.onSelectionChanged != null) {
              widget.onSelectionChanged!(_selectedTags);
            }
        
            if (widget.hasAnimation) {
              _animationController.reset();
              _animationController.forward();
            }
          },
          selectedColor: widget.selectedColor,
          backgroundColor: widget.unselectedColor,
          disabledColor: Colors.grey.shade300,
          avatar: widget.showLeadingIcons ? _getIconForOption(tag) : null,
          deleteIcon: widget.showTrailingIcons && isSelected ? const Icon(Icons.cancel, size: 18) : null,
          showCheckmark: !widget.showTrailingIcons,
          checkmarkColor: Color(0xFF0058FF),
          shape: widget.isPillShaped
              ? StadiumBorder(
                  side: BorderSide(
                    color: isSelected ? widget.selectedBorderColor : widget.unselectedBorderColor,
                    width: widget.hasBorder ? widget.borderWidth : 1.0
                  )
                )
              : widget.isRounded
                  ? RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                      side: BorderSide(
                        color: isSelected ? widget.selectedBorderColor : widget.unselectedBorderColor,
                        width: widget.hasBorder ? widget.borderWidth : 1.0
                      ),
                    )
                  : ContinuousRectangleBorder(
                      side: BorderSide(
                        color: isSelected ? widget.selectedBorderColor : widget.unselectedBorderColor,
                        width: widget.hasBorder ? widget.borderWidth : 1.0
                      ),
                    ),
          elevation: widget.hasShadow ? 4 : 0,
          pressElevation: widget.hasShadow ? 8 : 0,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          labelPadding: EdgeInsets.only(left: 0),
        ),
      );
    }

    // Apply animation if enabled
    if (widget.hasAnimation) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: isSelected ? _scaleAnimation.value : 1.0,
            child: widget.hasFadeEffect
                ? AnimatedOpacity(
                    opacity: isSelected ? 1.0 : 0.8,
                    duration: const Duration(milliseconds: 300),
                    child: child,
                  )
                : child,
          );
        },
        child: chipWidget,
      );
    }

    return chipWidget;
  }
   double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
  @override
  Widget build(BuildContext context) {
    // Create the chip layout
    Widget chipLayout;

    if (widget.isVertical) {
      // Vertical layout
      if (widget.isScrollable) {
        chipLayout = SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: widget.options.map((tag) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: _buildChip(tag),
            )).toList(),
          ),
        );
      } else {
        chipLayout = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: widget.options.map((tag) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: _buildChip(tag),
          )).toList(),
        );
      }
    } else {
      // Horizontal layout
      if (widget.isWrapped) {
        chipLayout = Wrap(
          spacing: 8.0,  // space between chips
          runSpacing: 8.0,  // space between rows
          children: widget.options.map((tag) => _buildChip(tag)).toList(),
        );
      } else if (widget.isScrollable) {
        chipLayout = SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: widget.options.map((tag) => Padding(
              padding: const EdgeInsets.only(right: 8),
              child: _buildChip(tag),
            )).toList(),
          ),
        );
      } else {
        chipLayout = Row(
          children: widget.options.map((tag) => Padding(
            padding: const EdgeInsets.only(right: 8),
            child: _buildChip(tag),
          )).toList(),
        );
      }
    }

    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          chipLayout,
          if (widget.onSubmit != null) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                if (widget.onSubmit != null) {
                  widget.onSubmit!(_selectedTags);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.selectedColor,
                foregroundColor: Colors.white,
              ),
              child: const Text("Submit"),
            ),
          ],
        ],
      ),
    );
  }
  
  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else if (screenWidth >= 768) {
      return 12.0; // Small
    } else {
      return 12.0; // Default for very small screens
    }
  }
  
  _getResponsiveIconSize(BuildContext context) {}
}