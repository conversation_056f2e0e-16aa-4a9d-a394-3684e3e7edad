import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/web_home_provider.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';

/// A global wrapper for responsive layouts that preserves screen state
/// when transitioning between web and mobile layouts.
///
/// This widget maintains the current screen state in a shared provider
/// and ensures that when the layout changes due to screen size changes,
/// the current screen/page is preserved rather than redirecting to the home page.
class GlobalResponsiveWrapper extends StatefulWidget {
  /// The child widget to wrap
  final Widget child;

  const GlobalResponsiveWrapper({
    super.key,
    required this.child,
  });

  @override
  State<GlobalResponsiveWrapper> createState() => _GlobalResponsiveWrapperState();
}

class _GlobalResponsiveWrapperState extends State<GlobalResponsiveWrapper> with WidgetsBindingObserver {
  // Track the current layout mode
  bool _isWebLayout = true;

  // Store the current screen index when layout changes
  String? _lastScreenIndex;

  @override
  void initState() {
    super.initState();
    // Add observer to detect screen size changes
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // Remove observer when widget is disposed
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    // This is called when screen metrics change (e.g., size, orientation)
    _checkLayoutChange();
    super.didChangeMetrics();
  }

  void _checkLayoutChange() {
    // Make sure we have a valid context
    if (!mounted) return;

    // Get the current screen width
    final screenWidth = MediaQuery.of(context).size.width;

    // Determine if we're in web layout based on new breakpoint logic:
    // < 767px: Mobile layout
    // >= 767px: Web layout (includes both tablet and desktop)
    final isCurrentlyWebLayout = screenWidth >= AppConstants.mobileBreakpoint;

    // If layout mode has changed
    if (isCurrentlyWebLayout != _isWebLayout) {
      try {
        // Get the WebHomeProvider
        final webHomeProvider = Provider.of<WebHomeProvider>(context, listen: false);

        // Store the current screen index before layout change
        if (_isWebLayout && !isCurrentlyWebLayout) {
          // Transitioning from web to mobile
          _lastScreenIndex = webHomeProvider.currentScreenIndex;
          Logger.info('Transitioning from web to mobile. Saving screen index: $_lastScreenIndex');

          // Important: Don't change the screen when transitioning to mobile
          // This ensures we stay on the same screen
        } else if (!_isWebLayout && isCurrentlyWebLayout) {
          // Transitioning from mobile to web
          Logger.info('Transitioning from mobile to web');

          // Check if we have a stored screen index
          if (_lastScreenIndex != null && _lastScreenIndex!.isNotEmpty) {
            Logger.info('Restoring screen index: $_lastScreenIndex');

            // Use a post-frame callback to ensure the state is applied after the layout change
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                webHomeProvider.currentScreenIndex = _lastScreenIndex!;
                Logger.info('Applied screen index: $_lastScreenIndex');
              }
            });

            // Clear the stored screen index after using it
            _lastScreenIndex = null;
          }
        }
      } catch (e) {
        Logger.error('Error handling layout change: $e');
      }

      // Update layout mode
      if (mounted) {
        setState(() {
          _isWebLayout = isCurrentlyWebLayout;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Wrap the child with a layout builder to detect size changes
    return LayoutBuilder(
      builder: (context, constraints) {
        // Check if layout has changed using the same logic as _checkLayoutChange
        final isCurrentlyWebLayout = constraints.maxWidth >= AppConstants.mobileBreakpoint;

        // If layout mode has changed
        if (isCurrentlyWebLayout != _isWebLayout) {
          // Schedule a callback to handle the layout change
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _checkLayoutChange();
          });
        }

        // Return the child widget
        return widget.child;
      },
    );
  }
}
