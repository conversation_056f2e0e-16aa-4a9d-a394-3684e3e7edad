import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A class that represents an image from various sources (asset, network, file)
/// and can be easily serialized to/from JSON.
class CustomImage {
  /// The type of image source
  final ImageSourceType type;

  /// The path or URL of the image
  final String path;

  /// Optional width of the image
  final double? width;

  /// Optional height of the image
  final double? height;

  /// Optional fit for the image
  final BoxFit? fit;

  /// Optional color to blend with the image
  final Color? color;

  /// Optional placeholder path for when the image is loading (for network images)
  final String? placeholder;

  /// Creates a CustomImage instance
  const CustomImage({
    required this.type,
    required this.path,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.placeholder,
  });

  /// Creates a CustomImage from an asset path
  factory CustomImage.asset(
    String path, {
    double? width,
    double? height,
    BoxFit? fit,
    Color? color,
  }) {
    return CustomImage(
      type: ImageSourceType.asset,
      path: path,
      width: width,
      height: height,
      fit: fit,
      color: color,
    );
  }

  /// Creates a CustomImage from a network URL
  factory CustomImage.network(
    String url, {
    double? width,
    double? height,
    BoxFit? fit,
    Color? color,
    String? placeholder,
  }) {
    return CustomImage(
      type: ImageSourceType.network,
      path: url,
      width: width,
      height: height,
      fit: fit,
      color: color,
      placeholder: placeholder,
    );
  }

  /// Creates a CustomImage from a file path
  factory CustomImage.file(
    String filePath, {
    double? width,
    double? height,
    BoxFit? fit,
    Color? color,
  }) {
    return CustomImage(
      type: ImageSourceType.file,
      path: filePath,
      width: width,
      height: height,
      fit: fit,
      color: color,
    );
  }

  /// Creates a CustomImage from a JSON map
  factory CustomImage.fromJson(Map<String, dynamic> json) {
    Color? color;
    if (json['color'] != null) {
      if (json['color'] is String && (json['color'] as String).startsWith('0x')) {
        // Parse hex string format
        color = Color(int.parse(json['color'] as String));
      } else if (json['color'] is int) {
        // Parse integer format
        color = Color(json['color'] as int);
      }
    }

    return CustomImage(
      type: _parseImageSourceType(json['type'] as String),
      path: json['path'] as String,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      fit: json['fit'] != null ? _parseBoxFit(json['fit'] as String) : null,
      color: color,
      placeholder: json['placeholder'] as String?,
    );
  }

  /// Converts the CustomImage to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'path': path,
      if (width != null) 'width': width,
      if (height != null) 'height': height,
      if (fit != null) 'fit': fit.toString().split('.').last,
      if (color != null) 'color': '0x${color!.value.toRadixString(16).padLeft(8, '0')}',
      if (placeholder != null) 'placeholder': placeholder,
    };
  }

  /// Converts the CustomImage to a JSON string
  String toJsonString() => jsonEncode(toJson());

  /// Creates a CustomImage from a JSON string
  static CustomImage fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString) as Map<String, dynamic>;
    return CustomImage.fromJson(json);
  }

  /// Builds a Widget from the CustomImage
  Widget toWidget({
    Key? key,
    double? width,
    double? height,
    BoxFit? fit,
    Color? color,
    Widget Function(BuildContext, Widget, ImageChunkEvent?)? loadingBuilder,
    Widget Function(BuildContext, Object, StackTrace?)? errorBuilder,
  }) {
    final double? finalWidth = width ?? this.width;
    final double? finalHeight = height ?? this.height;
    final BoxFit? finalFit = fit ?? this.fit;
    final Color? finalColor = color ?? this.color;

    // Check if the path is an SVG file
    bool isSvg = path.toLowerCase().endsWith('.svg');

    switch (type) {
      case ImageSourceType.asset:
        if (isSvg) {
          return SvgPicture.asset(
            path,
            width: finalWidth,
            height: finalHeight,
            colorFilter: finalColor != null
                ? ColorFilter.mode(finalColor, BlendMode.srcIn)
                : null,
            fit: finalFit ?? BoxFit.contain,
          );
        } else {
          return Image.asset(
            path,
            key: key,
            width: finalWidth,
            height: finalHeight,
            fit: finalFit,
            color: finalColor,
            errorBuilder: errorBuilder ?? _defaultErrorBuilder,
          );
        }
      case ImageSourceType.network:
        if (isSvg) {
          return SvgPicture.network(
            path,
            width: finalWidth,
            height: finalHeight,
            colorFilter: finalColor != null
                ? ColorFilter.mode(finalColor, BlendMode.srcIn)
                : null,
            fit: finalFit ?? BoxFit.contain,
            placeholderBuilder: placeholder != null
                ? (_) => Image.asset(
                    placeholder!,
                    width: finalWidth,
                    height: finalHeight,
                    fit: finalFit,
                  )
                : null,
          );
        } else {
          return Image.network(
            path,
            key: key,
            width: finalWidth,
            height: finalHeight,
            fit: finalFit,
            color: finalColor,
            loadingBuilder: loadingBuilder ?? _defaultLoadingBuilder,
            errorBuilder: errorBuilder ?? _defaultErrorBuilder,
          );
        }
      case ImageSourceType.file:
        if (isSvg) {
          return SvgPicture.asset(
          path,
            width: finalWidth,
            height: finalHeight,
            colorFilter: finalColor != null
                ? ColorFilter.mode(finalColor, BlendMode.srcIn)
                : null,
            fit: finalFit ?? BoxFit.contain,
          );
        } else {
          return Image.asset(
            path,
            key: key,
            width: finalWidth,
            height: finalHeight,
            fit: finalFit,
            color: finalColor,
            errorBuilder: errorBuilder ?? _defaultErrorBuilder,
          );
        }
    }
  }

  /// Default error builder that shows a placeholder or error icon
  Widget _defaultErrorBuilder(
    BuildContext context,
    Object error,
    StackTrace? stackTrace,
  ) {
    if (placeholder != null) {
      return Image.asset(
        placeholder!,
        width: width,
        height: height,
        fit: fit,
      );
    }

    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: Center(
        child: Icon(
          Icons.broken_image,
          color: Colors.grey[400],
          size: 24,
        ),
      ),
    );
  }

  /// Default loading builder that shows a placeholder if available
  Widget _defaultLoadingBuilder(
    BuildContext context,
    Widget child,
    ImageChunkEvent? loadingProgress,
  ) {
    if (loadingProgress == null) {
      return child;
    }

    if (placeholder != null) {
      return Image.asset(
        placeholder!,
        width: width,
        height: height,
        fit: fit,
      );
    }

    return Center(
      child: CircularProgressIndicator(
        value: loadingProgress.expectedTotalBytes != null
            ? loadingProgress.cumulativeBytesLoaded /
                loadingProgress.expectedTotalBytes!
            : null,
      ),
    );
  }

  /// Helper method to parse ImageSourceType from string
  static ImageSourceType _parseImageSourceType(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'asset':
        return ImageSourceType.asset;
      case 'network':
        return ImageSourceType.network;
      case 'file':
        return ImageSourceType.file;
      default:
        throw ArgumentError('Invalid image source type: $typeString');
    }
  }

  /// Helper method to parse BoxFit from string
  static BoxFit _parseBoxFit(String fitString) {
    switch (fitString.toLowerCase()) {
      case 'contain':
        return BoxFit.contain;
      case 'cover':
        return BoxFit.cover;
      case 'fill':
        return BoxFit.fill;
      case 'fitheight':
        return BoxFit.fitHeight;
      case 'fitwidth':
        return BoxFit.fitWidth;
      case 'none':
        return BoxFit.none;
      case 'scaledown':
        return BoxFit.scaleDown;
      default:
        return BoxFit.cover; // Default
    }
  }
}

/// Enum representing the type of image source
enum ImageSourceType {
  asset,
  network,
  file,
}
