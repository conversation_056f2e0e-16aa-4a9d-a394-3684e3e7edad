import 'package:flutter/material.dart';

import '../../theme/spacing.dart';

/// A responsive grid layout that adapts to different screen sizes
class AppResponsiveGrid extends StatelessWidget {
  /// The list of widgets to display in the grid
  final List<Widget> children;

  /// The minimum width of each item in the grid
  final double minItemWidth;

  /// The spacing between items horizontally
  final double horizontalSpacing;

  /// The spacing between items vertically
  final double verticalSpacing;

  /// The padding around the grid
  final EdgeInsetsGeometry? padding;

  /// The alignment of the grid items
  final CrossAxisAlignment crossAxisAlignment;

  /// The maximum number of items per row (optional)
  final int? maxItemsPerRow;

  const AppResponsiveGrid({
    super.key,
    required this.children,
    this.minItemWidth = 300,
    this.horizontalSpacing = AppSpacing.md,
    this.verticalSpacing = AppSpacing.md,
    this.padding,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.maxItemsPerRow,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate how many items can fit per row
        int itemsPerRow =
            (constraints.maxWidth / (minItemWidth + horizontalSpacing)).floor();

        // Ensure at least one item per row
        itemsPerRow = itemsPerRow > 0 ? itemsPerRow : 1;

        // Apply maxItemsPerRow constraint if provided
        if (maxItemsPerRow != null && itemsPerRow > maxItemsPerRow!) {
          itemsPerRow = maxItemsPerRow!;
        }

        // Calculate the width of each item
        final double itemWidth =
            (constraints.maxWidth - (horizontalSpacing * (itemsPerRow - 1))) /
                itemsPerRow;

        // Create rows of items
        final List<Widget> rows = [];
        for (int i = 0; i < children.length; i += itemsPerRow) {
          final List<Widget> rowChildren = [];

          // Add items to the current row
          for (int j = 0; j < itemsPerRow && i + j < children.length; j++) {
            rowChildren.add(
              SizedBox(
                width: itemWidth,
                child: children[i + j],
              ),
            );

            // Add spacing between items (except after the last item)
            if (j < itemsPerRow - 1 && i + j < children.length - 1) {
              rowChildren.add(SizedBox(width: horizontalSpacing));
            }
          }

          // Add the row to the list of rows
          rows.add(
            Row(
              crossAxisAlignment: crossAxisAlignment,
              mainAxisAlignment: MainAxisAlignment.start,
              children: rowChildren,
            ),
          );

          // Add spacing between rows (except after the last row)
          if (i + itemsPerRow < children.length) {
            rows.add(SizedBox(height: verticalSpacing));
          }
        }

        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: rows,
          ),
        );
      },
    );
  }
}

/// A responsive grid item that can be used with AppResponsiveGrid
class AppResponsiveGridItem extends StatelessWidget {
  /// The child widget to display in the grid item
  final Widget child;

  /// The background color of the grid item
  final Color? backgroundColor;

  /// The border radius of the grid item
  final BorderRadius? borderRadius;

  /// The padding inside the grid item
  final EdgeInsetsGeometry padding;

  /// The elevation of the grid item
  final double elevation;

  /// The border of the grid item
  final Border? border;

  /// Whether the grid item should expand to fill available height
  final bool expandHeight;

  /// Callback when the grid item is tapped
  final VoidCallback? onTap;

  const AppResponsiveGridItem({
    super.key,
    required this.child,
    this.backgroundColor,
    this.borderRadius,
    this.padding = const EdgeInsets.all(AppSpacing.md),
    this.elevation = 0,
    this.border,
    this.expandHeight = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final Widget content = Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        borderRadius: borderRadius ?? BorderRadius.circular(AppSpacing.md),
        border: border,
        boxShadow: elevation > 0
            ? [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withAlpha(25),
                  blurRadius: elevation * 2,
                  offset: Offset(0, elevation),
                ),
              ]
            : null,
      ),
      child: child,
    );

    final Widget responsiveItem =
        expandHeight ? IntrinsicHeight(child: content) : content;

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(AppSpacing.md),
        child: responsiveItem,
      );
    }

    return responsiveItem;
  }
}
