import 'package:flutter/material.dart';
import '../screens/settings_screen.dart';
import '../screens/web/web_settings_screen.dart';

/// A responsive builder that shows the appropriate settings screen based on the device size.
///
/// This widget determines whether to show the mobile or web version of the settings screen
/// based on the width of the screen.
class ResponsiveSettingsBuilder extends StatelessWidget {
  const ResponsiveSettingsBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use web layout for larger screens
        if (constraints.maxWidth >= 860) {
          return const WebSettingsScreen();
        }
        // Use mobile layout for smaller screens
        return const SettingsScreen();
      },
    );
  }
}
