import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/nsl_java_converter.dart';
import 'package:nsl/widgets/responsive_build_builder.dart';
import 'package:nsl/widgets/responsive_build_new_builder.dart';
import 'package:nsl/widgets/responsive_chat_builder.dart';
import 'package:nsl/widgets/responsive_components_builder.dart';
import 'package:nsl/widgets/responsive_settings_builder.dart';
import 'package:nsl/widgets/responsive_transact_builder.dart';
import 'package:nsl/widgets/responsive_widget_binder.dart';
import 'package:nsl/screens/auth/profile_screen.dart';

/// Provider to manage the current screen content in the web app
class ScreenContentProvider extends ChangeNotifier {
  // Current screen identifier
  String _currentScreen = 'create';

  // Current screen widget
  Widget _currentContent = const ResponsiveBuildBuilder();

  // Arguments for the current screen
  Map<String, dynamic>? _arguments;

  // Getters
  String get currentScreen => _currentScreen;
  Widget get currentContent => _currentContent;
  Map<String, dynamic>? get arguments => _arguments;

  /// Update the current screen and content
  void setScreen(String screenName, {Map<String, dynamic>? arguments}) {
    _currentScreen = screenName;
    _arguments = arguments;

    // Set the appropriate content based on the screen name
    switch (screenName) {
      case 'chat':
        _currentContent = const ResponsiveChatBuilder();
        break;
      case 'create':
        _currentContent = const ResponsiveBuildBuilder();
        break;
      case 'transact':
        _currentContent = const ResponsiveTransactBuilder();
        break;
      case 'build':
        _currentContent = const ResponsiveBuildNewBuilder();
        break;
      case 'settings':
        _currentContent = const ResponsiveSettingsBuilder();
        break;

      case 'code':
        _currentContent = NslJavaConverter();
        break;
      case 'components':
        _currentContent = const ResponsiveComponentsBuilder();
        break;
      case 'widgetBinder':
        _currentContent = const ResponsiveWidgetBinderBuilder();
        break;
      case 'profile':
        _currentContent = const ProfileScreen();
        break;
      default:
        _currentContent = const ResponsiveBuildBuilder();
    }

    notifyListeners();
  }
}
