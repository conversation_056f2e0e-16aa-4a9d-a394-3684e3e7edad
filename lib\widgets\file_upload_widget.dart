import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:nsl/services/file_upload_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';

class FileUploadWidget extends StatefulWidget {
  /// Callback function when a file is successfully uploaded
  final Function(Map<String, dynamic>) onFileUploaded;

  /// Optional callback function when file selection is canceled
  final VoidCallback? onCanceled;

  /// Optional button text
  final String buttonText;

  /// Optional icon
  final IconData? icon;

  const FileUploadWidget({
    super.key,
    required this.onFileUploaded,
    this.onCanceled,
    this.buttonText = 'Upload File',
    this.icon = Icons.upload_file,
  });

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  final FileUploadService _fileUploadService = FileUploadService();
  bool _isUploading = false;
  String? _errorMessage;
  PlatformFile? _selectedFile;

  Future<void> _pickAndUploadFile() async {
    setState(() {
      _isUploading = true;
      _errorMessage = null;
    });

    try {
      // Pick a file
      final file = await _fileUploadService.pickFile();

      if (file == null) {
        // User canceled the picker
        setState(() {
          _isUploading = false;
        });

        if (widget.onCanceled != null) {
          widget.onCanceled!();
        }

        return;
      }

      setState(() {
        _selectedFile = file;
      });

      // Upload the file
      final result = await _fileUploadService.uploadFile(file);

      if (result['success']) {
        // Call the callback with the result
        widget.onFileUploaded(result);
      } else {
        setState(() {
          _errorMessage = result['message'];
        });
      }
    } catch (e) {
      Logger.error('Error in file upload widget: $e');
      setState(() {
        _errorMessage = 'An error occurred: $e';
      });
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ElevatedButton.icon(
          onPressed: _isUploading ? null : _pickAndUploadFile,
          icon: _isUploading
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Icon(widget.icon),
          label: Text(_isUploading ? 'Uploading...' : widget.buttonText),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
          ),
        ),
        if (_selectedFile != null && !_isUploading)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 16),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Selected: ${_selectedFile!.name}',
                    style: TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        if (_errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red, fontSize: 14),
            ),
          ),
      ],
    );
  }
}
