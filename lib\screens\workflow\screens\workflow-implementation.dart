import 'dart:math';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_svg/svg.dart';

// Model classes
enum NodeType { start, approval, email, end }

class WorkflowNode {
  final String id;
  final String title;
  final NodeType type;
  final Offset position;
  final String actionLabel;
  final Map<String, dynamic>? lodetails;
  final int count;

  const WorkflowNode({
    required this.id,
    required this.title,
    required this.type,
    required this.position,
    required this.actionLabel,
    required this.count,
    this.lodetails,
  });

  WorkflowNode copyWith({
    String? id,
    String? title,
    NodeType? type,
    Offset? position,
    String? actionLabel,
    Map<String, dynamic>? lodetails,
    int? count,
  }) {
    return WorkflowNode(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      position: position ?? this.position,
      actionLabel: actionLabel ?? this.actionLabel,
      lodetails: lodetails ?? this.lodetails,
      count: count ?? this.count,
    );
  }
}

class WorkflowConnection {
  final String sourceId;
  final String targetId;
  final String? label;
  final Color? color;
  final bool isDashed;

  const WorkflowConnection({
    required this.sourceId,
    required this.targetId,
    this.label,
    this.color,
    this.isDashed = false,
  });
}

class AdvancedWorkflowBuilder extends StatefulWidget {
  final bool isFullScreen;
  final Function(WorkflowNode)? onNodeSelected;

  const AdvancedWorkflowBuilder({
    super.key,
    this.isFullScreen = true,
    this.onNodeSelected,
  });

  @override
  AdvancedWorkflowBuilderState createState() => AdvancedWorkflowBuilderState();
}

class AdvancedWorkflowBuilderState extends State<AdvancedWorkflowBuilder> {
  // Store workflow nodes and connections
  List<WorkflowNode> nodes = [];
  List<WorkflowConnection> connections = [];

  // Getter for nodes to allow parent widgets to access them
  List<WorkflowNode> getNodes() => nodes;
  String? selectedNodeId;
  String? sourceNodeForConnection;

  // Track the node selected for details panel
  WorkflowNode? selectedNodeForDetails;
  // Control the side panel visibility
  bool showSidePanel = false;

  @override
  void initState() {
    super.initState();
    // Initialize with workflow from JSON file
    _loadWorkflow();
  }

  // Load workflow data asynchronously
  Future<void> _loadWorkflow() async {
    await _initializeAdvancedWorkflow();
    if (mounted) {
      setState(() {}); // Refresh UI after loading
    }
  }

  Future<void> _initializeAdvancedWorkflow() async {
    try {
      // Load the JSON from the asset file
      final String jsonString =
          await rootBundle.loadString('assets/data/workflow_data.json');

      // Parse the JSON data
      final Map<String, dynamic> workflowData = jsonDecode(jsonString);

      // Parse nodes
      final List<dynamic> nodesData = workflowData['nodes'];
      nodes = nodesData.map((nodeData) {
        // Parse node type
        NodeType nodeType;
        switch (nodeData['type']) {
          case 'start':
            nodeType = NodeType.start;
            break;
          case 'approval':
            nodeType = NodeType.approval;
            break;
          case 'email':
            nodeType = NodeType.email;
            break;
          case 'end':
            nodeType = NodeType.end;
            break;
          default:
            nodeType = NodeType.start;
        }

        // Parse position
        final position = Offset(
          nodeData['position']['x'].toDouble(),
          nodeData['position']['y'].toDouble(),
        );

        // Create WorkflowNode
        return WorkflowNode(
          id: nodeData['id'],
          title: nodeData['title'],
          type: nodeType,
          position: position,
          actionLabel: nodeData['actionLabel'],
          count: nodeData['count'],
          lodetails: nodeData['lodetails'],
        );
      }).toList();

      // Parse connections
      final List<dynamic> connectionsData = workflowData['connections'];
      connections = connectionsData.map((connectionData) {
        // Parse color
        Color? color;
        switch (connectionData['color']) {
          case 'blue':
            color = Colors.blue;
            break;
          case 'orange':
            color = Colors.orange;
            break;
          case 'green':
            color = Colors.green;
            break;
          case 'red':
            color = Colors.red;
            break;
          case 'purple':
            color = Colors.purple;
            break;
          default:
            color = Colors.blue;
        }

        // Create WorkflowConnection
        return WorkflowConnection(
          sourceId: connectionData['sourceId'],
          targetId: connectionData['targetId'],
          label: connectionData['label'],
          color: color,
          isDashed: connectionData['isDashed'] ?? false,
        );
      }).toList();
    } catch (e) {
      // Use debugPrint for logging in development
      debugPrint('Error parsing workflow JSON: $e');
      // Fallback to empty lists if parsing fails
      nodes = [];
      connections = [];
    }
  }

  @override
  Widget build(BuildContext context) {
    // No need to calculate width as we're using Positioned widget

    return Stack(
      children: [
        // Main workflow area
        SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: Stack(
            children: [
              // SvgPicture.asset(
              //   'assets/images/dot_bg.svg',
              //   fit: BoxFit.fill,
              //   height: MediaQuery.of(context).size.height,
              //   width: MediaQuery.of(context).size.width,
              // ),
              widget.isFullScreen
                  ? SizedBox()
                  : SvgPicture.asset(
                      'assets/images/dot_bg_1.svg',
                      fit: BoxFit.fill,
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                    ),
              // Connections painter
              CustomPaint(
                size: Size.infinite,
                painter: AdvancedConnectionPainter(
                  nodes: nodes,
                  connections: connections,
                ),
              ),

              // Nodes
              ...nodes.map((node) => _buildDraggableNode(node)),
            ],
          ),
        ),

        // Side panel for node details - positioned on the right side
        if (showSidePanel && selectedNodeForDetails != null)
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: 350,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 8,
                    offset: const Offset(-2, 0),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 16),
                    child: Row(
                      children: [
                        _buildNodeIcon(selectedNodeForDetails!.type),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            selectedNodeForDetails!.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            setState(() {
                              showSidePanel = false;
                              selectedNodeForDetails = null;
                            });
                          },
                        ),
                      ],
                    ),
                  ),

                  const Divider(),

                  // Node details
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic information section
                          Text(
                            'Basic Information',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _buildInfoRow('Node ID', selectedNodeForDetails!.id),
                          _buildInfoRow(
                              'Type',
                              selectedNodeForDetails!.type
                                  .toString()
                                  .split('.')
                                  .last),
                          _buildInfoRow(
                              'Action', selectedNodeForDetails!.actionLabel),
                          _buildInfoRow('Count',
                              selectedNodeForDetails!.count.toString()),

                          const SizedBox(height: 24),

                          // Node details section (if available)
                          if (selectedNodeForDetails!.lodetails != null) ...[
                            Text(
                              'Detailed Information',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 12),
                            ...selectedNodeForDetails!.lodetails!.entries
                                .where((entry) => entry.key != 'inputs')
                                .map((entry) => _buildInfoRow(
                                    entry.key, entry.value.toString())),

                            // Show inputs section if available
                            if (selectedNodeForDetails!.lodetails!
                                    .containsKey('inputs') &&
                                selectedNodeForDetails!.lodetails!['inputs']
                                    is List) ...[
                              const SizedBox(height: 16),
                              const Text(
                                'Inputs',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              (selectedNodeForDetails!.lodetails!['inputs']
                                          as List)
                                      .isEmpty
                                  ? const Text('No inputs defined')
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: (selectedNodeForDetails!
                                              .lodetails!['inputs'] as List)
                                          .map<Widget>((input) {
                                        if (input is Map) {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: Text(
                                                '${input['name'] ?? 'Unnamed input'}: ${input['type'] ?? 'No type'}'),
                                          );
                                        }
                                        return const SizedBox.shrink();
                                      }).toList(),
                                    ),
                            ],
                            const SizedBox(height: 24),
                          ],

                          // Connections section
                          Text(
                            'Connections',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 12),

                          // Outgoing connections
                          ..._buildConnectionsList(selectedNodeForDetails!,
                              isOutgoing: true),

                          const SizedBox(height: 12),

                          // Incoming connections
                          ..._buildConnectionsList(selectedNodeForDetails!,
                              isOutgoing: false),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDraggableNode(WorkflowNode node) {
    final isSelected = node.id == selectedNodeId;

    return Positioned(
      left: node.position.dx,
      top: node.position.dy,
      child: GestureDetector(
        onTap: () {
          setState(() {
            if (sourceNodeForConnection != null) {
              // If we're in connection mode, create a connection
              if (sourceNodeForConnection != node.id) {
                connections.add(WorkflowConnection(
                  sourceId: sourceNodeForConnection!,
                  targetId: node.id,
                  color: Theme.of(context).colorScheme.primary,
                ));
              }
              sourceNodeForConnection = null;
            } else {
              selectedNodeId = node.id;
              // Show bottom sheet with node details
              _showNodeDetailsBottomSheet(context, node);
            }
          });
        },
        onPanUpdate: (details) {
          setState(() {
            final index = nodes.indexWhere((n) => n.id == node.id);
            if (index >= 0) {
              nodes[index] = nodes[index].copyWith(
                position: Offset(
                  nodes[index].position.dx + details.delta.dx,
                  nodes[index].position.dy + details.delta.dy,
                ),
              );
            }
          });
        },
        child: _buildNodeCard(node, isSelected),
      ),
    );
  }

  Widget _buildNodeCard(WorkflowNode node, bool isSelected) {
    // Build the node card UI based on type
    final cardWidth = 180.0;

    // Determine height based on type and if expanded (for approval)
    // double cardHeight = 45.0;
    // if (node.type == NodeType.approval && isSelected && node.metadata != null) {
    //   cardHeight = 300.0; // Expanded height for approval with details
    // }

    // Create the node content
    final nodeContent = Container(
      width: cardWidth,
      // height: cardHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? Theme.of(context).colorScheme.primary.withAlpha(50)
                : Theme.of(context).colorScheme.primary.withAlpha(25),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
        border: Border.all(
          color:
              isSelected ? Colors.transparent : Theme.of(context).dividerColor,
          width: isSelected ? 4 : 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.all(
                Radius.circular(10),

              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildNodeIcon(node.type),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    node.title,
                    style: TextStyle(fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  '${node.count}',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Action button
          // // Padding(
          // //   padding: EdgeInsets.all(12),
          // //   child: Row(
          // //     children: [
          // //       Expanded(
          // //         child: Container(
          // //           padding: EdgeInsets.symmetric(vertical: 8),
          // //           decoration: BoxDecoration(
          // //             border: Border.all(color: Colors.grey.shade300),
          // //             borderRadius: BorderRadius.circular(4),
          // //           ),
          // //           child: Center(
          // //               child: Text(
          // //             node.actionLabel,
          // //             maxLines: 1,
          // //           )),
          // //         ),
          // //       ),
          // //       SizedBox(width: 8),
          // //       Container(
          // //         width: 30,
          // //         height: 30,
          // //         decoration: BoxDecoration(
          // //           color: Colors.blue,
          // //           borderRadius: BorderRadius.circular(4),
          // //         ),
          // //         child: Icon(Icons.check, color: Colors.white, size: 16),
          // //       ),
          // //     ],
          // //   ),
          // // ),

          // Connection Label
          // Padding(
          //   padding: EdgeInsets.only(top: 1, bottom: 1),
          //   child: Container(
          //     padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          //     // decoration: BoxDecoration(
          //     //   color: Colors.green.shade50,
          //     //   borderRadius: BorderRadius.circular(10),
          //     //   border: Border.all(color: Colors.green.shade100),
          //     // ),
          //     child: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         // Text(
          //         //   'Bits count: ${node.count}',
          //         //   style: TextStyle(
          //         //     color: Theme.of(context).colorScheme.primary,
          //         //     fontSize: 12,
          //         //   ),
          //         // ),
          //         // SizedBox(width: 4),
          //         // Container(
          //         //   width: 6,
          //         //   height: 6,
          //         //   decoration: BoxDecoration(
          //         //     color: Colors.green,
          //         //     shape: BoxShape.circle,
          //         //   ),
          //         // ),
          //       ],
          //     ),
          //   ),
          // ),

          // // // Expanded details for approval type when selected
          // // if (node.type == NodeType.approval &&
          // //     isSelected &&
          // //     node.metadata != null)
          // //   Expanded(
          //     child: _buildDetailedInformation(node),
          //   ),
        ],
      ),
    );

    // If selected, wrap with dotted border
    if (isSelected) {
      return CustomPaint(
        painter: DottedBorderPainter(
          color: Theme.of(context).colorScheme.primary,
          strokeWidth: 5.0,
          gap: 0.0,
        ),
        child: nodeContent,
      );
    }

    return nodeContent;
  }

  // Removed unused method

  Widget _buildNodeIcon(NodeType type) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final secondaryColor = theme.colorScheme.secondary;
    final errorColor = theme.colorScheme.error;
    final successColor = theme.colorScheme.primary;
    final onPrimaryColor = theme.colorScheme.onPrimary;

    Color getNodeColor(NodeType type) {
      switch (type) {
        case NodeType.start:
          return successColor;
        case NodeType.approval:
          return primaryColor;
        case NodeType.email:
          return secondaryColor;
        case NodeType.end:
          return errorColor;
      }
    }

    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: getNodeColor(type),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getNodeIconData(type),
        color: onPrimaryColor,
        size: 16,
      ),
    );
  }

  IconData _getNodeIconData(NodeType type) {
    switch (type) {
      case NodeType.start:
        return Icons.play_arrow;
      case NodeType.approval:
        return Icons.check_circle_outline;
      case NodeType.email:
        return Icons.email;
      case NodeType.end:
        return Icons.stop;
    }

    // Re
  }

  // Removed unused method

  // Removed unused method

  // Removed unused methods

  // Removed unused method

  // Method to show node details in a bottom sheet or side panel based on platform
  void _showNodeDetailsBottomSheet(BuildContext context, WorkflowNode node) {
    if (kIsWeb) {
      // For web, update state to show the side panel
      _showNodeDetailsSidePanel(context, node);
    } else {
      // For mobile, show a bottom sheet
      _showNodeDetailsBottomSheetMobile(context, node);
    }
  }

  // Method to show node details in a bottom sheet for mobile
  void _showNodeDetailsBottomSheetMobile(
      BuildContext context, WorkflowNode node) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.4,
          minChildSize: 0.2,
          maxChildSize: 0.8,
          builder: (context, scrollController) {
            final theme = Theme.of(context);
            final surfaceColor = theme.colorScheme.surface;
            final shadowColor = theme.shadowColor.withAlpha(25);

            return Container(
              decoration: BoxDecoration(
                color: surfaceColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: shadowColor,
                    blurRadius: 10,
                    offset: Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Handle bar for dragging
                  Center(
                    child: Container(
                      margin: EdgeInsets.only(top: 12, bottom: 8),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: theme.dividerColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),

                  // Node title and icon
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    child: Row(
                      children: [
                        _buildNodeIcon(node.type),
                        SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            node.title,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                  ),

                  Divider(),

                  // Node details
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      padding: EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic information section
                          Text(
                            'Basic Information',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          SizedBox(height: 12),
                          _buildInfoRow('Node ID', node.id),
                          _buildInfoRow(
                              'Type', node.type.toString().split('.').last),
                          _buildInfoRow('Action', node.actionLabel),
                          _buildInfoRow('Count', node.count.toString()),

                          SizedBox(height: 24),

                          // Node details section (if available)
                          if (node.lodetails != null) ...[
                            Text(
                              'Detailed Information',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            SizedBox(height: 12),
                            ...node.lodetails!.entries
                                .where((entry) => entry.key != 'inputs')
                                .map((entry) => _buildInfoRow(
                                    entry.key, entry.value.toString())),

                            // Show inputs section if available
                            if (node.lodetails!.containsKey('inputs') &&
                                node.lodetails!['inputs'] is List) ...[
                              SizedBox(height: 16),
                              Text(
                                'Inputs',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              (node.lodetails!['inputs'] as List).isEmpty
                                  ? Text('No inputs defined')
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children:
                                          (node.lodetails!['inputs'] as List)
                                              .map<Widget>((input) {
                                        if (input is Map) {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: Text(
                                                '${input['name'] ?? 'Unnamed input'}: ${input['type'] ?? 'No type'}'),
                                          );
                                        }
                                        return SizedBox.shrink();
                                      }).toList(),
                                    ),
                            ],
                            SizedBox(height: 24),
                          ],

                          // Connections section
                          Text(
                            'Connections',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          SizedBox(height: 12),

                          // Outgoing connections
                          ..._buildConnectionsList(node, isOutgoing: true),

                          SizedBox(height: 12),

                          // Incoming connections
                          ..._buildConnectionsList(node, isOutgoing: false),

                          SizedBox(height: 24),

                          // Action buttons
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          //   children: [
                          //     ElevatedButton.icon(
                          //       onPressed: () {
                          //         Navigator.pop(context);
                          //         // Add edit functionality here
                          //       },
                          //       icon: Icon(Icons.edit),
                          //       label: Text('Edit Node'),
                          //       style: ElevatedButton.styleFrom(
                          //         foregroundColor: Colors.white,
                          //         backgroundColor: Colors.blue,
                          //       ),
                          //     ),
                          //     ElevatedButton.icon(
                          //       onPressed: () {
                          //         Navigator.pop(context);
                          //         _deleteSelectedNode();
                          //       },
                          //       icon: Icon(Icons.delete),
                          //       label: Text('Delete'),
                          //       style: ElevatedButton.styleFrom(
                          //         foregroundColor: Colors.white,
                          //         backgroundColor: Colors.red,
                          //       ),
                          //     ),
                          //   ],
                          // ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Method to show node details in a side panel for web
  void _showNodeDetailsSidePanel(BuildContext context, WorkflowNode node) {
    // If we have an onNodeSelected callback, use it
    if (widget.onNodeSelected != null) {
      widget.onNodeSelected!(node);
    } else {
      // Otherwise, update state to show the side panel with the selected node
      setState(() {
        selectedNodeForDetails = node;
        showSidePanel = true;
      });
    }
  }

  // Helper method to build info rows in the bottom sheet
  Widget _buildInfoRow(String label, String value) {
    final theme = Theme.of(context);
    final labelColor =
        theme.colorScheme.onSurface.withAlpha(179); // 0.7 opacity = 179 alpha
    final valueColor = theme.colorScheme.onSurface;

    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: labelColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build connections list
  List<Widget> _buildConnectionsList(WorkflowNode node,
      {required bool isOutgoing}) {
    final relevantConnections = connections
        .where((conn) =>
            isOutgoing ? conn.sourceId == node.id : conn.targetId == node.id)
        .toList();

    if (relevantConnections.isEmpty) {
      return [
        Text(
          isOutgoing ? 'No outgoing connections' : 'No incoming connections',
          style: TextStyle(
            fontStyle: FontStyle.italic,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
          ),
        ),
      ];
    }

    return [
      Text(
        isOutgoing ? 'Outgoing:' : 'Incoming:',
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
      SizedBox(height: 8),
      ...relevantConnections.map((conn) {
        final otherNodeId = isOutgoing ? conn.targetId : conn.sourceId;
        final otherNode = nodes.firstWhere(
          (n) => n.id == otherNodeId,
          orElse: () => nodes.first,
        );

        return Padding(
          padding: EdgeInsets.only(left: 16, bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(top: 5),
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: conn.color ?? Colors.blue,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${otherNode.title} ${conn.label != null ? "(${conn.label})" : ""}',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              if (conn.isDashed)
                Padding(
                  padding: EdgeInsets.only(left: 8),
                  child: Text(
                    'Alternative',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withAlpha(153),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],
          ),
        );
      }),
    ];
  }

  // Removed unused methods
}

class AdvancedConnectionPainter extends CustomPainter {
  final List<WorkflowNode> nodes;
  final List<WorkflowConnection> connections;

  AdvancedConnectionPainter({required this.nodes, required this.connections});

  @override
  void paint(Canvas canvas, Size size) {
    // Default connection color
    final defaultConnectionColor = Colors.blue;
    for (var connection in connections) {
      final sourceNode = nodes.firstWhere(
        (node) => node.id == connection.sourceId,
        orElse: () => nodes.first,
      );
      final targetNode = nodes.firstWhere(
        (node) => node.id == connection.targetId,
        orElse: () => nodes.first,
      );

      // Calculate center points of nodes
      final sourcePoint = Offset(
        sourceNode.position.dx + 164, // Right side of source node
        sourceNode.position.dy + 40, // Approximate middle of header area
      );
      final targetPoint = Offset(
        targetNode.position.dx, // Left side of target
        targetNode.position.dy + 40, // Approximate middle of header area
      );

      // Configure paint based on connection properties
      final paint = Paint()
        ..color = connection.color ?? defaultConnectionColor
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      if (connection.isDashed) {
        // Create dashed effect
        paint.strokeWidth = 2;
        final dashPath = Path();
        dashPath.moveTo(sourcePoint.dx, sourcePoint.dy);

        // Draw bezier curve
        final controlPoint1 = Offset(
          sourcePoint.dx + 50,
          sourcePoint.dy,
        );
        final controlPoint2 = Offset(
          targetPoint.dx - 50,
          targetPoint.dy,
        );

        dashPath.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          targetPoint.dx,
          targetPoint.dy,
        );

        // Draw dashed path
        // Commented out dashed path implementation
        // If needed, uncomment and implement with proper DashPath utility

        canvas.drawPath(dashPath, paint);
      } else {
        // Draw line
        final path = Path();
        path.moveTo(sourcePoint.dx, sourcePoint.dy);

        // Draw bezier curve for better appearance
        final controlPoint1 = Offset(
          sourcePoint.dx + 50,
          sourcePoint.dy,
        );
        final controlPoint2 = Offset(
          targetPoint.dx - 50,
          targetPoint.dy,
        );
        path.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          targetPoint.dx,
          targetPoint.dy,
        );

        canvas.drawPath(path, paint);
      }

      // Draw arrow at target
      final arrowSize = 10.0;
      final angle = atan2(
        targetPoint.dy - sourcePoint.dy,
        targetPoint.dx - sourcePoint.dx,
      );

      final arrowPath = Path();
      arrowPath.moveTo(targetPoint.dx, targetPoint.dy);
      arrowPath.lineTo(
        targetPoint.dx - arrowSize * cos(angle - pi / 6),
        targetPoint.dy - arrowSize * sin(angle - pi / 6),
      );
      arrowPath.lineTo(
        targetPoint.dx - arrowSize * cos(angle + pi / 6),
        targetPoint.dy - arrowSize * sin(angle + pi / 6),
      );
      arrowPath.close();

      canvas.drawPath(arrowPath,
          Paint()..color = connection.color ?? defaultConnectionColor);

      // Draw connection label if provided
      if (connection.label != null) {
        final midPoint = Offset(
          (sourcePoint.dx + targetPoint.dx) / 2,
          (sourcePoint.dy + targetPoint.dy) / 2,
        );

        // Draw label background
        final labelBgRect = Rect.fromCenter(
          center: midPoint,
          width: 70,
          height: 20,
        );
        canvas.drawRRect(
          RRect.fromRectAndRadius(labelBgRect, Radius.circular(10)),
          Paint()
            ..color = Colors.white.withAlpha(204), // 0.8 opacity = 204 alpha
        );

        // Draw label text
        final textSpan = TextSpan(
          text: connection.label,
          style: TextStyle(
            color: Colors
                .black87, // Keep this color for readability on white background
            fontSize: 12,
          ),
        );
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          midPoint.translate(-textPainter.width / 2, -textPainter.height / 2),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Custom painter for dotted border
class DottedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double gap;

  DottedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.gap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path();
    path.addRRect(RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(10),
    ));

    // Create a dashed effect
    final dashPath = Path();
    final dashWidth = strokeWidth * 3;
    final dashSpace = gap;
    final pathMetrics = path.computeMetrics().first;
    var distance = 0.0;

    while (distance < pathMetrics.length) {
      final extractPath =
          pathMetrics.extractPath(distance, distance + dashWidth);
      dashPath.addPath(extractPath, Offset.zero);
      distance += dashWidth + dashSpace;
    }

    canvas.drawPath(dashPath, paint);
  }

  @override
  bool shouldRepaint(covariant DottedBorderPainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.gap != gap;
  }
}

// Custom painter for dotted background
class DottedBackgroundPainter extends CustomPainter {
  final Color dotColor;
  final double dotSize;
  final double spacing;

  DottedBackgroundPainter({
    required this.dotColor,
    required this.dotSize,
    required this.spacing,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = dotColor
      ..strokeCap = StrokeCap.round
      ..strokeWidth = dotSize
      ..style = PaintingStyle.fill;

    // Calculate the number of dots based on the size and spacing
    final horizontalDots = (size.width / spacing).ceil();
    final verticalDots = (size.height / spacing).ceil();

    // Draw the dots in a grid pattern
    for (int i = 0; i < horizontalDots; i++) {
      for (int j = 0; j < verticalDots; j++) {
        final x = i * spacing;
        final y = j * spacing;
        canvas.drawCircle(Offset(x, y), dotSize / 2, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant DottedBackgroundPainter oldDelegate) {
    return oldDelegate.dotColor != dotColor ||
        oldDelegate.dotSize != dotSize ||
        oldDelegate.spacing != spacing;
  }
}

// A utility class to create dashed paths
class DashPath {
  static Path dash({
    required Path path,
    required CircularIntervalList<double> dashArray,
  }) {
    final dashPath = Path();
    // Offset is not used in this implementation but would be needed for more complex dashing

    var distance = 0.0;
    var extractedPath = path.computeMetrics().first;
    var dashLength = dashArray.next;

    while (distance < extractedPath.length) {
      var isDraw = dashArray.index.isEven;
      var nextDistance = distance + dashLength;

      if (nextDistance > extractedPath.length) {
        nextDistance = extractedPath.length;
      }

      if (isDraw) {
        dashPath.addPath(
          extractedPath.extractPath(distance, nextDistance),
          Offset.zero,
        );
      }

      distance = nextDistance;
      dashLength = dashArray.next;
    }

    return dashPath;
  }
}

// A helper class for circular iteration
class CircularIntervalList<T> {
  final List<T> _items;
  int _index = 0;

  CircularIntervalList(this._items);

  int get index => _index;

  T get next {
    if (_items.isEmpty) {
      throw StateError('Cannot get an element from an empty list');
    }

    final item = _items[_index];
    _index = (_index + 1) % _items.length;
    return item;
  }
}
