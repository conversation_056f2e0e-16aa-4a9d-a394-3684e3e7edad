import 'package:nsl/utils/response_text_parser.dart';

/// Example usage of ResponseTextParser
class ResponseTextParserExample {
  
  /// Example method showing how to use the parser in web_create_solution_screen.dart
  static void demonstrateUsage() {
    // Example response text with before and after content
    String responseText = '''I notice your input appears to be non-standard text (\\"adsfsaf\\"). As an expert Solution Architect, I'll help initiate our requirements gathering with a structured approach.\\n\\nLet me start by analyzing your domain context.\\n\\n1. **DOMAIN INTELLIGENCE**\\n- Unable to detect specific domain from initial input\\n- Will start with foundational business context discovery\\n- Prepared to apply expertise across multiple industries\\n\\nThis analysis helps us understand your business context better.\\n\\n2. **GRANULAR RECOMMENDATIONS**\\nInitial focus areas should include:\\n- Core business model definition\\n- Primary revenue streams\\n- Key stakeholder identification\\n- Critical business operations\\n- Compliance framework selection\\n\\nThese recommendations will guide our next steps.\\n\\n3. **MULTI-AREA EXTRACTION**\\nCurrently at initial stage requiring foundational context\\n\\n4. **COMPLETION PROGRESS**\\nRequirements gathering: 0% complete\\n- Foundation: Not started\\n- Organization: Not started\\n- Data: Not started\\n- Functions: Not started\\n- Intelligence: Not started\\n\\n5. **STRATEGIC NEXT QUESTION**\\nTo begin requirements gathering effectively, please describe your primary business operation and industry focus. Consider these specific aspects:\\n\\nRecommendations:\\n1. Define your main product/service offering and target market\\n2. Identify 2-3 key business processes that generate revenue''';

    // Parse the response text
    Map<String, dynamic> parsedData = ResponseTextParser.parseStructuredResponse(responseText);
    
    // Demonstrate content blocks functionality
    print('=== CONTENT BLOCKS DEMONSTRATION ===\n');
    
    // Get all content blocks in order
    List<Map<String, dynamic>> contentBlocks = ResponseTextParser.getContentBlocks(parsedData);
    
    print('CONTENT BLOCKS IN ORDER:');
    for (int i = 0; i < contentBlocks.length; i++) {
      Map<String, dynamic> block = contentBlocks[i];
      print('${i + 1}. Type: ${block['type']} | Order: ${block['order']}');
      
      if (block['type'] == 'before_sections' || block['type'] == 'before_section' || block['type'] == 'after_section') {
        List<String> content = List<String>.from(block['content'] ?? []);
        for (String line in content) {
          print('   - $line');
        }
      } else if (block['type'] == 'section') {
        print('   Section: ${block['sectionNumber']}. ${block['sectionTitle']}');
      } else if (block['type'] == 'recommendations') {
        List<String> recommendations = List<String>.from(block['content'] ?? []);
        for (String rec in recommendations) {
          print('   - $rec');
        }
      }
      print('');
    }
    
    // Demonstrate section content objects
    print('=== SECTION CONTENT OBJECTS ===\n');
    List<Map<String, dynamic>> sectionObjects = ResponseTextParser.createSectionContentObjects(parsedData);
    
    for (Map<String, dynamic> sectionObj in sectionObjects) {
      print('Section ${sectionObj['sectionNumber']}: ${sectionObj['sectionTitle']}');
      
      List<String> beforeContent = List<String>.from(sectionObj['beforeContent'] ?? []);
      if (beforeContent.isNotEmpty) {
        print('  Before Content:');
        for (String line in beforeContent) {
          print('    - $line');
        }
      }
      
      List<String> afterContent = List<String>.from(sectionObj['afterContent'] ?? []);
      if (afterContent.isNotEmpty) {
        print('  After Content:');
        for (String line in afterContent) {
          print('    - $line');
        }
      }
      print('');
    }
    
    // Demonstrate sequential content separation
    print('=== SEQUENTIAL CONTENT DEMONSTRATION ===\n');
    
    // Get content in display order
    Map<String, dynamic> displayOrder = ResponseTextParser.getContentInDisplayOrder(parsedData);
    
    print('1. CONTENT BEFORE SECTIONS:');
    List<String> beforeSections = ResponseTextParser.getContentBeforeSections(parsedData);
    for (int i = 0; i < beforeSections.length; i++) {
      print('   ${i + 1}. ${beforeSections[i]}');
    }
    
    print('\n2. STRUCTURED SECTIONS (in order):');
    List<Map<String, dynamic>> sections = displayOrder['sections'];
    for (Map<String, dynamic> section in sections) {
      print('   ${section['number']}. ${section['title']}');
    }
    
    print('\n3. RECOMMENDATIONS (appears last):');
    List<String> recommendations = displayOrder['recommendations'];
    for (int i = 0; i < recommendations.length; i++) {
      print('   ${i + 1}. ${recommendations[i]}');
    }
    
    // Print the formatted output
    print('\n' + ResponseTextParser.formatParsedData(parsedData));
    
    // Get specific sections
    Map<String, dynamic>? domainSection = ResponseTextParser.getSection(parsedData, 'DOMAIN INTELLIGENCE');
    if (domainSection != null) {
      print('\n=== DOMAIN INTELLIGENCE SECTION ===');
      print('Number: ${domainSection['number']}');
      print('Title: ${domainSection['title']}');
      print('Before Content: ${domainSection['beforeContent']}');
      print('After Content: ${domainSection['afterContent']}');
      print('Subsections: ${domainSection['subsections']}');
    }
    
    // Get summary
    Map<String, dynamic> summary = ResponseTextParser.createSummary(parsedData);
    print('\n=== SUMMARY ===');
    print('Total Sections: ${summary['totalSections']}');
    print('Total Recommendations: ${summary['totalRecommendations']}');
    print('Total Other Content: ${summary['totalOtherContent']}');
    print('Has Structured Sections: ${summary['hasStructuredSections']}');
    print('Section Titles: ${summary['sectionTitles']}');
    
    // Convert to JSON
    String jsonOutput = ResponseTextParser.toJson(parsedData);
    print('\n=== JSON OUTPUT ===');
    print(jsonOutput);
  }

  /// Method to integrate with web_create_solution_screen.dart
  static Map<String, dynamic> parseResponseForUI(String responseText) {
    // Parse the response text
    Map<String, dynamic> parsedData = ResponseTextParser.parseStructuredResponse(responseText);
    
    // Create a UI-friendly structure
    Map<String, dynamic> uiData = {
      'hasError': parsedData.containsKey('error'),
      'errorMessage': parsedData['error'] ?? '',
      'sections': [],
      'recommendations': parsedData['recommendations'] ?? [],
      'otherContent': parsedData['otherContent'] ?? [],
      'hasStructuredSections': parsedData['hasStructuredSections'] ?? false,
      'summary': ResponseTextParser.createSummary(parsedData),
    };

    // Process sections for UI display
    List<Map<String, dynamic>> sections = parsedData['sections'] ?? [];
    for (Map<String, dynamic> section in sections) {
      Map<String, dynamic> uiSection = {
        'id': 'section_${section['number']}',
        'number': section['number'],
        'title': section['title'],
        'points': <String>[],
        'subsections': <Map<String, dynamic>>[],
        'order': section['order'] ?? 0,
      };

      // Process subsections
      Map<String, List<String>> subsections = Map<String, List<String>>.from(section['subsections'] ?? {});
      for (String subsectionKey in subsections.keys) {
        if (subsectionKey.isNotEmpty) {
          List<String> points = subsections[subsectionKey] ?? [];
          uiSection['subsections'].add({
            'title': subsectionKey,
            'points': points,
          });
          // Also add to main points list
          uiSection['points'].addAll(points);
        }
      }

      uiData['sections'].add(uiSection);
    }

    return uiData;
  }

  /// Method to create expandable UI widgets from parsed data
  static List<Map<String, dynamic>> createExpandableWidgetData(String responseText) {
    Map<String, dynamic> uiData = parseResponseForUI(responseText);
    List<Map<String, dynamic>> widgetData = [];

    if (uiData['hasError']) {
      widgetData.add({
        'type': 'error',
        'title': 'Parsing Error',
        'content': uiData['errorMessage'],
        'isExpandable': false,
      });
      return widgetData;
    }

    // Add sections as expandable widgets
    List<Map<String, dynamic>> sections = uiData['sections'] ?? [];
    for (Map<String, dynamic> section in sections) {
      widgetData.add({
        'type': 'section',
        'id': section['id'],
        'title': '${section['number']}. ${section['title']}',
        'points': section['points'],
        'subsections': section['subsections'],
        'isExpandable': true,
        'isExpanded': false, // Default to collapsed
      });
    }

    // Add recommendations as a separate widget
    List<String> recommendations = uiData['recommendations'] ?? [];
    if (recommendations.isNotEmpty) {
      widgetData.add({
        'type': 'recommendations',
        'id': 'recommendations',
        'title': 'Recommendations',
        'points': recommendations,
        'isExpandable': true,
        'isExpanded': true, // Default to expanded for recommendations
      });
    }

    return widgetData;
  }

  /// Method to get progress information from parsed data
  static Map<String, dynamic> extractProgressInfo(String responseText) {
    Map<String, dynamic> parsedData = ResponseTextParser.parseStructuredResponse(responseText);
    
    // Look for completion progress section
    Map<String, dynamic>? progressSection = ResponseTextParser.getSection(parsedData, 'COMPLETION PROGRESS');
    
    Map<String, dynamic> progressInfo = {
      'hasProgress': false,
      'overallProgress': '0%',
      'components': <Map<String, String>>[],
    };

    if (progressSection != null) {
      progressInfo['hasProgress'] = true;
      
      // Extract progress information from subsections
      Map<String, List<String>> subsections = Map<String, List<String>>.from(progressSection['subsections'] ?? {});
      
      for (String key in subsections.keys) {
        List<String> points = subsections[key] ?? [];
        
        // Look for overall progress percentage
        for (String point in points) {
          if (point.contains('%')) {
            RegExp percentageRegex = RegExp(r'(\d+)%');
            Match? match = percentageRegex.firstMatch(point);
            if (match != null) {
              progressInfo['overallProgress'] = '${match.group(1)}%';
            }
          }
          
          // Extract component progress
          if (point.contains(':')) {
            List<String> parts = point.split(':');
            if (parts.length == 2) {
              progressInfo['components'].add({
                'name': parts[0].trim().replaceAll('- ', ''),
                'status': parts[1].trim(),
              });
            }
          }
        }
      }
    }

    return progressInfo;
  }

  /// Demonstrates the new splitting functionality
  static void demonstrateSplitByNumberedSections() {
    String responseText = '''I notice your input appears to be non-standard text (\\"adsfsaf\\"). As an expert Solution Architect, I'll help initiate our requirements gathering with a structured approach.\\n\\nLet me start by analyzing your domain context.\\n\\n1. **DOMAIN INTELLIGENCE**\\n- Unable to detect specific domain from initial input\\n- Will start with foundational business context discovery\\n- Prepared to apply expertise across multiple industries\\n\\nThis analysis helps us understand your business context better.\\n\\n2. **GRANULAR RECOMMENDATIONS**\\nInitial focus areas should include:\\n- Core business model definition\\n- Primary revenue streams\\n- Key stakeholder identification\\n- Critical business operations\\n- Compliance framework selection\\n\\nThese recommendations will guide our next steps.\\n\\n3. **MULTI-AREA EXTRACTION**\\nCurrently at initial stage requiring foundational context\\n\\n4. **COMPLETION PROGRESS**\\nRequirements gathering: 0% complete\\n- Foundation: Not started\\n- Organization: Not started\\n- Data: Not started\\n- Functions: Not started\\n- Intelligence: Not started\\n\\n5. **STRATEGIC NEXT QUESTION**\\nTo begin requirements gathering effectively, please describe your primary business operation and industry focus. Consider these specific aspects:\\n\\nRecommendations:\\n1. Define your main product/service offering and target market\\n2. Identify 2-3 key business processes that generate revenue''';

    print('=== SPLIT BY NUMBERED SECTIONS DEMONSTRATION ===\n');
    
    // Method 1: Full split with metadata
    List<Map<String, dynamic>> splitResult = ResponseTextParser.splitByNumberedSections(responseText);
    
    print('1. FULL SPLIT RESULT:');
    for (int i = 0; i < splitResult.length; i++) {
      Map<String, dynamic> part = splitResult[i];
      print('Part ${i + 1}:');
      print('  Type: ${part['type']}');
      print('  Section Number: ${part['sectionNumber'] ?? 'N/A'}');
      print('  Start Index: ${part['startIndex']}');
      print('  End Index: ${part['endIndex']}');
      print('  Content Preview: ${(part['content'] ?? '').substring(0, (part['content'] ?? '').length > 100 ? 100 : (part['content'] ?? '').length)}...');
      print('');
    }
    
    // Method 2: Simple split (content only)
    List<String> simpleSplit = ResponseTextParser.splitByNumberedSectionsSimple(responseText);
    
    print('2. SIMPLE SPLIT RESULT:');
    for (int i = 0; i < simpleSplit.length; i++) {
      print('Part ${i + 1}: ${simpleSplit[i].substring(0, simpleSplit[i].length > 100 ? 100 : simpleSplit[i].length)}...');
      print('');
    }
    
    // Method 3: Get content before first section
    String beforeFirstSection = ResponseTextParser.getContentBeforeFirstSection(responseText);
    print('3. CONTENT BEFORE FIRST SECTION:');
    print(beforeFirstSection);
    print('');
    
    // Method 4: Get all numbered sections
    List<Map<String, dynamic>> numberedSections = ResponseTextParser.getNumberedSections(responseText);
    print('4. ALL NUMBERED SECTIONS:');
    for (Map<String, dynamic> section in numberedSections) {
      print('Section ${section['sectionNumber']}:');
      print('  Content: ${(section['content'] ?? '').substring(0, (section['content'] ?? '').length > 150 ? 150 : (section['content'] ?? '').length)}...');
      print('');
    }
  }

  /// Example usage for chat message bubble integration
  static Map<String, dynamic> parseForChatBubble(String responseText) {
    // Use the new splitting method for better control
    List<Map<String, dynamic>> splitResult = ResponseTextParser.splitByNumberedSections(responseText);
    
    Map<String, dynamic> chatBubbleData = {
      'beforeContent': '',
      'sections': <Map<String, dynamic>>[],
      'hasStructuredContent': false,
    };
    
    for (Map<String, dynamic> part in splitResult) {
      if (part['type'] == 'before_content') {
        chatBubbleData['beforeContent'] = part['content'] ?? '';
      } else if (part['type'] == 'section') {
        chatBubbleData['hasStructuredContent'] = true;
        chatBubbleData['sections'].add({
          'number': part['sectionNumber'],
          'content': part['content'],
          'startIndex': part['startIndex'],
          'endIndex': part['endIndex'],
        });
      }
    }
    
    return chatBubbleData;
  }
}
