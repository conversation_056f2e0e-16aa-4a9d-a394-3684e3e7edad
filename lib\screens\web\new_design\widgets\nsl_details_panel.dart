import 'package:flutter/material.dart';
import '../../../../models/nsl_hierarchy_model.dart';

class NSLDetailsPanel extends StatelessWidget {
  final NSLHierarchyData nslData;
  final VoidCallback onClose;

  const NSLDetailsPanel({
    super.key,
    required this.nslData,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          Text(
            'NSL Node Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: Colors.black,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: Icon(
              Icons.close,
              color: Colors.grey.shade600,
              size: 20,
            ),
            onPressed: onClose,
            tooltip: 'Close',
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildNodeHeader(),
          const SizedBox(height: 24),
          
          // // Original sections (BET Breakdown, Financial Summary, Metrics)
          // _buildBetBreakdownSection(),
          // const SizedBox(height: 24),
          // _buildFinancialSummarySection(),
          // const SizedBox(height: 24),
          // _buildMetricsSection(),
          
          // New detailed financial sections from financial_data_by_node
          if (nslData.financialDataByNode != null) ...[
            const SizedBox(height: 24),
            _buildStandaloneFinancialSection(),
            const SizedBox(height: 24),
            _buildConsolidatedDataSection(),
            const SizedBox(height: 24),
            _buildDetailedBetBreakdownSection(),
            const SizedBox(height: 24),
            _buildTrendsSection(),
          ],
          
          if (nslData.employeeId != null) ...[
            const SizedBox(height: 24),
            _buildSection('Employee Information', [
              _buildInfoRow('Employee ID', nslData.employeeId!),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildNodeHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: NSLNode.getLevelColor(nslData.level),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Center(
              child: Text(
                nslData.level,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nslData.title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  nslData.type,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    fontFamily: 'TiemposText',
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: NSLNode.getLevelColor(nslData.level).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${nslData.level} - ${nslData.levelName}',
                    style: TextStyle(
                      fontSize: 12,
                      color: NSLNode.getLevelColor(nslData.level),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBetBreakdownSection() {
    return _buildSection('BET Breakdown', [
      _buildInfoRow('GOs (Global Objectives)', nslData.betBreakdown.gos.toString()),
      _buildInfoRow('LOs (Local Objectives)', nslData.betBreakdown.los.toString()),
      _buildInfoRow('NP Functions', nslData.betBreakdown.npFunctions.toString()),
      _buildInfoRow('Input/Output Stacks', nslData.betBreakdown.inputOutputStacks.toString()),
      _buildInfoRow('Subordinate NSL', nslData.betBreakdown.subordinateNsl.toString()),
      const SizedBox(height: 8),
      Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calculate,
              color: Colors.blue.shade700,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Total BETs: ${nslData.totalBets}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.blue.shade700,
                fontFamily: 'TiemposText',
              ),
            ),
          ],
        ),
      ),
    ]);
  }

  Widget _buildFinancialSummarySection() {
    return _buildSection('Financial Summary', [
      _buildInfoRow('Revenue', nslData.financialSummary.revenue),
      _buildInfoRow('Cost', nslData.financialSummary.cost),
      _buildInfoRow('Margin/Efficiency', nslData.financialSummary.margin),
    ]);
  }

  Widget _buildMetricsSection() {
    if (nslData.metrics == null) {
      return Container(); // Return empty container if no metrics
    }

    final metrics = nslData.metrics!;
    List<Widget> metricRows = [];

    // Add metrics based on the node level
    switch (nslData.level) {
      case 'M4': // Executive Level
        if (metrics.m3Nodes != null) metricRows.add(_buildInfoRow('M3 Nodes', metrics.m3Nodes.toString()));
        if (metrics.totalGos != null) metricRows.add(_buildInfoRow('Total GOs', metrics.totalGos.toString()));
        if (metrics.totalLos != null) metricRows.add(_buildInfoRow('Total LOs', metrics.totalLos.toString()));
        if (metrics.transactions != null) metricRows.add(_buildInfoRow('Transactions', metrics.transactions!));
        if (metrics.betEfficiency != null) metricRows.add(_buildInfoRow('BET Efficiency', metrics.betEfficiency!));
        break;

      case 'M3': // Department Level
        if (metrics.m2Nodes != null) metricRows.add(_buildInfoRow('M2 Nodes', metrics.m2Nodes.toString()));
        if (metrics.gos != null) metricRows.add(_buildInfoRow('GOs', metrics.gos.toString()));
        if (metrics.los != null) metricRows.add(_buildInfoRow('LOs', metrics.los.toString()));
        if (metrics.transactions != null) metricRows.add(_buildInfoRow('Transactions', metrics.transactions!));
        if (metrics.betEfficiency != null) metricRows.add(_buildInfoRow('BET Efficiency', metrics.betEfficiency!));
        break;

      case 'M2': // Team Level
        if (metrics.m1Employees != null) metricRows.add(_buildInfoRow('M1 Employees', metrics.m1Employees.toString()));
        if (metrics.gos != null) metricRows.add(_buildInfoRow('GOs', metrics.gos.toString()));
        if (metrics.los != null) metricRows.add(_buildInfoRow('LOs', metrics.los.toString()));
        if (metrics.teamEfficiency != null) metricRows.add(_buildInfoRow('Team Efficiency', metrics.teamEfficiency!));
        break;

      case 'M1': // Individual Employee Level
        if (metrics.localObjectives != null) metricRows.add(_buildInfoRow('Local Objectives', metrics.localObjectives.toString()));
        if (metrics.personalBets != null) metricRows.add(_buildInfoRow('Personal BETs', metrics.personalBets.toString()));
        if (metrics.loEfficiency != null) metricRows.add(_buildInfoRow('LO Efficiency', metrics.loEfficiency!));
        break;
    }

    if (metricRows.isEmpty) {
      return Container(); // Return empty container if no relevant metrics
    }

    return _buildSection('Metrics', metricRows);
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: 'TiemposText',
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStandaloneFinancialSection() {
    final financialData = nslData.financialDataByNode;
    if (financialData == null) return Container();

    final standalone = financialData.standalone;
    List<Widget> children = [];

    // Summary section
    children.add(_buildSubSection('Summary', [
      _buildInfoRow('Total Revenue', standalone.summary.totalRevenue['value']?.toString() ?? ''),
      _buildInfoRow('Net Margin', standalone.summary.netMargin['value']?.toString() ?? ''),
      _buildInfoRow('Total Transactions', standalone.summary.totalTransactions['value']?.toString() ?? ''),
      _buildInfoRow('BET Efficiency', standalone.summary.betEfficiency['value']?.toString() ?? ''),
      if (standalone.summary.totalCost != null)
        _buildInfoRow('Total Cost', standalone.summary.totalCost!['value']?.toString() ?? ''),
      if (standalone.summary.annualSalary != null)
        _buildInfoRow('Annual Salary', standalone.summary.annualSalary!['value']?.toString() ?? ''),
      if (standalone.summary.valueOutput != null)
        _buildInfoRow('Value Output', standalone.summary.valueOutput!['value']?.toString() ?? ''),
    ]));

    // Income Statement section
    if (standalone.incomeStatement.isNotEmpty) {
      children.add(const SizedBox(height: 16));
      children.add(_buildSubSection('Income Statement', 
        standalone.incomeStatement.map((item) => 
          _buildIncomeStatementItem(item)
        ).toList()
      ));
    }

    // Performance Metrics section (for M1 level)
    if (standalone.performanceMetrics != null && standalone.performanceMetrics!.isNotEmpty) {
      children.add(const SizedBox(height: 16));
      children.add(_buildSubSection('Performance Metrics', 
        standalone.performanceMetrics!.map((metric) => 
          _buildInfoRow(
            metric['metric']?.toString() ?? '', 
            '${metric['value']?.toString() ?? ''} (Target: ${metric['target']?.toString() ?? ''})'
          )
        ).toList()
      ));
    }

    return _buildSection('Standalone Financial Data', children);
  }

  Widget _buildConsolidatedDataSection() {
    final financialData = nslData.financialDataByNode;
    if (financialData == null) return Container();

    final consolidated = financialData.consolidated;
    if (consolidated.summary.isEmpty) return Container();

    return _buildSection('Consolidated Data', 
      consolidated.summary.map((item) => 
        _buildInfoRow(
          item['title']?.toString() ?? '', 
          '${item['value']?.toString() ?? ''} - ${item['change']?.toString() ?? ''}'
        )
      ).toList()
    );
  }

  Widget _buildDetailedBetBreakdownSection() {
    final financialData = nslData.financialDataByNode;
    if (financialData == null) return Container();

    final betBreakdown = financialData.betBreakdown;
    if (betBreakdown.isEmpty) return Container();

    List<Widget> children = [];

    for (var item in betBreakdown) {
      children.add(_buildBetBreakdownItem(item));
      if (item != betBreakdown.last) {
        children.add(const SizedBox(height: 16));
      }
    }

    return _buildSection('Detailed BET Breakdown', children);
  }

  Widget _buildTrendsSection() {
    final financialData = nslData.financialDataByNode;
    if (financialData == null) return Container();

    final trends = financialData.trends;
    if (trends.trends.isEmpty) return Container();

    return _buildSection('Trends Analysis', 
      trends.trends.map((trend) => 
        _buildTrendItem(trend)
      ).toList()
    );
  }

  Widget _buildSubSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            fontFamily: 'TiemposText',
            color: Colors.blue.shade700,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildIncomeStatementItem(IncomeStatementItem item) {
    final isTotal = item.type == 'total' || item.type == 'final';
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      decoration: isTotal ? BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(4),
      ) : null,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: isTotal ? 8.0 : 0.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    item.item,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: isTotal ? Colors.blue.shade700 : Colors.black,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    item.amount,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
                      fontFamily: 'TiemposText',
                      color: isTotal ? Colors.blue.shade700 : Colors.black,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 50,
                  child: Text(
                    item.percentage,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                      fontFamily: 'TiemposText',
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
            if (item.betContribution.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                item.betContribution,
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  fontFamily: 'TiemposText',
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBetBreakdownItem(DetailedBetBreakdownItem item) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.green.shade700,
                  ),
                ),
              ),
              Text(
                item.cost,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'TiemposText',
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            item.type,
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
          if (item.details.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...item.details.entries.map((entry) => 
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 2.0),
                child: Row(
                  children: [
                    SizedBox(
                      width: 120,
                      child: Text(
                        entry.key,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'TiemposText',
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        entry.value?.toString() ?? '',
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'TiemposText',
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ).toList(),
          ],
        ],
      ),
    );
  }

  Widget _buildTrendItem(Map<String, dynamic> trend) {
    final title = trend['title']?.toString() ?? '';
    final value = trend['value']?.toString() ?? '';
    final change = trend['change']?.toString() ?? '';
    
    final isPositive = value.startsWith('+');
    final color = isPositive ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: color.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.shade200),
      ),
      child: Row(
        children: [
          Icon(
            isPositive ? Icons.trending_up : Icons.trending_down,
            color: color.shade700,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: color.shade700,
                  ),
                ),
                Text(
                  change,
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'TiemposText',
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: color.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
