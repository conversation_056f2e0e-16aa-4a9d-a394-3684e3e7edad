import 'transaction_status.dart';

/// Model class representing a transaction in the application.
class Transaction {
  /// Unique identifier for the transaction.
  final String id;

  /// Name or title of the transaction.
  final String name;

  /// Description of the transaction.
  final String description;

  /// Current status of the transaction.
  final TransactionStatus status;

  /// Timestamp when the transaction was created.
  final DateTime timestamp;

  /// Additional data associated with the transaction.
  final Map<String, dynamic> data;

  /// Amount of the transaction.
  final double amount;

  /// Creates a new transaction.
  Transaction({
    required this.id,
    required this.name,
    required this.description,
    required this.status,
    required this.timestamp,
    required this.data,
    required this.amount,
  });

  /// The date of the transaction (alias for timestamp).
  DateTime get date => timestamp;

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      status: _parseStatus(json['status']),
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      data: json['data'] ?? {},
      amount: (json['amount'] ?? 0.0).toDouble(),
    );
  }

  /// Helper method to parse status string to TransactionStatus enum.
  static TransactionStatus _parseStatus(String? statusStr) {
    switch (statusStr?.toLowerCase()) {
      case 'completed':
        return TransactionStatus.completed;
      case 'failed':
        return TransactionStatus.failed;
      case 'pending':
      default:
        return TransactionStatus.pending;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'status': status.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
      'amount': amount,
    };
  }

  /// Creates a copy of this transaction with the given fields replaced with new values.
  Transaction copyWith({
    String? id,
    String? name,
    String? description,
    TransactionStatus? status,
    DateTime? timestamp,
    Map<String, dynamic>? data,
    double? amount,
  }) {
    return Transaction(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      data: data ?? this.data,
      amount: amount ?? this.amount,
    );
  }
}
