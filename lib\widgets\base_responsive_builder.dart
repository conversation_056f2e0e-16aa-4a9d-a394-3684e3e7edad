import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/responsive_state_provider.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';

/// A base class for responsive builders that preserves screen state
/// when transitioning between web and mobile layouts.
///
/// This class provides common functionality for all responsive builders,
/// ensuring that the current screen state is preserved when the layout
/// changes due to screen size changes.
abstract class BaseResponsiveBuilder extends StatefulWidget {
  final String builderKey;

  const BaseResponsiveBuilder({
    super.key,
    required this.builderKey,
  });

  @override
  BaseResponsiveBuilderState createState();

  /// Build the web layout
  Widget buildWebLayout(BuildContext context, String? currentRoute);

  /// Build the mobile layout
  Widget buildMobileLayout(BuildContext context, String? currentRoute);
}

abstract class BaseResponsiveBuilderState<T extends BaseResponsiveBuilder>
    extends State<T> {

  @override
  Widget build(BuildContext context) {
    return Consumer<ResponsiveStateProvider>(
      builder: (context, stateProvider, child) {
        return LayoutBuilder(
          builder: (context, constraints) {
            // Determine layout based on screen width:
            // < 767px: Mobile layout
            // >= 767px and <= 1024px: Web layout (tablet using web screens)
            // > 1024px: Web layout (desktop)
            final screenWidth = constraints.maxWidth;
            final isWebLayout = screenWidth >= AppConstants.mobileBreakpoint;

            // Update the web layout flag in the provider
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                stateProvider.setWebLayout(isWebLayout);
              }
            });

            // First check for content state (for single-page content changes)
            final contentState =
                stateProvider.getContentState(widget.builderKey);

            // If no content state is found, fall back to route state (for navigation)
            final currentRoute = contentState ??
                stateProvider.getCurrentRoute(widget.builderKey);

            Logger.info(
                'Building ${widget.builderKey} with screenWidth=$screenWidth, isWebLayout=$isWebLayout, contentState=$contentState, currentRoute=$currentRoute');

            // Return the appropriate layout based on screen width
            // Tablets (767-1024px) and desktops (>1024px) both use web layout
            if (isWebLayout) {
              return widget.buildWebLayout(context, currentRoute);
            } else {
              return widget.buildMobileLayout(context, currentRoute);
            }
          },
        );
      },
    );
  }

  /// Helper method to store content state when it changes within a single page
  void storeContentState(String contentState) {
    if (!mounted) return;

    try {
      final stateProvider =
          Provider.of<ResponsiveStateProvider>(context, listen: false);
      stateProvider.setContentState(widget.builderKey, contentState);
      Logger.info(
          'Stored content state for ${widget.builderKey}: $contentState');
    } catch (e) {
      Logger.error('Error storing content state: $e');
    }
  }
}
