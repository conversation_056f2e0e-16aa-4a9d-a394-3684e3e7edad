import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/screens/new_design/my_library_mobile/library_container_mobile.dart';

/// A reusable navigation item widget for mobile library screens
/// Used across Books, Solutions, Objects, and Agents library screens
class LibraryNavbarMobile extends StatefulWidget {
  final int selectedTabIndex;
  final Function(int)? onTabChanged;
  final int booksCount;
  final int solutionsCount;
  final int objectsCount;
  final int agentsCount;

  const LibraryNavbarMobile({
    super.key,
    required this.selectedTabIndex,
    this.onTabChanged,
    this.booksCount = 0,
    this.solutionsCount = 0,
    this.objectsCount = 0,
    this.agentsCount = 0,
  });

  @override
  State<LibraryNavbarMobile> createState() => _LibraryNavbarMobileState();
}

class _LibraryNavbarMobileState extends State<LibraryNavbarMobile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate button width: (available width - spacing between items) / number of buttons
          // Total spacing = 3 gaps * 8px = 24px (between 4 buttons)
          final double buttonWidth = (constraints.maxWidth - 24) / 4;

          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _LibraryNavItem(
                iconPath: 'assets/images/books-icon.svg',
                isActive: widget.selectedTabIndex == 0,
                width: buttonWidth,
                count: widget.booksCount,
                onTap: () {
                  if (widget.onTabChanged != null) {
                    widget.onTabChanged!(0);
                  } else {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const LibraryContainerMobile(initialTabIndex: 0),
                      ),
                    );
                  }
                },
              ),
              const SizedBox(width: 8),
              _LibraryNavItem(
                iconPath: 'assets/images/square-box-uncheck.svg',
                isActive: widget.selectedTabIndex == 1,
                width: buttonWidth,
                count: widget.solutionsCount,
                onTap: () {
                  if (widget.onTabChanged != null) {
                    widget.onTabChanged!(1);
                  } else {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const LibraryContainerMobile(initialTabIndex: 1),
                      ),
                    );
                  }
                },
              ),
              const SizedBox(width: 8),
              _LibraryNavItem(
                iconPath: 'assets/images/cube-box.svg',
                isActive: widget.selectedTabIndex == 2,
                width: buttonWidth,
                count: widget.objectsCount,
                onTap: () {
                  if (widget.onTabChanged != null) {
                    widget.onTabChanged!(2);
                  } else {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const LibraryContainerMobile(initialTabIndex: 2),
                      ),
                    );
                  }
                },
              ),
              const SizedBox(width: 8),
              _LibraryNavItem(
                iconPath: 'assets/images/agent-icon.svg',
                isActive: widget.selectedTabIndex == 3,
                width: buttonWidth,
                count: widget.agentsCount,
                onTap: () {
                  if (widget.onTabChanged != null) {
                    widget.onTabChanged!(3);
                  } else {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const LibraryContainerMobile(initialTabIndex: 3),
                      ),
                    );
                  }
                },
              ),
            ],
          );
        },
      ),
    );
  }
}

/// Individual navigation item widget
class _LibraryNavItem extends StatefulWidget {
  final String iconPath;
  final VoidCallback onTap;
  final bool isActive;
  final double width;
  final int count;

  const _LibraryNavItem({
    required this.iconPath,
    required this.onTap,
    required this.width,
    this.isActive = false,
    this.count = 0,
  });

  @override
  State<_LibraryNavItem> createState() => _LibraryNavItemState();
}

class _LibraryNavItemState extends State<_LibraryNavItem> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onTap,
      child: Container(
        width: widget.width,
        height: 34,
        decoration: BoxDecoration(
          color: widget.isActive
              ? const Color(0xff0058FF)
              : (isPressed ? Colors.white : const Color(0xffEBEBEB)),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.iconPath,
                width: 14,
                height: 14,
                colorFilter: ColorFilter.mode(
                  widget.isActive ? Colors.white : Colors.black87,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                '${widget.count}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: widget.isActive ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
