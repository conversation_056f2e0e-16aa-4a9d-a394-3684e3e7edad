import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A customizable tabbed layout component
class AppTabLayout extends StatefulWidget {
  /// The list of tab data
  final List<AppTabData> tabs;

  /// The initially selected tab index
  final int initialIndex;

  /// The background color of the tab bar
  final Color? tabBarBackgroundColor;

  /// The background color of the tab content
  final Color? contentBackgroundColor;

  /// The color of the selected tab indicator
  final Color? indicatorColor;

  /// The color of the selected tab label
  final Color? selectedLabelColor;

  /// The color of the unselected tab labels
  final Color? unselectedLabelColor;

  /// The padding inside the tab bar
  final EdgeInsetsGeometry tabBarPadding;

  /// The padding inside the content area
  final EdgeInsetsGeometry contentPadding;

  /// The border radius of the tab layout
  final BorderRadius? borderRadius;

  /// The elevation of the tab layout
  final double elevation;

  /// The border of the tab layout
  final Border? border;

  /// Whether to show dividers between tabs
  final bool showDividers;

  /// The width of the tab indicator
  final TabBarIndicatorSize indicatorSize;

  /// The height of the tab indicator
  final double indicatorWeight;

  /// Whether the tabs should be scrollable
  final bool isScrollable;

  /// The tab bar height
  final double tabBarHeight;

  /// Callback when a tab is selected
  final Function(int)? onTabChanged;

  const AppTabLayout({
    super.key,
    required this.tabs,
    this.initialIndex = 0,
    this.tabBarBackgroundColor,
    this.contentBackgroundColor,
    this.indicatorColor,
    this.selectedLabelColor,
    this.unselectedLabelColor,
    this.tabBarPadding =
        const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
    this.contentPadding = const EdgeInsets.all(AppTheme.spacingM),
    this.borderRadius,
    this.elevation = 1,
    this.border,
    this.showDividers = false,
    this.indicatorSize = TabBarIndicatorSize.tab,
    this.indicatorWeight = 3.0,
    this.isScrollable = false,
    this.tabBarHeight = 48.0,
    this.onTabChanged,
  })  : assert(tabs.length > 0, 'At least one tab must be provided'),
        assert(initialIndex >= 0 && initialIndex < tabs.length,
            'Initial index must be valid');

  @override
  State<AppTabLayout> createState() => _AppTabLayoutState();
}

class _AppTabLayoutState extends State<AppTabLayout>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.tabs.length,
      vsync: this,
      initialIndex: widget.initialIndex,
    );

    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      return;
    }

    if (widget.onTabChanged != null) {
      widget.onTabChanged!(_tabController.index);
    }
  }

  @override
  Widget build(BuildContext context) {
    final BorderRadius borderRadius =
        widget.borderRadius ?? BorderRadius.circular(AppTheme.borderRadiusM);

    return Container(
      decoration: BoxDecoration(
        color: widget.contentBackgroundColor ?? AppTheme.backgroundColor,
        borderRadius: borderRadius,
        border: widget.border,
        boxShadow: widget.elevation > 0
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation * 2,
                  offset: Offset(0, widget.elevation),
                ),
              ]
            : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Tab Bar
          Container(
            height: widget.tabBarHeight,
            padding: widget.tabBarPadding,
            decoration: BoxDecoration(
              color: widget.tabBarBackgroundColor ??
                  widget.contentBackgroundColor ??
                  AppTheme.backgroundColor,
              borderRadius: BorderRadius.only(
                topLeft: borderRadius.topLeft,
                topRight: borderRadius.topRight,
              ),
            ),
            child: TabBar(
              controller: _tabController,
              tabs: widget.tabs
                  .map(
                    (tab) => Tab(
                      icon: tab.icon != null ? Icon(tab.icon) : null,
                      text: tab.label,
                      height: widget.tabBarHeight,
                      iconMargin: const EdgeInsets.only(bottom: 2.0),
                    ),
                  )
                  .toList(),
              indicatorColor: widget.indicatorColor ?? AppTheme.primaryColor,
              labelColor: widget.selectedLabelColor ?? AppTheme.primaryColor,
              unselectedLabelColor:
                  widget.unselectedLabelColor ?? AppTheme.textSecondaryColor,
              indicatorSize: widget.indicatorSize,
              indicatorWeight: widget.indicatorWeight,
              isScrollable: widget.isScrollable,
              dividerColor: widget.showDividers ? null : Colors.transparent,
              labelStyle: AppTheme.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
              unselectedLabelStyle: AppTheme.bodyMedium,
            ),
          ),

          // Divider
          if (widget.showDividers) const Divider(height: 1, thickness: 1),

          // Tab Content
          Flexible(
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: borderRadius.bottomLeft,
                bottomRight: borderRadius.bottomRight,
              ),
              child: TabBarView(
                controller: _tabController,
                children: widget.tabs
                    .map(
                      (tab) => Padding(
                        padding: widget.contentPadding,
                        child: tab.content,
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A customizable tab layout with a fixed height
class AppFixedHeightTabLayout extends StatelessWidget {
  /// The tab layout
  final AppTabLayout tabLayout;

  /// The height of the content area
  final double contentHeight;

  const AppFixedHeightTabLayout({
    super.key,
    required this.tabLayout,
    required this.contentHeight,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: contentHeight + tabLayout.tabBarHeight,
      child: tabLayout,
    );
  }
}

/// Data for a tab
class AppTabData {
  /// The label of the tab
  final String? label;

  /// The icon of the tab
  final IconData? icon;

  /// The content of the tab
  final Widget content;

  const AppTabData({
    this.label,
    this.icon,
    required this.content,
  }) : assert(label != null || icon != null,
            'At least one of label or icon must be provided');
}

/// A customizable segmented control tab layout
class AppSegmentedTabLayout extends StatefulWidget {
  /// The list of tab data
  final List<AppTabData> tabs;

  /// The initially selected tab index
  final int initialIndex;

  /// The background color of the tab bar
  final Color? tabBarBackgroundColor;

  /// The background color of the tab content
  final Color? contentBackgroundColor;

  /// The color of the selected tab
  final Color? selectedTabColor;

  /// The color of the selected tab label
  final Color? selectedLabelColor;

  /// The color of the unselected tab labels
  final Color? unselectedLabelColor;

  /// The padding inside the tab bar
  final EdgeInsetsGeometry tabBarPadding;

  /// The padding inside the content area
  final EdgeInsetsGeometry contentPadding;

  /// The border radius of the tab layout
  final BorderRadius? borderRadius;

  /// The border radius of the segmented control
  final BorderRadius? segmentBorderRadius;

  /// The elevation of the tab layout
  final double elevation;

  /// The border of the tab layout
  final Border? border;

  /// The height of the tab bar
  final double tabBarHeight;

  /// Callback when a tab is selected
  final Function(int)? onTabChanged;

  const AppSegmentedTabLayout({
    super.key,
    required this.tabs,
    this.initialIndex = 0,
    this.tabBarBackgroundColor,
    this.contentBackgroundColor,
    this.selectedTabColor,
    this.selectedLabelColor,
    this.unselectedLabelColor,
    this.tabBarPadding = const EdgeInsets.all(AppTheme.spacingM),
    this.contentPadding = const EdgeInsets.all(AppTheme.spacingM),
    this.borderRadius,
    this.segmentBorderRadius,
    this.elevation = 1,
    this.border,
    this.tabBarHeight = 40.0,
    this.onTabChanged,
  })  : assert(tabs.length > 0, 'At least one tab must be provided'),
        assert(initialIndex >= 0 && initialIndex < tabs.length,
            'Initial index must be valid');

  @override
  State<AppSegmentedTabLayout> createState() => _AppSegmentedTabLayoutState();
}

class _AppSegmentedTabLayoutState extends State<AppSegmentedTabLayout> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
  }

  void _handleTabChange(int index) {
    setState(() {
      _selectedIndex = index;
    });

    if (widget.onTabChanged != null) {
      widget.onTabChanged!(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    final BorderRadius borderRadius =
        widget.borderRadius ?? BorderRadius.circular(AppTheme.borderRadiusM);
    final BorderRadius segmentBorderRadius = widget.segmentBorderRadius ??
        BorderRadius.circular(AppTheme.borderRadiusM);

    return Container(
      decoration: BoxDecoration(
        color: widget.contentBackgroundColor ?? AppTheme.backgroundColor,
        borderRadius: borderRadius,
        border: widget.border,
        boxShadow: widget.elevation > 0
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation * 2,
                  offset: Offset(0, widget.elevation),
                ),
              ]
            : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Tab Bar
          Container(
            height: widget.tabBarHeight,
            padding: widget.tabBarPadding,
            decoration: BoxDecoration(
              color: widget.tabBarBackgroundColor ??
                  widget.contentBackgroundColor ??
                  AppTheme.backgroundColor,
              borderRadius: BorderRadius.only(
                topLeft: borderRadius.topLeft,
                topRight: borderRadius.topRight,
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: segmentBorderRadius,
              ),
              child: Row(
                children: List.generate(
                  widget.tabs.length * 2 - 1,
                  (index) {
                    if (index.isOdd) {
                      // Divider
                      return Container(
                        width: 1,
                        color: AppTheme.textSecondaryColor.withAlpha(51),
                      );
                    }

                    final tabIndex = index ~/ 2;
                    final tab = widget.tabs[tabIndex];
                    final isSelected = tabIndex == _selectedIndex;

                    return Expanded(
                      child: InkWell(
                        onTap: () => _handleTabChange(tabIndex),
                        borderRadius: segmentBorderRadius,
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? widget.selectedTabColor ??
                                    AppTheme.primaryColor
                                : Colors.transparent,
                            borderRadius: BorderRadius.only(
                              topLeft: tabIndex == 0
                                  ? segmentBorderRadius.topLeft
                                  : Radius.zero,
                              bottomLeft: tabIndex == 0
                                  ? segmentBorderRadius.bottomLeft
                                  : Radius.zero,
                              topRight: tabIndex == widget.tabs.length - 1
                                  ? segmentBorderRadius.topRight
                                  : Radius.zero,
                              bottomRight: tabIndex == widget.tabs.length - 1
                                  ? segmentBorderRadius.bottomRight
                                  : Radius.zero,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (tab.icon != null) ...[
                                Icon(
                                  tab.icon,
                                  size: 18,
                                  color: isSelected
                                      ? widget.selectedLabelColor ??
                                          AppTheme.textLightColor
                                      : widget.unselectedLabelColor ??
                                          AppTheme.textSecondaryColor,
                                ),
                                if (tab.label != null)
                                  const SizedBox(width: AppTheme.spacingS),
                              ],
                              if (tab.label != null)
                                Text(
                                  tab.label!,
                                  style: AppTheme.bodySmall.copyWith(
                                    color: isSelected
                                        ? widget.selectedLabelColor ??
                                            AppTheme.textLightColor
                                        : widget.unselectedLabelColor ??
                                            AppTheme.textSecondaryColor,
                                    fontWeight: isSelected
                                        ? FontWeight.w500
                                        : FontWeight.normal,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          // Tab Content
          Padding(
            padding: widget.contentPadding,
            child: widget.tabs[_selectedIndex].content,
          ),
        ],
      ),
    );
  }
}
