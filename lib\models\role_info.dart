/// Model class for role information
class RoleInfo {
  final String title;
  final String description;
  final bool isChecked;
  final String? id;
  final String? version;
  final String? createdBy;
  final String? createdDate;
  final String? modifiedBy;
  final String? modifiedDate;
  final List<String>? useCases;
  final Map<String, List<String>>? permissions;
  final List<String>? coreResponsibilities;
  final List<dynamic>? kpis;
  final List<String>? decisionAuthority;

  RoleInfo({
    required this.title,
    required this.description,
    this.isChecked = false,
    this.id,
    this.version,
    this.createdBy,
    this.createdDate,
    this.modifiedBy,
    this.modifiedDate,
    this.useCases,
    this.permissions,
    this.coreResponsibilities,
    this.kpis,
    this.decisionAuthority,
  });

  /// Factory constructor to create a RoleInfo from JSON
  factory RoleInfo.fromJson(Map<String, dynamic> json) {
    return RoleInfo(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      version: json['version'],
      createdBy: json['createdBy'],
      createdDate: json['createdDate'],
      modifiedBy: json['modifiedBy'],
      modifiedDate: json['modifiedDate'],
      useCases:
          json['useCases'] != null ? List<String>.from(json['useCases']) : null,
      permissions: json['permissions'] != null
          ? {
              'entities': List<String>.from(json['permissions']['entities']),
              'objectives':
                  List<String>.from(json['permissions']['objectives']),
            }
          : null,
      coreResponsibilities: json['coreResponsibilities'] != null
          ? List<String>.from(json['coreResponsibilities'])
          : null,
      kpis: json['kpis'] != null ? List<dynamic>.from(json['kpis']) : null,
      decisionAuthority: json['decisionAuthority'] != null
          ? List<String>.from(json['decisionAuthority'])
          : null,
    );
  }

  /// Convert RoleInfo to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'version': version,
      'createdBy': createdBy,
      'createdDate': createdDate,
      'modifiedBy': modifiedBy,
      'modifiedDate': modifiedDate,
      'useCases': useCases,
      'permissions': permissions,
      'coreResponsibilities': coreResponsibilities,
      'kpis': kpis,
      'decisionAuthority': decisionAuthority,
    };
  }

  /// Create a copy of this RoleInfo with some fields replaced
  RoleInfo copyWith({
    String? title,
    String? description,
    bool? isChecked,
    String? id,
    String? version,
    String? createdBy,
    String? createdDate,
    String? modifiedBy,
    String? modifiedDate,
    List<String>? useCases,
    Map<String, List<String>>? permissions,
    List<String>? coreResponsibilities,
    List<dynamic>? kpis,
    List<String>? decisionAuthority,
  }) {
    return RoleInfo(
      title: title ?? this.title,
      description: description ?? this.description,
      isChecked: isChecked ?? this.isChecked,
      id: id ?? this.id,
      version: version ?? this.version,
      createdBy: createdBy ?? this.createdBy,
      createdDate: createdDate ?? this.createdDate,
      modifiedBy: modifiedBy ?? this.modifiedBy,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      useCases: useCases ?? this.useCases,
      permissions: permissions ?? this.permissions,
      coreResponsibilities: coreResponsibilities ?? this.coreResponsibilities,
      kpis: kpis ?? this.kpis,
      decisionAuthority: decisionAuthority ?? this.decisionAuthority,
    );
  }
}
