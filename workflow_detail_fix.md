# Fix for Workflow Detail Screen

The issue is that when a section is submitted, the StatefulBuilder is not being rebuilt, so the UI doesn't reflect the updated submission state. Here's how to fix it:

## 1. Add a key to the StatefulBuilder based on the submission state

In `_buildInputSection` method, modify the StatefulBuilder creation:

```dart
final sectionWidget = StatefulBuilder(
  key: ValueKey('section_${sectionId}_submitted_${_submittedSections[sectionId] ?? false}'),
  builder: (context, setStateLocal) {
    final isExpanded = _expandedSections[sectionId] ?? false;
    final isSubmitted = _submittedSections[sectionId] ?? false;
    Logger.info('Building section $sectionId with expansion state: $isExpanded, submission state: $isSubmitted');
    return Card(
      // ... rest of the code
    );
  }
);
```

By adding a key that includes the submission state, we force the StatefulBuilder to be rebuilt when the submission state changes.

## 2. Force a rebuild of the entire section when the submission state changes

In `_handleNextButtonPressed` method, after updating the `_submittedSections` map, force a rebuild of the entire section:

```dart
// Mark this section as submitted
setState(() {
  _submittedSections[sectionId] = true;
  // Collapse the section after submission
  _expandedSections[sectionId] = false;
  
  // Force a rebuild of the section
  final index = _inputSections.indexWhere((widget) => 
    (widget as KeyedSubtree).key.toString().contains(sectionId));
  if (index != -1) {
    final inputs = Provider.of<WorkflowProvider>(context, listen: false)
      .allWorkflowInputs
      .firstWhere((inputs) => 
        _inputSections[index].key.toString().contains('section_${inputs.inputFields.first.attributeId}'),
        orElse: () => Provider.of<WorkflowProvider>(context, listen: false).workflowInputs!);
    
    final newSectionWidget = _buildInputSection(inputs, sectionId);
    _inputSections[index] = KeyedSubtree(key: ValueKey(sectionId), child: newSectionWidget);
  }
});
```

This approach might be complex and error-prone. A simpler solution would be:

## Alternative Solution: Use a StatefulWidget instead of StatefulBuilder

Instead of using a StatefulBuilder, create a separate StatefulWidget for the input section:

1. Create a new file `input_section_widget.dart`:

```dart
import 'package:flutter/material.dart';
import '../models/workflow.dart';
import '../widgets/input_field_widget.dart';

class InputSectionWidget extends StatefulWidget {
  final WorkflowInputs inputs;
  final String sectionId;
  final bool isLastSection;
  final bool isSubmitted;
  final Function(String) onNextButtonPressed;

  const InputSectionWidget({
    Key? key,
    required this.inputs,
    required this.sectionId,
    required this.isLastSection,
    required this.isSubmitted,
    required this.onNextButtonPressed,
  }) : super(key: key);

  @override
  State<InputSectionWidget> createState() => _InputSectionWidgetState();
}

class _InputSectionWidgetState extends State<InputSectionWidget> {
  bool _isExpanded = false;
  final Map<String, GlobalKey<InputFieldWidgetState>> _inputFieldKeys = {};

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.isLastSection;
  }

  @override
  void didUpdateWidget(InputSectionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSubmitted != oldWidget.isSubmitted) {
      setState(() {
        _isExpanded = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      // ... rest of the code from _buildInputSection
    );
  }
}
```

2. Use this widget in `_buildInputSection`:

```dart
Widget _buildInputSection(WorkflowInputs inputs, String sectionId) {
  final isLastSection = sectionId == 'section_${_getLastSectionIndex()}';
  final isSubmitted = _submittedSections[sectionId] ?? false;
  
  return InputSectionWidget(
    key: ValueKey(sectionId),
    inputs: inputs,
    sectionId: sectionId,
    isLastSection: isLastSection,
    isSubmitted: isSubmitted,
    onNextButtonPressed: _handleNextButtonPressed,
  );
}
```

This approach is cleaner and more maintainable, as it properly encapsulates the state and behavior of each input section.

## Simplest Solution: Force a rebuild of all sections

The simplest solution would be to force a rebuild of all sections when the submission state changes:

```dart
// In _handleNextButtonPressed after updating _submittedSections
setState(() {
  _submittedSections[sectionId] = true;
  _expandedSections[sectionId] = false;
  
  // Force a rebuild of all sections by recreating them
  final workflowProvider = Provider.of<WorkflowProvider>(context, listen: false);
  _inputSections.clear();
  for (int i = 0; i < workflowProvider.allWorkflowInputs.length; i++) {
    final inputs = workflowProvider.allWorkflowInputs[i];
    final sectionId = 'section_$i';
    final sectionWidget = _buildInputSection(inputs, sectionId);
    _inputSections.add(KeyedSubtree(key: ValueKey(sectionId), child: sectionWidget));
  }
});
```

This approach is simpler but less efficient, as it rebuilds all sections even if only one has changed.
