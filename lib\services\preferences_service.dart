import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// A service for managing shared preferences with caching
class PreferencesService {
  // Singleton instance
  static final PreferencesService _instance = PreferencesService._internal();
  
  // Factory constructor to return the same instance
  factory PreferencesService() => _instance;
  
  // Private constructor
  PreferencesService._internal();
  
  // SharedPreferences instance
  late SharedPreferences _prefs;
  
  // Cache for frequently accessed values
  final Map<String, dynamic> _cache = {};
  
  // Initialization flag
  bool _isInitialized = false;
  
  /// Initialize the preferences service
  Future<void> init() async {
    if (_isInitialized) {
      Logger.info('PreferencesService already initialized');
      return;
    }
    
    Logger.info('Initializing PreferencesService');
    _prefs = await SharedPreferences.getInstance();
    _isInitialized = true;
    Logger.info('PreferencesService initialized successfully');
  }
  
  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Get a string value
  String? getString(String key) {
    _checkInitialized();
    
    if (_cache.containsKey(key)) {
      return _cache[key] as String?;
    }
    
    final value = _prefs.getString(key);
    _cache[key] = value;
    return value;
  }
  
  /// Set a string value
  Future<bool> setString(String key, String value) async {
    _checkInitialized();
    
    _cache[key] = value;
    return await _prefs.setString(key, value);
  }
  
  /// Get a boolean value
  bool? getBool(String key) {
    _checkInitialized();
    
    if (_cache.containsKey(key)) {
      return _cache[key] as bool?;
    }
    
    final value = _prefs.getBool(key);
    _cache[key] = value;
    return value;
  }
  
  /// Set a boolean value
  Future<bool> setBool(String key, bool value) async {
    _checkInitialized();
    
    _cache[key] = value;
    return await _prefs.setBool(key, value);
  }
  
  /// Get an integer value
  int? getInt(String key) {
    _checkInitialized();
    
    if (_cache.containsKey(key)) {
      return _cache[key] as int?;
    }
    
    final value = _prefs.getInt(key);
    _cache[key] = value;
    return value;
  }
  
  /// Set an integer value
  Future<bool> setInt(String key, int value) async {
    _checkInitialized();
    
    _cache[key] = value;
    return await _prefs.setInt(key, value);
  }
  
  /// Get a double value
  double? getDouble(String key) {
    _checkInitialized();
    
    if (_cache.containsKey(key)) {
      return _cache[key] as double?;
    }
    
    final value = _prefs.getDouble(key);
    _cache[key] = value;
    return value;
  }
  
  /// Set a double value
  Future<bool> setDouble(String key, double value) async {
    _checkInitialized();
    
    _cache[key] = value;
    return await _prefs.setDouble(key, value);
  }
  
  /// Get a string list
  List<String>? getStringList(String key) {
    _checkInitialized();
    
    if (_cache.containsKey(key)) {
      return _cache[key] as List<String>?;
    }
    
    final value = _prefs.getStringList(key);
    _cache[key] = value;
    return value;
  }
  
  /// Set a string list
  Future<bool> setStringList(String key, List<String> value) async {
    _checkInitialized();
    
    _cache[key] = value;
    return await _prefs.setStringList(key, value);
  }
  
  /// Get an object (stored as JSON)
  T? getObject<T>(String key, T Function(Map<String, dynamic> json) fromJson) {
    _checkInitialized();
    
    if (_cache.containsKey(key)) {
      return _cache[key] as T?;
    }
    
    final jsonString = _prefs.getString(key);
    if (jsonString == null) {
      return null;
    }
    
    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final value = fromJson(json);
      _cache[key] = value;
      return value;
    } catch (e) {
      Logger.error('Error decoding JSON for key $key: $e');
      return null;
    }
  }
  
  /// Set an object (stored as JSON)
  Future<bool> setObject<T>(String key, T value, Map<String, dynamic> Function(T value) toJson) async {
    _checkInitialized();
    
    try {
      final json = toJson(value);
      final jsonString = jsonEncode(json);
      _cache[key] = value;
      return await _prefs.setString(key, jsonString);
    } catch (e) {
      Logger.error('Error encoding JSON for key $key: $e');
      return false;
    }
  }
  
  /// Remove a value
  Future<bool> remove(String key) async {
    _checkInitialized();
    
    _cache.remove(key);
    return await _prefs.remove(key);
  }
  
  /// Clear all values
  Future<bool> clear() async {
    _checkInitialized();
    
    _cache.clear();
    return await _prefs.clear();
  }
  
  /// Check if a key exists
  bool containsKey(String key) {
    _checkInitialized();
    
    return _prefs.containsKey(key);
  }
  
  /// Get all keys
  Set<String> getKeys() {
    _checkInitialized();
    
    return _prefs.getKeys();
  }
  
  /// Reload preferences from disk
  Future<void> reload() async {
    _checkInitialized();
    
    await _prefs.reload();
    _cache.clear();
  }
  
  /// Check if the service is initialized and throw an exception if not
  void _checkInitialized() {
    if (!_isInitialized) {
      Logger.error('PreferencesService not initialized');
      throw Exception('PreferencesService not initialized. Call init() first.');
    }
  }
}
