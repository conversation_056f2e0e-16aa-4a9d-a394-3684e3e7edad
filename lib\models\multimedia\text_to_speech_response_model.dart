// To parse this JSON data, do
//
//     final textToSpeechResponseModel = textToSpeechResponseModelFromJson(jsonString);

import 'dart:convert';

TextToSpeechResponseModel textToSpeechResponseModelFromJson(String str) =>
    TextToSpeechResponseModel.fromJson(json.decode(str));

String textToSpeechResponseModelToJson(TextToSpeechResponseModel data) =>
    json.encode(data.toJson());

class TextToSpeechResponseModel {
  final bool? success;
  final String? outputFile;
  final String? message;
  final dynamic error;

  TextToSpeechResponseModel({
    this.success,
    this.outputFile,
    this.message,
    this.error,
  });

  TextToSpeechResponseModel copyWith({
    bool? success,
    String? outputFile,
    String? message,
    dynamic error,
  }) =>
      TextToSpeechResponseModel(
        success: success ?? this.success,
        outputFile: outputFile ?? this.outputFile,
        message: message ?? this.message,
        error: error ?? this.error,
      );

  factory TextToSpeechResponseModel.fromJson(Map<String, dynamic> json) =>
      TextToSpeechResponseModel(
        success: json["success"],
        outputFile: json["output_file"],
        message: json["message"],
        error: json["error"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "output_file": outputFile,
        "message": message,
        "error": error,
      };
}
