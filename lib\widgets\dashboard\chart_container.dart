import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';

/// A container for displaying charts
class ChartContainer extends StatelessWidget {
  final String title;
  final String chartType;
  final List<dynamic> data;
  final Map<String, dynamic> chartConfig;

  const ChartContainer({
    super.key,
    required this.title,
    required this.chartType,
    required this.data,
    required this.chartConfig,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // Equivalent to opacity 0.1
            blurRadius: 4.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              _buildChartTypeIcon(),
            ],
          ),
          const SizedBox(height: 16.0),
          Expanded(
            child: _buildChart(context),
          ),
        ],
      ),
    );
  }

  Widget _buildChartTypeIcon() {
    IconData iconData;
    switch (chartType) {
      case 'LineChart':
        iconData = Icons.show_chart;
        break;
      case 'BarChart':
        iconData = Icons.bar_chart;
        break;
      case 'PieChart':
        iconData = Icons.pie_chart;
        break;
      case 'AreaChart':
        iconData = Icons.stacked_line_chart;
        break;
      default:
        iconData = Icons.analytics;
    }

    return Icon(
      iconData,
      color: AppColors.primaryIndigo,
    );
  }

  Widget _buildChart(BuildContext context) {
    // In a real implementation, we would use a charting library like fl_chart
    // For now, we'll just display a placeholder

    switch (chartType) {
      case 'LineChart':
        return _buildLineChartPlaceholder(context);
      case 'BarChart':
        return _buildBarChartPlaceholder(context);
      case 'PieChart':
        return _buildPieChartPlaceholder(context);
      default:
        return Center(
          child: Text('Chart type not implemented: $chartType'),
        );
    }
  }

  Widget _buildLineChartPlaceholder(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: LineChartPlaceholderPainter(
        data: data,
        xField: chartConfig['x_axis']['data_field'],
        yField: chartConfig['y_axis']['data_field'],
      ),
    );
  }

  Widget _buildBarChartPlaceholder(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: BarChartPlaceholderPainter(
        data: data,
        xField: chartConfig['x_axis']['data_field'],
        yField: chartConfig['y_axis']['data_field'],
        isHorizontal: chartConfig['orientation'] == 'horizontal',
      ),
    );
  }

  Widget _buildPieChartPlaceholder(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: PieChartPlaceholderPainter(
        data: data,
        labelField: chartConfig['x_axis']['data_field'],
        valueField: chartConfig['y_axis']['data_field'],
      ),
    );
  }
}

/// A placeholder painter for line charts
class LineChartPlaceholderPainter extends CustomPainter {
  final List<dynamic> data;
  final String xField;
  final String yField;

  LineChartPlaceholderPainter({
    required this.data,
    required this.xField,
    required this.yField,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..color = AppColors.primaryIndigo
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final dotPaint = Paint()
      ..color = AppColors.primaryIndigo
      ..strokeWidth = 1.0
      ..style = PaintingStyle.fill;

    // Find the maximum y value for scaling
    double maxY = 0;
    for (final item in data) {
      final y = item[yField] as num;
      if (y > maxY) maxY = y.toDouble();
    }

    // Calculate the width of each segment
    final segmentWidth = size.width / (data.length - 1);

    // Create a path for the line
    final path = Path();

    // Move to the first point
    final firstItem = data.first;
    final firstY = firstItem[yField] as num;
    final firstX = 0.0;
    final firstYScaled = size.height - (firstY / maxY * size.height);
    path.moveTo(firstX, firstYScaled);

    // Add points to the path
    for (int i = 1; i < data.length; i++) {
      final item = data[i];
      final y = item[yField] as num;
      final x = i * segmentWidth;
      final yScaled = size.height - (y / maxY * size.height);
      path.lineTo(x, yScaled);
    }

    // Draw the line
    canvas.drawPath(path, paint);

    // Draw dots at each data point
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final y = item[yField] as num;
      final x = i * segmentWidth;
      final yScaled = size.height - (y / maxY * size.height);
      canvas.drawCircle(Offset(x, yScaled), 4.0, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// A placeholder painter for bar charts
class BarChartPlaceholderPainter extends CustomPainter {
  final List<dynamic> data;
  final String xField;
  final String yField;
  final bool isHorizontal;

  BarChartPlaceholderPainter({
    required this.data,
    required this.xField,
    required this.yField,
    required this.isHorizontal,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..color = AppColors.primaryIndigo
      ..style = PaintingStyle.fill;

    // Find the maximum y value for scaling
    double maxY = 0;
    for (final item in data) {
      final y = item[yField] as num;
      if (y > maxY) maxY = y.toDouble();
    }

    if (isHorizontal) {
      // Calculate the height of each bar
      final barHeight = size.height / data.length * 0.7;
      final barSpacing = size.height / data.length * 0.3;

      // Draw each bar
      for (int i = 0; i < data.length; i++) {
        final item = data[i];
        final y = item[yField] as num;
        final barWidth = (y / maxY) * size.width;
        final top = i * (barHeight + barSpacing);

        canvas.drawRect(
          Rect.fromLTWH(0, top, barWidth, barHeight),
          paint,
        );
      }
    } else {
      // Calculate the width of each bar
      final barWidth = size.width / data.length * 0.7;
      final barSpacing = size.width / data.length * 0.3;

      // Draw each bar
      for (int i = 0; i < data.length; i++) {
        final item = data[i];
        final y = item[yField] as num;
        final barHeight = (y / maxY) * size.height;
        final left = i * (barWidth + barSpacing);

        canvas.drawRect(
          Rect.fromLTWH(left, size.height - barHeight, barWidth, barHeight),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// A placeholder painter for pie charts
class PieChartPlaceholderPainter extends CustomPainter {
  final List<dynamic> data;
  final String labelField;
  final String valueField;

  PieChartPlaceholderPainter({
    required this.data,
    required this.labelField,
    required this.valueField,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    // Calculate the total value
    double total = 0;
    for (final item in data) {
      final value = item[valueField] as num;
      total += value.toDouble();
    }

    // Define the center and radius of the pie
    final center = Offset(size.width / 2, size.height / 2);
    final radius =
        size.width < size.height ? size.width / 2 * 0.8 : size.height / 2 * 0.8;

    // Define the starting angle
    double startAngle = 0;

    // Define colors for the pie slices
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.red,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.amber,
    ];

    // Draw each slice
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final value = item[valueField] as num;
      final sweepAngle = (value / total) * 2 * 3.14159;

      final paint = Paint()
        ..color = colors[i % colors.length]
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
