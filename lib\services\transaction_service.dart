import 'package:dio/dio.dart';
import '../models/transaction.dart';
import '../models/transaction_detail.dart';
import '../models/global_objective.dart';
import '../utils/logger.dart';
import '../utils/environment.dart';
import 'base_api_service.dart';
import 'service_locator.dart';
import 'auth_service.dart';

class TransactionService extends BaseApiService {
  // Get auth service instance
  final AuthService _authService = ServiceLocator().authService;
  // Get environment instance
  final Environment _env = Environment.instance;

  // Use environment for endpoint URLs
  String get transactionsUrl => _env.transactionsUrl;
  String get executeUrl => _env.executeUrl;
  String get globalObjectivesUrl => _env.globalObjectivesUrl;
  String get transactionDetailsUrl => _env.transactionDetailsUrl;

  // Get all global objectives
  Future<List<GlobalObjective>> getGlobalObjectives(
      {String tenantId = 't001'}) async {
    try {
      Logger.info('Fetching global objectives from: $globalObjectivesUrl');

      // Get a valid token
      final token = await _authService.getValidToken();

      // Get user ID from SharedPreferences
      final userId = await _authService.getUserId();
      Logger.info('Using user ID for global objectives API: $userId');

      final response = await dio.get(
        '$globalObjectivesUrl?tenant_id=$tenantId${userId != null ? '&user_id=$userId' : ''}',
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Global objectives response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Global objectives fetched successfully');
        final List<dynamic> objectivesJson = response.data;
        return objectivesJson
            .map((json) => GlobalObjective.fromJson(json))
            .toList();
      } else {
        Logger.error(
            'Error fetching global objectives: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.error('Exception while fetching global objectives: $e');
      return [];
    }
  }

  // Get all transactions
  Future<List<Transaction>> getTransactions() async {
    try {
      Logger.info('Fetching transactions from: $transactionsUrl');

      // Get a valid token
      final token = await _authService.getValidToken();

      final response = await dio.get(
        transactionsUrl,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info('Transactions response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Transactions fetched successfully');
        final List<dynamic> transactionsJson = response.data;
        return transactionsJson
            .map((json) => Transaction.fromJson(json))
            .toList();
      } else {
        Logger.error('Error fetching transactions: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.error('Exception while fetching transactions: $e');
      return [];
    }
  }

  // Execute a transaction
  Future<Map<String, dynamic>> executeTransaction(String input) async {
    try {
      Logger.info('Executing transaction at: $executeUrl');

      // Get a valid token
      final token = await _authService.getValidToken();

      final response = await dio.post(
        executeUrl,
        data: {
          'input': input,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Execute transaction response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Transaction executed successfully');
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        Logger.error('Error executing transaction: ${response.statusCode}');
        return {
          'success': false,
          'error': 'Failed to execute transaction',
        };
      }
    } catch (e) {
      Logger.error('Exception while executing transaction: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Check if a global objective is available in the user's transactions
  Future<List<dynamic>> validateGoAvailableInMyTransaction(String goId) async {
    try {
      final url = _env.validateGoAvailableUrl(goId);
      Logger.info('Checking if GO is available in my transactions: $url');

      // Get a valid token
      final token = await _authService.getValidToken();

      final response = await dio.get(
        url,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200) {
        Logger.info('GO availability check successful');

        // Check if the response contains transactions_data
        if (response.data is Map &&
            response.data.containsKey('transactions_data')) {
          final transactionsData =
              response.data['transactions_data'] as List<dynamic>;
          Logger.info(
              'Found ${transactionsData.length} transactions in transactions_data');
          return transactionsData;
        } else if (response.data is List) {
          // Fallback to the original implementation if the response is a list
          Logger.info('Response is a list with ${response.data.length} items');
          return response.data as List<dynamic>;
        } else {
          Logger.warning(
              'Unexpected response format: ${response.data.runtimeType}');
          return [];
        }
      } else {
        Logger.error('Error checking GO availability: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.error('Exception while checking GO availability: $e');
      return [];
    }
  }

  // Get transaction details
  Future<List<TransactionDetail>> getTransactionDetails() async {
    try {
      Logger.info('Fetching transaction details from: $transactionDetailsUrl');

      // Get a valid token
      final token = await _authService.getValidToken();

      // Get user ID from SharedPreferences
      final userId = await _authService.getUserId();
      Logger.info('Using user ID for transaction details API: $userId');

      // Add user_id as query parameter if available
      final url = userId != null
          ? '$transactionDetailsUrl?user_id=$userId'
          : transactionDetailsUrl;

      final response = await dio.get(
        url,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => TransactionDetail.fromJson(json)).toList();
      } else {
        throw Exception(
            'Failed to load transaction details: ${response.statusCode}');
      }
    } catch (e) {
      Logger.error('Error fetching transaction details: $e');
      throw Exception('Failed to fetch transaction details: $e');
    }
  }
}
