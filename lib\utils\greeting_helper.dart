class GreetingHelper {
  /// Returns the appropriate time-based greeting (morning, afternoon, evening, night)
  static String getTimeBasedGreeting() {
    final hour = DateTime.now().hour;

    if (hour >= 6 && hour < 12) {
      return "this morning";
    } else if (hour >= 12 && hour < 18) {
      return "this afternoon";
    } else if (hour >= 18 && hour < 20) {
      return "this evening";
    } else {
      return "tonight";
    }
  }
}
