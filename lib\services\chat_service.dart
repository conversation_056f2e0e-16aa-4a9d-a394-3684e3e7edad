import 'package:dio/dio.dart';
import '../models/message.dart';
import '../utils/logger.dart';
import '../utils/environment.dart';
import 'dio_client.dart';

class ChatService {
  final Environment _env = Environment.instance;
  final Dio _dio = DioClient().client;

  // Use environment for endpoint URL and API key
  String get chatCompletionsUrl => _env.chatCompletionsUrl;
  String? get apiKey => _env.openAiApiKey;

  Future<String> sendMessage(List<Message> messages) async {
    if (apiKey == null ||
        apiKey!.isEmpty ||
        apiKey == 'your_openai_api_key_here') {
      return 'Please set your OpenAI API key in the env file';
    }

    final options = Options(
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
    );

    final data = {
      'model': 'gpt-3.5-turbo',
      'messages': messages
          .map((message) => {
                'role': message.role.toString().split('.').last,
                'content': message.content,
              })
          .toList(),
      'temperature': 0.7,
    };

    try {
      final response = await _dio.post(
        chatCompletionsUrl,
        options: options,
        data: data,
      );

      if (response.statusCode == 200) {
        return response.data['choices'][0]['message']['content'];
      } else {
        Logger.error('Error: ${response.statusCode}');
        Logger.error('Response: ${response.data}');
        return 'Error: ${response.statusCode}. ${response.data}';
      }
    } on DioException catch (e) {
      Logger.error('Dio Exception: ${e.message}');
      return 'Error: ${e.message}';
    } catch (e) {
      Logger.error('Exception: $e');
      return 'Error: $e';
    }
  }
}
