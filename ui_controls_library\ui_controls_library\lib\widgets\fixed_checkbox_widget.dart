import 'package:flutter/material.dart';

class CheckboxWidget extends StatefulWidget {
  // Configurable properties
  final bool initialValue;
  final Color checkColor;
  final Color activeColor;
  final Color checkOutlineColor;
  final Color? hoverColor;
  final Color? focusColor;
  final Function(bool)? onChanged;  // Callback for checkbox state change
  final double size;
  final String? label;
  final bool showLabel;
  final bool isDisabled;
  final bool isRounded;
  final double borderRadius;
  final double borderWidth;
  final TextAlign labelPosition;
  final EdgeInsetsGeometry padding;
  final bool isVerticalLayout;
  final TextStyle? labelStyle;
  final MaterialTapTargetSize? tapTargetSize;
  final bool tristate;
  final bool? isError;
  final String? semanticLabel;

  const CheckboxWidget({
    super.key,
    this.initialValue = false,        // Default value is false
    this.checkColor = Colors.white,    // Default checkmark color is white
    this.activeColor = Colors.blue,    // Default active color is blue
    this.checkOutlineColor = Colors.black,  // Default outline color is black
    this.hoverColor,                  // Color when hovered
    this.focusColor,                  // Color when focused
    this.onChanged,                   // Callback for state change
    this.size = 40.0,                 // Default size is 40
    this.label,                       // Optional label text
    this.showLabel = true,            // Whether to show the label
    this.isDisabled = false,          // Whether the checkbox is disabled
    this.isRounded = false,           // Whether to use rounded corners
    this.borderRadius = 4.0,          // Border radius for rounded corners
    this.borderWidth = 1.5,           // Width of the border
    this.labelPosition = TextAlign.end, // Position of the label (start=left, end=right)
    this.padding = const EdgeInsets.all(8.0), // Padding around the widget
    this.isVerticalLayout = false,    // Whether to arrange checkbox and label vertically
    this.labelStyle,                  // Custom style for the label
    this.tapTargetSize = MaterialTapTargetSize.shrinkWrap, // Size of the tap target
    this.tristate = false,            // Whether to allow null state
    this.isError = false,             // Whether to show error state
    this.semanticLabel,               // Semantic label for accessibility
  });

  @override
  State<CheckboxWidget> createState() => _CheckboxWidgetState();
}

class _CheckboxWidgetState extends State<CheckboxWidget> {
  late bool _isChecked;  // The current state of the checkbox

  @override
  void initState() {
    super.initState();
    _isChecked = widget.initialValue;  // Initialize with the provided value
  }

  @override
  Widget build(BuildContext context) {
    // Create the checkbox with all the specified properties
    final checkbox = SizedBox(
      width: widget.size,
      height: widget.size,
      child: Semantics(
        label: widget.semanticLabel,
        child: Checkbox(
          value: _isChecked,  // The current state of the checkbox
          tristate: widget.tristate, // Allow null state if tristate is true
          onChanged: widget.isDisabled ? null : (bool? newValue) {
            setState(() {
              _isChecked = newValue ?? false;  // Update the state with the new value
            });
            if (widget.onChanged != null) {
              widget.onChanged!(_isChecked);  // Trigger callback with new value
            }
          },
          checkColor: widget.checkColor,  // Set the checkmark color
          activeColor: widget.activeColor,  // Set the color when checkbox is checked
          hoverColor: widget.hoverColor, // Color when hovered
          focusColor: widget.focusColor, // Color when focused
          side: BorderSide(
            color: widget.isError == true ? Colors.red : widget.checkOutlineColor,  // Set the outline color
            width: widget.borderWidth,
          ),
          shape: widget.isRounded ? RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ) : null,
          materialTapTargetSize: widget.tapTargetSize, // Set the tap target size
        ),
      ),
    );

    // If no label or showLabel is false, just return the checkbox
    if (widget.label == null || !widget.showLabel) {
      return Padding(
        padding: widget.padding,
        child: checkbox,
      );
    }

    // Create the label widget with custom style or default style
    final labelWidget = Text(
      widget.label!,
      style: widget.labelStyle ?? TextStyle(
        fontSize: widget.size / 2.5,
        color: widget.isDisabled ? Colors.grey : (widget.isError == true ? Colors.red : Colors.black87),
        fontWeight: FontWeight.normal,
      ),
    );

    // Arrange the checkbox and label based on the labelPosition and layout
    if (widget.isVerticalLayout) {
      // Vertical layout (top/bottom)
      List<Widget> columnChildren = [];

      if (widget.labelPosition == TextAlign.start) {
        // Label on top
        columnChildren.add(labelWidget);
        columnChildren.add(const SizedBox(height: 8));
        columnChildren.add(checkbox);
      } else {
        // Label on bottom
        columnChildren.add(checkbox);
        columnChildren.add(const SizedBox(height: 8));
        columnChildren.add(labelWidget);
      }

      return Padding(
        padding: widget.padding,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: columnChildren,
        ),
      );
    } else {
      // Horizontal layout (left/right)
      List<Widget> rowChildren = [];

      if (widget.labelPosition == TextAlign.start) {
        // Label on left (leading)
        rowChildren.add(labelWidget);
        rowChildren.add(const SizedBox(width: 8));
        rowChildren.add(checkbox);
      } else {
        // Label on right (trailing)
        rowChildren.add(checkbox);
        rowChildren.add(const SizedBox(width: 8));
        rowChildren.add(labelWidget);
      }

      return Padding(
        padding: widget.padding,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: rowChildren,
        ),
      );
    }
  }
}
