import 'package:flutter/material.dart';
import '../editors/app_quill_editor.dart';
import '../theme/app_theme.dart';
import '../cards/app_card.dart';

/// A showcase component for the Quill rich text editor
class QuillEditorShowcase extends StatefulWidget {
  const QuillEditorShowcase({super.key});

  @override
  State<QuillEditorShowcase> createState() => _QuillEditorShowcaseState();
}

class _QuillEditorShowcaseState extends State<QuillEditorShowcase> {
  String _editorContent = '';

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quill Rich Text Editor',
              style: AppTheme.headingMedium,
            ),
            const SizedBox(height: AppTheme.spacingM),

            // Basic editor with toolbar
            const Text(
              'Basic Editor with Toolbar',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingS),
            AppQuillEditor(
              initialText: 'Try formatting this text using the toolbar above.',
              onTextChanged: (text) {
                setState(() {
                  _editorContent = text;
                });
              },
              minHeight: 250,
              maxHeight: 500,
              placeholder: 'Start typing here...',
            ),
            const SizedBox(height: AppTheme.spacingM),

            // Read-only editor
            const Text(
              'Read-only Editor',
              style: AppTheme.headingSmall,
            ),
            const SizedBox(height: AppTheme.spacingS),
            AppQuillEditor(
              initialText:
                  'This is a read-only editor. You cannot edit this text.',
              readOnly: true,
              showToolbar: false,
              minHeight: 100,
            ),
            const SizedBox(height: AppTheme.spacingM),

            // Content preview
            if (_editorContent.isNotEmpty) ...[
              const Text(
                'Editor Content Preview',
                style: AppTheme.headingSmall,
              ),
              const SizedBox(height: AppTheme.spacingS),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Text(_editorContent),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
