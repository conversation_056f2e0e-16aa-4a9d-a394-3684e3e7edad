import 'dart:math' show min, max;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_book_solution_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/agent_table.dart';
import 'package:nsl/screens/web/new_design/web_inside_book_solution.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_new.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_details_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_table.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/role_details_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/workflow_table.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_entity_version_dropdown.dart';
import 'package:nsl/screens/web/new_design/web_add_modules_page.dart';
import 'package:nsl/theme/app_theme_constants.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/resizable_panel.dart';
import 'package:provider/provider.dart';
import '../../../../../theme/app_colors.dart';

class WebBookSolutionPage extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const WebBookSolutionPage({super.key, this.initialData});

  @override
  State<WebBookSolutionPage> createState() => _WebBookSolutionPageState();
}

class _WebBookSolutionPageState extends State<WebBookSolutionPage> {
  bool _showRightPanel = false;
  bool _isExpanded = false;
  bool _showUploadModal = false;
  int selectedSectionIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        return Consumer<ManualCreationProvider>(
          builder: (context, manualCreationprovider, child) {
            return Scaffold(
              backgroundColor: const Color(0xFFF5F7FA),
              body: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: SizedBox()),
                  Expanded(
                    flex: 8,
                    child: Row(
                      children: [
                        // Main content
                        Expanded(
                          child: Column(
                            children: [
                              _buildHeader(
                                  context, provider, manualCreationprovider),

                              // Row(
                              //   mainAxisAlignment: MainAxisAlignment.end,
                              //   crossAxisAlignment: CrossAxisAlignment.end,
                              //   children: [
                              //     Text("data"),
                              //   ],
                              // ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: AppSpacing.lg),
                                  child: manualCreationprovider
                                              .showAgentTableForBook ||
                                          manualCreationprovider
                                              .showEntityTableForBook ||
                                          manualCreationprovider
                                              .showWorkflowTableForBook ||
                                          manualCreationprovider
                                              .showSolutionsForBook
                                      ? mainContent(
                                          context, manualCreationprovider)
                                      : _buildSolutionsList(provider),
                                ),
                              ),
                              ChatField(
                                  isGeneralLoading: false,
                                  isFileLoading: false,
                                  isSpeechLoading: false,
                                  onSendMessage: () {},
                                  controller: TextEditingController(),
                                  onFileSelected: (p0, p1) {},
                                  onToggleRecording: () {})
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: _isExpanded ? 4 : 1,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Visibility(
                          visible:
                              !manualCreationprovider.showSideEntityPanel &&
                                  !manualCreationprovider.showSidePanel,
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _showRightPanel = !_showRightPanel;
                                  _isExpanded = !_isExpanded;
                                });
                              },
                              child: Padding(
                                padding: EdgeInsets.only(
                                    top: 16,
                                    right: 12), // adjust value as needed
                                child: AnimatedRotation(
                                  turns: _isExpanded
                                      ? 0.5
                                      : 0.0, // 0.5 turns = 180 degrees
                                  duration: Duration(milliseconds: 300),
                                  child: SvgPicture.asset(
                                    'assets/images/expand-arrow-left-new.svg',
                                    width: AppSpacing.md,
                                    height: AppSpacing.md,
                                    colorFilter: ColorFilter.mode(
                                      AppColors
                                          .textPrimaryLight, // Replaced Colors.black
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        // Right panel
                        if (_showRightPanel &&
                            !manualCreationprovider.showSideEntityPanel)
                          Container(
                            width: MediaQuery.of(context).size.width * 0.22,
                            decoration: BoxDecoration(
                              color: AppColors
                                  .surfaceLight, // Replaced Colors.white
                              border: Border(
                                left: BorderSide(
                                  color: AppColors
                                      .greyBg, // Replaced Colors.grey.shade200
                                  width: 1,
                                ),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.textPrimaryLight.withOpacity(
                                      0.03), // Replaced Colors.black.withValues(alpha: 0.03)
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // Header
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 10),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border(
                                      bottom: BorderSide(
                                          color: Colors.grey.shade200,
                                          width: 1),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'All Documents/Artefacts',
                                        style: FontManager.getCustomStyle(
                                          fontWeight: FontManager.semiBold,
                                          fontSize: FontManager.s14,
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? AppColors.textPrimaryDark
                                              : Colors.black,
                                          fontFamily:
                                              FontManager.fontFamilyInter,
                                        ),
                                      ),
                                      Container(
                                        width: AppSpacing.lg,
                                        height: AppSpacing.lg,
                                        decoration: BoxDecoration(
                                          color: _showUploadModal
                                              ? AppColors.textBlue2
                                              : Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: _showUploadModal
                                                  ? AppColors.textBlue
                                                  : AppColors
                                                      .greyBg, // Replaced Color(0xFF0058FF)
                                              width: 1),
                                        ),
                                        child: IconButton(
                                          icon: Icon(
                                              _showUploadModal
                                                  ? Icons.close
                                                  : Icons.add,
                                              color: _showUploadModal
                                                  ? Colors.white
                                                  : Colors.black,
                                              size: AppSpacing.md),
                                          padding: EdgeInsets.zero,
                                          onPressed: () {
                                            setState(() {
                                              _showUploadModal =
                                                  !_showUploadModal;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Scrollable content area
                                Expanded(
                                  child: Container(
                                    color: Colors.white,
                                    child: SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 20, vertical: 8),
                                        child: _showUploadModal
                                            ? _buildUploadModal()
                                            : Container(), // Show upload modal when active
                                      ),
                                    ),
                                  ),
                                ),
                                // Bottom input
                                Container(
                                  height: MediaQuery.of(context).size.height *
                                      0.2, // Dynamic height based on screen size
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(
                                      color: Colors.grey.shade200,
                                      width: 1,
                                    ),
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(16),
                                      topRight: Radius.circular(16),
                                      bottomLeft: Radius.circular(0),
                                      bottomRight: Radius.circular(0),
                                    ),
                                  ),
                                  child: TextField(
                                    maxLines: 6, // or null for infinite lines
                                    minLines: 4,
                                    keyboardType: TextInputType.multiline,
                                    decoration: InputDecoration(
                                      hintText: 'Add Instructions',
                                      hintStyle: TextStyle(
                                          fontSize: AppSpacing.sm,
                                          color: Colors.grey.shade500),
                                      filled: true,
                                      fillColor: AppColors
                                          .surfaceLight, // Replaced Color(0xFFFFFFFF)
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(4),
                                        borderSide: BorderSide(
                                            color: AppColors
                                                .greyBg, // Replaced Colors.grey.shade200
                                            width: 1),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(4),
                                        borderSide: BorderSide(
                                            color: AppColors
                                                .greyBg, // Replaced Colors.grey.shade200
                                            width: 1),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(4),
                                        borderSide: BorderSide(
                                            color: Colors.blue, width: 1),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (manualCreationprovider.showSidePanel)
                    ResizablePanel(
                      width: manualCreationprovider.sidePanelWidth,
                      minWidth: manualCreationprovider.minSidePanelWidth,
                      maxWidth: manualCreationprovider.maxSidePanelWidth,
                      handlePosition: ResizeHandlePosition.left,
                      onResize: (newWidth) {
                        manualCreationprovider.updateSidePanelWidth(newWidth);
                      },
                      child: RoleDetailsPanel(
                        role: manualCreationprovider.selectedRole!,
                        onClose: () {
                          manualCreationprovider.hideSidePanel();
                        },
                        showLegacySections: false,
                        users: manualCreationprovider.extractedUsers,
                      ),
                    )
                  else if (manualCreationprovider.showSideEntityPanel &&
                      !_showRightPanel)
                    ResizablePanel(
                      width: manualCreationprovider.sidePanelWidth,
                      minWidth: manualCreationprovider.minSidePanelWidth,
                      maxWidth: manualCreationprovider.maxSidePanelWidth,
                      handlePosition: ResizeHandlePosition.left,
                      onResize: (newWidth) {
                        manualCreationprovider.updateSidePanelWidth(newWidth);
                      },
                      child: EntityDetailsPanel(
                        entity: manualCreationprovider.selectedEntity!,
                        onClose: () =>
                            manualCreationprovider.hideEntitySidePanel(),
                        chatController: null,
                        onSendMessage: null,
                        globalEntityElements: {},
                      ),
                    )
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebBookSolutionProvider provider,
      ManualCreationProvider manualCreationprovider) {
    return Container(
      height: AppSpacing.xxl,
      padding: const EdgeInsets.only(left: 0),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left section - Back button and Book Name
          Row(
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(4),
                    onTap: () {
                      if (manualCreationprovider.showAgentTableForBook ||
                          manualCreationprovider.showEntityTableForBook ||
                          manualCreationprovider.showWorkflowTableForBook ||
                          manualCreationprovider.showSolutionsForBook) {
                        manualCreationprovider.clearBookResults();
                      } else {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMyLibrary;
                      }
                    },
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    child: Row(
                      children: [
                        Icon(
                          Icons.arrow_back,
                          color: Colors.grey,
                          size: AppSpacing.md,
                        ),
                        const SizedBox(width: AppSpacing.xs),
                        Text(
                          'Previous page',
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            fontWeight: FontManager.medium,
                            color: Colors.black,
                            fontFamily: FontManager.fontFamilyInter,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

          // Row(
          //   children: [
          //     MouseRegion(
          //       cursor: SystemMouseCursors.click,
          //       child: InkWell(
          //         borderRadius: BorderRadius.circular(4),
          //         onTap: () {
          //           if (manualCreationprovider.showAgentTableForBook ||
          //               manualCreationprovider.showEntityTableForBook ||
          //               manualCreationprovider.showWorkflowTableForBook ||
          //               manualCreationprovider.showSolutionsForBook) {
          //             manualCreationprovider.clearBookResults();
          //           } else {
          //             Provider.of<WebHomeProvider>(context, listen: false)
          //                 .currentScreenIndex = ScreenConstants.webMyLibrary;
          //           }
          //         },
          //         child: Icon(
          //           Icons.arrow_back,
          //           color: Colors.grey,
          //           size: AppSpacing.md,
          //         ),
          //       ),
          //     ),
          //     const SizedBox(width: AppSpacing.xs),
          //     Text(
          //       'Previous page',
          //       style: FontManager.getCustomStyle(
          //         fontSize: FontManager.s14,
          //         fontWeight: FontManager.medium,
          //         color: Colors.black,
          //         fontFamily: FontManager.fontFamilyInter,
          //       ),
          //     ),
          //   ],
          // ),
          // Center section with count items grouped together
          if ((manualCreationprovider.showSideEntityPanel &&
                  !_showRightPanel) ||
              manualCreationprovider.showSidePanel)
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ExpandedAgentObjectMenu(),
                ],
              ),
            ),
          if (!manualCreationprovider.showSideEntityPanel &&
              !_showRightPanel &&
              !manualCreationprovider.showSidePanel)
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildCountItem('assets/images/agent.svg', '5 Agents',
                      () {
                    manualCreationprovider.handleAgentValidationForBook();
                  }),
                  const SizedBox(width: AppSpacing.lg),
                  _buildCountItem(
                      'assets/images/cube-box.svg', '4 Objects', () {
                    manualCreationprovider.handleEntityValidationForBook();
                  }),
                  const SizedBox(width: AppSpacing.lg),
                  _buildCountItem(
                      'assets/images/square-box-uncheck.svg', '15 Solutions',
                      () {
                    // manualCreationprovider.handleWorkflowValidationForBook();
                    manualCreationprovider.handleWorkflowTableForBook();
                  }),
                  // const SizedBox(width: AppSpacing.lg),
                ],
              ),
            ),

          // Right section - Add Modules button
          MouseRegion(
            cursor: SystemMouseCursors.click,
            onEnter: (_) {
              provider.isAddModulesHeaderButtonHovering = true;
            },
            onExit: (_) {
              provider.isAddModulesHeaderButtonHovering = false;
            },
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WebAddModulesPage(),
                  ),
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.transparent,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      'assets/images/add-module.svg',
                      width: AppSpacing.size14,
                      height: AppSpacing.size14,
                      colorFilter: ColorFilter.mode(
                        Colors.grey.shade600,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Add Modules',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                        fontWeight: FontManager.medium,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xxs),
                    Icon(
                      Icons.arrow_drop_down,
                      size: AppSpacing.lg,
                      color: provider.isAddModulesHeaderButtonHovering
                          ? const Color(0xff0058FF)
                          : Colors.grey.shade600,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountItem(String iconPath, String text, Function()? onTap) {
    return Consumer<ManualCreationProvider>(
      builder: (context, manualCreationProvider, child) {
        // Determine if this item is active based on the current state
        bool isActive = false;
        if (text.contains('Agent')) {
          isActive = manualCreationProvider.showAgentTableForBook;
        } else if (text.contains('Objects')) {
          isActive = manualCreationProvider.showEntityTableForBook;
        } else if (text.contains('Solutions')) {
          isActive = manualCreationProvider.showWorkflowTableForBook ||
              manualCreationProvider.showSolutionsForBook;
        }
        bool isHovered = false;
        return StatefulBuilder(
          builder: (context, setLocalState) {
            return MouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (_) => setLocalState(() => isHovered = true),
              onExit: (_) => setLocalState(() => isHovered = false),
              child: GestureDetector(
                onTap: onTap,
                child: Container(
                  width: 126, // Fixed width to match hover_nav_item
                  height: 34,
                  decoration: BoxDecoration(
                    color: isActive
                        ? const Color(0xff0058FF)
                        : (isHovered
                            ? const Color(0xffF0F0F0)
                            : Colors.transparent),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Center(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          iconPath,
                          width: 14,
                          height: 14,
                          colorFilter: ColorFilter.mode(
                            isActive ? Colors.white : Colors.black87,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          text,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            color: isActive ? Colors.white : Colors.black87,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSolutionsList(WebBookSolutionProvider provider) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final dynamicHeight = constraints.maxHeight *
            0.8; // Calculate dynamic height based on parent constraints
        return SizedBox(
          height: dynamicHeight,
          child: Column(
            children: provider.solutionItems
                .take(12)
                .map((item) => _buildSolutionItem(item))
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildSolutionItem(SolutionItem item) {
    return StatefulBuilder(
      builder: (context, setState) {
        bool isHovered = false;

        return MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: Container(
            margin: const EdgeInsets.only(
              bottom: 16,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isHovered ? const Color(0xff0058FF) : Color(0xffD0D0D0),
                width: 1.0,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.title,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.textPrimaryDark
                                    : const Color(0xff000000),
                            fontWeight: FontManager.semiBold,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          'Last Message: ${item.lastMessageTime}',
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            fontWeight: FontManager.regular,
                            color: Colors.grey.shade600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Right content
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      CustomEntityVersionDropdown(
                        selectedVersion: item.versionId,
                        versions: [
                          'V00113',
                          'V00012',
                          'V00001',
                          'V00000'
                        ], // Available versions
                        onVersionSelected: (String selectedVersion) {
                          // Handle version selection
                          // You can update the provider or item state here
                          print('Selected version: $selectedVersion');
                        },
                      ),
                      const SizedBox(height: AppSpacing.xxs),
                      Text(
                        item.date,
                        style: TextStyle(
                          fontSize: AppSpacing.sm,
                          fontFamily: 'TiemposText',
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUploadModal() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 1),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius:
                BorderRadius.circular(AppThemeConstants.borderRadiusM),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: IntrinsicWidth(
            // Makes width fit the content
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Upload a file option
                Container(
                  padding: EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                    ),
                  ),
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        // Handle file upload
                      },
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/images/attached-2.svg',
                            width: AppSpacing.size14,
                            height: AppSpacing.size14,
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Text(
                            'Upload a File',
                            style: FontManager.getCustomStyle(
                              fontWeight: FontManager.medium,
                              fontSize: FontManager.s12,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? AppColors.textPrimaryDark
                                  : AppColors.textPrimaryLight,
                              fontFamily: 'Inter',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                // Add from Google Drive option

                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      // Handle Google Drive integration
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/G-Drive.svg',
                          width: AppSpacing.size14,
                          height: AppSpacing.size14,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Text('Add From Google Drive',
                            style: FontManager.getCustomStyle(
                              fontWeight: FontManager.medium,
                              fontSize: FontManager.s12,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? AppColors.textPrimaryDark
                                  : AppColors.textPrimaryLight,
                              fontFamily: 'Inter',
                            )),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget mainContent(context, ManualCreationProvider manualCreationprovider) {
    return manualCreationprovider.showAgentTableForBook
        ? AgentTable(
            provider: manualCreationprovider,
            onRoleSelected: (role) {
              manualCreationprovider.showRoleDetailsPanel(role);
            },
          )
        : manualCreationprovider.showEntityTableForBook
            ? EntityTable(
                provider: manualCreationprovider,
                onEntitySelected: (entity) {
                  manualCreationprovider.showEntityDetailsPanel(entity);
                },
                selectedSectionIndex: selectedSectionIndex,
                onSectionIndexChanged: (index) {
                  setState(() {
                    selectedSectionIndex = index;
                  });
                },
              )
            : manualCreationprovider.showSolutionsForBook
                ? WebInsideBookSolutionPage()
                : manualCreationprovider.showWorkflowTableForBook
                    ? WorkflowTable(
                        provider: manualCreationprovider,
                        onNodeSelected: () {
                          if (manualCreationprovider
                                  .workflowLoValidationResult !=
                              null) {
                            manualCreationprovider.showWorkflowLoDetailsPanel(
                                manualCreationprovider
                                    .workflowLoValidationResult!);
                          }
                        },
                      )
                    : Container();
  }
}

class _PaginationChevronButton extends StatelessWidget {
  final bool isLeft;
  const _PaginationChevronButton({this.isLeft = false});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        final isHovering = isLeft
            ? provider.isPaginationLeftChevronHovering
            : provider.isPaginationRightChevronHovering;

        final bool isDisabled = isLeft
            ? provider.currentPage <= 1
            : provider.currentPage >= provider.totalPages;

        return MouseRegion(
          cursor:
              isDisabled ? SystemMouseCursors.basic : SystemMouseCursors.click,
          onEnter: (_) {
            if (!isDisabled) {
              if (isLeft) {
                provider.isPaginationLeftChevronHovering = true;
              } else {
                provider.isPaginationRightChevronHovering = true;
              }
            }
          },
          onExit: (_) {
            if (!isDisabled) {
              if (isLeft) {
                provider.isPaginationLeftChevronHovering = false;
              } else {
                provider.isPaginationRightChevronHovering = false;
              }
            }
          },
          child: GestureDetector(
            onTap: isDisabled
                ? null
                : () {
                    if (isLeft) {
                      provider.previousPage();
                    } else {
                      provider.nextPage();
                    }
                  },
            child: AnimatedContainer(
              duration: Duration(milliseconds: 120),
              decoration: BoxDecoration(
                color: isDisabled
                    ? Colors.grey.shade100
                    : (isHovering
                        ? (Theme.of(context).brightness == Brightness.dark
                            ? AppColors.textPrimaryDark
                            : Color(0xFFF5F7FA))
                        : Colors.transparent),
                border: isHovering && !isDisabled
                    ? Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.textPrimaryDark
                            : Color(0xFFBDBDBD))
                    : Border.all(color: Colors.transparent),
                borderRadius:
                    BorderRadius.circular(AppThemeConstants.borderRadiusS),
              ),
              child: Icon(
                isLeft ? Icons.chevron_left : Icons.chevron_right,
                size: FontManager.s22,
                color: isDisabled ? Colors.grey.shade300 : Colors.grey.shade600,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Hover solution item widget that follows the consistent hover pattern
class _HoverSolutionItem extends StatefulWidget {
  final int itemIndex;
  final double screenWidth;

  const _HoverSolutionItem({
    required this.itemIndex,
    required this.screenWidth,
  });

  @override
  State<_HoverSolutionItem> createState() => _HoverSolutionItemState();
}

class _HoverSolutionItemState extends State<_HoverSolutionItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Adjust font size based on screen width
    double fontSize;
    double itemHeight;
    double verticalPadding;
    double horizontalPadding;

    if (widget.screenWidth <= 1366) {
      fontSize = FontManager.s14;
      itemHeight = 32;
      verticalPadding = 4;
      horizontalPadding = 6;
    } else if (widget.screenWidth <= 1560) {
      fontSize = FontManager.s14;
      itemHeight = 30;
      verticalPadding = 5;
      horizontalPadding = 7;
    } else {
      fontSize = FontManager.s14;
      itemHeight = 34;
      verticalPadding = 7;
      horizontalPadding = 7;
    }

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Container(
        height: itemHeight,
        padding: EdgeInsets.symmetric(
            vertical: verticalPadding, horizontal: horizontalPadding),
        decoration: BoxDecoration(
          border: Border.all(
            color: isHovered ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            'Solutions-${widget.itemIndex.toString().padLeft(2, '0')}',
            style: FontManager.getCustomStyle(
              fontSize: fontSize,
              fontWeight:
                  isHovered ? FontManager.semiBold : FontManager.regular,
              color: isHovered
                  ? (Theme.of(context).brightness == Brightness.dark
                      ? AppColors.textPrimaryLight
                      : AppColors.textBlue)
                  : (Theme.of(context).brightness == Brightness.dark
                      ? AppColors.textPrimaryDark
                      : AppColors.textPrimaryLight),
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
      ),
    );
  }
}

class ExpandedAgentObjectMenu extends StatelessWidget {
  const ExpandedAgentObjectMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ToggleMenuProvider(),
      child: Consumer<ToggleMenuProvider>(
        builder: (context, provider, child) {
          return MouseRegion(
            onEnter: (_) => provider.setHovered(true),
            onExit: (_) => provider.setHovered(false),
            child: InkWell(
              onTap: () {},
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              splashFactory: NoSplash.splashFactory,
              child: Center(
                child: Container(
                  height: 28,
                  width: 28,
                  // margin: EdgeInsets.only(top: 0),
                  decoration: BoxDecoration(
                    color: provider.isMenuOpen
                        ? Color(0xff0058FF)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Center(
                    child: PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        size: 20,
                        color: provider.isMenuOpen
                            ? Colors.white
                            : provider.isHovered
                                ? Color(0xff0058FF)
                                : Colors.grey.shade800,
                      ),
                      onSelected: (String value) {
                        if (value == 'agents') {
                          Provider.of<ManualCreationProvider>(context,
                                  listen: false)
                              .handleAgentValidationForBook();
                        } else if (value == 'solution') {
                          Provider.of<ManualCreationProvider>(context,
                                  listen: false)
                              .handleWorkflowTableForBook();

                          // .handleWorkflowValidationForBook();
                        } else if (value == 'objects') {
                          Provider.of<ManualCreationProvider>(context,
                                  listen: false)
                              .handleEntityValidationForBook();
                        } else {
                          // Fallback for any other menu selections
                          provider.handleMenuSelection(value);
                        }
                      },
                      onOpened: () {
                        provider.setMenuOpen(true);
                      },
                      onCanceled: () {
                        provider.setMenuOpen(false);
                      },
                      constraints: BoxConstraints(
                        minWidth: 100,
                        maxWidth: 120,
                      ),
                      itemBuilder: (BuildContext context) =>
                          <PopupMenuEntry<String>>[
                        PopupMenuItem<String>(
                          value: 'agents',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/agent.svg',
                                width: 12,
                                height: 12,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  '5 Agents',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "TiemposText",
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'objects',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/cube-box.svg',
                                width: 12,
                                height: 12,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  '4 Objects',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "TiemposText",
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'solution',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/square-box-uncheck.svg',
                                width: 12,
                                height: 12,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  '15 Solutions',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "TiemposText",
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      offset: const Offset(0, 35),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: BorderSide(
                          color: Color(0xFF0058FF),
                          width: 0.5,
                        ),
                      ),
                      elevation: 8,
                      color: Colors.white,
                      splashRadius: 20,
                      padding: EdgeInsets.symmetric(vertical: 4),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
