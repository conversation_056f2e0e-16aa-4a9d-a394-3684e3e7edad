import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/message.dart';
import '../utils/constants.dart';
import 'base_repository.dart';

/// A repository for message data
class MessageRepository extends BaseRepository<Message> {
  MessageRepository(super.apiService);

  @override
  Future<List<Message>> getAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(AppConstants.chatMessagesKey);

      if (jsonString == null) {
        return [];
      }

      final jsonList = jsonDecode(jsonString) as List<dynamic>;
      return fromJsonList(jsonList);
    } catch (e) {
      logError('getAll', e);
      return [];
    }
  }

  @override
  Future<Message?> getById(String id) async {
    try {
      final messages = await getAll();
      return messages.firstWhere((message) => message.id == id);
    } catch (e) {
      logError('getById', e);
      return null;
    }
  }

  @override
  Future<Message?> create(Message item) async {
    try {
      final messages = await getAll();
      messages.add(item);
      await _saveMessages(messages);
      return item;
    } catch (e) {
      logError('create', e);
      return null;
    }
  }

  @override
  Future<Message?> update(Message item) async {
    try {
      final messages = await getAll();
      final index = messages.indexWhere((m) => m.id == item.id);

      if (index == -1) {
        return null;
      }

      messages[index] = item;
      await _saveMessages(messages);
      return item;
    } catch (e) {
      logError('update', e);
      return null;
    }
  }

  @override
  Future<bool> delete(String id) async {
    try {
      final messages = await getAll();
      final index = messages.indexWhere((m) => m.id == id);

      if (index == -1) {
        return false;
      }

      messages.removeAt(index);
      await _saveMessages(messages);
      return true;
    } catch (e) {
      logError('delete', e);
      return false;
    }
  }

  @override
  Message fromJson(Map<String, dynamic> json) {
    return Message.fromJson(json);
  }

  /// Save messages to shared preferences
  Future<void> _saveMessages(List<Message> messages) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = messages.map((message) => message.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      await prefs.setString(AppConstants.chatMessagesKey, jsonString);
    } catch (e) {
      logError('_saveMessages', e);
    }
  }

  /// Clear all messages
  Future<void> clearMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.chatMessagesKey);
    } catch (e) {
      logError('clearMessages', e);
    }
  }

  /// Add a user message
  Future<Message?> addUserMessage(String content) async {
    try {
      final message = Message.user(content: content);
      return await create(message);
    } catch (e) {
      logError('addUserMessage', e);
      return null;
    }
  }

  /// Add an NSL message
  Future<Message?> addNslMessage({
    required String content,
    MessageContentType contentType = MessageContentType.text,
    String? title,
    String? language,
  }) async {
    try {
      final message = Message.nsl(
        content: content,
        contentType: contentType,
        title: title,
        language: language,
      );
      return await create(message);
    } catch (e) {
      logError('addNslMessage', e);
      return null;
    }
  }

  /// Add a system message
  Future<Message?> addSystemMessage(String content) async {
    try {
      final message = Message.system(content: content);
      return await create(message);
    } catch (e) {
      logError('addSystemMessage', e);
      return null;
    }
  }
}
