import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../models/message.dart';
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';

class ChatNSLBubble extends StatelessWidget {
  final Message message;
  final VoidCallback? onLongPress;

  const ChatNSLBubble({
    super.key,
    required this.message,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    // Get device type for responsive spacing
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    return GestureDetector(
        onLongPress: onLongPress,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            vertical:
                AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
          ),
          child: Center(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal:
                    AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType),
              ),
              constraints: BoxConstraints(
                maxWidth: 800, // Limit max width for very large screens
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Display the message content using Markdown
                  MarkdownBody(
                    data: message.content,
                    styleSheet: MarkdownStyleSheet(
                      p: TextStyle(
                        color: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.color, // Use theme text color
                        fontFamily:
                            'TiemposText', // Use TiemposText font for NSL responses
                      ),
                      h1: TextStyle(
                        color: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.color, // Use theme text color
                        fontFamily: 'TiemposText',
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                      h2: TextStyle(
                        color: Theme.of(context)
                            .textTheme
                            .headlineSmall
                            ?.color, // Use theme text color
                        fontFamily: 'TiemposText',
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                      h3: TextStyle(
                        color: Theme.of(context)
                            .textTheme
                            .titleLarge
                            ?.color, // Use theme text color
                        fontFamily: 'TiemposText',
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      blockquote: TextStyle(
                        color: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.color
                            ?.withAlpha(204), // 0.8 opacity (204/255)
                        fontFamily: 'TiemposText',
                        fontStyle: FontStyle.italic,
                      ),
                      em: TextStyle(
                        fontFamily: 'TiemposText',
                        fontStyle: FontStyle.italic,
                      ),
                      strong: TextStyle(
                        fontFamily: 'TiemposText',
                        fontWeight: FontWeight.bold,
                      ),
                      code: TextStyle(
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        color: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.color, // Use theme text color
                        fontFamily:
                            'SFProText', // Keep SFProText for code blocks
                      ),
                      codeblockDecoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
}
