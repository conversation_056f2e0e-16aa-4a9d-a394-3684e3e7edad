import 'package:flutter/material.dart';
import 'dart:math';

/// A widget that calculates and displays various financial and other ratios.
///
/// This widget allows users to input values and calculate different types of ratios
/// such as financial ratios (liquidity, profitability, etc.), mathematical ratios,
/// and custom ratios with visualization options.
class RatioAnalysisWidget extends StatefulWidget {
  /// Initial numerator value
  final double? initialNumerator;

  /// Initial denominator value
  final double? initialDenominator;

  /// Initial ratio type
  final RatioType ratioType;

  /// Whether to show the ratio visualization
  final bool showVisualization;

  /// Whether to show the ratio interpretation
  final bool showInterpretation;

  /// Whether to show the ratio formula
  final bool showFormula;

  /// Whether to show the ratio benchmark comparison
  final bool showBenchmark;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color for the ratio visualization
  final Color ratioColor;

  /// The color for the benchmark visualization
  final Color benchmarkColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the ratio result changes
  final Function(double, String)? onRatioCalculated;

  /// Creates a ratio analysis widget.
  const RatioAnalysisWidget({
    super.key,
    this.initialNumerator,
    this.initialDenominator,
    this.ratioType = RatioType.custom,
    this.showVisualization = true,
    this.showInterpretation = true,
    this.showFormula = true,
    this.showBenchmark = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.ratioColor = Colors.blue,
    this.benchmarkColor = Colors.green,
    this.width,
    this.height,
    this.onRatioCalculated,
  });

  @override
  State<RatioAnalysisWidget> createState() => _RatioAnalysisWidgetState();
}

/// Enum for ratio types
enum RatioType {
  /// Custom ratio (user-defined)
  custom,

  /// Current ratio (Current Assets / Current Liabilities)
  currentRatio,

  /// Quick ratio ((Current Assets - Inventory) / Current Liabilities)
  quickRatio,

  /// Debt-to-Equity ratio (Total Debt / Total Equity)
  debtToEquity,

  /// Return on Assets (Net Income / Total Assets)
  returnOnAssets,

  /// Return on Equity (Net Income / Shareholders' Equity)
  returnOnEquity,

  /// Profit Margin (Net Income / Revenue)
  profitMargin,

  /// Asset Turnover (Revenue / Total Assets)
  assetTurnover,

  /// Inventory Turnover (Cost of Goods Sold / Average Inventory)
  inventoryTurnover,

  /// Price-to-Earnings ratio (Share Price / Earnings per Share)
  priceToEarnings,

  /// Golden ratio (approximately 1.618)
  goldenRatio,

  /// Aspect ratio (Width / Height)
  aspectRatio,
}

class _RatioAnalysisWidgetState extends State<RatioAnalysisWidget> {
  final TextEditingController _numeratorController = TextEditingController();
  final TextEditingController _denominatorController = TextEditingController();

  RatioType _ratioType = RatioType.custom;
  double? _numerator;
  double? _denominator;
  double? _ratioResult;
  String? _errorMessage;
  String _interpretation = '';
  String _formula = '';
  double _benchmark = 0.0;

  // Map of ratio types to their labels
  final Map<RatioType, String> _ratioLabels = {
    RatioType.custom: 'Custom Ratio',
    RatioType.currentRatio: 'Current Ratio',
    RatioType.quickRatio: 'Quick Ratio',
    RatioType.debtToEquity: 'Debt-to-Equity Ratio',
    RatioType.returnOnAssets: 'Return on Assets (ROA)',
    RatioType.returnOnEquity: 'Return on Equity (ROE)',
    RatioType.profitMargin: 'Profit Margin',
    RatioType.assetTurnover: 'Asset Turnover',
    RatioType.inventoryTurnover: 'Inventory Turnover',
    RatioType.priceToEarnings: 'Price-to-Earnings (P/E) Ratio',
    RatioType.goldenRatio: 'Golden Ratio',
    RatioType.aspectRatio: 'Aspect Ratio',
  };

  // Map of ratio types to their formulas
  final Map<RatioType, String> _ratioFormulas = {
    RatioType.custom: 'Numerator / Denominator',
    RatioType.currentRatio: 'Current Assets / Current Liabilities',
    RatioType.quickRatio: '(Current Assets - Inventory) / Current Liabilities',
    RatioType.debtToEquity: 'Total Debt / Total Equity',
    RatioType.returnOnAssets: 'Net Income / Total Assets',
    RatioType.returnOnEquity: 'Net Income / Shareholders\' Equity',
    RatioType.profitMargin: 'Net Income / Revenue',
    RatioType.assetTurnover: 'Revenue / Total Assets',
    RatioType.inventoryTurnover: 'Cost of Goods Sold / Average Inventory',
    RatioType.priceToEarnings: 'Share Price / Earnings per Share',
    RatioType.goldenRatio: 'Approximately 1.618 (a + b) / a = a / b',
    RatioType.aspectRatio: 'Width / Height',
  };

  // Map of ratio types to their benchmarks
  final Map<RatioType, double> _ratioBenchmarks = {
    RatioType.custom: 1.0,
    RatioType.currentRatio: 2.0,
    RatioType.quickRatio: 1.0,
    RatioType.debtToEquity: 1.5,
    RatioType.returnOnAssets: 0.05,
    RatioType.returnOnEquity: 0.15,
    RatioType.profitMargin: 0.1,
    RatioType.assetTurnover: 1.0,
    RatioType.inventoryTurnover: 5.0,
    RatioType.priceToEarnings: 15.0,
    RatioType.goldenRatio: 1.618,
    RatioType.aspectRatio: 1.78, // 16:9 aspect ratio
  };

  // Map of ratio types to their interpretations
  final Map<RatioType, Function(double)> _ratioInterpretations = {
    RatioType.custom: (value) => 'Custom ratio value is $value.',
    RatioType.currentRatio: (value) => value < 1.0
        ? 'Current ratio of $value indicates potential liquidity issues. The company may not have enough current assets to cover its short-term obligations.'
        : value < 2.0
            ? 'Current ratio of $value is acceptable but could be improved. The company has enough current assets to cover its short-term obligations.'
            : 'Current ratio of $value is good. The company has sufficient current assets to cover its short-term obligations.',
    RatioType.quickRatio: (value) => value < 1.0
        ? 'Quick ratio of $value indicates potential liquidity issues. The company may not have enough liquid assets to cover its short-term obligations.'
        : value < 1.5
            ? 'Quick ratio of $value is acceptable. The company has enough liquid assets to cover its short-term obligations.'
            : 'Quick ratio of $value is good. The company has strong liquidity.',
    RatioType.debtToEquity: (value) => value < 1.0
        ? 'Debt-to-equity ratio of $value indicates low leverage. The company is using more equity than debt to finance its assets.'
        : value < 2.0
            ? 'Debt-to-equity ratio of $value is moderate. The company is using a balanced mix of debt and equity.'
            : 'Debt-to-equity ratio of $value is high. The company is using more debt than equity, which could increase financial risk.',
    RatioType.returnOnAssets: (value) => value < 0.02
        ? 'ROA of ${(value * 100).toStringAsFixed(2)}% is low. The company is not efficiently using its assets to generate profits.'
        : value < 0.05
            ? 'ROA of ${(value * 100).toStringAsFixed(2)}% is moderate. The company is generating reasonable returns from its assets.'
            : 'ROA of ${(value * 100).toStringAsFixed(2)}% is good. The company is efficiently using its assets to generate profits.',
    RatioType.returnOnEquity: (value) => value < 0.10
        ? 'ROE of ${(value * 100).toStringAsFixed(2)}% is low. The company is not efficiently using shareholders\' equity to generate profits.'
        : value < 0.15
            ? 'ROE of ${(value * 100).toStringAsFixed(2)}% is moderate. The company is generating reasonable returns for shareholders.'
            : 'ROE of ${(value * 100).toStringAsFixed(2)}% is good. The company is efficiently using shareholders\' equity to generate profits.',
    RatioType.profitMargin: (value) => value < 0.05
        ? 'Profit margin of ${(value * 100).toStringAsFixed(2)}% is low. The company is not generating significant profits from its revenue.'
        : value < 0.10
            ? 'Profit margin of ${(value * 100).toStringAsFixed(2)}% is moderate. The company is generating reasonable profits from its revenue.'
            : 'Profit margin of ${(value * 100).toStringAsFixed(2)}% is good. The company is efficiently converting revenue into profits.',
    RatioType.assetTurnover: (value) => value < 0.5
        ? 'Asset turnover of $value is low. The company is not efficiently using its assets to generate revenue.'
        : value < 1.0
            ? 'Asset turnover of $value is moderate. The company is generating reasonable revenue from its assets.'
            : 'Asset turnover of $value is good. The company is efficiently using its assets to generate revenue.',
    RatioType.inventoryTurnover: (value) => value < 3.0
        ? 'Inventory turnover of $value is low. The company may have excess or obsolete inventory.'
        : value < 5.0
            ? 'Inventory turnover of $value is moderate. The company is managing its inventory reasonably well.'
            : 'Inventory turnover of $value is good. The company is efficiently managing its inventory.',
    RatioType.priceToEarnings: (value) => value < 10.0
        ? 'P/E ratio of $value is low. The stock may be undervalued or the market expects declining earnings.'
        : value < 20.0
            ? 'P/E ratio of $value is moderate. The stock is reasonably valued relative to earnings.'
            : 'P/E ratio of $value is high. The stock may be overvalued or the market expects significant growth.',
    RatioType.goldenRatio: (value) => (value - 1.618).abs() < 0.01
        ? 'The ratio of $value is very close to the golden ratio (1.618), which is often found in nature and considered aesthetically pleasing.'
        : 'The ratio of $value differs from the golden ratio (1.618). The golden ratio is often found in nature and considered aesthetically pleasing.',
    RatioType.aspectRatio: (value) => value == 1.0
        ? 'Aspect ratio of 1:1 (square).'
        : value == 1.33
            ? 'Aspect ratio of 4:3 (traditional TV/monitor).'
            : value == 1.78
                ? 'Aspect ratio of 16:9 (widescreen).'
                : value == 1.6
                    ? 'Aspect ratio of 16:10 (common in some monitors).'
                    : 'Aspect ratio of $value.',
  };

  @override
  void initState() {
    super.initState();

    _ratioType = widget.ratioType;

    // Initialize with provided values
    if (widget.initialNumerator != null) {
      _numerator = widget.initialNumerator;
      _numeratorController.text = _numerator.toString();
    }

    if (widget.initialDenominator != null) {
      _denominator = widget.initialDenominator;
      _denominatorController.text = _denominator.toString();
    }

    // Set formula based on ratio type
    _formula = _ratioFormulas[_ratioType] ?? 'Numerator / Denominator';

    // Set benchmark based on ratio type
    _benchmark = _ratioBenchmarks[_ratioType] ?? 1.0;

    // Calculate initial ratio if both values are provided
    if (_numerator != null && _denominator != null) {
      _calculateRatio();
    }
  }

  @override
  void dispose() {
    _numeratorController.dispose();
    _denominatorController.dispose();
    super.dispose();
  }

  void _parseNumerator() {
    try {
      final input = _numeratorController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _numerator = null;
          _errorMessage = 'Please enter a numerator value';
        });
        return;
      }

      final value = double.parse(input);
      setState(() {
        _numerator = value;
        _errorMessage = null;
      });

      if (_denominator != null) {
        _calculateRatio();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid numerator format';
      });
    }
  }

  void _parseDenominator() {
    try {
      final input = _denominatorController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _denominator = null;
          _errorMessage = 'Please enter a denominator value';
        });
        return;
      }

      final value = double.parse(input);
      if (value == 0) {
        setState(() {
          _errorMessage = 'Denominator cannot be zero';
        });
        return;
      }

      setState(() {
        _denominator = value;
        _errorMessage = null;
      });

      if (_numerator != null) {
        _calculateRatio();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid denominator format';
      });
    }
  }

  void _calculateRatio() {
    if (_numerator == null || _denominator == null) {
      setState(() {
        _ratioResult = null;
        _interpretation = '';
      });
      return;
    }

    if (_denominator == 0) {
      setState(() {
        _errorMessage = 'Denominator cannot be zero';
        _ratioResult = null;
        _interpretation = '';
      });
      return;
    }

    try {
      double result;

      // Special handling for golden ratio
      if (_ratioType == RatioType.goldenRatio) {
        result = 1.618; // Golden ratio is a constant
      } else {
        result = _numerator! / _denominator!;
      }

      setState(() {
        _ratioResult = result;
        _errorMessage = null;

        // Get interpretation based on ratio type
        final interpretationFunction = _ratioInterpretations[_ratioType];
        if (interpretationFunction != null) {
          _interpretation = interpretationFunction(result);
        } else {
          _interpretation = 'Ratio value is $result.';
        }
      });

      // Notify callback if provided
      if (widget.onRatioCalculated != null) {
        widget.onRatioCalculated!(_ratioResult!, _interpretation);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error calculating ratio: ${e.toString()}';
      });
    }
  }

  void _setRatioType(RatioType type) {
    setState(() {
      _ratioType = type;
      _formula = _ratioFormulas[_ratioType] ?? 'Numerator / Denominator';
      _benchmark = _ratioBenchmarks[_ratioType] ?? 1.0;

      // Special handling for golden ratio
      if (type == RatioType.goldenRatio) {
        _numeratorController.text = '1.618';
        _denominatorController.text = '1';
        _numerator = 1.618;
        _denominator = 1.0;
      }

      // Recalculate with new ratio type
      if (_numerator != null && _denominator != null) {
        _calculateRatio();
      }
    });
  }

  String _getRatioTypeLabel(RatioType type) {
    return _ratioLabels[type] ?? 'Unknown Ratio';
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Ratio Type Selection
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Ratio Type:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    for (final type in RatioType.values)
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: ChoiceChip(
                          label: Text(
                            _getRatioTypeLabel(type),
                            style: TextStyle(
                              color: _ratioType == type ? Colors.white : effectiveTextColor,
                              fontSize: widget.fontSize - 2,
                            ),
                          ),
                          selected: _ratioType == type,
                          onSelected: widget.isDisabled || widget.isReadOnly
                              ? null
                              : (selected) {
                                  if (selected) {
                                    _setRatioType(type);
                                  }
                                },
                          backgroundColor: widget.backgroundColor,
                          selectedColor: widget.ratioColor,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Formula Display
          if (widget.showFormula) ...[
            Text(
              'Formula:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Text(
                _formula,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Input Fields
          Row(
            children: [
              // Numerator
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Numerator:',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _numeratorController,
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter numerator',
                        hintStyle: TextStyle(
                          color: effectiveTextColor.withAlpha(128),
                          fontSize: widget.fontSize,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      enabled: !widget.isDisabled && !widget.isReadOnly && _ratioType != RatioType.goldenRatio,
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      onChanged: (value) {
                        _parseNumerator();
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Denominator
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Denominator:',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _denominatorController,
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter denominator',
                        hintStyle: TextStyle(
                          color: effectiveTextColor.withAlpha(128),
                          fontSize: widget.fontSize,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      enabled: !widget.isDisabled && !widget.isReadOnly && _ratioType != RatioType.goldenRatio,
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      onChanged: (value) {
                        _parseDenominator();
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Error Message
          if (_errorMessage != null || widget.errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize - 2,
              ),
            ),
          ],

          // Result
          if (_ratioResult != null) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Result: ${_ratioResult!.toStringAsFixed(4)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Visualization
          if (widget.showVisualization && _ratioResult != null) ...[
            const SizedBox(height: 16),
            Text(
              'Visualization:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 60,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: _buildRatioVisualization(),
              ),
            ),
          ],

          // Benchmark Comparison
          if (widget.showBenchmark && _ratioResult != null) ...[
            const SizedBox(height: 16),
            Text(
              'Benchmark Comparison:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 60,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: _buildBenchmarkComparison(),
              ),
            ),
          ],

          // Interpretation
          if (widget.showInterpretation && _ratioResult != null && _interpretation.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Interpretation:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Text(
                _interpretation,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
              ),
            ),
          ],

          // Helper Text
          if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRatioVisualization() {
    if (_ratioResult == null) {
      return const Center(child: Text('No data to display'));
    }

    return CustomPaint(
      size: const Size(double.infinity, 60),
      painter: _RatioVisualizationPainter(
        numerator: _numerator!,
        denominator: _denominator!,
        ratioColor: widget.ratioColor,
        textColor: widget.textColor,
      ),
    );
  }

  Widget _buildBenchmarkComparison() {
    if (_ratioResult == null) {
      return const Center(child: Text('No data to display'));
    }

    return CustomPaint(
      size: const Size(double.infinity, 60),
      painter: _BenchmarkComparisonPainter(
        ratio: _ratioResult!,
        benchmark: _benchmark,
        ratioColor: widget.ratioColor,
        benchmarkColor: widget.benchmarkColor,
        textColor: widget.textColor,
      ),
    );
  }
}

class _RatioVisualizationPainter extends CustomPainter {
  final double numerator;
  final double denominator;
  final Color ratioColor;
  final Color textColor;

  _RatioVisualizationPainter({
    required this.numerator,
    required this.denominator,
    required this.ratioColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = ratioColor
      ..style = PaintingStyle.fill;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Ratio is calculated as numerator / denominator

    // Draw numerator and denominator bars
    final barHeight = 20.0;
    final maxBarWidth = size.width - 100; // Leave space for labels

    // Determine which value is larger for scaling
    final maxValue = max(numerator, denominator);

    // Draw numerator bar
    final numeratorWidth = (numerator / maxValue) * maxBarWidth;
    final numeratorRect = Rect.fromLTWH(50, 0, numeratorWidth, barHeight);
    canvas.drawRect(numeratorRect, paint);

    // Draw denominator bar
    final denominatorWidth = (denominator / maxValue) * maxBarWidth;
    final denominatorRect = Rect.fromLTWH(50, barHeight + 10, denominatorWidth, barHeight);
    canvas.drawRect(denominatorRect, paint..color = ratioColor.withAlpha(128));

    // Draw labels
    textPainter.text = TextSpan(
      text: 'N',
      style: TextStyle(color: textColor, fontSize: 12),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(25, barHeight / 2 - textPainter.height / 2));

    textPainter.text = TextSpan(
      text: 'D',
      style: TextStyle(color: textColor, fontSize: 12),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(25, barHeight + 10 + barHeight / 2 - textPainter.height / 2));

    // Draw values
    textPainter.text = TextSpan(
      text: numerator.toStringAsFixed(2),
      style: TextStyle(color: textColor, fontSize: 12),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(numeratorWidth + 55, barHeight / 2 - textPainter.height / 2));

    textPainter.text = TextSpan(
      text: denominator.toStringAsFixed(2),
      style: TextStyle(color: textColor, fontSize: 12),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(denominatorWidth + 55, barHeight + 10 + barHeight / 2 - textPainter.height / 2));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class _BenchmarkComparisonPainter extends CustomPainter {
  final double ratio;
  final double benchmark;
  final Color ratioColor;
  final Color benchmarkColor;
  final Color textColor;

  _BenchmarkComparisonPainter({
    required this.ratio,
    required this.benchmark,
    required this.ratioColor,
    required this.benchmarkColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..style = PaintingStyle.fill;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Draw scale line
    canvas.drawLine(
      Offset(50, size.height / 2),
      Offset(size.width - 50, size.height / 2),
      paint..color = Colors.grey,
    );

    // Determine scale range
    final maxValue = max(ratio, benchmark) * 1.5;
    final scaleWidth = size.width - 100;

    // Draw benchmark marker
    final benchmarkX = 50 + (benchmark / maxValue) * scaleWidth;
    canvas.drawLine(
      Offset(benchmarkX, 10),
      Offset(benchmarkX, size.height - 10),
      paint..color = benchmarkColor,
    );

    // Draw triangle marker at the top
    final trianglePath = Path()
      ..moveTo(benchmarkX, 10)
      ..lineTo(benchmarkX - 5, 0)
      ..lineTo(benchmarkX + 5, 0)
      ..close();

    canvas.drawPath(trianglePath, fillPaint..color = benchmarkColor);

    // Draw ratio marker
    final ratioX = 50 + (ratio / maxValue) * scaleWidth;
    canvas.drawLine(
      Offset(ratioX, 10),
      Offset(ratioX, size.height - 10),
      paint..color = ratioColor,
    );

    // Draw circle marker at the bottom
    canvas.drawCircle(
      Offset(ratioX, size.height - 5),
      5,
      fillPaint..color = ratioColor,
    );

    // Draw labels
    textPainter.text = TextSpan(
      text: 'Benchmark: ${benchmark.toStringAsFixed(2)}',
      style: TextStyle(color: benchmarkColor, fontSize: 10),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(benchmarkX - textPainter.width / 2, 15));

    textPainter.text = TextSpan(
      text: 'Ratio: ${ratio.toStringAsFixed(2)}',
      style: TextStyle(color: ratioColor, fontSize: 10),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(ratioX - textPainter.width / 2, size.height - 30));

    // Draw scale values
    textPainter.text = TextSpan(
      text: '0',
      style: TextStyle(color: textColor, fontSize: 10),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(50 - textPainter.width / 2, size.height / 2 + 5));

    textPainter.text = TextSpan(
      text: maxValue.toStringAsFixed(1),
      style: TextStyle(color: textColor, fontSize: 10),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width - 50 - textPainter.width / 2, size.height / 2 + 5));

    // Draw middle value
    textPainter.text = TextSpan(
      text: (maxValue / 2).toStringAsFixed(1),
      style: TextStyle(color: textColor, fontSize: 10),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(50 + scaleWidth / 2 - textPainter.width / 2, size.height / 2 + 5));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}