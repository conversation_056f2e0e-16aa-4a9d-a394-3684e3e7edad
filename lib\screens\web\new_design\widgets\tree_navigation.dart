import 'package:flutter/material.dart';

/// A tree navigation widget that displays a hierarchical navigation structure.
///
/// This widget takes a list of items with a tree structure and displays them
/// as a navigation panel with expandable/collapsible sections.
class TreeNavigation extends StatefulWidget {
  /// The list of items to display in the tree navigation.
  /// Each item should have 'id', 'title', 'icon', and 'children' fields.
  final List<Map<String, dynamic>> items;

  /// The width of the navigation panel.
  final double width;

  /// The background color of the navigation panel.
  final Color backgroundColor;

  /// The border color of the navigation panel.
  final Color borderColor;

  /// Callback when an item is selected.
  final Function(Map<String, dynamic>)? onItemSelected;

  const TreeNavigation({
    super.key,
    required this.items,
    required this.width,
    required this.backgroundColor,
    required this.borderColor,
    this.onItemSelected,
  });

  @override
  State<TreeNavigation> createState() => _TreeNavigationState();
}

class _TreeNavigationState extends State<TreeNavigation> {
  // Set to store expanded item IDs
  final Set<String> _expandedItems = {};

  // Currently selected item ID
  String? _selectedItemId;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        border: Border(
          right: BorderSide(
            color: widget.borderColor,
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Text(
                  'Navigation',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Divider
          Divider(height: 1, thickness: 1, color: widget.borderColor),

          // Tree items (scrollable)
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: widget.items.map((item) => _buildTreeItem(item, 0)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTreeItem(Map<String, dynamic> item, int level) {
    final String id = item['id'];
    final String title = item['title'];
    final String iconPath = item['icon'];
    final List<dynamic> children = item['children'] ?? [];
    final bool hasChildren = children.isNotEmpty;
    final bool isExpanded = _expandedItems.contains(id);
    final bool isSelected = _selectedItemId == id;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Item row with indentation
        InkWell(
          onTap: () {
            setState(() {
              // Toggle expansion if has children
              if (hasChildren) {
                if (isExpanded) {
                  _expandedItems.remove(id);
                } else {
                  _expandedItems.add(id);
                }
              }

              // Select this item
              _selectedItemId = id;
            });

            // Note: We're handling the CRM item click in the parent widget
            // through the onItemSelected callback

            // Notify parent if callback provided
            if (widget.onItemSelected != null) {
              widget.onItemSelected!(item);
            }
          },
          onHover: (isHovering) {
            // Change cursor to pointer on hover
            if (isHovering) {
              // We don't need to setState here as the cursor change is handled by InkWell
            }
          },
          child: Container(
            padding: EdgeInsets.only(
              left: 16.0 + (level * 16.0),
              right: 16.0,
              top: 12.0,
              bottom: 12.0,
            ),
            decoration: BoxDecoration(
              color: isSelected ? Color(0xffE6F0FF) : Colors.transparent,
              border: Border(
                left: BorderSide(
                  color: isSelected ? Color(0xff0058FF) : Colors.transparent,
                  width: 3.0,
                ),
              ),
            ),
            child: Row(
              children: [
                // Icon
                Image.asset(
                  iconPath,
                  width: 20,
                  height: 20,
                  color: isSelected ? Color(0xff0058FF) : Colors.black87,
                ),
                SizedBox(width: 12),

                // Title
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? Color(0xff0058FF) : Colors.black87,
                    ),
                  ),
                ),

                // Expand/collapse arrow for items with children
                if (hasChildren)
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                    size: 20,
                    color: isSelected ? Color(0xff0058FF) : Colors.black54,
                  ),
              ],
            ),
          ),
        ),

        // Children (if expanded)
        if (isExpanded && hasChildren)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children
                .map((child) => _buildTreeItem(child, level + 1))
                .toList(),
          ),
      ],
    );
  }
}
