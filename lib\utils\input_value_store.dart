import 'logger.dart';

/// A simple global store for input values
class InputValueStore {
  // Private constructor
  InputValueStore._();

  // Singleton instance
  static final InputValueStore _instance = InputValueStore._();

  // Factory constructor to return the singleton instance
  factory InputValueStore() => _instance;

  // Map to store values: sectionId:fieldId -> value
  final Map<String, dynamic> _values = {};

  // Store a value
  void setValue(String sectionIdOrKey, dynamic fieldIdOrValue,
      [dynamic value]) {
    // Support both formats:
    // 1. setValue(sectionId, fieldId, value)
    // 2. setValue(key, value)
    if (value != null) {
      // Format 1: setValue(sectionId, fieldId, value)
      final key = '$sectionIdOrKey:$fieldIdOrValue';
      _values[key] = value;
      Logger.info('InputValueStore: Stored value for $key: $value');
    } else {
      // Format 2: setValue(key, value)
      _values[sectionIdOrKey] = fieldIdOrValue;
      Logger.info(
          'InputValueStore: Stored value for $sectionIdOrKey: $fieldIdOrValue');
    }
  }

  // Get a value
  dynamic getValue(String sectionIdOrKey, [String? fieldId]) {
    // Support both formats:
    // 1. getValue(sectionId, fieldId)
    // 2. getValue(key)
    String key = sectionIdOrKey;
    if (fieldId != null) {
      // Format 1: getValue(sectionId, fieldId)
      key = '$sectionIdOrKey:$fieldId';
    }

    final value = _values[key];
    Logger.info('InputValueStore: Retrieved value for $key: $value');
    return value;
  }

  // Check if a value exists
  bool hasValue(String sectionIdOrKey, [String? fieldId]) {
    // Support both formats:
    // 1. hasValue(sectionId, fieldId)
    // 2. hasValue(key)
    String key = sectionIdOrKey;
    if (fieldId != null) {
      // Format 1: hasValue(sectionId, fieldId)
      key = '$sectionIdOrKey:$fieldId';
    }

    return _values.containsKey(key);
  }

  // Debug: print all values
  void debugPrintValues() {
    Logger.info('InputValueStore: All values:');
    for (final entry in _values.entries) {
      Logger.info('  ${entry.key}: ${entry.value}');
    }
  }

  // Clear all values
  void clear() {
    _values.clear();
    Logger.info('InputValueStore: All values cleared');

    // Debug: print stack trace to identify where clear is called from
    try {
      throw Exception('Stack trace for InputValueStore.clear()');
    } catch (e, stackTrace) {
      Logger.info('InputValueStore.clear() called from:\n$stackTrace');
    }
  }

  // Get all values as a map
  Map<String, dynamic> getAllValues() {
    // Return a copy of the values map to avoid modification
    return Map<String, dynamic>.from(_values);
  }

  // Get values for parent fields of a dependent field
  Map<String, dynamic> getParentValues(
      String sectionId, List<String> parentIds) {
    final Map<String, dynamic> parentValues = {};

    for (final parentId in parentIds) {
      final key = '$sectionId:$parentId';
      if (_values.containsKey(key)) {
        parentValues[parentId] = _values[key];
        Logger.info(
            'InputValueStore: Retrieved parent value for $key: ${_values[key]}');
      }
    }

    return parentValues;
  }

  // Check if all parent values are available for a dependent field
  bool hasAllParentValues(String sectionId, List<String> parentIds) {
    for (final parentId in parentIds) {
      final key = '$sectionId:$parentId';
      if (!_values.containsKey(key) || _values[key] == null) {
        return false;
      }
    }

    return true;
  }
}
