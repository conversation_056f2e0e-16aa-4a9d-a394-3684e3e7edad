import 'package:flutter/foundation.dart';

/// Controller for managing accordion-style expansion behavior
/// where only one panel can be expanded at a time
class AccordionController extends ChangeNotifier {
  String? _expandedPanelId;

  /// Get the currently expanded panel ID
  String? get expandedPanelId => _expandedPanelId;

  /// Check if a specific panel is expanded
  bool isPanelExpanded(String panelId) {
    return _expandedPanelId == panelId;
  }

  /// Expand a panel (and collapse any other expanded panel)
  void expandPanel(String panelId) {
    if (_expandedPanelId != panelId) {
      _expandedPanelId = panelId;
      notifyListeners();
    }
  }

  /// Collapse a panel
  void collapsePanel(String panelId) {
    if (_expandedPanelId == panelId) {
      _expandedPanelId = null;
      notifyListeners();
    }
  }

  /// Toggle a panel's expansion state
  void togglePanel(String panelId) {
    if (_expandedPanelId == panelId) {
      // If this panel is currently expanded, collapse it
      _expandedPanelId = null;
    } else {
      // If this panel is not expanded, expand it (and collapse any other)
      _expandedPanelId = panelId;
    }
    notifyListeners();
  }

  /// Collapse all panels
  void collapseAll() {
    if (_expandedPanelId != null) {
      _expandedPanelId = null;
      notifyListeners();
    }
  }
}
