import 'package:flutter/material.dart';

class CustomCheckbox extends StatefulWidget {
  bool initialValue;
  final Function(bool) onChanged;
  final MaterialTapTargetSize materialTapTargetSize;

  CustomCheckbox({
    super.key,
    required this.initialValue,
    required this.onChanged,
    this.materialTapTargetSize = MaterialTapTargetSize.padded,
  });

  @override
  State<CustomCheckbox> createState() => _CustomCheckboxState();
}

class _CustomCheckboxState extends State<CustomCheckbox> {
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Container(
        decoration: BoxDecoration(
          // Custom hover effect with square shape and rounded corners
          color: _isHovered
              ? Colors.grey.withValues(alpha: 0.1)
              : Colors.transparent, // 0.1 * 255 ≈ 26
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Checkbox(
          visualDensity: VisualDensity.compact,
          side: BorderSide(
            color: Color(0xFFBEBEBE), // Custom border color
            width: 1, // Custom border width
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                2.0), // Very slight rounding for a square look
          ),
          value: widget.initialValue,
          onChanged: (bool? value) {
            setState(() {
              widget.initialValue = value ?? false;
              widget.onChanged(value ?? false);
            });
          },
          materialTapTargetSize: widget.materialTapTargetSize,
          // Override the default hover color to make it transparent
          // so our custom hover effect shows through
          hoverColor: Colors.transparent,
        ),
      ),
    );
  }
}
