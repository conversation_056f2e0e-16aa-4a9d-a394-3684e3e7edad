import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';


class RootNodeTopBar1 extends StatelessWidget {
  final NSLNode rootNode;

  const RootNodeTopBar1({
    super.key,
    required this.rootNode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xFFF5F8FF),
        border: Border(
          bottom: BorderSide(color: Color(0xFF797676), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // Left section - Organisation Departments (matching left panel width)
          Container(
            width: MediaQuery.of(context).size.width * 0.0695,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.xs,
            ),
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Color(0xffD0D0D0), width: 1),
              ),
            ),
            child: Text(
              'Organisation Departments',
              maxLines: 2,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          // Right section - Metrics
          Expanded(
            child: Row(
              children: [
                _buildMetricItem('Total Transactions', _getTotalTransactions()),
               // Divider(color: Color(0xffD0D0D0), ),
                _buildMetricItem('Total GOs', _getTotalGOs()),
                _buildMetricItem('Total LOs', _getTotalLOs()),
                _buildMetricItem('Revenue', _getRevenue()),
                _buildMetricItem('Cost', _getCost()),
                _buildMetricItem('Margin', _getMargin()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(String label, String value) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color:Colors.white,
              border: Border(
                right: BorderSide(color: Color(0xffD0D0D0), width: 1),
              ),),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.sm,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //  mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s10,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              SizedBox(height: 2),
              Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.bold,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTotalTransactions() {
    // Try to get from financial data first
    final financialData = rootNode.originalData.financialDataByNode;
    if (financialData?.standalone.summary.totalTransactions['value'] != null) {
      return financialData!.standalone.summary.totalTransactions['value'].toString();
    }
    
    // Fallback to metrics or default
    return rootNode.originalData.metrics?.transactions ?? '0';
  }

  String _getTotalGOs() {
    // Try to get from metrics first
    if (rootNode.originalData.metrics?.totalGos != null) {
      return rootNode.originalData.metrics!.totalGos.toString();
    }
    
    // Fallback to bet breakdown
    return rootNode.betBreakdown.gos.toString();
  }

  String _getTotalLOs() {
    // Try to get from metrics first
    if (rootNode.originalData.metrics?.totalLos != null) {
      return rootNode.originalData.metrics!.totalLos.toString();
    }
    
    // Fallback to bet breakdown
    return rootNode.betBreakdown.los.toString();
  }

  String _getRevenue() {
    // Try to get from financial data first
    final financialData = rootNode.originalData.financialDataByNode;
    if (financialData?.standalone.summary.totalRevenue['value'] != null) {
      return financialData!.standalone.summary.totalRevenue['value'].toString();
    }
    
    // Fallback to financial summary
    return rootNode.financialSummary.revenue;
  }

  String _getCost() {
    // Try to get from financial data first
    final financialData = rootNode.originalData.financialDataByNode;
    if (financialData?.standalone.summary.totalCost != null && 
        financialData!.standalone.summary.totalCost!['value'] != null) {
      return financialData.standalone.summary.totalCost!['value'].toString();
    }
    
    // Fallback to financial summary
    return rootNode.financialSummary.cost;
  }

  String _getMargin() {
    // Try to get from financial data first
    final financialData = rootNode.originalData.financialDataByNode;
    if (financialData?.standalone.summary.netMargin['value'] != null) {
      return financialData!.standalone.summary.netMargin['value'].toString();
    }
    
    // Fallback to financial summary
    return rootNode.financialSummary.margin;
  }
}
