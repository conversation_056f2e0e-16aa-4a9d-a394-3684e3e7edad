import 'package:flutter/material.dart';

/// A widget that allows users to resize adjacent panels by dragging.
class ResizableDivider extends StatefulWidget {
  /// The width of the divider handle.
  final double width;

  /// The color of the divider.
  final Color? color;

  /// The color of the divider when being dragged or hovered.
  final Color? activeColor;

  /// Callback that provides the delta movement when the divider is dragged.
  final Function(double delta) onResize;

  /// The cursor to display when hovering over the divider.
  final MouseCursor cursor;

  const ResizableDivider({
    super.key,
    this.width = 8.0,
    this.color,
    this.activeColor,
    required this.onResize,
    this.cursor = SystemMouseCursors.resizeColumn,
  });

  @override
  State<ResizableDivider> createState() => _ResizableDividerState();
}

class _ResizableDividerState extends State<ResizableDivider> {
  bool _isDragging = false;
  bool _isHovered = false;
  double _startDragX = 0.0;

  @override
  Widget build(BuildContext context) {
    final dividerColor = widget.color ?? Theme.of(context).dividerColor;
    final activeDividerColor =
        widget.activeColor ?? Theme.of(context).colorScheme.primary;

    return MouseRegion(
      cursor: widget.cursor,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onHorizontalDragStart: (details) {
          setState(() {
            _isDragging = true;
            _startDragX = details.globalPosition.dx;
          });
        },
        onHorizontalDragUpdate: (details) {
          final delta = details.globalPosition.dx - _startDragX;
          widget.onResize(delta);
          _startDragX = details.globalPosition.dx;
        },
        onHorizontalDragEnd: (details) {
          setState(() {
            _isDragging = false;
          });
        },
        child: Container(
          width: widget.width,
          decoration: BoxDecoration(
            color: _isDragging || _isHovered
                ? activeDividerColor.withAlpha(15)
                : Colors.transparent,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Vertical line
              Container(
                width: 1,
                height: double.infinity,
                color: _isDragging
                    ? activeDividerColor
                    : _isHovered
                    ? activeDividerColor.withAlpha(100)
                    : activeDividerColor.withAlpha(40),
              ),
              // Drag handle
              if (_isDragging || _isHovered)
                Container(
                  width: 4,
                  height: 80,
                  decoration: BoxDecoration(
                    color: activeDividerColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                )
              else
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 4,
                      height: 60,
                      decoration: BoxDecoration(
                        color:  activeDividerColor.withAlpha(90),
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),

                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
