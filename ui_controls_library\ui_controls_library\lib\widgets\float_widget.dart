
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// A widget for inputting and displaying floating-point values.
///
/// This widget provides a text field for entering float values with
/// optional features like increment/decrement buttons, validation,
/// formatting, and styling options.
class FloatWidget extends StatefulWidget {
  /// The initial value of the float input.
  final double initialValue;

  /// The default value to use when input is cleared or empty.
  final double defaultValue;

  /// The minimum allowed value.
  final double minValue;

  /// The maximum allowed value.
  final double maxValue;

  /// The number of decimal places to display.
  final int decimalPlaces;

  /// Whether to allow negative values.
  final bool allowNegative;

  /// The text color.
  final Color textColor;

  /// The background color.
  final Color backgroundColor;

  /// The border color.
  final Color borderColor;

  /// The border width.
  final double borderWidth;

  /// The border radius.
  final double borderRadius;

  /// Whether to show a border.
  final bool hasBorder;

  /// Whether the widget is read-only.
  final bool isReadOnly;

  /// Whether the widget is disabled.
  final bool isDisabled;

  /// The label text.
  final String? label;

  /// The hint text.
  final String? hint;

  /// The helper text.
  final String? helperText;

  /// The error text.
  final String? errorText;

  /// Whether to show a prefix icon.
  final bool showPrefix;

  /// Whether to show a suffix icon.
  final bool showSuffix;

  /// The prefix icon.
  final IconData? prefixIcon;

  /// The suffix icon.
  final IconData? suffixIcon;

  /// The font size.
  final double fontSize;

  /// The font weight.
  final FontWeight fontWeight;

  /// Whether to use a compact layout.
  final bool isCompact;

  /// The unit to display (e.g., "$", "%", etc.).
  final String? unit;

  /// Whether to show the unit.
  final bool showUnit;

  /// Whether to show the unit on the left side.
  final bool unitOnLeft;

  /// Callback for when the value changes.
  final ValueChanged<double>? onChanged;

  /// Callback for when the value is submitted.
  final ValueChanged<double>? onSubmitted;

  /// The text alignment.
  final TextAlign textAlign;

  /// Whether to autofocus the input.
  final bool autofocus;

  /// The focus node.
  final FocusNode? focusNode;

  /// The text input action.
  final TextInputAction textInputAction;

  /// Whether to enable interactive selection.
  final bool enableInteractiveSelection;

  /// Whether to obscure the text.
  final bool obscureText;

  /// Whether to enable autocorrect.
  final bool autocorrect;

  /// Whether to enable suggestions.
  final bool enableSuggestions;

  /// The maximum length of the input.
  final int? maxLength;

  /// Whether to show a shadow.
  final bool hasShadow;

  /// The elevation of the shadow.
  final double elevation;

  /// Whether to use a dark theme.
  final bool isDarkTheme;

  /// Whether to use Material 3 design.
  final bool useMaterial3;

  /// Whether to use animations.
  final bool hasAnimation;

  /// The prefix text.
  final String? prefixText;

  /// The suffix text.
  final String? suffixText;

  /// Whether to show increment/decrement buttons.
  final bool showIncrementButtons;

  /// The amount to increment/decrement by.
  final double incrementAmount;

  /// The tooltip text.
  final String? tooltip;

  /// Whether to show a clear button.
  final bool showClearButton;

  /// Whether to show a copy button.
  final bool showCopyButton;

  /// Whether to show a paste button.
  final bool showPasteButton;

  /// Whether to show a validation icon.
  final bool showValidationIcon;

  /// Whether to show thousands separators.
  final bool showThousandsSeparator;

  /// The thousands separator character.
  final String thousandsSeparator;

  /// The decimal separator character.
  final String decimalSeparator;

  /// Whether to show scientific notation for large or small numbers.
  final bool useScientificNotation;

  /// The threshold for using scientific notation.
  final double scientificNotationThreshold;

  /// Whether to show the exponent in scientific notation.
  final bool showExponent;

  /// Whether to use engineering notation (exponents in multiples of 3).
  final bool useEngineeringNotation;

  /// Whether to highlight the input when it's out of range.
  final bool highlightOutOfRange;

  /// The color to use for highlighting out-of-range values.
  final Color outOfRangeColor;

  /// Whether to show a slider for adjusting the value.
  final bool showSlider;

  /// Whether to show the value as a percentage.
  final bool showAsPercentage;

  /// Creates a float widget.
  const FloatWidget({
    super.key,
    this.initialValue = 0.0,
    this.defaultValue = 0.0,
    this.minValue = double.negativeInfinity,
    this.maxValue = double.infinity,
    this.decimalPlaces = 2,
    this.allowNegative = true,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = false,
    this.showSuffix = false,
    this.prefixIcon,
    this.suffixIcon,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.unit,
    this.showUnit = false,
    this.unitOnLeft = false,
    this.onChanged,
    this.onSubmitted,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.focusNode,
    this.textInputAction = TextInputAction.done,
    this.enableInteractiveSelection = true,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLength,
    this.hasShadow = false,
    this.elevation = 0.0,
    this.isDarkTheme = false,
    this.useMaterial3 = false,
    this.hasAnimation = false,
    this.prefixText,
    this.suffixText,
    this.showIncrementButtons = false,
    this.incrementAmount = 1.0,
    this.tooltip,
    this.showClearButton = false,
    this.showCopyButton = false,
    this.showPasteButton = false,
    this.showValidationIcon = false,
    this.showThousandsSeparator = false,
    this.thousandsSeparator = ',',
    this.decimalSeparator = '.',
    this.useScientificNotation = false,
    this.scientificNotationThreshold = 1000000.0,
    this.showExponent = true,
    this.useEngineeringNotation = false,
    this.highlightOutOfRange = true,
    this.outOfRangeColor = Colors.red,
    this.showSlider = false,
    this.showAsPercentage = false,
  });

  @override
  State<FloatWidget> createState() => _FloatWidgetState();
}

class _FloatWidgetState extends State<FloatWidget> with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late FocusNode _focusNode;
  double _currentValue = 0.0;
  bool _hasFocus = false;
  bool _isValid = true;
  double? _sliderValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
    _sliderValue = _currentValue;
    _controller = TextEditingController(text: _formatNumber(_currentValue));
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
      if (widget.hasAnimation) {
        if (_hasFocus) {
          _animationController.forward();
        } else {
          _animationController.reverse();
          _validateAndFormat();
        }
      } else if (!_hasFocus) {
        _validateAndFormat();
      }
    });
  }

  void _validateAndFormat() {
    final text = _controller.text;
    if (text.isEmpty) {
      setState(() {
        _currentValue = widget.defaultValue;
        _controller.text = _formatNumber(_currentValue);
        _isValid = true;
      });
      return;
    }

    final parsedValue = _parseFormattedValue(text);
    _validateAndUpdateValue(parsedValue);
  }

  void _validateAndUpdateValue(double value) {
    bool isValid = true;
    double constrainedValue = value;

    // Apply min/max constraints
    if (value < widget.minValue) {
      constrainedValue = widget.minValue;
      isValid = !widget.highlightOutOfRange;
    } else if (value > widget.maxValue) {
      constrainedValue = widget.maxValue;
      isValid = !widget.highlightOutOfRange;
    }

    // Don't allow negative values if not permitted
    if (!widget.allowNegative && constrainedValue < 0) {
      constrainedValue = 0;
      isValid = !widget.highlightOutOfRange;
    }

    setState(() {
      _currentValue = constrainedValue;
      _sliderValue = constrainedValue;
      _isValid = isValid;
      _controller.text = _formatNumber(_currentValue);
    });

    if (widget.onChanged != null) {
      widget.onChanged!(_currentValue);
    }
  }

  void _handleValueChanged(String text) {
    if (text.isEmpty) {
      setState(() {
        _currentValue = widget.defaultValue;
        _isValid = true;
      });

      if (widget.onChanged != null) {
        widget.onChanged!(_currentValue);
      }
      return;
    }

    final double parsedValue = _parseFormattedValue(text);

    // Apply min/max constraints
    double constrainedValue = parsedValue;
    if (parsedValue < widget.minValue) {
      constrainedValue = parsedValue; // Don't constrain during typing
      _isValid = !widget.highlightOutOfRange;
    } else if (parsedValue > widget.maxValue) {
      constrainedValue = parsedValue; // Don't constrain during typing
      _isValid = !widget.highlightOutOfRange;
    } else {
      _isValid = true;
    }

    // Don't allow negative values if not permitted
    if (!widget.allowNegative && constrainedValue < 0) {
      constrainedValue = parsedValue; // Don't constrain during typing
      _isValid = !widget.highlightOutOfRange;
    }

    setState(() {
      _currentValue = constrainedValue;
      _sliderValue = constrainedValue;
    });

    if (widget.onChanged != null) {
      widget.onChanged!(_currentValue);
    }
  }

  void _increment() {
    final newValue = _currentValue + widget.incrementAmount;
    if (newValue <= widget.maxValue) {
      setState(() {
        _currentValue = newValue;
        _sliderValue = newValue;
        _controller.text = _formatNumber(_currentValue);
        _isValid = true;
      });

      if (widget.onChanged != null) {
        widget.onChanged!(_currentValue);
      }
    }
  }

  void _decrement() {
    final newValue = _currentValue - widget.incrementAmount;
    if (newValue >= widget.minValue && (widget.allowNegative || newValue >= 0)) {
      setState(() {
        _currentValue = newValue;
        _sliderValue = newValue;
        _controller.text = _formatNumber(_currentValue);
        _isValid = true;
      });

      if (widget.onChanged != null) {
        widget.onChanged!(_currentValue);
      }
    }
  }

  void _clearValue() {
    setState(() {
      _currentValue = widget.defaultValue;
      _sliderValue = widget.defaultValue;
      _controller.text = _formatNumber(_currentValue);
      _isValid = true;
    });

    if (widget.onChanged != null) {
      widget.onChanged!(_currentValue);
    }
  }

  void _copyValue() {
    Clipboard.setData(ClipboardData(text: _currentValue.toString()));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Value copied to clipboard')),
    );
  }

  void _pasteValue() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null && clipboardData.text != null) {
      final text = clipboardData.text!;
      final value = double.tryParse(text);
      if (value != null) {
        _validateAndUpdateValue(value);
      }
    }
  }

  double _parseFormattedValue(String text) {
    // Remove thousands separators
    String cleanText = text.replaceAll(widget.thousandsSeparator, '');

    // Replace decimal separator with standard period if needed
    if (widget.decimalSeparator != '.') {
      cleanText = cleanText.replaceAll(widget.decimalSeparator, '.');
    }

    // Handle percentage format
    if (widget.showAsPercentage && cleanText.contains('%')) {
      cleanText = cleanText.replaceAll('%', '');
      final value = double.tryParse(cleanText) ?? 0.0;
      return value / 100.0;
    }

    // Handle scientific notation
    if (cleanText.toLowerCase().contains('e')) {
      return double.tryParse(cleanText) ?? 0.0;
    }

    // Handle unit
    if (widget.unit != null && cleanText.contains(widget.unit!)) {
      cleanText = cleanText.replaceAll(widget.unit!, '');
    }

    return double.tryParse(cleanText) ?? 0.0;
  }

  String _formatNumber(double? value) {
    if (value == null) return '';

    // Handle scientific notation
    if (widget.useScientificNotation &&
        (value.abs() >= widget.scientificNotationThreshold ||
         (value != 0 && value.abs() < 1 / widget.scientificNotationThreshold))) {

      if (widget.useEngineeringNotation) {
        // Engineering notation (exponents in multiples of 3)
        final exponent = (math.log(value.abs()) / math.ln10 / 3).floor() * 3;
        final mantissa = value / math.pow(10, exponent);
        return '${mantissa.toStringAsFixed(widget.decimalPlaces)}e$exponent';
      } else {
        // Standard scientific notation
        return value.toStringAsExponential(widget.decimalPlaces);
      }
    }

    // Format with decimal places
    String formatted = value.toStringAsFixed(widget.decimalPlaces);

    // Add thousands separators if enabled
    if (widget.showThousandsSeparator) {
      final parts = formatted.split('.');
      final wholePart = parts[0];
      final decimalPart = parts.length > 1 ? parts[1] : '';

      final formattedWholePart = _addThousandsSeparators(wholePart);

      if (decimalPart.isEmpty) {
        formatted = formattedWholePart;
      } else {
        formatted = '$formattedWholePart${widget.decimalSeparator}$decimalPart';
      }
    } else if (widget.decimalSeparator != '.') {
      // Just replace the decimal separator if needed
      formatted = formatted.replaceAll('.', widget.decimalSeparator);
    }

    // Add percentage symbol if needed
    if (widget.showAsPercentage) {
      // Convert value to percentage first
      final percentValue = value * 100;
      formatted = percentValue.toStringAsFixed(widget.decimalPlaces);

      // Apply thousands separator if needed
      if (widget.showThousandsSeparator) {
        final parts = formatted.split('.');
        final wholePart = parts[0];
        final decimalPart = parts.length > 1 ? parts[1] : '';

        final formattedWholePart = _addThousandsSeparators(wholePart);

        if (decimalPart.isEmpty) {
          formatted = '$formattedWholePart%';
        } else {
          formatted = '$formattedWholePart${widget.decimalSeparator}$decimalPart%';
        }
      } else {
        if (widget.decimalSeparator != '.') {
          formatted = formatted.replaceAll('.', widget.decimalSeparator);
        }
        formatted = '$formatted%';
      }
    }

    // Add unit if needed
    if (widget.showUnit && widget.unit != null) {
      if (widget.unitOnLeft) {
        formatted = '${widget.unit}$formatted';
      } else {
        formatted = '$formatted${widget.unit}';
      }
    }

    return formatted;
  }

  String _addThousandsSeparators(String value) {
    final buffer = StringBuffer();
    final length = value.length;

    // Handle negative sign
    int startIndex = 0;
    if (value.startsWith('-')) {
      buffer.write('-');
      startIndex = 1;
    }

    for (int i = 0; i < length - startIndex; i++) {
      if (i > 0 && (length - startIndex - i) % 3 == 0) {
        buffer.write(widget.thousandsSeparator);
      }
      buffer.write(value[startIndex + i]);
    }

    return buffer.toString();
  }

  double pow(num x, num exponent) {
    return math.pow(x, exponent).toDouble();
  }

  @override
  Widget build(BuildContext context) {
    // Create the input formatter for float
    final List<TextInputFormatter> inputFormatters = [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9${widget.decimalSeparator}${widget.thousandsSeparator}\-\+eE]')),
    ];

    if (!widget.allowNegative) {
      inputFormatters.add(FilteringTextInputFormatter.deny(RegExp(r'[\-]')));
    }

    if (widget.maxLength != null) {
      inputFormatters.add(LengthLimitingTextInputFormatter(widget.maxLength!));
    }

    // Create the decoration
    final InputDecoration decoration = InputDecoration(
      labelText: widget.label,
      hintText: widget.hint,
      helperText: widget.helperText,
      errorText: !_isValid ? widget.errorText ?? 'Invalid value' : null,
      prefixIcon: widget.showPrefix && widget.prefixIcon != null
          ? Icon(widget.prefixIcon)
          : (widget.unitOnLeft && widget.showUnit && widget.unit != null
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: Text(
                    widget.unit!,
                    style: TextStyle(
                      color: widget.isDisabled ? Colors.grey : widget.textColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                )
              : null),
      suffixIcon: _buildSuffixIcon(),
      prefixText: widget.prefixText,
      suffixText: !widget.unitOnLeft && widget.showUnit && widget.unit != null
          ? widget.unit
          : widget.suffixText,
      filled: true,
      fillColor: widget.isDisabled
          ? Colors.grey.shade200
          : (widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor),
      border: widget.hasBorder
          ? OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              borderSide: BorderSide(
                color: !_isValid
                    ? widget.outOfRangeColor
                    : widget.borderColor,
                width: widget.borderWidth,
              ),
            )
          : InputBorder.none,
      enabledBorder: widget.hasBorder
          ? OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              borderSide: BorderSide(
                color: !_isValid
                    ? widget.outOfRangeColor
                    : widget.borderColor,
                width: widget.borderWidth,
              ),
            )
          : null,
      focusedBorder: widget.hasBorder
          ? OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              borderSide: BorderSide(
                color: !_isValid
                    ? widget.outOfRangeColor
                    : Colors.blue,
                width: widget.borderWidth * 1.5,
              ),
            )
          : null,
      errorBorder: widget.hasBorder
          ? OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              borderSide: BorderSide(
                color: widget.outOfRangeColor,
                width: widget.borderWidth,
              ),
            )
          : null,
      contentPadding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    );

    // Create the text field with all the specified properties
    Widget textField = TextField(
      controller: _controller,
      focusNode: _focusNode,
      keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
      textAlign: widget.textAlign,
      style: TextStyle(
        color: widget.isDisabled ? Colors.grey : widget.textColor,
        fontSize: widget.fontSize,
        fontWeight: widget.fontWeight,
      ),
      decoration: decoration,
      inputFormatters: inputFormatters,
      enabled: !widget.isDisabled && !widget.isReadOnly,
      readOnly: widget.isReadOnly,
      autofocus: widget.autofocus,
      textInputAction: widget.textInputAction,
      enableInteractiveSelection: widget.enableInteractiveSelection,
      obscureText: widget.obscureText,
      autocorrect: widget.autocorrect,
      enableSuggestions: widget.enableSuggestions,
      onChanged: (value) {
        _handleValueChanged(value);
      },
      onSubmitted: (value) {
        if (widget.onSubmitted != null) {
          widget.onSubmitted!(_currentValue);
        }
      },
      onTap: () {
        if (widget.isCompact) {
          // Select all text when tapped in compact mode
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      },
    );

    // Apply animation if enabled
    if (widget.hasAnimation) {
      textField = ScaleTransition(
        scale: _scaleAnimation,
        child: textField,
      );
    }

    // Apply shadow if enabled
    if (widget.hasShadow) {
      textField = Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: widget.elevation * 2,
              spreadRadius: widget.elevation / 2,
              offset: Offset(0, widget.elevation),
            ),
          ],
        ),
        child: textField,
      );
    }

    // Add increment/decrement buttons if enabled
    if (widget.showIncrementButtons) {
      textField = Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          IconButton(
            icon: const Icon(Icons.remove),
            onPressed: widget.isDisabled || widget.isReadOnly ? null : _decrement,
            tooltip: 'Decrease by ${widget.incrementAmount}',
          ),
          Expanded(child: textField),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: widget.isDisabled || widget.isReadOnly ? null : _increment,
            tooltip: 'Increase by ${widget.incrementAmount}',
          ),
        ],
      );
    }

    // Apply tooltip if provided
    if (widget.tooltip != null) {
      textField = Tooltip(
        message: widget.tooltip!,
        child: textField,
      );
    }

    // Add slider if enabled
    if (widget.showSlider && widget.minValue != double.negativeInfinity && widget.maxValue != double.infinity) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          textField,
          const SizedBox(height: 8.0),
          Slider(
            value: _sliderValue?.clamp(widget.minValue, widget.maxValue) ?? widget.minValue,
            min: widget.minValue,
            max: widget.maxValue,
            onChanged: widget.isDisabled || widget.isReadOnly
                ? null
                : (value) {
                    setState(() {
                      _sliderValue = value;
                      _currentValue = value;
                      _controller.text = _formatNumber(_currentValue);
                      _isValid = true;
                    });
                    if (widget.onChanged != null) {
                      widget.onChanged!(value);
                    }
                  },
            divisions: (widget.maxValue - widget.minValue) <= 100
                ? (widget.maxValue - widget.minValue).toInt()
                : 100,
            label: _formatNumber(_sliderValue),
          ),
        ],
      );
    }

    return textField;
  }

  Widget? _buildSuffixIcon() {
    List<Widget> suffixIcons = [];

    // Add validation icon if enabled
    if (widget.showValidationIcon) {
      suffixIcons.add(
        Icon(
          _isValid ? Icons.check_circle : Icons.error,
          color: _isValid ? Colors.green : widget.outOfRangeColor,
          size: 20.0,
        ),
      );
    }

    // Add clear button if enabled
    if (widget.showClearButton) {
      suffixIcons.add(
        IconButton(
          icon: const Icon(Icons.clear, size: 18.0),
          onPressed: widget.isDisabled || widget.isReadOnly ? null : _clearValue,
          tooltip: 'Clear',
        ),
      );
    }

    // Add copy button if enabled
    if (widget.showCopyButton) {
      suffixIcons.add(
        IconButton(
          icon: const Icon(Icons.copy, size: 18.0),
          onPressed: widget.isDisabled ? null : _copyValue,
          tooltip: 'Copy',
        ),
      );
    }

    // Add paste button if enabled
    if (widget.showPasteButton) {
      suffixIcons.add(
        IconButton(
          icon: const Icon(Icons.paste, size: 18.0),
          onPressed: widget.isDisabled || widget.isReadOnly ? null : _pasteValue,
          tooltip: 'Paste',
        ),
      );
    }

    // Add custom suffix icon if provided
    if (widget.showSuffix && widget.suffixIcon != null) {
      suffixIcons.add(Icon(widget.suffixIcon));
    }

    if (suffixIcons.isEmpty) {
      return null;
    } else if (suffixIcons.length == 1) {
      return suffixIcons.first;
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: suffixIcons,
      );
    }
  }
}