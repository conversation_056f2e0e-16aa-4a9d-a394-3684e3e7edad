// To parse this JSON data, do
//
//     final localObjectiveInputsModel = localObjectiveInputsModelFromJson(jsonString);

import 'dart:convert';

LocalObjectiveInputsModel localObjectiveInputsModelFromJson(String str) =>
    LocalObjectiveInputsModel.fromJson(json.decode(str));

String localObjectiveInputsModelToJson(LocalObjectiveInputsModel data) =>
    json.encode(data.toJson());

class LocalObjectiveInputsModel {
  String? localObjective;
  List<Input>? userInputs;
  List<Input>? systemInputs;
  List<dynamic>? infoInputs;
  List<Input>? dependentInputs;
  Dependencies? dependencies;
  dynamic loUiStack;

  LocalObjectiveInputsModel({
    this.localObjective,
    this.userInputs,
    this.systemInputs,
    this.infoInputs,
    this.dependentInputs,
    this.dependencies,
    this.loUiStack,
  });

  LocalObjectiveInputsModel copyWith({
    String? localObjective,
    List<Input>? userInputs,
    List<Input>? systemInputs,
    List<dynamic>? infoInputs,
    List<Input>? dependentInputs,
    Dependencies? dependencies,
    dynamic loUiStack,
  }) =>
      LocalObjectiveInputsModel(
        localObjective: localObjective ?? this.localObjective,
        userInputs: userInputs ?? this.userInputs,
        systemInputs: systemInputs ?? this.systemInputs,
        infoInputs: infoInputs ?? this.infoInputs,
        dependentInputs: dependentInputs ?? this.dependentInputs,
        dependencies: dependencies ?? this.dependencies,
        loUiStack: loUiStack ?? this.loUiStack,
      );

  factory LocalObjectiveInputsModel.fromJson(Map<String, dynamic> json) =>
      LocalObjectiveInputsModel(
        localObjective: json["local_objective"],
        userInputs: json["user_inputs"] == null
            ? []
            : List<Input>.from(
                json["user_inputs"]!.map((x) => Input.fromJson(x))),
        systemInputs: json["system_inputs"] == null
            ? []
            : List<Input>.from(
                json["system_inputs"]!.map((x) => Input.fromJson(x))),
        infoInputs: json["info_inputs"] == null
            ? []
            : List<dynamic>.from(json["info_inputs"]!.map((x) => x)),
        dependentInputs: json["dependent_inputs"] == null
            ? []
            : List<Input>.from(
                json["dependent_inputs"]!.map((x) => Input.fromJson(x))),
        dependencies: json["dependencies"] == null
            ? null
            : Dependencies.fromJson(json["dependencies"]),
        loUiStack: json["lo_ui_stack"],
      );

  Map<String, dynamic> toJson() => {
        "local_objective": localObjective,
        "user_inputs": userInputs == null
            ? []
            : List<dynamic>.from(userInputs!.map((x) => x.toJson())),
        "system_inputs": systemInputs == null
            ? []
            : List<dynamic>.from(systemInputs!.map((x) => x.toJson())),
        "info_inputs": infoInputs == null
            ? []
            : List<dynamic>.from(infoInputs!.map((x) => x)),
        "dependent_inputs": dependentInputs == null
            ? []
            : List<dynamic>.from(dependentInputs!.map((x) => x.toJson())),
        "dependencies": dependencies?.toJson(),
        "lo_ui_stack": loUiStack,
      };
}

class Dependencies {
  List<String>? startDateEndDate;
  List<String>? leaveTypeName;

  Dependencies({
    this.startDateEndDate,
    this.leaveTypeName,
  });

  Dependencies copyWith({
    List<String>? startDateEndDate,
    List<String>? leaveTypeName,
  }) =>
      Dependencies(
        startDateEndDate: startDateEndDate ?? this.startDateEndDate,
        leaveTypeName: leaveTypeName ?? this.leaveTypeName,
      );

  factory Dependencies.fromJson(Map<String, dynamic> json) => Dependencies(
        startDateEndDate: json["startDate,endDate"] == null
            ? []
            : List<String>.from(json["startDate,endDate"]!.map((x) => x)),
        leaveTypeName: json["leaveTypeName"] == null
            ? []
            : List<String>.from(json["leaveTypeName"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "startDate,endDate": startDateEndDate == null
            ? []
            : List<dynamic>.from(startDateEndDate!.map((x) => x)),
        "leaveTypeName": leaveTypeName == null
            ? []
            : List<dynamic>.from(leaveTypeName!.map((x) => x)),
      };
}

class Input {
  int? id;
  String? itemId;
  String? inputStackId;
  String? slotId;
  String? sourceType;
  String? sourceDescription;
  bool? required;
  String? loId;
  String? dataType;
  String? uiControl;
  String? nestedFunctionId;
  dynamic isVisible;
  dynamic name;
  dynamic type;
  bool? readOnly;
  String? agentType;
  bool? dependentAttribute;
  String? dependentAttributeValue;
  Map<String, String>? dependentAttributeIds;
  List<String>? enumValues;
  String? defaultValue;
  bool? informationField;
  bool? constantField;
  String? entityId;
  String? attributeId;
  String? entityName;
  String? attributeName;
  String? displayName;
  String? inputValue;
  dynamic allowedValues;
  dynamic validations;
  bool? hasDropdownSource;
  dynamic dropdownOptions;
  dynamic dependencies;
  dynamic dependencyType;
  bool? needsParentValue;
  dynamic parentIds;
  dynamic metadata;
  String? contextualId;

  Input({
    this.id,
    this.itemId,
    this.inputStackId,
    this.slotId,
    this.sourceType,
    this.sourceDescription,
    this.required,
    this.loId,
    this.dataType,
    this.uiControl,
    this.nestedFunctionId,
    this.isVisible,
    this.name,
    this.type,
    this.readOnly,
    this.agentType,
    this.dependentAttribute,
    this.dependentAttributeValue,
    this.dependentAttributeIds,
    this.enumValues,
    this.defaultValue,
    this.informationField,
    this.constantField,
    this.entityId,
    this.attributeId,
    this.entityName,
    this.attributeName,
    this.displayName,
    this.inputValue,
    this.allowedValues,
    this.validations,
    this.hasDropdownSource,
    this.dropdownOptions,
    this.dependencies,
    this.dependencyType,
    this.needsParentValue,
    this.parentIds,
    this.metadata,
    this.contextualId,
  });

  Input copyWith({
    int? id,
    String? itemId,
    String? inputStackId,
    String? slotId,
    String? sourceType,
    String? sourceDescription,
    bool? required,
    String? loId,
    String? dataType,
    String? uiControl,
    String? nestedFunctionId,
    dynamic isVisible,
    dynamic name,
    dynamic type,
    bool? readOnly,
    String? agentType,
    bool? dependentAttribute,
    String? dependentAttributeValue,
    Map<String, String>? dependentAttributeIds,
    List<String>? enumValues,
    String? defaultValue,
    bool? informationField,
    bool? constantField,
    String? entityId,
    String? attributeId,
    String? entityName,
    String? attributeName,
    String? displayName,
    String? inputValue,
    dynamic allowedValues,
    dynamic validations,
    bool? hasDropdownSource,
    dynamic dropdownOptions,
    dynamic dependencies,
    dynamic dependencyType,
    bool? needsParentValue,
    dynamic parentIds,
    dynamic metadata,
    String? contextualId,
  }) =>
      Input(
        id: id ?? this.id,
        itemId: itemId ?? this.itemId,
        inputStackId: inputStackId ?? this.inputStackId,
        slotId: slotId ?? this.slotId,
        sourceType: sourceType ?? this.sourceType,
        sourceDescription: sourceDescription ?? this.sourceDescription,
        required: required ?? this.required,
        loId: loId ?? this.loId,
        dataType: dataType ?? this.dataType,
        uiControl: uiControl ?? this.uiControl,
        nestedFunctionId: nestedFunctionId ?? this.nestedFunctionId,
        isVisible: isVisible ?? this.isVisible,
        name: name ?? this.name,
        type: type ?? this.type,
        readOnly: readOnly ?? this.readOnly,
        agentType: agentType ?? this.agentType,
        dependentAttribute: dependentAttribute ?? this.dependentAttribute,
        dependentAttributeValue:
            dependentAttributeValue ?? this.dependentAttributeValue,
        dependentAttributeIds:
            dependentAttributeIds ?? this.dependentAttributeIds,
        enumValues: enumValues ?? this.enumValues,
        defaultValue: defaultValue ?? this.defaultValue,
        informationField: informationField ?? this.informationField,
        constantField: constantField ?? this.constantField,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        entityName: entityName ?? this.entityName,
        attributeName: attributeName ?? this.attributeName,
        displayName: displayName ?? this.displayName,
        inputValue: inputValue ?? this.inputValue,
        allowedValues: allowedValues ?? this.allowedValues,
        validations: validations ?? this.validations,
        hasDropdownSource: hasDropdownSource ?? this.hasDropdownSource,
        dropdownOptions: dropdownOptions ?? this.dropdownOptions,
        dependencies: dependencies ?? this.dependencies,
        dependencyType: dependencyType ?? this.dependencyType,
        needsParentValue: needsParentValue ?? this.needsParentValue,
        parentIds: parentIds ?? this.parentIds,
        metadata: metadata ?? this.metadata,
        contextualId: contextualId ?? this.contextualId,
      );

  factory Input.fromJson(Map<String, dynamic> json) => Input(
        id: json["id"],
        itemId: json["item_id"],
        inputStackId: json["input_stack_id"],
        slotId: json["slot_id"],
        sourceType: json["source_type"],
        sourceDescription: json["source_description"],
        required: json["required"],
        loId: json["lo_id"],
        dataType: json["data_type"],
        uiControl: json["ui_control"],
        nestedFunctionId: json["nested_function_id"],
        isVisible: json["is_visible"],
        name: json["name"],
        type: json["type"],
        readOnly: json["read_only"],
        agentType: json["agent_type"],
        dependentAttribute: json["dependent_attribute"],
        dependentAttributeValue: json["dependent_attribute_value"],
        dependentAttributeIds: json["dependent_attribute_ids"] == null
            ? null
            : Map<String, String>.from(json["dependent_attribute_ids"]),
        enumValues: json["enum_values"] == null
            ? []
            : List<String>.from(json["enum_values"]!.map((x) => x)),
        defaultValue: json["default_value"],
        informationField: json["information_field"],
        constantField: json["constant_field"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        entityName: json["entity_name"],
        attributeName: json["attribute_name"],
        displayName: json["display_name"],
        inputValue: json["input_value"],
        allowedValues: json["allowed_values"],
        validations: json["validations"],
        hasDropdownSource: json["has_dropdown_source"],
        dropdownOptions: json["dropdown_options"],
        dependencies: json["dependencies"],
        dependencyType: json["dependency_type"],
        needsParentValue: json["needs_parent_value"],
        parentIds: json["parent_ids"],
        metadata: json["metadata"],
        contextualId: json["contextual_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "item_id": itemId,
        "input_stack_id": inputStackId,
        "slot_id": slotId,
        "source_type": sourceType,
        "source_description": sourceDescription,
        "required": required,
        "lo_id": loId,
        "data_type": dataType,
        "ui_control": uiControl,
        "nested_function_id": nestedFunctionId,
        "is_visible": isVisible,
        "name": name,
        "type": type,
        "read_only": readOnly,
        "agent_type": agentType,
        "dependent_attribute": dependentAttribute,
        "dependent_attribute_value": dependentAttributeValue,
        "dependent_attribute_ids": dependentAttributeIds,
        "enum_values": enumValues == null
            ? []
            : List<dynamic>.from(enumValues!.map((x) => x)),
        "default_value": defaultValue,
        "information_field": informationField,
        "constant_field": constantField,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "entity_name": entityName,
        "attribute_name": attributeName,
        "display_name": displayName,
        "input_value": inputValue,
        "allowed_values": allowedValues,
        "validations": validations,
        "has_dropdown_source": hasDropdownSource,
        "dropdown_options": dropdownOptions,
        "dependencies": dependencies,
        "dependency_type": dependencyType,
        "needs_parent_value": needsParentValue,
        "parent_ids": parentIds,
        "metadata": metadata,
        "contextual_id": contextualId,
      };
}
