import 'package:flutter/material.dart';
import '../models/global_objective.dart';
import 'workflow_detail_screen_fixed.dart';
import '../utils/input_value_store.dart';

class ExistingTransactionsScreen extends StatelessWidget {
  final GlobalObjective objective;
  final List<dynamic> transactions;

  const ExistingTransactionsScreen({
    super.key,
    required this.objective,
    required this.transactions,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Existing Transactions'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Existing transactions for ${objective.name}',
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: transactions.length,
              itemBuilder: (context, index) {
                final transaction = transactions[index];
                // Format dates if they exist
                String formattedCreatedAt = 'N/A';
                String formattedUpdatedAt = 'N/A';

                if (transaction['created_at'] != null) {
                  try {
                    final createdAt = DateTime.parse(transaction['created_at']);
                    formattedCreatedAt =
                        '${createdAt.day}/${createdAt.month}/${createdAt.year} ${createdAt.hour}:${createdAt.minute.toString().padLeft(2, '0')}';
                  } catch (e) {
                    formattedCreatedAt = transaction['created_at'];
                  }
                }

                if (transaction['updated_at'] != null) {
                  try {
                    final updatedAt = DateTime.parse(transaction['updated_at']);
                    formattedUpdatedAt =
                        '${updatedAt.day}/${updatedAt.month}/${updatedAt.year} ${updatedAt.hour}:${updatedAt.minute.toString().padLeft(2, '0')}';
                  } catch (e) {
                    formattedUpdatedAt = transaction['updated_at'];
                  }
                }

                return Card(
                  margin: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 8.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header row with GO name and status
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                transaction['go_name'] ?? objective.name,
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            _buildStatusChip(
                                transaction['status'] ?? 'pending'),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // Workflow Instance ID
                        Row(
                          children: [
                            Text(
                              'Instance ID: ',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            Expanded(
                              child: Text(
                                transaction['workflow_instance_id'] ??
                                    transaction['id'] ??
                                    'N/A',
                                style: Theme.of(context).textTheme.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),

                        // Created At
                        Row(
                          children: [
                            Text(
                              'Created: ',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            Text(
                              formattedCreatedAt,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),

                        // Updated At
                        Row(
                          children: [
                            Text(
                              'Updated: ',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            Text(
                              formattedUpdatedAt,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),

                        // Resume button
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: () {
                              // Clear the input store before navigating to workflow detail screen
                              final inputStore = InputValueStore();
                              inputStore.clear();

                              // Navigate to workflow detail screen with the transaction ID
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => WorkflowDetailScreen(
                                    objective: objective,
                                    transactionId: transaction['id'],
                                  ),
                                ),
                              );
                            },
                            child: const Text('Resume'),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Clear the input store before navigating to workflow detail screen
                  final inputStore = InputValueStore();
                  inputStore.clear();

                  // Navigate to workflow detail screen to start a fresh transaction
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          WorkflowDetailScreen(objective: objective),
                    ),
                  );
                },
                child: const Text('Start New Transaction'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build status chip with different colors for different status
  Widget _buildStatusChip(String status) {
    Color chipColor;
    String statusText = status.toUpperCase();

    switch (status.toLowerCase()) {
      case 'completed':
        chipColor = Colors.green;
        break;
      case 'pending':
        chipColor = Colors.orange;
        break;
      case 'failed':
        chipColor = Colors.red;
        break;
      default:
        chipColor = Colors.grey;
    }

    // Return just the text without any container decoration
    return Text(
      statusText,
      style: TextStyle(
        color: chipColor,
        fontWeight: FontWeight.bold,
        fontSize: 12,
      ),
    );
  }
}
