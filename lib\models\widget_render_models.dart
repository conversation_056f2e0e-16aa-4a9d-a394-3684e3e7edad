/// Model representing the entire widget render configuration
class WidgetRenderModel {
  final IntentModel intent;
  final LayoutModel layout;
  final List<ComponentModel> components;
  final List<GlobalFilterModel>? globalFilters;
  final List<GlobalActionModel>? globalActions;
  final DataRequirementsModel? dataRequirements;
  final List<CrossComponentInteractionModel>? crossComponentInteractions;

  WidgetRenderModel({
    required this.intent,
    required this.layout,
    required this.components,
    this.globalFilters,
    this.globalActions,
    this.dataRequirements,
    this.crossComponentInteractions,
  });

  factory WidgetRenderModel.fromJson(Map<String, dynamic> json) {
    return WidgetRenderModel(
      intent: IntentModel.fromJson(json['intent']),
      layout: LayoutModel.fromJson(json['layout']),
      components: (json['components'] as List)
          .map((component) => ComponentModel.fromJson(component))
          .toList(),
      globalFilters: json['global_filters'] != null
          ? (json['global_filters'] as List)
              .map((filter) => GlobalFilterModel.fromJson(filter))
              .toList()
          : null,
      globalActions: json['global_actions'] != null
          ? (json['global_actions'] as List)
              .map((action) => GlobalActionModel.fromJson(action))
              .toList()
          : null,
      dataRequirements: json['data_requirements'] != null
          ? DataRequirementsModel.fromJson(json['data_requirements'])
          : null,
      crossComponentInteractions: json['cross_component_interaction'] != null
          ? (json['cross_component_interaction'] as List)
              .map((interaction) =>
                  CrossComponentInteractionModel.fromJson(interaction))
              .toList()
          : null,
    );
  }
}

/// Model representing the intent of the widget render
class IntentModel {
  final String type;
  final String query;

  IntentModel({
    required this.type,
    required this.query,
  });

  factory IntentModel.fromJson(Map<String, dynamic> json) {
    return IntentModel(
      type: json['type'],
      query: json['query'],
    );
  }
}

/// Model representing the layout configuration
class LayoutModel {
  final String type;
  final String deviceType;
  final List<ContainerModel> containers;

  LayoutModel({
    required this.type,
    required this.deviceType,
    required this.containers,
  });

  factory LayoutModel.fromJson(Map<String, dynamic> json) {
    return LayoutModel(
      type: json['type'],
      deviceType: json['device_type'],
      containers: (json['containers'] as List)
          .map((container) => ContainerModel.fromJson(container))
          .toList(),
    );
  }
}

/// Model representing a container in the layout
class ContainerModel {
  final String id;
  final String size;
  final String layout;
  final List<String> components;

  ContainerModel({
    required this.id,
    required this.size,
    required this.layout,
    required this.components,
  });

  factory ContainerModel.fromJson(Map<String, dynamic> json) {
    return ContainerModel(
      id: json['id'],
      size: json['size'],
      layout: json['layout'],
      components: List<String>.from(json['components']),
    );
  }
}

/// Model representing a component to be rendered
class ComponentModel {
  final String id;
  final String widget;
  final Map<String, dynamic> config;

  ComponentModel({
    required this.id,
    required this.widget,
    required this.config,
  });

  factory ComponentModel.fromJson(Map<String, dynamic> json) {
    return ComponentModel(
      id: json['id'],
      widget: json['widget'],
      config: json['config'],
    );
  }
}

/// Model representing a global filter
class GlobalFilterModel {
  final String id;
  final String widget;
  final String label;
  final String defaultPreset;
  final List<Map<String, dynamic>> presets;
  final List<String> affectsComponents;
  final String callback;

  GlobalFilterModel({
    required this.id,
    required this.widget,
    required this.label,
    required this.defaultPreset,
    required this.presets,
    required this.affectsComponents,
    required this.callback,
  });

  factory GlobalFilterModel.fromJson(Map<String, dynamic> json) {
    return GlobalFilterModel(
      id: json['id'],
      widget: json['widget'],
      label: json['label'],
      defaultPreset: json['default_preset'],
      presets: (json['presets'] as List)
          .map((preset) => preset as Map<String, dynamic>)
          .toList(),
      affectsComponents: List<String>.from(json['affects_components']),
      callback: json['callback'],
    );
  }
}

/// Model representing a global action
class GlobalActionModel {
  final String widget;
  final String icon;
  final String label;
  final List<Map<String, dynamic>>? options;
  final String callback;

  GlobalActionModel({
    required this.widget,
    required this.icon,
    required this.label,
    this.options,
    required this.callback,
  });

  factory GlobalActionModel.fromJson(Map<String, dynamic> json) {
    return GlobalActionModel(
      widget: json['widget'],
      icon: json['icon'],
      label: json['label'],
      options: json['options'] != null
          ? (json['options'] as List)
              .map((option) => option as Map<String, dynamic>)
              .toList()
          : null,
      callback: json['callback'],
    );
  }
}

/// Model representing data requirements
class DataRequirementsModel {
  final List<DataSourceModel> sources;

  DataRequirementsModel({
    required this.sources,
  });

  factory DataRequirementsModel.fromJson(Map<String, dynamic> json) {
    return DataRequirementsModel(
      sources: (json['sources'] as List)
          .map((source) => DataSourceModel.fromJson(source))
          .toList(),
    );
  }
}

/// Model representing a data source
class DataSourceModel {
  final String id;
  final String query;
  final String? aggregation;
  final List<String>? metrics;
  final List<String>? groupBy;
  final Map<String, dynamic>? createField;
  final List<Map<String, dynamic>>? calculate;
  final List<Map<String, dynamic>>? sort;
  final List<String>? fields;

  DataSourceModel({
    required this.id,
    required this.query,
    this.aggregation,
    this.metrics,
    this.groupBy,
    this.createField,
    this.calculate,
    this.sort,
    this.fields,
  });

  factory DataSourceModel.fromJson(Map<String, dynamic> json) {
    return DataSourceModel(
      id: json['id'],
      query: json['query'],
      aggregation: json['aggregation'],
      metrics:
          json['metrics'] != null ? List<String>.from(json['metrics']) : null,
      groupBy:
          json['group_by'] != null ? List<String>.from(json['group_by']) : null,
      createField: json['create_field'] as Map<String, dynamic>?,
      calculate: json['calculate'] != null
          ? (json['calculate'] as List)
              .map((calc) => calc as Map<String, dynamic>)
              .toList()
          : null,
      sort: json['sort'] != null
          ? (json['sort'] as List)
              .map((sort) => sort as Map<String, dynamic>)
              .toList()
          : null,
      fields: json['fields'] != null ? List<String>.from(json['fields']) : null,
    );
  }
}

/// Model representing cross-component interaction
class CrossComponentInteractionModel {
  final String source;
  final String event;
  final List<String> targets;
  final String action;
  final Map<String, dynamic> parameters;

  CrossComponentInteractionModel({
    required this.source,
    required this.event,
    required this.targets,
    required this.action,
    required this.parameters,
  });

  factory CrossComponentInteractionModel.fromJson(Map<String, dynamic> json) {
    return CrossComponentInteractionModel(
      source: json['source'],
      event: json['event'],
      targets: List<String>.from(json['targets']),
      action: json['action'],
      parameters: json['parameters'] as Map<String, dynamic>,
    );
  }
}
