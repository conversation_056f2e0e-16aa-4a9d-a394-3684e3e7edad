# Solution for Workflow Detail Screen

The issue is that when one set of inputs is executed, it should be collapsed and the submit button for that set of inputs should be disabled, while retaining the values submitted for those inputs in their respective UI controls.

## Key Changes Required

1. **Track Submitted Sections**:
   ```dart
   // Add a map to track submitted sections
   final Map<String, bool> _submittedSections = {};
   ```

2. **Mark Sections as Submitted After Execution**:
   ```dart
   // In _handleNextButtonPressed after successful API call
   setState(() {
     _submittedSections[sectionId] = true;
     // Collapse the section after submission
     _expandedSections[sectionId] = false;
   });
   ```

3. **Disable Submit Button for Submitted Sections**:
   ```dart
   // In the button's onPressed property
   onPressed: (isLastSection && !isSubmitted)
       ? () => _handleNextButtonPressed(sectionId)
       : null,
   ```

4. **Update Button Text for Submitted Sections**:
   ```dart
   // In the button's child property
   child: Text(
     isSubmitted
         ? 'Submitted'
         : (isLastSection ? 'Submit' : 'Next')
   ),
   ```

5. **Make Input Fields Read-Only for Submitted Sections**:
   ```dart
   // Add readOnly property to InputFieldWidget
   class InputFieldWidget extends StatefulWidget {
     final InputField field;
     final bool readOnly;

     const InputFieldWidget({
       super.key,
       required this.field,
       this.readOnly = false,
     });
     // ...
   }
   ```

6. **Pass readOnly Property to InputFieldWidget**:
   ```dart
   // When creating the InputFieldWidget
   InputFieldWidget(
     key: key,
     field: field,
     readOnly: isSubmitted, // Use the local isSubmitted variable
   ),
   ```

7. **Update All Input Controls to Respect readOnly Property**:
   ```dart
   // For text fields
   enabled: !widget.readOnly,
   
   // For date pickers
   enabled: !widget.readOnly,
   
   // For dropdowns
   onChanged: widget.readOnly ? null : (value) { ... },
   
   // For checkboxes and radio buttons
   onChanged: widget.readOnly ? null : (checked) { ... },
   
   // For sliders
   onChanged: widget.readOnly ? null : (value) { ... },
   ```

## Implementation Notes

1. The key issue was that the StatefulBuilder was creating a local state that wasn't being properly updated when the parent state changed. By using a local `isSubmitted` variable derived from `_submittedSections[sectionId]`, we ensure that the UI reflects the current submission state.

2. We need to make sure that all input controls respect the `readOnly` property to prevent users from modifying submitted values.

3. The section should be automatically collapsed after submission to provide a cleaner UI and focus the user on the next set of inputs.

4. The submit button should be disabled and show "Submitted" text to clearly indicate that the section has been processed.

5. All submitted values should be retained and displayed in their respective UI controls, but users should not be able to modify them.

This solution ensures that when a set of inputs is executed, it is automatically collapsed, the submit button is disabled, and the submitted values are retained in their respective UI controls but cannot be modified.
