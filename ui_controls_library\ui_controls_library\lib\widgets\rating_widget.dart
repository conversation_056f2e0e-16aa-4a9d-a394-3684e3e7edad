import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'dart:math' as math;
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension RatingColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Defines the visual style of the rating widget
enum RatingStyle {
  /// Star icons (★)
  stars,

  /// Heart icons (♥)
  hearts,

  /// Thumbs up icons (👍)
  thumbs,

  /// Smiley face icons (😊)
  smileys,

  /// Numeric rating (1, 2, 3, etc.)
  numbers,

  /// Dot indicators (•)
  dots,

  /// Custom icon
  custom,
}

/// Defines the direction of the rating widget
enum RatingDirection {
  /// Left to right (default)
  leftToRight,

  /// Right to left
  rightToLeft,

  /// Top to bottom
  topToBottom,

  /// Bottom to top
  bottomToTop,
}

/// Defines the size behavior of the rating widget
enum RatingSizeBehavior {
  /// All items are the same size
  uniform,

  /// Selected items are larger
  selectedLarger,

  /// Hovered items are larger
  hoveredLarger,

  /// Both selected and hovered items are larger
  bothLarger,
}

/// A comprehensive rating widget that allows users to provide ratings with various styles.
class RatingWidget extends StatefulWidget {
  // Basic properties
  final int itemCount;
  final double initialRating;
  final bool allowHalfRating;
  final bool allowClearRating;
  final bool readOnly;
  final bool ignoreGestures;

  // Style properties
  final RatingStyle ratingStyle;
  final IconData? customIcon;
  final RatingDirection direction;
  final double itemSize;
  final double itemPadding;
  final RatingSizeBehavior sizeBehavior;
  final double selectedItemScale;
  final double hoveredItemScale;

  // Color properties
  final Color selectedColor;
  final Color unselectedColor;
  final Color disabledColor;
  final Color? halfFilledColor;
  final Color? hoverColor;
  final Color backgroundColor;
  final bool useGradient;
  final List<Color>? gradientColors;
  final Alignment gradientBegin;
  final Alignment gradientEnd;

  // Text properties
  final bool showRatingText;
  final String? ratingTextPrefix;
  final String? ratingTextSuffix;
  final TextStyle? ratingTextStyle;
  final bool showMaxRatingText;
  final String? maxRatingTextPrefix;
  final String? maxRatingTextSuffix;
  final TextStyle? maxRatingTextStyle;

  // Feedback properties
  final bool hasFeedback;
  final bool hasTooltip;
  final List<String>? tooltips;
  final bool showTooltipOnHover;
  final bool hasAnimation;
  final Duration animationDuration;
  final Curve animationCurve;

  // Layout properties
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasShadow;
  final Color shadowColor;
  final double elevation;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  // Callbacks
  final ValueChanged<double>? onRatingChanged;
  final ValueChanged<double>? onRatingUpdate;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Rating-specific JSON configuration
  /// Whether to use JSON rating configuration
  final bool useJsonRatingConfig;

  /// Rating-specific JSON configuration
  final Map<String, dynamic>? ratingConfig;

  const RatingWidget({
    super.key,
    this.itemCount = 5,
    this.initialRating = 0,
    this.allowHalfRating = false,
    this.allowClearRating = false,
    this.readOnly = false,
    this.ignoreGestures = false,
    this.ratingStyle = RatingStyle.stars,
    this.customIcon,
    this.direction = RatingDirection.leftToRight,
    this.itemSize = 24.0,
    this.itemPadding = 4.0,
    this.sizeBehavior = RatingSizeBehavior.uniform,
    this.selectedItemScale = 1.2,
    this.hoveredItemScale = 1.1,
    this.selectedColor = Colors.amber,
    this.unselectedColor = Colors.grey,
    this.disabledColor = Colors.grey,
    this.halfFilledColor,
    this.hoverColor,
    this.backgroundColor = Colors.transparent,
    this.useGradient = false,
    this.gradientColors,
    this.gradientBegin = Alignment.topLeft,
    this.gradientEnd = Alignment.bottomRight,
    this.showRatingText = false,
    this.ratingTextPrefix,
    this.ratingTextSuffix,
    this.ratingTextStyle,
    this.showMaxRatingText = false,
    this.maxRatingTextPrefix,
    this.maxRatingTextSuffix,
    this.maxRatingTextStyle,
    this.hasFeedback = true,
    this.hasTooltip = false,
    this.tooltips,
    this.showTooltipOnHover = true,
    this.hasAnimation = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasShadow = false,
    this.shadowColor = Colors.black,
    this.elevation = 2.0,
    this.padding = const EdgeInsets.all(8.0),
    this.margin = EdgeInsets.zero,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.onRatingChanged,
    this.onRatingUpdate,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Rating-specific JSON configuration
    this.useJsonRatingConfig = false,
    this.ratingConfig,
  });

  /// Creates a RatingWidget from a JSON map
  ///
  /// This factory constructor allows for creating a RatingWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory RatingWidget.fromJson(Map<String, dynamic> json) {
    // Parse rating style
    RatingStyle ratingStyle = RatingStyle.stars;
    if (json.containsKey('ratingStyle')) {
      final String styleStr = json['ratingStyle'].toString().toLowerCase();
      if (styleStr == 'hearts') {
        ratingStyle = RatingStyle.hearts;
      } else if (styleStr == 'thumbs') {
        ratingStyle = RatingStyle.thumbs;
      } else if (styleStr == 'smileys') {
        ratingStyle = RatingStyle.smileys;
      } else if (styleStr == 'numbers') {
        ratingStyle = RatingStyle.numbers;
      } else if (styleStr == 'dots') {
        ratingStyle = RatingStyle.dots;
      } else if (styleStr == 'custom') {
        ratingStyle = RatingStyle.custom;
      }
    }

    // Parse rating direction
    RatingDirection direction = RatingDirection.leftToRight;
    if (json.containsKey('direction')) {
      final String dirStr = json['direction'].toString().toLowerCase();
      if (dirStr == 'righttoleft') {
        direction = RatingDirection.rightToLeft;
      } else if (dirStr == 'toptobottom') {
        direction = RatingDirection.topToBottom;
      } else if (dirStr == 'bottomtotop') {
        direction = RatingDirection.bottomToTop;
      }
    }

    // Parse size behavior
    RatingSizeBehavior sizeBehavior = RatingSizeBehavior.uniform;
    if (json.containsKey('sizeBehavior')) {
      final String behaviorStr = json['sizeBehavior'].toString().toLowerCase();
      if (behaviorStr == 'selectedlarger') {
        sizeBehavior = RatingSizeBehavior.selectedLarger;
      } else if (behaviorStr == 'hoveredlarger') {
        sizeBehavior = RatingSizeBehavior.hoveredLarger;
      } else if (behaviorStr == 'bothlarger') {
        sizeBehavior = RatingSizeBehavior.bothLarger;
      }
    }

    // Parse colors
    Color selectedColor = Colors.amber;
    if (json.containsKey('selectedColor')) {
      selectedColor = _parseColor(json['selectedColor']);
    }

    Color unselectedColor = Colors.grey;
    if (json.containsKey('unselectedColor')) {
      unselectedColor = _parseColor(json['unselectedColor']);
    }

    Color disabledColor = Colors.grey;
    if (json.containsKey('disabledColor')) {
      disabledColor = _parseColor(json['disabledColor']);
    }

    Color? halfFilledColor;
    if (json.containsKey('halfFilledColor')) {
      halfFilledColor = _parseColor(json['halfFilledColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color backgroundColor = Colors.transparent;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color borderColor = Colors.grey;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color shadowColor = Colors.black;
    if (json.containsKey('shadowColor')) {
      shadowColor = _parseColor(json['shadowColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse gradient colors
    List<Color>? gradientColors;
    if (json.containsKey('gradientColors') && json['gradientColors'] is List) {
      gradientColors =
          (json['gradientColors'] as List).map((c) => _parseColor(c)).toList();
    }

    // Parse gradient alignments
    Alignment gradientBegin = Alignment.topLeft;
    if (json.containsKey('gradientBegin')) {
      gradientBegin = _parseAlignment(json['gradientBegin']);
    }

    Alignment gradientEnd = Alignment.bottomRight;
    if (json.containsKey('gradientEnd')) {
      gradientEnd = _parseAlignment(json['gradientEnd']);
    }

    // Parse text styles
    TextStyle? ratingTextStyle;
    if (json.containsKey('ratingTextStyle')) {
      ratingTextStyle = _parseTextStyle(json['ratingTextStyle']);
    }

    TextStyle? maxRatingTextStyle;
    if (json.containsKey('maxRatingTextStyle')) {
      maxRatingTextStyle = _parseTextStyle(json['maxRatingTextStyle']);
    }

    // Parse animation curve
    Curve animationCurve = Curves.easeInOut;
    if (json.containsKey('animationCurve')) {
      final String curveStr = json['animationCurve'].toString().toLowerCase();
      if (curveStr == 'linear') {
        animationCurve = Curves.linear;
      } else if (curveStr == 'decelerate') {
        animationCurve = Curves.decelerate;
      } else if (curveStr == 'ease') {
        animationCurve = Curves.ease;
      } else if (curveStr == 'easein') {
        animationCurve = Curves.easeIn;
      } else if (curveStr == 'easeout') {
        animationCurve = Curves.easeOut;
      } else if (curveStr == 'elasticin') {
        animationCurve = Curves.elasticIn;
      } else if (curveStr == 'elasticout') {
        animationCurve = Curves.elasticOut;
      } else if (curveStr == 'elasticinout') {
        animationCurve = Curves.elasticInOut;
      }
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.all(8.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse alignments
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.center;
    if (json.containsKey('mainAxisAlignment')) {
      final String alignStr =
          json['mainAxisAlignment'].toString().toLowerCase();
      if (alignStr == 'start') {
        mainAxisAlignment = MainAxisAlignment.start;
      } else if (alignStr == 'end') {
        mainAxisAlignment = MainAxisAlignment.end;
      } else if (alignStr == 'spacebetween') {
        mainAxisAlignment = MainAxisAlignment.spaceBetween;
      } else if (alignStr == 'spacearound') {
        mainAxisAlignment = MainAxisAlignment.spaceAround;
      } else if (alignStr == 'spaceevenly') {
        mainAxisAlignment = MainAxisAlignment.spaceEvenly;
      }
    }

    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center;
    if (json.containsKey('crossAxisAlignment')) {
      final String alignStr =
          json['crossAxisAlignment'].toString().toLowerCase();
      if (alignStr == 'start') {
        crossAxisAlignment = CrossAxisAlignment.start;
      } else if (alignStr == 'end') {
        crossAxisAlignment = CrossAxisAlignment.end;
      } else if (alignStr == 'stretch') {
        crossAxisAlignment = CrossAxisAlignment.stretch;
      } else if (alignStr == 'baseline') {
        crossAxisAlignment = CrossAxisAlignment.baseline;
      }
    }

    // Parse animation duration
    Duration animationDuration = const Duration(milliseconds: 300);
    if (json.containsKey('animationDuration')) {
      animationDuration = Duration(
        milliseconds: json['animationDuration'] as int? ?? 300,
      );
    }

    // Parse tooltips
    List<String>? tooltips;
    if (json.containsKey('tooltips') && json['tooltips'] is List) {
      tooltips = (json['tooltips'] as List).map((t) => t.toString()).toList();
    }

    // Parse custom icon
    IconData? customIcon;
    if (json.containsKey('customIcon')) {
      customIcon = _parseIconData(json['customIcon']);
    }

    return RatingWidget(
      // Basic properties
      itemCount: json['itemCount'] as int? ?? 5,
      initialRating:
          json['initialRating'] != null
              ? (json['initialRating'] as num).toDouble()
              : 0,
      allowHalfRating: json['allowHalfRating'] as bool? ?? false,
      allowClearRating: json['allowClearRating'] as bool? ?? false,
      readOnly: json['readOnly'] as bool? ?? false,
      ignoreGestures: json['ignoreGestures'] as bool? ?? false,

      // Style properties
      ratingStyle: ratingStyle,
      customIcon: customIcon,
      direction: direction,
      itemSize:
          json['itemSize'] != null
              ? (json['itemSize'] as num).toDouble()
              : 24.0,
      itemPadding:
          json['itemPadding'] != null
              ? (json['itemPadding'] as num).toDouble()
              : 4.0,
      sizeBehavior: sizeBehavior,
      selectedItemScale:
          json['selectedItemScale'] != null
              ? (json['selectedItemScale'] as num).toDouble()
              : 1.2,
      hoveredItemScale:
          json['hoveredItemScale'] != null
              ? (json['hoveredItemScale'] as num).toDouble()
              : 1.1,

      // Color properties
      selectedColor: selectedColor,
      unselectedColor: unselectedColor,
      disabledColor: disabledColor,
      halfFilledColor: halfFilledColor,
      hoverColor: hoverColor,
      backgroundColor: backgroundColor,
      useGradient: json['useGradient'] as bool? ?? false,
      gradientColors: gradientColors,
      gradientBegin: gradientBegin,
      gradientEnd: gradientEnd,

      // Text properties
      showRatingText: json['showRatingText'] as bool? ?? false,
      ratingTextPrefix: json['ratingTextPrefix'] as String?,
      ratingTextSuffix: json['ratingTextSuffix'] as String?,
      ratingTextStyle: ratingTextStyle,
      showMaxRatingText: json['showMaxRatingText'] as bool? ?? false,
      maxRatingTextPrefix: json['maxRatingTextPrefix'] as String?,
      maxRatingTextSuffix: json['maxRatingTextSuffix'] as String?,
      maxRatingTextStyle: maxRatingTextStyle,

      // Feedback properties
      hasFeedback: json['hasFeedback'] as bool? ?? true,
      hasTooltip: json['hasTooltip'] as bool? ?? false,
      tooltips: tooltips,
      showTooltipOnHover: json['showTooltipOnHover'] as bool? ?? true,
      hasAnimation: json['hasAnimation'] as bool? ?? true,
      animationDuration: animationDuration,
      animationCurve: animationCurve,

      // Layout properties
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: borderColor,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      shadowColor: shadowColor,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      padding: padding,
      margin: margin,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,

      // Advanced interaction properties
      autofocus: json['autofocus'] as bool? ?? false,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonRatingConfig: json['useJsonRatingConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'transparent':
            return Colors.transparent;
          default:
            return Colors.amber;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.amber; // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0); // Default padding
  }

  /// Parses a text style from a map
  static TextStyle? _parseTextStyle(dynamic value) {
    if (value is! Map) return null;

    Color color = Colors.black;
    if (value.containsKey('color')) {
      color = _parseColor(value['color']);
    }

    double fontSize = 14.0;
    if (value.containsKey('fontSize')) {
      fontSize = (value['fontSize'] as num).toDouble();
    }

    FontWeight fontWeight = FontWeight.normal;
    if (value.containsKey('fontWeight')) {
      if (value['fontWeight'] is String) {
        switch ((value['fontWeight'] as String).toLowerCase()) {
          case 'thin':
            fontWeight = FontWeight.w100;
            break;
          case 'extralight':
            fontWeight = FontWeight.w200;
            break;
          case 'light':
            fontWeight = FontWeight.w300;
            break;
          case 'regular':
            fontWeight = FontWeight.w400;
            break;
          case 'medium':
            fontWeight = FontWeight.w500;
            break;
          case 'semibold':
            fontWeight = FontWeight.w600;
            break;
          case 'bold':
            fontWeight = FontWeight.w700;
            break;
          case 'extrabold':
            fontWeight = FontWeight.w800;
            break;
          case 'black':
            fontWeight = FontWeight.w900;
            break;
        }
      } else if (value['fontWeight'] is int) {
        final int weight = value['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    FontStyle fontStyle = FontStyle.normal;
    if (value.containsKey('fontStyle')) {
      if (value['fontStyle'] is String &&
          (value['fontStyle'] as String).toLowerCase() == 'italic') {
        fontStyle = FontStyle.italic;
      }
    }

    TextDecoration decoration = TextDecoration.none;
    if (value.containsKey('decoration')) {
      if (value['decoration'] is String) {
        switch ((value['decoration'] as String).toLowerCase()) {
          case 'underline':
            decoration = TextDecoration.underline;
            break;
          case 'overline':
            decoration = TextDecoration.overline;
            break;
          case 'linethrough':
            decoration = TextDecoration.lineThrough;
            break;
        }
      }
    }

    return TextStyle(
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      decoration: decoration,
    );
  }

  /// Parses an alignment from a string
  static Alignment _parseAlignment(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'topleft':
          return Alignment.topLeft;
        case 'topcenter':
          return Alignment.topCenter;
        case 'topright':
          return Alignment.topRight;
        case 'centerleft':
          return Alignment.centerLeft;
        case 'center':
          return Alignment.center;
        case 'centerright':
          return Alignment.centerRight;
        case 'bottomleft':
          return Alignment.bottomLeft;
        case 'bottomcenter':
          return Alignment.bottomCenter;
        case 'bottomright':
          return Alignment.bottomRight;
        default:
          return Alignment.center;
      }
    }
    return Alignment.center;
  }

  /// Parses icon data from a string
  static IconData? _parseIconData(String? iconName) {
    if (iconName == null) return null;

    switch (iconName.toLowerCase()) {
      case 'star':
        return Icons.star;
      case 'star_border':
        return Icons.star_border;
      case 'star_half':
        return Icons.star_half;
      case 'favorite':
        return Icons.favorite;
      case 'favorite_border':
        return Icons.favorite_border;
      case 'thumb_up':
        return Icons.thumb_up;
      case 'thumb_up_outlined':
        return Icons.thumb_up_outlined;
      case 'sentiment_very_dissatisfied':
        return Icons.sentiment_very_dissatisfied;
      case 'sentiment_very_dissatisfied_outlined':
        return Icons.sentiment_very_dissatisfied_outlined;
      case 'sentiment_neutral':
        return Icons.sentiment_neutral;
      case 'sentiment_neutral_outlined':
        return Icons.sentiment_neutral_outlined;
      case 'sentiment_very_satisfied':
        return Icons.sentiment_very_satisfied;
      case 'sentiment_very_satisfied_outlined':
        return Icons.sentiment_very_satisfied_outlined;
      default:
        return Icons.star;
    }
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'itemCount': itemCount,
      'initialRating': initialRating,
      'allowHalfRating': allowHalfRating,
      'allowClearRating': allowClearRating,
      'readOnly': readOnly,
      'ignoreGestures': ignoreGestures,

      // Style properties
      'ratingStyle': ratingStyle.toString().split('.').last,
      'direction': direction.toString().split('.').last,
      'itemSize': itemSize,
      'itemPadding': itemPadding,
      'sizeBehavior': sizeBehavior.toString().split('.').last,
      'selectedItemScale': selectedItemScale,
      'hoveredItemScale': hoveredItemScale,

      // Color properties
      'selectedColor': '#${selectedColor.toHexString()}',
      'unselectedColor': '#${unselectedColor.toHexString()}',
      'disabledColor': '#${disabledColor.toHexString()}',
      'halfFilledColor':
          halfFilledColor != null ? '#${halfFilledColor!.toHexString()}' : null,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'useGradient': useGradient,
      'gradientBegin': _alignmentToString(gradientBegin),
      'gradientEnd': _alignmentToString(gradientEnd),

      // Text properties
      'showRatingText': showRatingText,
      'ratingTextPrefix': ratingTextPrefix,
      'ratingTextSuffix': ratingTextSuffix,
      'showMaxRatingText': showMaxRatingText,
      'maxRatingTextPrefix': maxRatingTextPrefix,
      'maxRatingTextSuffix': maxRatingTextSuffix,

      // Feedback properties
      'hasFeedback': hasFeedback,
      'hasTooltip': hasTooltip,
      'showTooltipOnHover': showTooltipOnHover,
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,
      'animationCurve': _curveToString(animationCurve),

      // Layout properties
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasShadow': hasShadow,
      'shadowColor': '#${shadowColor.toHexString()}',
      'elevation': elevation,
      'padding': _edgeInsetsToJson(padding),
      'margin': _edgeInsetsToJson(margin),
      'mainAxisAlignment': mainAxisAlignment.toString().split('.').last,
      'crossAxisAlignment': crossAxisAlignment.toString().split('.').last,

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonRatingConfig': useJsonRatingConfig,
    };
  }

  /// Converts EdgeInsetsGeometry to a JSON representation
  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsetsGeometry edgeInsets) {
    if (edgeInsets is EdgeInsets) {
      if (edgeInsets.left == edgeInsets.top &&
          edgeInsets.left == edgeInsets.right &&
          edgeInsets.left == edgeInsets.bottom) {
        return {'all': edgeInsets.left};
      } else if (edgeInsets.left == edgeInsets.right &&
          edgeInsets.top == edgeInsets.bottom) {
        return {'horizontal': edgeInsets.left, 'vertical': edgeInsets.top};
      } else {
        return {
          'left': edgeInsets.left,
          'top': edgeInsets.top,
          'right': edgeInsets.right,
          'bottom': edgeInsets.bottom,
        };
      }
    }
    return {'all': 0.0}; // Default
  }

  /// Converts Alignment to a string representation
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.topLeft) return 'topLeft';
    if (alignment == Alignment.topCenter) return 'topCenter';
    if (alignment == Alignment.topRight) return 'topRight';
    if (alignment == Alignment.centerLeft) return 'centerLeft';
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.centerRight) return 'centerRight';
    if (alignment == Alignment.bottomLeft) return 'bottomLeft';
    if (alignment == Alignment.bottomCenter) return 'bottomCenter';
    if (alignment == Alignment.bottomRight) return 'bottomRight';
    return 'center';
  }

  /// Converts Curve to a string representation
  static String _curveToString(Curve curve) {
    if (curve == Curves.linear) return 'linear';
    if (curve == Curves.decelerate) return 'decelerate';
    if (curve == Curves.ease) return 'ease';
    if (curve == Curves.easeIn) return 'easeIn';
    if (curve == Curves.easeOut) return 'easeOut';
    if (curve == Curves.easeInOut) return 'easeInOut';
    if (curve == Curves.elasticIn) return 'elasticIn';
    if (curve == Curves.elasticOut) return 'elasticOut';
    if (curve == Curves.elasticInOut) return 'elasticInOut';
    return 'easeInOut';
  }

  @override
  State<RatingWidget> createState() => _RatingWidgetState();
}

class _RatingWidgetState extends State<RatingWidget>
    with SingleTickerProviderStateMixin {
  late double _rating;
  double? _hoverRating;
  late AnimationController _animationController;

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(RatingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Create the container for the rating widget
    Widget ratingContainer = Container(
      //padding: widget.padding,
      //margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
        boxShadow:
            widget.hasShadow
                ? [
                  BoxShadow(
                    color: widget.shadowColor.withAlpha(26),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ]
                : null,
        gradient:
            widget.useGradient && widget.gradientColors != null
                ? LinearGradient(
                  colors: widget.gradientColors!,
                  begin: widget.gradientBegin,
                  end: widget.gradientEnd,
                )
                : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: widget.crossAxisAlignment,
        children: [
          // Rating items
          _buildRatingItems(),

          // Rating text
          if (widget.showRatingText || widget.showMaxRatingText)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: _buildRatingText(),
            ),
        ],
      ),
    );

    Widget content = ratingContainer;

    // Apply hover detection
    if (widget.onHover != null) {
      content = MouseRegion(
        onEnter: (event) {
          widget.onHover!(true);
        },
        onExit: (event) {
          widget.onHover!(false);
        },
        child: content,
      );
    }

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Apply advanced gesture detection
    if (widget.onDoubleTap != null || widget.onLongPress != null) {
      content = GestureDetector(
        onDoubleTap:
            widget.onDoubleTap != null
                ? () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                }
                : null,
        onLongPress:
            widget.onLongPress != null
                ? () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                }
                : null,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }

  Widget _buildRatingItems() {
    // Determine the direction of the rating items
    Axis direction;
    bool reversed;

    switch (widget.direction) {
      case RatingDirection.leftToRight:
        direction = Axis.horizontal;
        reversed = false;
        break;
      case RatingDirection.rightToLeft:
        direction = Axis.horizontal;
        reversed = true;
        break;
      case RatingDirection.topToBottom:
        direction = Axis.vertical;
        reversed = false;
        break;
      case RatingDirection.bottomToTop:
        direction = Axis.vertical;
        reversed = true;
        break;
    }

    // Create the rating items
    List<Widget> ratingItems = List.generate(
      widget.itemCount,
      (index) => _buildRatingItem(index + 1),
    );

    if (reversed) {
      ratingItems = ratingItems.reversed.toList();
    }

    return Flex(
      direction: direction,
      mainAxisAlignment: widget.mainAxisAlignment,
      mainAxisSize: MainAxisSize.min,
      children: ratingItems,
    );
  }

  Widget _buildRatingItem(int index) {
    // Determine if the item is selected, half-selected, or unselected
    bool isSelected = index <= _rating;
    bool isHalfSelected =
        widget.allowHalfRating &&
        (index == _rating.ceil()) &&
        (_rating % 1 != 0);
    bool isHovered = _hoverRating != null && index <= _hoverRating!;
    bool isHalfHovered =
        widget.allowHalfRating &&
        _hoverRating != null &&
        (index == _hoverRating!.ceil()) &&
        (_hoverRating! % 1 != 0);

    // Determine the color of the item
    Color itemColor;
    if (widget.readOnly || widget.ignoreGestures) {
      if (isSelected) {
        itemColor = widget.selectedColor;
      } else if (isHalfSelected) {
        itemColor = widget.halfFilledColor ?? widget.selectedColor;
      } else {
        itemColor = widget.unselectedColor;
      }
    } else {
      if (isHovered || isHalfHovered) {
        itemColor = widget.hoverColor ?? widget.selectedColor;
      } else if (isSelected) {
        itemColor = widget.selectedColor;
      } else if (isHalfSelected) {
        itemColor = widget.halfFilledColor ?? widget.selectedColor;
      } else {
        itemColor = widget.unselectedColor;
      }
    }

    // Determine the size of the item
    double itemScale = 1.0;
    if (widget.sizeBehavior == RatingSizeBehavior.selectedLarger &&
        isSelected) {
      itemScale = widget.selectedItemScale;
    } else if (widget.sizeBehavior == RatingSizeBehavior.hoveredLarger &&
        isHovered) {
      itemScale = widget.hoveredItemScale;
    } else if (widget.sizeBehavior == RatingSizeBehavior.bothLarger) {
      if (isHovered) {
        itemScale = widget.hoveredItemScale;
      } else if (isSelected) {
        itemScale = widget.selectedItemScale;
      }
    }

    // Create the rating item
    Widget ratingItem;

    switch (widget.ratingStyle) {
      case RatingStyle.stars:
        ratingItem = Icon(
          isHalfSelected || isHalfHovered
              ? Icons.star_half
              : (isSelected || isHovered ? Icons.star : Icons.star_border),
          color: itemColor,
          size: widget.itemSize * itemScale,
        );
        break;
      case RatingStyle.hearts:
        ratingItem = Icon(
          isSelected || isHovered ? Icons.favorite : Icons.favorite_border,
          color: itemColor,
          size: widget.itemSize * itemScale,
        );
        break;
      case RatingStyle.thumbs:
        ratingItem = Icon(
          isSelected || isHovered ? Icons.thumb_up : Icons.thumb_up_outlined,
          color: itemColor,
          size: widget.itemSize * itemScale,
        );
        break;
      case RatingStyle.smileys:
        IconData iconData;
        if (index <= widget.itemCount / 3) {
          iconData =
              isSelected || isHovered
                  ? Icons.sentiment_very_dissatisfied
                  : Icons.sentiment_very_dissatisfied_outlined;
        } else if (index <= 2 * widget.itemCount / 3) {
          iconData =
              isSelected || isHovered
                  ? Icons.sentiment_neutral
                  : Icons.sentiment_neutral_outlined;
        } else {
          iconData =
              isSelected || isHovered
                  ? Icons.sentiment_very_satisfied
                  : Icons.sentiment_very_satisfied_outlined;
        }
        ratingItem = Icon(
          iconData,
          color: itemColor,
          size: widget.itemSize * itemScale,
        );
        break;
      case RatingStyle.numbers:
        ratingItem = Container(
          width: widget.itemSize * itemScale,
          height: widget.itemSize * itemScale,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isSelected || isHovered ? itemColor : Colors.transparent,
            border: Border.all(color: itemColor, width: 1.0),
          ),
          child: Center(
            child: Text(
              index.toString(),
              style: TextStyle(
                color: isSelected || isHovered ? Colors.white : itemColor,
                fontSize: widget.itemSize * 0.5 * itemScale,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
        break;
      case RatingStyle.dots:
        ratingItem = Container(
          width: widget.itemSize * itemScale,
          height: widget.itemSize * itemScale,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isSelected || isHovered ? itemColor : Colors.transparent,
            border: Border.all(color: itemColor, width: 1.0),
          ),
        );
        break;
      case RatingStyle.custom:
        ratingItem = Icon(
          widget.customIcon ?? Icons.star,
          color: itemColor,
          size: widget.itemSize * itemScale,
        );
        break;
    }

    // Add padding to the rating item
    Widget paddedItem = Padding(
      //padding: EdgeInsets.all(widget.itemPadding),
      padding: const EdgeInsets.only(right:6),
      child: ratingItem,
    );

    // Add tooltip if needed
    if (widget.hasTooltip &&
        widget.tooltips != null &&
        index - 1 < widget.tooltips!.length) {
      paddedItem = Tooltip(
        message: widget.tooltips![index - 1],
        preferBelow: true,
        showDuration: const Duration(seconds: 1),
        child: paddedItem,
      );
    }

    // Add animation if needed
    if (widget.hasAnimation) {
      paddedItem = AnimatedContainer(
        duration: widget.animationDuration,
        curve: widget.animationCurve,
        transform: Matrix4.identity()..scale(itemScale),
        transformAlignment: Alignment.center,
        child: paddedItem,
      );
    }

    // Add gesture detection if not read-only
    if (!widget.readOnly && !widget.ignoreGestures) {
      return GestureDetector(
        onTap: () => _updateRating(index.toDouble()),
        onHorizontalDragUpdate: (details) => _handleDragUpdate(details, index),
        onVerticalDragUpdate: (details) => _handleDragUpdate(details, index),
        child: MouseRegion(
          onEnter: (_) => _handleHover(index.toDouble()),
          onExit: (_) => _handleHoverExit(),
          onHover: (event) => _handleHoverUpdate(event, index),
          cursor: SystemMouseCursors.click,
          child: paddedItem,
        ),
      );
    }

    return paddedItem;
  }

  Widget _buildRatingText() {
    final TextStyle defaultTextStyle = TextStyle(
      fontSize: 14.0,
      color: widget.selectedColor,
      fontWeight: FontWeight.bold,
    );

    List<Widget> textWidgets = [];

    // Add rating text if needed
    if (widget.showRatingText) {
      textWidgets.add(
        Text(
          '${widget.ratingTextPrefix ?? ''}${_rating.toStringAsFixed(widget.allowHalfRating ? 1 : 0)}${widget.ratingTextSuffix ?? ''}',
          style: widget.ratingTextStyle ?? defaultTextStyle,
        ),
      );
    }

    // Add max rating text if needed
    if (widget.showMaxRatingText) {
      if (widget.showRatingText) {
        textWidgets.add(const Text(' / '));
      }

      textWidgets.add(
        Text(
          '${widget.maxRatingTextPrefix ?? ''}${widget.itemCount}${widget.maxRatingTextSuffix ?? ''}',
          style:
              widget.maxRatingTextStyle ??
              defaultTextStyle.copyWith(
                color: widget.unselectedColor,
                fontWeight: FontWeight.normal,
              ),
        ),
      );
    }

    return Row(mainAxisSize: MainAxisSize.min, children: textWidgets);
  }

  void _updateRating(double rating) {
    // If the same rating is tapped again and allowClearRating is true, clear the rating
    if (widget.allowClearRating && _rating == rating) {
      rating = 0;
    }

    if (rating != _rating) {
      setState(() {
        _rating = rating;
      });

      // Provide haptic feedback if enabled
      if (widget.hasFeedback) {
        // HapticFeedback.lightImpact();
      }

      // Trigger animation if enabled
      if (widget.hasAnimation) {
        _animationController.reset();
        _animationController.forward().then((_) {
          _animationController.reverse();
        });
      }

      // Notify listeners
      if (widget.onRatingChanged != null) {
        widget.onRatingChanged!(_rating);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onRatingChanged')) {
        _executeJsonCallback('onRatingChanged', _rating);
      }
    }
  }

  void _handleDragUpdate(DragUpdateDetails details, int index) {
    if (widget.direction == RatingDirection.leftToRight ||
        widget.direction == RatingDirection.rightToLeft) {
      // Horizontal drag
      RenderBox box = context.findRenderObject() as RenderBox;
      double dx = details.localPosition.dx;
      double itemWidth = box.size.width / widget.itemCount;
      double newRating =
          widget.direction == RatingDirection.leftToRight
              ? (dx / itemWidth).clamp(0, widget.itemCount.toDouble())
              : widget.itemCount -
                  (dx / itemWidth).clamp(0, widget.itemCount.toDouble());

      if (widget.allowHalfRating) {
        newRating = (newRating * 2).round() / 2;
      } else {
        newRating = newRating.round().toDouble();
      }

      if (newRating != _rating) {
        setState(() {
          _rating = newRating;
        });

        // Call standard callback
        if (widget.onRatingUpdate != null) {
          widget.onRatingUpdate!(_rating);
        }

        // Execute JSON callback if defined
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onRatingUpdate')) {
          _executeJsonCallback('onRatingUpdate', _rating);
        }
      }
    } else {
      // Vertical drag
      RenderBox box = context.findRenderObject() as RenderBox;
      double dy = details.localPosition.dy;
      double itemHeight = box.size.height / widget.itemCount;
      double newRating =
          widget.direction == RatingDirection.topToBottom
              ? (dy / itemHeight).clamp(0, widget.itemCount.toDouble())
              : widget.itemCount -
                  (dy / itemHeight).clamp(0, widget.itemCount.toDouble());

      if (widget.allowHalfRating) {
        newRating = (newRating * 2).round() / 2;
      } else {
        newRating = newRating.round().toDouble();
      }

      if (newRating != _rating) {
        setState(() {
          _rating = newRating;
        });

        // Call standard callback
        if (widget.onRatingUpdate != null) {
          widget.onRatingUpdate!(_rating);
        }

        // Execute JSON callback if defined
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onRatingUpdate')) {
          _executeJsonCallback('onRatingUpdate', _rating);
        }
      }
    }
  }

  void _handleHover(double rating) {
    if (!widget.readOnly && !widget.ignoreGestures) {
      setState(() {
        _hoverRating = rating;
      });
    }
  }

  void _handleHoverExit() {
    if (!widget.readOnly && !widget.ignoreGestures) {
      setState(() {
        _hoverRating = null;
      });
    }
  }

  void _handleHoverUpdate(PointerHoverEvent event, int index) {
    if (!widget.readOnly && !widget.ignoreGestures && widget.allowHalfRating) {
      RenderBox box = context.findRenderObject() as RenderBox;
      Offset localPosition = box.globalToLocal(event.position);

      double itemSize =
          widget.direction == RatingDirection.leftToRight ||
                  widget.direction == RatingDirection.rightToLeft
              ? box.size.width / widget.itemCount
              : box.size.height / widget.itemCount;

      double position =
          widget.direction == RatingDirection.leftToRight ||
                  widget.direction == RatingDirection.rightToLeft
              ? localPosition.dx
              : localPosition.dy;

      int itemIndex = (position / itemSize).floor();
      double remainder = (position % itemSize) / itemSize;

      double newRating;
      if (remainder > 0.75) {
        newRating = (itemIndex + 1).toDouble();
      } else if (remainder > 0.25) {
        newRating = itemIndex + 0.5;
      } else {
        newRating = itemIndex.toDouble();
      }

      newRating = math.min(newRating, widget.itemCount.toDouble());

      if (widget.direction == RatingDirection.rightToLeft ||
          widget.direction == RatingDirection.bottomToTop) {
        newRating = widget.itemCount - newRating;
      }

      if (_hoverRating != newRating) {
        setState(() {
          _hoverRating = newRating;
        });
      }
    }
  }
}
