import 'package:flutter/material.dart';

/// A highly configurable card widget that can be customized with various properties.
///
/// This widget wraps <PERSON><PERSON><PERSON>'s Card widget and provides additional customization options.
/// It supports configuration through JSON for easy integration with dynamic systems.
class CardWidget extends StatefulWidget {
  /// The content inside the card.
  final Widget? content;

  /// Optional title for the card.
  final String? title;

  /// Optional subtitle for the card.
  final String? subtitle;

  /// Optional icon to display in the card header.
  final IconData? icon;

  /// Optional image to display at the top of the card.
  final String? imageUrl;

  /// Height of the image.
  final double imageHeight;

  /// Width of the image.
  final double imageWidth;

  /// How the image should be inscribed into the available space.
  final BoxFit imageFit;

  /// Whether to display the image as a header (above the title).
  final bool imageAsHeader;

  /// Whether to display the image as a background.
  final bool imageAsBackground;

  /// Optional actions to display at the bottom of the card.
  final List<Widget>? actions;

  /// Background color of the card.
  final Color backgroundColor;

  /// Text color for content inside the card.
  final Color textColor;

  /// Color for the card's border.
  final Color borderColor;

  /// Width of the card's border.
  final double borderWidth;

  /// Radius of the card's corners.
  final double borderRadius;

  /// Whether to show a border around the card.
  final bool hasBorder;

  /// Whether to show a shadow under the card.
  final bool hasShadow;

  /// Elevation of the card (affects shadow).
  final double elevation;

  /// Padding inside the card.
  final EdgeInsetsGeometry padding;

  /// Margin around the card.
  final EdgeInsetsGeometry margin;

  /// Width of the card.
  final double? width;

  /// Height of the card.
  final double? height;

  /// Whether the card is clickable.
  final bool isClickable;

  /// Callback when the card is tapped.
  final VoidCallback? onTap;

  /// Whether to show a divider between header and content.
  final bool showDivider;

  /// Color of the divider.
  final Color dividerColor;

  /// Whether the card is in a loading state.
  final bool isLoading;

  /// Whether the card is disabled.
  final bool isDisabled;

  /// Whether the card is selected.
  final bool isSelected;

  /// Color to use when the card is selected.
  final Color selectedColor;

  /// Whether to use a dark theme.
  final bool isDarkTheme;

  /// Font size for the title.
  final double titleFontSize;

  /// Font size for the subtitle.
  final double subtitleFontSize;

  /// Font size for the content.
  final double contentFontSize;

  /// Font weight for the title.
  final FontWeight titleFontWeight;

  /// Font weight for the subtitle.
  final FontWeight subtitleFontWeight;

  /// Font weight for the content.
  final FontWeight contentFontWeight;

  /// Color for the icon.
  final Color iconColor;

  /// Size of the icon.
  final double iconSize;

  /// Whether to show a header.
  final bool showHeader;

  /// Whether to show a footer.
  final bool showFooter;

  /// Alignment of the content.
  final Alignment contentAlignment;

  /// Whether to make the card expandable.
  final bool isExpandable;

  /// Whether the card is initially expanded.
  final bool initiallyExpanded;

  /// Optional text for the expand button.
  final String? expandButtonText;

  /// Optional text for the collapse button.
  final String? collapseButtonText;

  /// Creates a card widget with the specified properties.
  const CardWidget({
    super.key,
    this.content,
    this.title,
    this.subtitle,
    this.icon,
    this.imageUrl,
    this.imageHeight = 150.0,
    this.imageWidth = 300.0,
    this.imageFit = BoxFit.cover,
    this.imageAsHeader = false,
    this.imageAsBackground = false,
    this.actions,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = false,
    this.hasShadow = true,
    this.elevation = 2.0,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.all(8.0),
    this.width,
    this.height,
    this.isClickable = false,
    this.onTap,
    this.showDivider = true,
    this.dividerColor = Colors.grey,
    this.isLoading = false,
    this.isDisabled = false,
    this.isSelected = false,
    this.selectedColor = Colors.blue,
    this.isDarkTheme = false,
    this.titleFontSize = 18.0,
    this.subtitleFontSize = 14.0,
    this.contentFontSize = 14.0,
    this.titleFontWeight = FontWeight.bold,
    this.subtitleFontWeight = FontWeight.normal,
    this.contentFontWeight = FontWeight.normal,
    this.iconColor = Colors.blue,
    this.iconSize = 24.0,
    this.showHeader = true,
    this.showFooter = true,
    this.contentAlignment = Alignment.center,
    this.isExpandable = false,
    this.initiallyExpanded = true,
    this.expandButtonText,
    this.collapseButtonText,
  });

  @override
  State<CardWidget> createState() => _CardWidgetState();

  /// Creates a CardWidget from a JSON map.
  ///
  /// This factory constructor allows creating a CardWidget from a JSON object,
  /// making it easy to configure the widget dynamically.
  factory CardWidget.fromJson(Map<String, dynamic> json) {
    // Helper function to get a color from a string or int
    Color getColorFromValue(dynamic value) {
      if (value is String) {
        if (value.startsWith('#')) {
          // Handle hex color
          final hex = value.replaceFirst('#', '');
          final hexValue = int.tryParse(hex, radix: 16);
          if (hexValue != null) {
            return Color(hexValue | 0xFF000000); // Add alpha if needed
          }
        }

        // Handle named colors
        switch (value.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.blue; // Default color
        }
      } else if (value is int) {
        return Color(value);
      }
      return Colors.blue; // Default color
    }

    // Helper function to get a font weight from a string or int
    FontWeight getFontWeightFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'bold': return FontWeight.bold;
          case 'normal': return FontWeight.normal;
          case 'light': return FontWeight.w300;
          case 'medium': return FontWeight.w500;
          case 'semibold': return FontWeight.w600;
          case 'extrabold': return FontWeight.w800;
          case 'black': return FontWeight.w900;
          default: return FontWeight.normal;
        }
      } else if (value is int) {
        return FontWeight.values.firstWhere(
          (weight) => weight.index == value,
          orElse: () => FontWeight.normal,
        );
      }
      return FontWeight.normal;
    }

    // Helper function to get an icon from a string
    IconData? getIconFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'info': return Icons.info;
          case 'warning': return Icons.warning;
          case 'error': return Icons.error;
          case 'success': return Icons.check_circle;
          case 'help': return Icons.help;
          case 'settings': return Icons.settings;
          case 'person': return Icons.person;
          case 'home': return Icons.home;
          case 'star': return Icons.star;
          case 'favorite': return Icons.favorite;
          case 'menu': return Icons.menu;
          case 'search': return Icons.search;
          case 'add': return Icons.add;
          case 'remove': return Icons.remove;
          case 'edit': return Icons.edit;
          case 'delete': return Icons.delete;
          case 'share': return Icons.share;
          case 'download': return Icons.download;
          case 'upload': return Icons.upload;
          case 'calendar': return Icons.calendar_today;
          case 'notification': return Icons.notifications;
          case 'email': return Icons.email;
          case 'phone': return Icons.phone;
          case 'location': return Icons.location_on;
          case 'camera': return Icons.camera_alt;
          case 'image': return Icons.image;
          case 'video': return Icons.videocam;
          case 'music': return Icons.music_note;
          case 'file': return Icons.insert_drive_file;
          case 'folder': return Icons.folder;
          case 'cloud': return Icons.cloud;
          case 'wifi': return Icons.wifi;
          case 'bluetooth': return Icons.bluetooth;
          case 'battery': return Icons.battery_full;
          case 'lock': return Icons.lock;
          case 'unlock': return Icons.lock_open;
          case 'key': return Icons.vpn_key;
          case 'security': return Icons.security;
          case 'time': return Icons.access_time;
          case 'alarm': return Icons.alarm;
          case 'timer': return Icons.timer;
          case 'play': return Icons.play_arrow;
          case 'pause': return Icons.pause;
          case 'stop': return Icons.stop;
          case 'next': return Icons.skip_next;
          case 'previous': return Icons.skip_previous;
          case 'shuffle': return Icons.shuffle;
          case 'repeat': return Icons.repeat;
          case 'volume': return Icons.volume_up;
          case 'mute': return Icons.volume_off;
          case 'mic': return Icons.mic;
          case 'mic_off': return Icons.mic_off;
          case 'call': return Icons.call;
          case 'call_end': return Icons.call_end;
          case 'message': return Icons.message;
          case 'chat': return Icons.chat;
          case 'comment': return Icons.comment;
          case 'send': return Icons.send;
          case 'attach': return Icons.attach_file;
          case 'link': return Icons.link;
          case 'copy': return Icons.content_copy;
          case 'paste': return Icons.content_paste;
          case 'cut': return Icons.content_cut;
          case 'undo': return Icons.undo;
          case 'redo': return Icons.redo;
          case 'refresh': return Icons.refresh;
          case 'sync': return Icons.sync;
          case 'more': return Icons.more_horiz;
          case 'menu_vertical': return Icons.more_vert;
          case 'list': return Icons.list;
          case 'grid': return Icons.grid_view;
          case 'dashboard': return Icons.dashboard;
          case 'table': return Icons.table_chart;
          case 'chart': return Icons.bar_chart;
          case 'pie_chart': return Icons.pie_chart;
          case 'line_chart': return Icons.show_chart;
          case 'map': return Icons.map;
          case 'directions': return Icons.directions;
          case 'navigation': return Icons.navigation;
          case 'place': return Icons.place;
          case 'restaurant': return Icons.restaurant;
          case 'store': return Icons.store;
          case 'shopping': return Icons.shopping_cart;
          case 'money': return Icons.attach_money;
          case 'credit_card': return Icons.credit_card;
          case 'wallet': return Icons.account_balance_wallet;
          case 'gift': return Icons.card_giftcard;
          case 'discount': return Icons.local_offer;
          case 'flight': return Icons.flight;
          case 'hotel': return Icons.hotel;
          case 'car': return Icons.directions_car;
          case 'train': return Icons.train;
          case 'bus': return Icons.directions_bus;
          case 'bike': return Icons.directions_bike;
          case 'walk': return Icons.directions_walk;
          case 'run': return Icons.directions_run;
          case 'accessibility': return Icons.accessibility;
          case 'language': return Icons.language;
          case 'translate': return Icons.translate;
          case 'print': return Icons.print;
          case 'save': return Icons.save;
          case 'bookmark': return Icons.bookmark;
          case 'favorite_border': return Icons.favorite_border;
          case 'star_border': return Icons.star_border;
          case 'check': return Icons.check;
          case 'close': return Icons.close;
          case 'arrow_up': return Icons.arrow_upward;
          case 'arrow_down': return Icons.arrow_downward;
          case 'arrow_left': return Icons.arrow_back;
          case 'arrow_right': return Icons.arrow_forward;
          case 'expand': return Icons.expand_more;
          case 'collapse': return Icons.expand_less;
          case 'fullscreen': return Icons.fullscreen;
          case 'exit_fullscreen': return Icons.fullscreen_exit;
          case 'zoom_in': return Icons.zoom_in;
          case 'zoom_out': return Icons.zoom_out;
          case 'filter': return Icons.filter_list;
          case 'sort': return Icons.sort;
          case 'view': return Icons.visibility;
          case 'hide': return Icons.visibility_off;
          case 'account': return Icons.account_circle;
          case 'group': return Icons.group;
          case 'business': return Icons.business;
          case 'work': return Icons.work;
          case 'school': return Icons.school;
          case 'book': return Icons.book;
          case 'library': return Icons.local_library;
          case 'code': return Icons.code;
          case 'terminal': return Icons.terminal;
          case 'web': return Icons.web;
          case 'laptop': return Icons.laptop;
          case 'desktop': return Icons.desktop_windows;
          case 'tablet': return Icons.tablet;
          case 'phone_android': return Icons.phone_android;
          case 'phone_iphone': return Icons.phone_iphone;
          case 'tv': return Icons.tv;
          case 'speaker': return Icons.speaker;
          case 'headset': return Icons.headset;
          case 'bluetooth_audio': return Icons.bluetooth_audio;
          case 'keyboard': return Icons.keyboard;
          case 'mouse': return Icons.mouse;
          case 'memory': return Icons.memory;
          case 'sim_card': return Icons.sim_card;
          case 'sd_card': return Icons.sd_card;
          case 'usb': return Icons.usb;
          case 'wifi_off': return Icons.wifi_off;
          case 'bluetooth_disabled': return Icons.bluetooth_disabled;
          case 'power': return Icons.power_settings_new;
          case 'build': return Icons.build;
          case 'tune': return Icons.tune;
          case 'bug': return Icons.bug_report;
          case 'history': return Icons.history;
          case 'update': return Icons.update;
          case 'info_outline': return Icons.info_outline;
          case 'warning_outline': return Icons.warning_amber_outlined;
          case 'error_outline': return Icons.error_outline;
          case 'help_outline': return Icons.help_outline;
          case 'feedback': return Icons.feedback;
          case 'lightbulb': return Icons.lightbulb;
          case 'lightbulb_outline': return Icons.lightbulb_outline;
          default: return null;
        }
      }
      return null;
    }

    // Helper function to get BoxFit from string
    BoxFit getBoxFitFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'cover': return BoxFit.cover;
          case 'contain': return BoxFit.contain;
          case 'fill': return BoxFit.fill;
          case 'fitheight':
          case 'fit_height':
          case 'height': return BoxFit.fitHeight;
          case 'fitwidth':
          case 'fit_width':
          case 'width': return BoxFit.fitWidth;
          case 'none': return BoxFit.none;
          case 'scaledown':
          case 'scale_down': return BoxFit.scaleDown;
          default: return BoxFit.cover;
        }
      }
      return BoxFit.cover;
    }

    // Helper function to get EdgeInsets from value
    EdgeInsetsGeometry getEdgeInsetsFromValue(dynamic value) {
      if (value is String) {
        // Parse formats like "16.0" or "16.0,8.0,16.0,8.0" (top,right,bottom,left)
        final parts = value.split(',');
        if (parts.length == 1) {
          final all = double.tryParse(parts[0]) ?? 16.0;
          return EdgeInsets.all(all);
        } else if (parts.length == 2) {
          final vertical = double.tryParse(parts[0]) ?? 16.0;
          final horizontal = double.tryParse(parts[1]) ?? 16.0;
          return EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal);
        } else if (parts.length == 4) {
          final top = double.tryParse(parts[0]) ?? 16.0;
          final right = double.tryParse(parts[1]) ?? 16.0;
          final bottom = double.tryParse(parts[2]) ?? 16.0;
          final left = double.tryParse(parts[3]) ?? 16.0;
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (value is double) {
        return EdgeInsets.all(value);
      } else if (value is int) {
        return EdgeInsets.all(value.toDouble());
      } else if (value is Map) {
        final top = (value['top'] as num?)?.toDouble() ?? 0.0;
        final right = (value['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (value['bottom'] as num?)?.toDouble() ?? 0.0;
        final left = (value['left'] as num?)?.toDouble() ?? 0.0;

        if (value.containsKey('all')) {
          final all = (value['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
          final horizontal = (value['horizontal'] as num?)?.toDouble() ?? 0.0;
          final vertical = (value['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      }
      return const EdgeInsets.all(16.0);
    }

    // Helper function to get Alignment from string
    Alignment getAlignmentFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'topleft':
          case 'top_left': return Alignment.topLeft;
          case 'topcenter':
          case 'top_center':
          case 'top': return Alignment.topCenter;
          case 'topright':
          case 'top_right': return Alignment.topRight;
          case 'centerleft':
          case 'center_left':
          case 'left': return Alignment.centerLeft;
          case 'center': return Alignment.center;
          case 'centerright':
          case 'center_right':
          case 'right': return Alignment.centerRight;
          case 'bottomleft':
          case 'bottom_left': return Alignment.bottomLeft;
          case 'bottomcenter':
          case 'bottom_center':
          case 'bottom': return Alignment.bottomCenter;
          case 'bottomright':
          case 'bottom_right': return Alignment.bottomRight;
          default: return Alignment.center;
        }
      }
      return Alignment.center;
    }

    // Extract properties from JSON
    return CardWidget(
      content: json['content'] as Widget?,
      title: json['title'] as String?,
      subtitle: json['subtitle'] as String?,
      icon: getIconFromValue(json['icon']),
      imageUrl: json['imageUrl'] as String?,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 150.0,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 300.0,
      imageFit: json.containsKey('imageFit') ? getBoxFitFromValue(json['imageFit']) : BoxFit.cover,
      imageAsHeader: json['imageAsHeader'] as bool? ?? false,
      imageAsBackground: json['imageAsBackground'] as bool? ?? false,
      actions: json['actions'] as List<Widget>?,
      backgroundColor: json.containsKey('backgroundColor') ? getColorFromValue(json['backgroundColor']) : Colors.white,
      textColor: json.containsKey('textColor') ? getColorFromValue(json['textColor']) : Colors.black87,
      borderColor: json.containsKey('borderColor') ? getColorFromValue(json['borderColor']) : Colors.grey,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 8.0,
      hasBorder: json['hasBorder'] as bool? ?? false,
      hasShadow: json['hasShadow'] as bool? ?? true,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      padding: json.containsKey('padding') ? getEdgeInsetsFromValue(json['padding']) : const EdgeInsets.all(16.0),
      margin: json.containsKey('margin') ? getEdgeInsetsFromValue(json['margin']) : const EdgeInsets.all(8.0),
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      isClickable: json['isClickable'] as bool? ?? false,
      onTap: json['onTap'] as VoidCallback?,
      showDivider: json['showDivider'] as bool? ?? true,
      dividerColor: json.containsKey('dividerColor') ? getColorFromValue(json['dividerColor']) : Colors.grey,
      isLoading: json['isLoading'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      isSelected: json['isSelected'] as bool? ?? false,
      selectedColor: json.containsKey('selectedColor') ? getColorFromValue(json['selectedColor']) : Colors.blue,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      titleFontSize: (json['titleFontSize'] as num?)?.toDouble() ?? 18.0,
      subtitleFontSize: (json['subtitleFontSize'] as num?)?.toDouble() ?? 14.0,
      contentFontSize: (json['contentFontSize'] as num?)?.toDouble() ?? 14.0,
      titleFontWeight: json.containsKey('titleFontWeight') ? getFontWeightFromValue(json['titleFontWeight']) : FontWeight.bold,
      subtitleFontWeight: json.containsKey('subtitleFontWeight') ? getFontWeightFromValue(json['subtitleFontWeight']) : FontWeight.normal,
      contentFontWeight: json.containsKey('contentFontWeight') ? getFontWeightFromValue(json['contentFontWeight']) : FontWeight.normal,
      iconColor: json.containsKey('iconColor') ? getColorFromValue(json['iconColor']) : Colors.blue,
      iconSize: (json['iconSize'] as num?)?.toDouble() ?? 24.0,
      showHeader: json['showHeader'] as bool? ?? true,
      showFooter: json['showFooter'] as bool? ?? true,
      contentAlignment: json.containsKey('contentAlignment') ? getAlignmentFromValue(json['contentAlignment']) : Alignment.center,
      isExpandable: json['isExpandable'] as bool? ?? false,
      initiallyExpanded: json['initiallyExpanded'] as bool? ?? true,
      expandButtonText: json['expandButtonText'] as String?,
      collapseButtonText: json['collapseButtonText'] as String?,
    );
  }

  /// Converts the CardWidget to a JSON map.
  ///
  /// This method allows serializing the widget's configuration to JSON,
  /// making it easy to save and restore widget states.
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subtitle': subtitle,
      'imageUrl': imageUrl,
      'imageHeight': imageHeight,
      'imageWidth': imageWidth,
      'imageFit': imageFit.toString().split('.').last,
      'imageAsHeader': imageAsHeader,
      'imageAsBackground': imageAsBackground,
      'backgroundColor': backgroundColor.toString(),
      'textColor': textColor.toString(),
      'borderColor': borderColor.toString(),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'padding': {
        'all': padding is EdgeInsets ? (padding as EdgeInsets).top : null,
      },
      'margin': {
        'all': margin is EdgeInsets ? (margin as EdgeInsets).top : null,
      },
      'width': width,
      'height': height,
      'isClickable': isClickable,
      'showDivider': showDivider,
      'dividerColor': dividerColor.toString(),
      'isLoading': isLoading,
      'isDisabled': isDisabled,
      'isSelected': isSelected,
      'selectedColor': selectedColor.toString(),
      'isDarkTheme': isDarkTheme,
      'titleFontSize': titleFontSize,
      'subtitleFontSize': subtitleFontSize,
      'contentFontSize': contentFontSize,
      'iconColor': iconColor.toString(),
      'iconSize': iconSize,
      'showHeader': showHeader,
      'showFooter': showFooter,
      'contentAlignment': contentAlignment.toString(),
      'isExpandable': isExpandable,
      'initiallyExpanded': initiallyExpanded,
      'expandButtonText': expandButtonText,
      'collapseButtonText': collapseButtonText,
    };
  }
}

class _CardWidgetState extends State<CardWidget> with SingleTickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Determine the effective background color based on theme and selection state
    Color effectiveBackgroundColor = widget.backgroundColor;
    if (widget.isDarkTheme) {
      effectiveBackgroundColor = Colors.grey.shade800;
    }
    if (widget.isSelected) {
      effectiveBackgroundColor = widget.selectedColor.withAlpha(25); // 0.1 * 255 = 25
    }

    // Build the card content
    Widget cardContent = _buildCardContent();

    // Apply loading state if needed
    if (widget.isLoading) {
      cardContent = Stack(
        children: [
          cardContent,
          Positioned.fill(
            child: Container(
              color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        ],
      );
    }

    // Prepare the card content
    Widget contentWidget;

    // If we have a background image, wrap the content in a Stack
    if (widget.imageUrl != null && widget.imageAsBackground) {
      contentWidget = Stack(
        children: [
          // Background image
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(widget.borderRadius - widget.borderWidth),
              child: Image.network(
                widget.imageUrl!,
                fit: widget.imageFit,
                errorBuilder: (context, error, stackTrace) {
                  return Container(color: Colors.grey.shade200);
                },
              ),
            ),
          ),
          // Semi-transparent overlay for better text readability
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(180),
                borderRadius: BorderRadius.circular(widget.borderRadius - widget.borderWidth),
              ),
            ),
          ),
          // Actual content
          Container(
            width: widget.width,
            height: widget.height,
            padding: widget.padding,
            child: cardContent,
          ),
        ],
      );
    } else {
      // Regular content without background image
      contentWidget = Container(
        width: widget.width,
        height: widget.height,
        padding: widget.padding,
        child: cardContent,
      );
    }

    // Build the card
    Widget card = Card(
      color: effectiveBackgroundColor,
      elevation: widget.hasShadow ? widget.elevation : 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        side: widget.hasBorder
            ? BorderSide(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : BorderSide.none,
      ),
      margin: widget.margin,
      child: contentWidget,
    );

    // Make the card clickable if needed
    if (widget.isClickable && !widget.isDisabled) {
      card = InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: card,
      );
    }

    // Apply disabled state if needed
    if (widget.isDisabled) {
      card = Opacity(
        opacity: 0.5,
        child: card,
      );
    }

    return card;
  }

  Widget _buildCardContent() {
    List<Widget> children = [];

    // Add header if needed
    if (widget.showHeader && (widget.title != null || widget.icon != null)) {
      children.add(
        Row(
          children: [
            if (widget.icon != null) ...[
              Icon(
                widget.icon,
                color: widget.iconColor,
                size: widget.iconSize,
              ),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.title != null)
                    Text(
                      widget.title!,
                      style: TextStyle(
                        fontSize: widget.titleFontSize,
                        fontWeight: widget.titleFontWeight,
                        color: widget.textColor,
                      ),
                    ),
                  if (widget.subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      widget.subtitle!,
                      style: TextStyle(
                        fontSize: widget.subtitleFontSize,
                        fontWeight: widget.subtitleFontWeight,
                        color: widget.textColor.withAlpha(179), // 0.7 * 255 = 179
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (widget.isExpandable)
              IconButton(
                icon: Icon(
                  _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: widget.iconColor,
                ),
                onPressed: _toggleExpanded,
              ),
          ],
        ),
      );

      // Add divider if needed
      if (widget.showDivider) {
        children.add(
          Divider(
            color: widget.dividerColor,
            height: 16,
          ),
        );
      }
    }

    // Add image if provided
    if (widget.imageUrl != null) {
      // Create the image widget
      Widget imageWidget = ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius - 4),
        child: Image.network(
          widget.imageUrl!,
          width: widget.imageAsHeader ? double.infinity : widget.imageWidth,
          height: widget.imageHeight,
          fit: widget.imageFit,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              height: widget.imageHeight,
              width: widget.imageWidth,
              color: Colors.grey.shade300,
              child: Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.grey.shade700,
                ),
              ),
            );
          },
        ),
      );

      // If image is used as header, add it at the beginning
      if (widget.imageAsHeader) {
        children.insert(0, imageWidget);
        children.insert(1, const SizedBox(height: 8));
      }
      // If image is not used as header or background, add it after the header
      else if (!widget.imageAsBackground) {
        children.add(imageWidget);
        children.add(const SizedBox(height: 8));
      }
      // If image is used as background, it will be handled in the build method
    }

    // Add content if provided
    if (widget.content != null) {
      if (widget.isExpandable) {
        children.add(
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: widget.content!,
          ),
        );
      } else {
        children.add(widget.content!);
      }
    }

    // Add footer actions if provided
    if (widget.showFooter && widget.actions != null && widget.actions!.isNotEmpty) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: widget.actions!,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
