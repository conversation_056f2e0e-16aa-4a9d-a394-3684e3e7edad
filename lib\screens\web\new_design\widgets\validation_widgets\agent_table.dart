import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/models/role_info.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/build_role_card.dart';
import 'package:nsl/utils/constants.dart';
import 'package:nsl/utils/font_manager.dart';

/// A reusable agent table widget that displays agent data in a table format.
///
/// This widget shows agent information extracted from validation results
/// and provides interaction capabilities similar to the role cards.
class AgentTable extends StatefulWidget {
  /// The manual creation provider containing agent data
  final ManualCreationProvider provider;

  /// Callback when an agent/role is selected
  final Function(RoleInfo)? onRoleSelected;

  const AgentTable({
    super.key,
    required this.provider,
    this.onRoleSelected,
  });

  @override
  State<AgentTable> createState() => _AgentTableState();
}

class _AgentTableState extends State<AgentTable> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;
  String _selectedOption = 'EN100003';

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isDropdownOpen = true;
    });
  }

  void _closeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isDropdownOpen = false;
    });
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: () => _closeDropdown(),
        behavior: HitTestBehavior.translucent,
        child: Stack(
          children: [
            // Invisible full-screen overlay to detect outside clicks
            Positioned.fill(
              child: Container(
                color: Colors.transparent,
              ),
            ),
            // Dropdown content
            Positioned(
              right: 20,
              top: offset.dy + renderBox.size.height + 5,
              child: GestureDetector(
                onTap: () {}, // Prevent closing when clicking inside dropdown
                child: CompositedTransformFollower(
                  link: _layerLink,
                  showWhenUnlinked: false,
                  offset: Offset(0.0, 0.0),
                  child: Material(
                    elevation: 4.0,
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                    child: Container(
                      width: 120,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children:
                            ['EN100003', 'EN100004', 'EN100005'].map((option) {
                          return InkWell(
                            onTap: () {
                              setState(() {
                                _selectedOption = option;
                              });
                              _toggleDropdown();
                            },
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                              child: Text(
                                option,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontFamily: 'TiemposText',
                                  color: Colors.grey[800],
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _overlayEntry?.remove();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.provider.extractedAgentData == null ||
        widget.provider.extractedAgentData!.agents == null ||
        widget.provider.extractedAgentData!.agents!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No agent data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      padding: EdgeInsets.all(AppSpacing.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
                vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xffD0D0D0)),
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Check if we're on a mobile screen
                final isMobile = MediaQuery.of(context).size.width <
                    AppConstants.mobileBreakpoint;

                if (isMobile) {
                  // Mobile layout: Column
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title row
                      Text(
                        'Roles: There are ${widget.provider.extractedAgentData!.agents!.length} roles',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: AppSpacing.xs),
                      // Controls row
                      Row(
                        children: [
                          // Dropdown with folder icon
                          CompositedTransformTarget(
                            link: _layerLink,
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () {
                                  _toggleDropdown();
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 4),
                                  decoration: BoxDecoration(
                                    border: _isDropdownOpen
                                        ? Border.all(
                                            color: Colors.grey.shade300)
                                        : Border.all(color: Colors.transparent),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.folder_outlined,
                                          size: 14, color: Colors.black),
                                      Icon(
                                        _isDropdownOpen
                                            ? Icons.keyboard_arrow_up
                                            : Icons.keyboard_arrow_down,
                                        size: 14,
                                        color: Colors.black,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        _selectedOption,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontFamily: 'TiemposText',
                                          color: Colors.grey[800],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Spacer(),
                          // Agent count button
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: AppSpacing.xs,
                                vertical: AppSpacing.xxs),
                            decoration: BoxDecoration(
                              color: Color(0xffFFE5B4),
                              borderRadius:
                                  BorderRadius.circular(AppSpacing.xxs),
                            ),
                            child: Text(
                              '${widget.provider.extractedAgentData!.agents!.length} Agent${widget.provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'TiemposText',
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                } else {
                  // Desktop/Tablet layout: Row (original layout)
                  return Row(
                    children: [
                      Text(
                        'Roles: There are ${widget.provider.extractedAgentData!.agents!.length} roles',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                        // style: TextStyle(
                        //   fontSize: 16,
                        //   fontWeight: FontWeight.w600,
                        //   fontFamily: 'TiemposText',
                        //   color: Colors.black,
                        // ),
                      ),
                      Spacer(),
                      // Dropdown with folder icon (separate from agent count)
                      CompositedTransformTarget(
                        link: _layerLink,
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              _toggleDropdown();
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 4),
                              decoration: BoxDecoration(
                                border: _isDropdownOpen
                                    ? Border.all(color: Colors.grey.shade300)
                                    : Border.all(color: Colors.transparent),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.folder_outlined,
                                      size: 14, color: Colors.black),
                                  Icon(
                                    _isDropdownOpen
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    size: 14,
                                    color: Colors.black,
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    _selectedOption,
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontFamily: 'TiemposText',
                                      color: Colors.grey[800],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      // Separate Agent count button
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.xs,
                            vertical: AppSpacing.xxs),
                        decoration: BoxDecoration(
                          color: Color(0xffFFE5B4),
                          borderRadius: BorderRadius.circular(AppSpacing.xxs),
                        ),
                        child: Text(
                          '${widget.provider.extractedAgentData!.agents!.length} Agent${widget.provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'TiemposText',
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  );
                }
              },
            ),
          ),

          // Agent rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: widget.provider.extractedAgentData!.agents!.length,
              itemBuilder: (context, index) {
                final agent =
                    widget.provider.extractedAgentData!.agents![index];

                // Convert AgentInfo to RoleInfo for BuildRoleCard
                final role = RoleInfo(
                  id: agent.id,
                  title: agent.title,
                  description: agent.description,
                  version: agent.version,
                  createdBy: agent.createdBy,
                  createdDate: _formatDate(agent.createdDate ?? DateTime.now()),
                  modifiedBy: agent.modifiedBy,
                  modifiedDate:
                      _formatDate(agent.modifiedDate ?? DateTime.now()),
                  // Extract use cases from agent sections
                  coreResponsibilities: agent.sections
                      .where((section) => section.title
                          .toLowerCase()
                          .contains('responsibilities'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                  // Extract permissions from agent sections
                  kpis: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('performance'))
                      .expand((section) => section.items)
                      .toList(),
                  decisionAuthority: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('authority'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                );

                return Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      right: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      bottom: BorderSide(color: Color(0xffD0D0D0), width: 1),
                    ),
                  ),
                  child: BuildRoleCard(
                    role: role,
                    isSelected: widget.provider.selectedRole?.id == role.id,
                    isBorderRequired: false,
                    isHoverCardRequired: true,
                    selectedColor: Color(0xffF2F2F2),
                    onRoleTap: (selectedRole) {
                      widget.onRoleSelected?.call(selectedRole);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
