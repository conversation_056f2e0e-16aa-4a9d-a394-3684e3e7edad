import 'package:flutter/material.dart';
import '../models/workflow.dart';
import '../utils/widget_factory.dart';

/// A responsive form layout that arranges form fields based on their UI control types
/// This layout automatically assigns flex values to form fields based on their UI control type
class ResponsiveFormLayout extends StatelessWidget {
  /// The list of input fields to display in the form
  final List<InputField> fields;
  
  /// The section ID for the form
  final String sectionId;
  
  /// Whether the form is read-only
  final bool readOnly;
  
  /// Callback for when a field value changes
  final Function(String, String, dynamic, String) onValueChanged;
  
  /// The number of columns to use in the grid
  /// Default is 12 (standard grid system)
  final int gridColumns;
  
  /// The spacing between form fields horizontally
  final double horizontalSpacing;
  
  /// The spacing between form fields vertically
  final double verticalSpacing;
  
  /// The padding around the form
  final EdgeInsetsGeometry padding;
  
  /// Constructor for the responsive form layout
  const ResponsiveFormLayout({
    super.key,
    required this.fields,
    required this.sectionId,
    required this.onValueChanged,
    this.readOnly = false,
    this.gridColumns = 12,
    this.horizontalSpacing = 16.0,
    this.verticalSpacing = 16.0,
    this.padding = const EdgeInsets.all(16.0),
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return _buildResponsiveLayout(context, constraints);
        },
      ),
    );
  }
  
  /// Builds the responsive layout based on the available width
  Widget _buildResponsiveLayout(BuildContext context, BoxConstraints constraints) {
    // Calculate the width of a single column
    final double columnWidth = constraints.maxWidth / gridColumns;
    
    // Group fields into rows based on their flex values
    final List<List<InputField>> rows = _groupFieldsIntoRows();
    
    // Build the rows
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _buildRows(context, rows, columnWidth),
    );
  }
  
  /// Groups fields into rows based on their flex values
  /// This ensures that fields are arranged optimally based on their UI control type
  List<List<InputField>> _groupFieldsIntoRows() {
    final List<List<InputField>> rows = [];
    List<InputField> currentRow = [];
    int currentRowFlex = 0;
    
    for (final field in fields) {
      // Get the flex value for this field's UI control type
      final fieldFlex = WidgetFactory.getFlexValueForControl(field.uiControl);
      
      // If adding this field would exceed the grid columns, start a new row
      if (currentRowFlex + fieldFlex > gridColumns && currentRow.isNotEmpty) {
        rows.add(List.from(currentRow));
        currentRow = [];
        currentRowFlex = 0;
      }
      
      // Add the field to the current row
      currentRow.add(field);
      currentRowFlex += fieldFlex;
      
      // If we've exactly filled a row, start a new one
      if (currentRowFlex == gridColumns) {
        rows.add(List.from(currentRow));
        currentRow = [];
        currentRowFlex = 0;
      }
    }
    
    // Add any remaining fields in the last row
    if (currentRow.isNotEmpty) {
      rows.add(currentRow);
    }
    
    return rows;
  }
  
  /// Builds the rows of form fields
  List<Widget> _buildRows(BuildContext context, List<List<InputField>> rows, double columnWidth) {
    final List<Widget> rowWidgets = [];
    
    for (int i = 0; i < rows.length; i++) {
      // Build the row
      rowWidgets.add(_buildRow(context, rows[i]));
      
      // Add spacing between rows (except after the last row)
      if (i < rows.length - 1) {
        rowWidgets.add(SizedBox(height: verticalSpacing));
      }
    }
    
    return rowWidgets;
  }
  
  /// Builds a single row of form fields
  Widget _buildRow(BuildContext context, List<InputField> rowFields) {
    final List<Widget> rowChildren = [];
    
    for (int i = 0; i < rowFields.length; i++) {
      final field = rowFields[i];
      
      // Create the field widget
      final fieldWidget = WidgetFactory.createInputWidget(
        context: context,
        field: field,
        key: GlobalKey(),
        sectionId: sectionId,
        readOnly: readOnly,
        onValueChanged: onValueChanged,
      );
      
      // Wrap the field widget with appropriate flex value
      rowChildren.add(
        WidgetFactory.createFlexibleWidget(
          child: fieldWidget,
          uiControl: field.uiControl,
        ),
      );
      
      // Add spacing between fields (except after the last field)
      if (i < rowFields.length - 1) {
        rowChildren.add(SizedBox(width: horizontalSpacing));
      }
    }
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rowChildren,
    );
  }
}
