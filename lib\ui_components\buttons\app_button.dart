import 'package:flutter/material.dart';

/// Constants for the app theme
class AppThemeConstants {
  // Private constructor to prevent instantiation
  AppThemeConstants._();

  // Colors
  static const Color primaryColor = Color(0xFF0058FF);
  static const Color textLightColor = Colors.white;
  static const Color textSecondaryColor = Color(0xFF757575);

  // Border radius
  static const double borderRadiusS = 4.0;
  static const double borderRadiusM = 8.0;
  static const double borderRadiusL = 12.0;
  static const double borderRadiusCircular = 24.0;

  // Spacing
  static const double spacingXs = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXl = 32.0;

  // Button padding
  static const EdgeInsets buttonPaddingS = EdgeInsets.symmetric(
    horizontal: spacingM,
    vertical: spacingXs,
  );
  static const EdgeInsets buttonPaddingM = EdgeInsets.symmetric(
    horizontal: spacingL,
    vertical: spacingS,
  );
  static const EdgeInsets buttonPaddingL = EdgeInsets.symmetric(
    horizontal: spacingXl,
    vertical: spacingM,
  );

  // Icon sizes
  static const double iconSizeS = 16.0;
  static const double iconSizeM = 20.0;
  static const double iconSizeL = 24.0;

  // Text styles
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12.0,
    fontWeight: FontWeight.normal,
  );
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.normal,
  );
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16.0,
    fontWeight: FontWeight.normal,
  );
}

/// Button variants available in the app
enum AppButtonVariant {
  primary,
  secondary,
  text,
  icon,
}

/// Button sizes available in the app
enum AppButtonSize {
  small,
  medium,
  large,
}

/// A customizable button component that follows the app's design system
class AppButton extends StatelessWidget {
  final String? text;
  final IconData? icon;
  final VoidCallback? onPressed;
  final AppButtonVariant variant;
  final AppButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final EdgeInsets? padding;
  final double? borderRadius;

  const AppButton({
    super.key,
    this.text,
    this.icon,
    this.onPressed,
    this.variant = AppButtonVariant.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.padding,
    this.borderRadius,
  }) : assert(text != null || icon != null,
            'Either text or icon must be provided');

  @override
  Widget build(BuildContext context) {
    // Determine button style based on variant
    ButtonStyle buttonStyle;
    switch (variant) {
      case AppButtonVariant.primary:
        buttonStyle = ElevatedButton.styleFrom(
          backgroundColor: AppThemeConstants.primaryColor,
          foregroundColor: AppThemeConstants.textLightColor,
          disabledBackgroundColor:
              AppThemeConstants.primaryColor.withAlpha(128),
          disabledForegroundColor:
              AppThemeConstants.textLightColor.withAlpha(179),
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                borderRadius ?? AppThemeConstants.borderRadiusM),
          ),
        );
        break;
      case AppButtonVariant.secondary:
        buttonStyle = OutlinedButton.styleFrom(
          foregroundColor: AppThemeConstants.primaryColor,
          disabledForegroundColor:
              AppThemeConstants.primaryColor.withAlpha(128),
          side: BorderSide(
            color: onPressed != null
                ? AppThemeConstants.primaryColor
                : AppThemeConstants.primaryColor.withAlpha(128),
          ),
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                borderRadius ?? AppThemeConstants.borderRadiusM),
          ),
        );
        break;
      case AppButtonVariant.text:
        buttonStyle = TextButton.styleFrom(
          foregroundColor: AppThemeConstants.primaryColor,
          disabledForegroundColor:
              AppThemeConstants.primaryColor.withAlpha(128),
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                borderRadius ?? AppThemeConstants.borderRadiusM),
          ),
        );
        break;
      case AppButtonVariant.icon:
        buttonStyle = IconButton.styleFrom(
          foregroundColor: AppThemeConstants.primaryColor,
          disabledForegroundColor:
              AppThemeConstants.primaryColor.withAlpha(128),
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                borderRadius ?? AppThemeConstants.borderRadiusCircular),
          ),
        );
        break;
    }

    // Build the button content
    Widget buttonContent = _buildButtonContent(context);

    // Apply full width if needed
    if (isFullWidth) {
      buttonContent = SizedBox(
        width: double.infinity,
        child: buttonContent,
      );
    }

    // Return the appropriate button type based on variant
    switch (variant) {
      case AppButtonVariant.primary:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonContent,
        );
      case AppButtonVariant.secondary:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonContent,
        );
      case AppButtonVariant.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonContent,
        );
      case AppButtonVariant.icon:
        return IconButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          icon: buttonContent,
        );
    }
  }

  Widget _buildButtonContent(BuildContext context) {
    // Get the current theme brightness
    final brightness = MediaQuery.of(context).platformBrightness;
    final iconColor =
        brightness == Brightness.light ? Colors.white : Colors.black;

    if (isLoading) {
      return SizedBox(
        width: _getLoadingSize(),
        height: _getLoadingSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(
            variant == AppButtonVariant.primary
                ? AppThemeConstants.textLightColor
                : AppThemeConstants.primaryColor,
          ),
        ),
      );
    }

    if (variant == AppButtonVariant.icon) {
      return Icon(icon, size: _getIconSize(), color: iconColor);
    }

    if (text != null && icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: _getIconSize(),
            color: variant == AppButtonVariant.primary ? iconColor : null,
          ),
          SizedBox(width: AppThemeConstants.spacingS),
          Text(
            text!,
            style: _getTextStyle(),
          ),
        ],
      );
    }

    if (icon != null) {
      return Icon(
        icon,
        size: _getIconSize(),
        color: variant == AppButtonVariant.primary ? iconColor : null,
      );
    }

    return Text(
      text!,
      style: _getTextStyle(),
    );
  }

  EdgeInsets _getPadding() {
    if (padding != null) return padding!;

    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppThemeConstants.spacingM,
          vertical: AppThemeConstants.spacingXs,
        );
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppThemeConstants.spacingL,
          vertical: AppThemeConstants.spacingS,
        );
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppThemeConstants.spacingXl,
          vertical: AppThemeConstants.spacingM,
        );
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case AppButtonSize.small:
        return AppThemeConstants.bodySmall.copyWith(
          fontWeight: FontWeight.w500,
          color: variant == AppButtonVariant.primary
              ? AppThemeConstants.textLightColor
              : AppThemeConstants.primaryColor,
        );
      case AppButtonSize.medium:
        return AppThemeConstants.bodyMedium.copyWith(
          fontWeight: FontWeight.w500,
          color: variant == AppButtonVariant.primary
              ? AppThemeConstants.textLightColor
              : AppThemeConstants.primaryColor,
        );
      case AppButtonSize.large:
        return AppThemeConstants.bodyLarge.copyWith(
          fontWeight: FontWeight.w500,
          color: variant == AppButtonVariant.primary
              ? AppThemeConstants.textLightColor
              : AppThemeConstants.primaryColor,
        );
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return 16.0;
      case AppButtonSize.medium:
        return 20.0;
      case AppButtonSize.large:
        return 24.0;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case AppButtonSize.small:
        return 14.0;
      case AppButtonSize.medium:
        return 18.0;
      case AppButtonSize.large:
        return 22.0;
    }
  }
}
