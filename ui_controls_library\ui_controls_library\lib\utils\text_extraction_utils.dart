import 'package:flutter/material.dart';

/// Extracts a double value from a string based on a key
/// 
/// Example: extractDouble("size=10.5", "size") returns 10.5
/// Example: extractDouble("width 20", "width") returns 20.0
double? extractDouble(String text, String key) {
  // Pattern 1: key=value
  final regex1 = RegExp('$key\\s*=\\s*([0-9.]+)');
  final match1 = regex1.firstMatch(text);
  if (match1 != null) {
    return double.tryParse(match1.group(1)!);
  }
  
  // Pattern 2: key value
  final regex2 = RegExp('$key\\s+([0-9.]+)');
  final match2 = regex2.firstMatch(text);
  if (match2 != null) {
    return double.tryParse(match2.group(1)!);
  }
  
  // Pattern 3: key: value
  final regex3 = RegExp('$key\\s*:\\s*([0-9.]+)');
  final match3 = regex3.firstMatch(text);
  if (match3 != null) {
    return double.tryParse(match3.group(1)!);
  }
  
  return null;
}

/// Extracts an integer value from a string based on a key
/// 
/// Example: extractInt("count=10", "count") returns 10
/// Example: extractInt("items 20", "items") returns 20
int? extractInt(String text, String key) {
  final doubleValue = extractDouble(text, key);
  if (doubleValue != null) {
    return doubleValue.toInt();
  }
  return null;
}

/// Extracts a boolean value from a string based on a key
/// 
/// Example: extractBool("enabled=true", "enabled") returns true
/// Example: extractBool("visible false", "visible") returns false
bool? extractBool(String text, String key) {
  // Pattern 1: key=value
  final regex1 = RegExp('$key\\s*=\\s*(true|false)', caseSensitive: false);
  final match1 = regex1.firstMatch(text);
  if (match1 != null) {
    return match1.group(1)!.toLowerCase() == 'true';
  }
  
  // Pattern 2: key value
  final regex2 = RegExp('$key\\s+(true|false)', caseSensitive: false);
  final match2 = regex2.firstMatch(text);
  if (match2 != null) {
    return match2.group(1)!.toLowerCase() == 'true';
  }
  
  // Pattern 3: key: value
  final regex3 = RegExp('$key\\s*:\\s*(true|false)', caseSensitive: false);
  final match3 = regex3.firstMatch(text);
  if (match3 != null) {
    return match3.group(1)!.toLowerCase() == 'true';
  }
  
  // Check for presence of key alone (implies true)
  if (RegExp('\\b$key\\b', caseSensitive: false).hasMatch(text)) {
    return true;
  }
  
  // Check for "no key" or "not key" (implies false)
  if (RegExp('\\bno\\s+$key\\b|\\bnot\\s+$key\\b', caseSensitive: false).hasMatch(text)) {
    return false;
  }
  
  return null;
}

/// Extracts a string value from a string based on a key
/// 
/// Example: extractString('label="Hello"', "label") returns "Hello"
/// Example: extractString("title World", "title") returns "World"
String? extractString(String text, String key) {
  // Pattern 1: key="value" (double quotes)
  final regex1 = RegExp('$key\\s*=\\s*"([^"]*)"');
  final match1 = regex1.firstMatch(text);
  if (match1 != null) {
    return match1.group(1);
  }
  
  // Pattern 2: key='value' (single quotes)
  final regex2 = RegExp("$key\\s*=\\s*'([^']*)'");
  final match2 = regex2.firstMatch(text);
  if (match2 != null) {
    return match2.group(1);
  }
  
  // Pattern 3: key value (single word)
  final regex3 = RegExp('$key\\s+([^\\s]+)');
  final match3 = regex3.firstMatch(text);
  if (match3 != null) {
    return match3.group(1);
  }
  
  // Pattern 4: key: value (single word)
  final regex4 = RegExp('$key\\s*:\\s*([^\\s]+)');
  final match4 = regex4.firstMatch(text);
  if (match4 != null) {
    return match4.group(1);
  }
  
  return null;
}

/// Extracts a color from a string based on a key
/// 
/// Example: extractColor("color=red", "color") returns Colors.red
/// Example: extractColor("background #FF0000", "background") returns Color(0xFFFF0000)
Color? extractColor(String text, String key) {
  // First try to extract the color string
  String? colorStr;
  
  // Pattern 1: key=value
  final regex1 = RegExp('$key\\s*=\\s*([^\\s,]+)');
  final match1 = regex1.firstMatch(text);
  if (match1 != null) {
    colorStr = match1.group(1);
  }
  
  // Pattern 2: key value
  if (colorStr == null) {
    final regex2 = RegExp('$key\\s+([^\\s,]+)');
    final match2 = regex2.firstMatch(text);
    if (match2 != null) {
      colorStr = match2.group(1);
    }
  }
  
  // Pattern 3: key: value
  if (colorStr == null) {
    final regex3 = RegExp('$key\\s*:\\s*([^\\s,]+)');
    final match3 = regex3.firstMatch(text);
    if (match3 != null) {
      colorStr = match3.group(1);
    }
  }
  
  // Pattern 4: colorName key (e.g., "red button")
  if (colorStr == null) {
    final colorNames = [
      'red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink', 'brown',
      'grey', 'gray', 'black', 'white', 'cyan', 'magenta', 'lime', 'indigo',
      'teal', 'amber'
    ];
    
    for (final color in colorNames) {
      if (RegExp('\\b$color\\s+$key\\b', caseSensitive: false).hasMatch(text)) {
        colorStr = color;
        break;
      }
    }
  }
  
  // If we found a color string, convert it to a Color
  if (colorStr != null) {
    return parseColorFromString(colorStr);
  }
  
  return null;
}

/// Parses a color from a string
/// 
/// Example: parseColorFromString("red") returns Colors.red
/// Example: parseColorFromString("#FF0000") returns Color(0xFFFF0000)
Color parseColorFromString(String colorStr) {
  // Handle hex colors
  if (colorStr.startsWith('#')) {
    String hex = colorStr.substring(1);
    
    // Handle shorthand hex like #RGB
    if (hex.length == 3) {
      hex = hex.split('').map((c) => '$c$c').join('');
    }
    
    // Add alpha channel if missing
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    
    // Parse the hex value
    try {
      return Color(int.parse('0x$hex'));
    } catch (e) {
      // Fallback to a default color
      return Colors.blue;
    }
  }
  
  // Handle named colors
  switch (colorStr.toLowerCase()) {
    case 'red': return Colors.red;
    case 'green': return Colors.green;
    case 'blue': return Colors.blue;
    case 'yellow': return Colors.yellow;
    case 'orange': return Colors.orange;
    case 'purple': return Colors.purple;
    case 'pink': return Colors.pink;
    case 'brown': return Colors.brown;
    case 'grey':
    case 'gray': return Colors.grey;
    case 'black': return Colors.black;
    case 'white': return Colors.white;
    case 'cyan': return Colors.cyan;
    case 'magenta': return Colors.pink;
    case 'lime': return Colors.lime;
    case 'indigo': return Colors.indigo;
    case 'teal': return Colors.teal;
    case 'amber': return Colors.amber;
    default: return Colors.blue; // Default color
  }
}
