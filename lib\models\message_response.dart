// To parse this JSON data, do
//
//     final messageResponse = messageResponseFrom<PERSON>son(jsonString);

import 'dart:convert';

MessageResponse messageResponseFromJson(String str) => MessageResponse.fromJson(json.decode(str));

String messageResponseTo<PERSON>son(MessageResponse data) => json.encode(data.toJson());

class MessageResponse {
    String answer;
    String conversationId;
    String mode;
    double processingTime;
    double processingTimeSeconds;
    List<Reasoning> reasoning;
    List<dynamic> subQuestions;

    MessageResponse({
        required this.answer,
        required this.conversationId,
        required this.mode,
        required this.processingTime,
        required this.processingTimeSeconds,
        required this.reasoning,
        required this.subQuestions,
    });

    factory MessageResponse.fromJson(Map<String, dynamic> json) => MessageResponse(
        answer: json["answer"],
        conversationId: json["conversation_id"],
        mode: json["mode"],
        processingTime: json["processing_time"]?.toDouble(),
        processingTimeSeconds: json["processing_time_seconds"]?.toDouble(),
        reasoning: List<Reasoning>.from(json["reasoning"].map((x) => Reasoning.fromJson(x))),
        subQuestions: List<dynamic>.from(json["sub_questions"].map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "answer": answer,
        "conversation_id": conversationId,
        "mode": mode,
        "processing_time": processingTime,
        "processing_time_seconds": processingTimeSeconds,
        "reasoning": List<dynamic>.from(reasoning.map((x) => x.toJson())),
        "sub_questions": List<dynamic>.from(subQuestions.map((x) => x)),
    };
}

class Reasoning {
    String content;
    int stepNumber;

    Reasoning({
        required this.content,
        required this.stepNumber,
    });

    factory Reasoning.fromJson(Map<String, dynamic> json) => Reasoning(
        content: json["content"],
        stepNumber: json["step_number"],
    );

    Map<String, dynamic> toJson() => {
        "content": content,
        "step_number": stepNumber,
    };
}
