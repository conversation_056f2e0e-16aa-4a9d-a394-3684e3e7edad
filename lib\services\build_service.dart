import 'package:dio/dio.dart';
import '../utils/logger.dart';
import '../models/message.dart';
import '../models/project_details.dart';
import '../utils/environment.dart';

class BuildResponse {
  final String conversationId;
  final String yamlOutput;
  final String prescriptiveText;
  final String javaCode;
  final String validationStatus;
  final String? validationError;

  BuildResponse({
    required this.conversationId,
    required this.yamlOutput,
    required this.prescriptiveText,
    required this.javaCode,
    required this.validationStatus,
    this.validationError,
  });

  factory BuildResponse.fromJson(Map<String, dynamic> json) {
    return BuildResponse(
      conversationId: json['conversation_id'] ?? '',
      yamlOutput: json['yaml_output'] ?? '',
      prescriptiveText: json['prescriptive_text'] ?? '',
      javaCode: json['java_code'] ?? '',
      validationStatus: json['validation_status'] ?? '',
      validationError: json['validation_error'],
    );
  }
}

class BuildService {
  final Dio _dio = Dio();
  final Environment _env = Environment.instance;

  // Use environment for endpoint URLs
  String get buildChatUrl => _env.buildChatUrl;
  String get solutionsUrl => _env.solutionsUrl;
  String get deployUrl => _env.deployUrl;

  Future<List<Solution>> getSolutions() async {
    try {
      Logger.info('Fetching solutions from: $solutionsUrl');

      final options = Options(
        headers: {
          'Content-Type': 'application/json',
        },
      );

      final response = await _dio.get(
        solutionsUrl,
        options: options,
      );

      Logger.info('Solutions response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Solutions fetched successfully');
        final List<dynamic> solutionsJson = response.data;
        return solutionsJson.map((json) => Solution.fromJson(json)).toList();
      } else {
        Logger.error('Error fetching solutions: ${response.statusCode}');
        return [];
      }
    } on DioException catch (e) {
      Logger.error('Dio Exception while fetching solutions: ${e.message}');
      return [];
    } catch (e) {
      Logger.error('Exception while fetching solutions: $e');
      return [];
    }
  }

  Future<BuildResponse> sendMessage(String message,
      [ProjectDetails? projectDetails, CancelToken? cancelToken]) async {
    try {
      Logger.info('Sending request to: $buildChatUrl');

      final options = Options(
        headers: {
          'Content-Type': 'application/json',
        },
      );

      Map<String, dynamic> data = {
        'message': message,
      };

      // Add project details to the request if available
      if (projectDetails != null) {
        data['project_details'] = {
          'project_name': projectDetails.projectName,
          'description': projectDetails.description,
          'file_path': projectDetails.filePath,
        };
      }

      final response = await _dio.post(
        buildChatUrl,
        options: options,
        data: data,
        cancelToken: cancelToken, // Add this
      );

      Logger.info('Response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Response received successfully');
        return BuildResponse.fromJson(response.data);
      } else {
        Logger.error('Error: ${response.statusCode}');
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } on DioException catch (e) {
      Logger.error('Dio Exception: ${e.message}');
      throw Exception('Error sending message: ${e.message}');
    } catch (e) {
      Logger.error('Exception: $e');
      throw Exception('Error sending message: $e');
    }
  }

  Future<bool> deployToRuntime(String conversationId) async {
    try {
      Logger.info('Deploying to runtime with conversation ID: $conversationId');
      Logger.info('Deploy URL: $deployUrl');

      final options = Options(
        headers: {
          'Content-Type': 'application/json',
        },
      );

      final data = {
        'conversation_id': conversationId,
      };

      final response = await _dio.post(
        deployUrl,
        options: options,
        data: data,
      );

      Logger.info('Deploy response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Deployment successful');
        return true;
      } else {
        Logger.error('Deployment error: ${response.statusCode}');
        return false;
      }
    } on DioException catch (e) {
      Logger.error('Dio Exception during deployment: ${e.message}');
      return false;
    } catch (e) {
      Logger.error('Exception during deployment: $e');
      return false;
    }
  }
}

class Solution {
  final String conversationId;
  final String yamlVersion;
  final String yamlContent;
  final List<Message> chatHistory;
  final String prescriptiveText;
  final String javaCode;
  final ProjectDetails? projectDetails;

  Solution({
    required this.conversationId,
    required this.yamlVersion,
    required this.yamlContent,
    required this.chatHistory,
    required this.prescriptiveText,
    required this.javaCode,
    this.projectDetails,
  });

  factory Solution.fromJson(Map<String, dynamic> json) {
    final List<dynamic> chatHistoryJson = json['chat_history'] ?? [];
    final List<Message> chatHistory = chatHistoryJson.map((messageJson) {
      return Message(
        role: messageJson['role'] == 'user'
            ? MessageRole.user
            : MessageRole.assistant,
        content: messageJson['content'] ?? '',
        timestamp: DateTime.now(),
      );
    }).toList();

    // Parse project details if available
    ProjectDetails? projectDetails;
    if (json['project_details'] != null) {
      projectDetails = ProjectDetails.fromJson(json['project_details']);
    }

    return Solution(
      conversationId: json['conversation_id'] ?? '',
      yamlVersion: json['yaml_version'] ?? '',
      yamlContent: json['yaml_content'] ?? '',
      chatHistory: chatHistory,
      prescriptiveText: json['prescriptive_text'] ?? '',
      javaCode: json['java_code'] ?? '',
      projectDetails: projectDetails,
    );
  }
}
