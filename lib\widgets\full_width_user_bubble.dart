import 'package:flutter/material.dart';
import '../models/message.dart';
import '../services/speech_service.dart';
// import '../theme/app_colors.dart'; // Commented out as not currently needed
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';
import '../utils/logger.dart';

class FullWidthUserBubble extends StatefulWidget {
  final Message message;
  final VoidCallback? onLongPress;

  const FullWidthUserBubble({
    super.key,
    required this.message,
    this.onLongPress,
  });

  @override
  State<FullWidthUserBubble> createState() => _FullWidthUserBubbleState();
}

class _FullWidthUserBubbleState extends State<FullWidthUserBubble> {
  final SpeechService _speechService = SpeechService();
  bool _isSpeaking = false;

  @override
  void initState() {
    super.initState();
    _initializeSpeechService();
  }

  Future<void> _initializeSpeechService() async {
    await _speechService.initialize();
  }

  Future<void> _speakMessage() async {
    if (_isSpeaking) {
      await _speechService.stop();
      setState(() {
        _isSpeaking = false;
      });
    } else {
      // Check microphone permission first (needed for some devices)
      bool hasPermission = await _speechService.checkMicrophonePermission();
      if (!hasPermission) {
        // Request permission
        hasPermission = await _speechService.requestMicrophonePermission();
        if (!hasPermission) {
          // Show error message if permission denied
          _showPermissionDeniedDialog();
          return;
        }
      }

      setState(() {
        _isSpeaking = true;
      });
      Logger.info(
          'Speaking user message: ${widget.message.content.substring(0, widget.message.content.length > 50 ? 50 : widget.message.content.length)}...');
      await _speechService.speak(widget.message.content);
      setState(() {
        _isSpeaking = false;
      });
    }
  }

  // Method to handle long press from parent
  void _handleLongPress() {
    if (widget.onLongPress != null) {
      widget.onLongPress!();
    } else {
      _speakMessage();
    }
  }

  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Microphone Permission Required'),
          content: Text(
              'This app needs microphone access for text-to-speech functionality. '
              'Please grant microphone permission in your device settings.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get device type for responsive spacing
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    return GestureDetector(
        onLongPress: _handleLongPress,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            vertical:
                AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
          ),
          margin: EdgeInsets.symmetric(
            horizontal:
                AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType),
          ),
          decoration: BoxDecoration(
            color: Color(0xFFF0EEE8), // Soft off-white color as requested
            borderRadius: BorderRadius.circular(12), // Rounded corners
          ),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal:
                  AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType),
            ),
            constraints: BoxConstraints(
              maxWidth: 800, // Limit max width for very large screens
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Text(
                      //   'You',
                      //   style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      //     fontWeight: FontWeight.bold,
                      //     color: AppColors.primaryIndigo,
                      //     fontFamily: 'SFProText',
                      //   ),
                      // ),
                      // SizedBox(height: AppSpacing.xxs),
                      Text(
                        widget.message.content,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors
                                  .black87, // Dark text for contrast against light background
                              fontFamily:
                                  'SFProText', // Use SFProText font for user messages
                              height:
                                  1.5, // Improved line height for better readability
                              letterSpacing:
                                  0.2, // Slightly increased letter spacing for readability
                            ),
                      ),
                    ],
                  ),
                ),
                // Speech button
                IconButton(
                  icon: Icon(
                    _isSpeaking ? Icons.volume_off : Icons.volume_up,
                    size: 20,
                    color: _isSpeaking
                        ? Theme.of(context).colorScheme.error
                        : Colors.black54,
                  ),
                  onPressed: _speakMessage,
                  tooltip: _isSpeaking ? 'Stop speaking' : 'Speak message',
                ),
              ],
            ),
          ),
        ));
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
}
