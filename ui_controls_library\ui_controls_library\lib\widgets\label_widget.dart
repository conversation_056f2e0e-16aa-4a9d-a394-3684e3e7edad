import 'dart:convert';
import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toString().padLeft(2, '0')}${g.round().toString().padLeft(2, '0')}${b.round().toString().padLeft(2, '0')}';
  }
}

/// A customizable label widget for displaying text with various styling options.
///
/// This widget provides a rich set of customization options for displaying
/// text labels, including font styling, colors, alignment, decoration,
/// and layout options.
class LabelWidget extends StatefulWidget {
  /// The text to display in the label.
  final String text;

  /// The style to use for the text.
  final TextStyle? textStyle;

  /// The font size of the text.
  final double? fontSize;

  /// The font weight of the text.
  final FontWeight? fontWeight;

  /// The font style of the text (normal or italic).
  final FontStyle? fontStyle;

  /// The color of the text.
  final Color? textColor;

  /// The background color of the label.
  final Color? backgroundColor;

  /// The alignment of the text within the label.
  final TextAlign textAlign;

  /// The maximum number of lines for the text.
  final int? maxLines;

  /// Whether to overflow the text with an ellipsis.
  final bool overflow;

  /// Whether to wrap the text to multiple lines.
  final bool wrap;

  /// The width of the label.
  final double? width;

  /// The height of the label.
  final double? height;

  /// The padding around the text.
  final EdgeInsetsGeometry padding;

  /// The margin around the label.
  final EdgeInsetsGeometry margin;

  /// The border radius of the label.
  final double borderRadius;

  /// The border color of the label.
  final Color? borderColor;

  /// The border width of the label.
  final double borderWidth;

  /// Whether to show a shadow under the label.
  final bool shadow;

  /// The elevation of the shadow.
  final double elevation;

  /// The shadow color.
  final Color? shadowColor;

  /// The decoration type for the text (underline, overline, etc.).
  final TextDecoration? decoration;

  /// The color of the text decoration.
  final Color? decorationColor;

  /// The style of the text decoration (solid, dotted, etc.).
  final TextDecorationStyle? decorationStyle;

  /// The thickness of the text decoration.
  final double? decorationThickness;

  /// The letter spacing of the text.
  final double? letterSpacing;

  /// The word spacing of the text.
  final double? wordSpacing;

  /// The line height of the text.
  final double? lineHeight;

  /// Whether to make the text selectable.
  final bool selectable;

  /// Whether to make the text bold.
  final bool bold;

  /// Whether to make the text italic.
  final bool italic;

  /// Whether to make the text uppercase.
  final bool uppercase;

  /// Whether to make the text lowercase.
  final bool lowercase;

  /// Whether to capitalize the first letter of each word.
  final bool capitalize;

  /// The icon to display before the text.
  final IconData? prefixIcon;

  /// The color of the prefix icon.
  final Color? prefixIconColor;

  /// The size of the prefix icon.
  final double prefixIconSize;

  /// The icon to display after the text.
  final IconData? suffixIcon;

  /// The color of the suffix icon.
  final Color? suffixIconColor;

  /// The size of the suffix icon.
  final double suffixIconSize;

  /// The spacing between the prefix icon and the text.
  final double prefixIconSpacing;

  /// The spacing between the suffix icon and the text.
  final double suffixIconSpacing;

  /// The tooltip text to show when hovering over the label.
  final String? tooltip;

  /// The callback to execute when the label is tapped.
  final VoidCallback? onTap;

  /// The callback to execute when the label is long-pressed.
  final VoidCallback? onLongPress;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  // Animation properties
  /// Whether to animate the widget when it changes
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Label-specific JSON configuration
  /// Whether to use JSON label configuration
  final bool useJsonLabelConfig;

  /// Label-specific JSON configuration
  final Map<String, dynamic>? labelConfig;

  /// Creates a label widget.
  const LabelWidget({
    super.key,
    required this.text,
    this.textStyle,
    this.fontSize,
    this.fontWeight,
    this.fontStyle,
    this.textColor,
    this.backgroundColor,
    this.textAlign = TextAlign.left,
    this.maxLines,
    this.overflow = false,
    this.wrap = true,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(8.0),
    this.margin = EdgeInsets.zero,
    this.borderRadius = 0.0,
    this.borderColor,
    this.borderWidth = 0.0,
    this.shadow = false,
    this.elevation = 2.0,
    this.shadowColor,
    this.decoration,
    this.decorationColor,
    this.decorationStyle,
    this.decorationThickness,
    this.letterSpacing,
    this.wordSpacing,
    this.lineHeight,
    this.selectable = false,
    this.bold = false,
    this.italic = false,
    this.uppercase = false,
    this.lowercase = false,
    this.capitalize = false,
    this.prefixIcon,
    this.prefixIconColor,
    this.prefixIconSize = 16.0,
    this.suffixIcon,
    this.suffixIconColor,
    this.suffixIconSize = 16.0,
    this.prefixIconSpacing = 8.0,
    this.suffixIconSpacing = 8.0,
    this.tooltip,
    this.onTap,
    this.onLongPress,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Label-specific JSON configuration
    this.useJsonLabelConfig = false,
    this.labelConfig,
  });

  /// Creates a LabelWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the LabelWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "text": "Hello World",
  ///   "textColor": "#FF0000",
  ///   "bold": true
  /// }
  /// ```
  factory LabelWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return EdgeInsets.zero;
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return EdgeInsets.zero;
    }

    // Parse duration
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) {
        return const Duration(milliseconds: 300);
      }

      if (durationValue is int) {
        return Duration(milliseconds: durationValue);
      } else if (durationValue is Map<String, dynamic>) {
        final int milliseconds =
            (durationValue['milliseconds'] as num?)?.toInt() ?? 0;
        final int seconds = (durationValue['seconds'] as num?)?.toInt() ?? 0;
        final int minutes = (durationValue['minutes'] as num?)?.toInt() ?? 0;

        return Duration(
          milliseconds: milliseconds,
          seconds: seconds,
          minutes: minutes,
        );
      } else if (durationValue is String) {
        // Parse strings like "300ms", "2s", "1m"
        final RegExp durationRegExp = RegExp(r'(\d+)(ms|s|m)');
        final match = durationRegExp.firstMatch(durationValue);

        if (match != null) {
          final int value = int.parse(match.group(1)!);
          final String unit = match.group(2)!;

          switch (unit) {
            case 'ms':
              return Duration(milliseconds: value);
            case 's':
              return Duration(seconds: value);
            case 'm':
              return Duration(minutes: value);
            default:
              return const Duration(milliseconds: 300);
          }
        }
      }

      return const Duration(milliseconds: 300);
    }

    // Parse curve
    Curve parseCurve(dynamic curveValue) {
      if (curveValue == null) return Curves.easeInOut;

      if (curveValue is String) {
        switch (curveValue.toLowerCase()) {
          case 'linear':
            return Curves.linear;
          case 'decelerate':
            return Curves.decelerate;
          case 'ease':
            return Curves.ease;
          case 'easein':
          case 'ease_in':
            return Curves.easeIn;
          case 'easeout':
          case 'ease_out':
            return Curves.easeOut;
          case 'easeinout':
          case 'ease_in_out':
            return Curves.easeInOut;
          case 'elasticin':
          case 'elastic_in':
            return Curves.elasticIn;
          case 'elasticout':
          case 'elastic_out':
            return Curves.elasticOut;
          case 'elasticinout':
          case 'elastic_in_out':
            return Curves.elasticInOut;
          case 'bouncein':
          case 'bounce_in':
            return Curves.bounceIn;
          case 'bounceout':
          case 'bounce_out':
            return Curves.bounceOut;
          case 'bounceinout':
          case 'bounce_in_out':
            return Curves.bounceInOut;
          default:
            return Curves.easeInOut;
        }
      }

      return Curves.easeInOut;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final color = parseColor(styleValue['color']);
        final fontSize =
            styleValue['fontSize'] != null
                ? (styleValue['fontSize'] as num).toDouble()
                : null;
        final fontWeight =
            styleValue['fontWeight'] != null
                ? (styleValue['fontWeight'] == 'bold'
                    ? FontWeight.bold
                    : styleValue['fontWeight'] == 'normal'
                    ? FontWeight.normal
                    : FontWeight.normal)
                : null;
        final fontStyle =
            styleValue['fontStyle'] != null
                ? (styleValue['fontStyle'] == 'italic'
                    ? FontStyle.italic
                    : FontStyle.normal)
                : null;
        final letterSpacing =
            styleValue['letterSpacing'] != null
                ? (styleValue['letterSpacing'] as num).toDouble()
                : null;
        final wordSpacing =
            styleValue['wordSpacing'] != null
                ? (styleValue['wordSpacing'] as num).toDouble()
                : null;
        final height =
            styleValue['height'] != null
                ? (styleValue['height'] as num).toDouble()
                : null;
        final decoration =
            styleValue['decoration'] != null
                ? (styleValue['decoration'] == 'underline'
                    ? TextDecoration.underline
                    : styleValue['decoration'] == 'lineThrough'
                    ? TextDecoration.lineThrough
                    : styleValue['decoration'] == 'overline'
                    ? TextDecoration.overline
                    : null)
                : null;
        final fontFamily = styleValue['fontFamily'] as String?;

        return TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          wordSpacing: wordSpacing,
          height: height,
          decoration: decoration,
          fontFamily: fontFamily,
        );
      }

      return null;
    }

    // Parse text decoration
    TextDecoration? parseTextDecoration(dynamic decorationValue) {
      if (decorationValue == null) return null;

      if (decorationValue is String) {
        switch (decorationValue.toLowerCase()) {
          case 'underline':
            return TextDecoration.underline;
          case 'overline':
            return TextDecoration.overline;
          case 'linethrough':
            return TextDecoration.lineThrough;
          case 'none':
            return TextDecoration.none;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse text decoration style
    TextDecorationStyle? parseTextDecorationStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is String) {
        switch (styleValue.toLowerCase()) {
          case 'solid':
            return TextDecorationStyle.solid;
          case 'double':
            return TextDecorationStyle.double;
          case 'dotted':
            return TextDecorationStyle.dotted;
          case 'dashed':
            return TextDecorationStyle.dashed;
          case 'wavy':
            return TextDecorationStyle.wavy;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse text align
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'left':
            return TextAlign.left;
          case 'right':
            return TextAlign.right;
          case 'center':
            return TextAlign.center;
          case 'justify':
            return TextAlign.justify;
          case 'start':
            return TextAlign.start;
          case 'end':
            return TextAlign.end;
          default:
            return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight? parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return null;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'w100':
            return FontWeight.w100;
          case 'w200':
            return FontWeight.w200;
          case 'w300':
            return FontWeight.w300;
          case 'w400':
            return FontWeight.w400;
          case 'w500':
            return FontWeight.w500;
          case 'w600':
            return FontWeight.w600;
          case 'w700':
            return FontWeight.w700;
          case 'w800':
            return FontWeight.w800;
          case 'w900':
            return FontWeight.w900;
          default:
            return null;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse font style
    FontStyle? parseFontStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is String) {
        switch (styleValue.toLowerCase()) {
          case 'italic':
            return FontStyle.italic;
          case 'normal':
            return FontStyle.normal;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        // This is a simplified version. In a real app, you would need a more comprehensive mapping
        switch (iconValue.toLowerCase()) {
          case 'add':
            return Icons.add;
          case 'remove':
            return Icons.remove;
          case 'edit':
            return Icons.edit;
          case 'delete':
            return Icons.delete;
          case 'search':
            return Icons.search;
          case 'home':
            return Icons.home;
          case 'settings':
            return Icons.settings;
          case 'person':
            return Icons.person;
          case 'info':
            return Icons.info;
          case 'warning':
            return Icons.warning;
          case 'error':
            return Icons.error;
          case 'check':
            return Icons.check;
          case 'close':
            return Icons.close;
          case 'menu':
            return Icons.menu;
          case 'more':
            return Icons.more_vert;
          case 'arrow_back':
            return Icons.arrow_back;
          case 'arrow_forward':
            return Icons.arrow_forward;
          case 'arrow_up':
            return Icons.arrow_upward;
          case 'arrow_down':
            return Icons.arrow_downward;
          case 'star':
            return Icons.star;
          case 'favorite':
            return Icons.favorite;
          case 'thumb_up':
            return Icons.thumb_up;
          case 'thumb_down':
            return Icons.thumb_down;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    // Parse Label-specific configuration
    Map<String, dynamic>? labelConfig;
    bool useJsonLabelConfig = json['useJsonLabelConfig'] as bool? ?? false;

    if (json['labelConfig'] != null) {
      if (json['labelConfig'] is Map) {
        labelConfig = Map<String, dynamic>.from(json['labelConfig'] as Map);
        useJsonLabelConfig = true;
      } else if (json['labelConfig'] is String) {
        try {
          labelConfig =
              jsonDecode(json['labelConfig'] as String) as Map<String, dynamic>;
          useJsonLabelConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return LabelWidget(
      // Basic properties
      text: json['text'] as String? ?? '',
      textStyle: parseTextStyle(json['textStyle']),
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : null,
      fontWeight: parseFontWeight(json['fontWeight']),
      fontStyle: parseFontStyle(json['fontStyle']),
      textColor: parseColor(json['textColor']),
      backgroundColor: parseColor(json['backgroundColor']),
      textAlign: parseTextAlign(json['textAlign']),
      maxLines: json['maxLines'] as int?,
      overflow: json['overflow'] as bool? ?? false,
      wrap: json['wrap'] as bool? ?? true,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 0.0,
      borderColor: parseColor(json['borderColor']),
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 0.0,
      shadow: json['shadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      shadowColor: parseColor(json['shadowColor']),
      decoration: parseTextDecoration(json['decoration']),
      decorationColor: parseColor(json['decorationColor']),
      decorationStyle: parseTextDecorationStyle(json['decorationStyle']),
      decorationThickness:
          json['decorationThickness'] != null
              ? (json['decorationThickness'] as num).toDouble()
              : null,
      letterSpacing:
          json['letterSpacing'] != null
              ? (json['letterSpacing'] as num).toDouble()
              : null,
      wordSpacing:
          json['wordSpacing'] != null
              ? (json['wordSpacing'] as num).toDouble()
              : null,
      lineHeight:
          json['lineHeight'] != null
              ? (json['lineHeight'] as num).toDouble()
              : null,
      selectable: json['selectable'] as bool? ?? false,
      bold: json['bold'] as bool? ?? false,
      italic: json['italic'] as bool? ?? false,
      uppercase: json['uppercase'] as bool? ?? false,
      lowercase: json['lowercase'] as bool? ?? false,
      capitalize: json['capitalize'] as bool? ?? false,
      prefixIcon: parseIconData(json['prefixIcon']),
      prefixIconColor: parseColor(json['prefixIconColor']),
      prefixIconSize:
          json['prefixIconSize'] != null
              ? (json['prefixIconSize'] as num).toDouble()
              : 16.0,
      suffixIcon: parseIconData(json['suffixIcon']),
      suffixIconColor: parseColor(json['suffixIconColor']),
      suffixIconSize:
          json['suffixIconSize'] != null
              ? (json['suffixIconSize'] as num).toDouble()
              : 16.0,
      prefixIconSpacing:
          json['prefixIconSpacing'] != null
              ? (json['prefixIconSpacing'] as num).toDouble()
              : 8.0,
      suffixIconSpacing:
          json['suffixIconSpacing'] != null
              ? (json['suffixIconSpacing'] as num).toDouble()
              : 8.0,
      tooltip: json['tooltip'] as String?,

      // Advanced interaction properties
      onHover: null, // Cannot be created from JSON directly
      onFocus: null, // Cannot be created from JSON directly
      focusNode: null, // Cannot be created from JSON directly
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: null, // Cannot be created from JSON directly
      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: parseDuration(json['animationDuration']),
      animationCurve: parseCurve(json['animationCurve']),

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,

      // Label-specific JSON configuration
      useJsonLabelConfig: useJsonLabelConfig,
      labelConfig: labelConfig,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'text': text,
      'fontSize': fontSize,
      'textAlign':
          textAlign == TextAlign.left
              ? 'left'
              : textAlign == TextAlign.right
              ? 'right'
              : textAlign == TextAlign.center
              ? 'center'
              : textAlign == TextAlign.justify
              ? 'justify'
              : textAlign == TextAlign.start
              ? 'start'
              : 'end',
      'maxLines': maxLines,
      'overflow': overflow,
      'wrap': wrap,
      'width': width,
      'height': height,
      'borderRadius': borderRadius,
      'borderWidth': borderWidth,
      'shadow': shadow,
      'elevation': elevation,
      'selectable': selectable,
      'bold': bold,
      'italic': italic,
      'uppercase': uppercase,
      'lowercase': lowercase,
      'capitalize': capitalize,
      'prefixIconSize': prefixIconSize,
      'suffixIconSize': suffixIconSize,
      'prefixIconSpacing': prefixIconSpacing,
      'suffixIconSpacing': suffixIconSpacing,

      // Colors
      'textColor': textColor != null ? '#${textColor!.toHexString()}' : null,
      'backgroundColor':
          backgroundColor != null ? '#${backgroundColor!.toHexString()}' : null,
      'borderColor':
          borderColor != null ? '#${borderColor!.toHexString()}' : null,
      'shadowColor':
          shadowColor != null ? '#${shadowColor!.toHexString()}' : null,
      'decorationColor':
          decorationColor != null ? '#${decorationColor!.toHexString()}' : null,
      'prefixIconColor':
          prefixIconColor != null ? '#${prefixIconColor!.toHexString()}' : null,
      'suffixIconColor':
          suffixIconColor != null ? '#${suffixIconColor!.toHexString()}' : null,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Text style properties
      'letterSpacing': letterSpacing,
      'wordSpacing': wordSpacing,
      'lineHeight': lineHeight,
      'decorationThickness': decorationThickness,

      // Advanced properties
      'tooltip': tooltip,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,

      // Animation properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonLabelConfig': useJsonLabelConfig,
    };
  }

  @override
  LabelWidgetState createState() => LabelWidgetState();
}

class LabelWidgetState extends State<LabelWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  Map<String, dynamic> _callbackState = {};
  bool _isHovered = false;
  final bool _isFocused = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Initialize with full opacity if not animating
    if (!widget.hasAnimation) {
      _animationController.value = 1.0;
    } else {
      _animationController.forward();
    }

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void didUpdateWidget(LabelWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration and curve if changed
    if (oldWidget.animationDuration != widget.animationDuration) {
      _animationController.duration = widget.animationDuration;
    }

    if (oldWidget.animationCurve != widget.animationCurve) {
      _animation = CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      );
    }

    // Handle animation state changes
    if (!oldWidget.hasAnimation && widget.hasAnimation) {
      _animationController.forward(from: 0.0);
    } else if (oldWidget.hasAnimation && !widget.hasAnimation) {
      _animationController.value = 1.0;
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Process text transformations
    String displayText = widget.text;
    if (widget.uppercase) {
      displayText = displayText.toUpperCase();
    } else if (widget.lowercase) {
      displayText = displayText.toLowerCase();
    } else if (widget.capitalize) {
      displayText = displayText
          .split(' ')
          .map((word) {
            if (word.isEmpty) return word;
            return word[0].toUpperCase() + word.substring(1).toLowerCase();
          })
          .join(' ');
    }

    // Create text style
    TextStyle effectiveTextStyle =
        widget.textStyle ??
        Theme.of(context).textTheme.bodyMedium ??
        const TextStyle();

    // Apply text style properties
    if (widget.fontSize != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        fontSize: _getResponsiveValueFontSize(context),
        // widget.fontSize,
      );
    }

    if (widget.fontWeight != null || widget.bold) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        fontWeight: widget.fontWeight ?? (widget.bold ? FontWeight.bold : null),
      );
    }

    if (widget.fontStyle != null || widget.italic) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        fontStyle:
            widget.fontStyle ?? (widget.italic ? FontStyle.italic : null),
      );
    }

    if (widget.textColor != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(color: widget.textColor);
    }

    if (widget.decoration != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        decoration: widget.decoration,
        decorationColor: widget.decorationColor,
        decorationStyle: widget.decorationStyle,
        decorationThickness: widget.decorationThickness,
      );
    }

    if (widget.letterSpacing != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        letterSpacing: widget.letterSpacing,
      );
    }

    if (widget.wordSpacing != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        wordSpacing: widget.wordSpacing,
      );
    }

    if (widget.lineHeight != null) {
      effectiveTextStyle = effectiveTextStyle.copyWith(
        height: widget.lineHeight,
      );
    }

    // Create the text widget
    Widget textWidget =
        widget.selectable
            ? SelectableText(
              displayText,
              style: effectiveTextStyle,
              textAlign: widget.textAlign,
              maxLines: widget.maxLines,
            )
            : Text(
              displayText,
              style: effectiveTextStyle,
              textAlign: widget.textAlign,
              maxLines: widget.maxLines,
              overflow:
                  widget.overflow
                      ? TextOverflow.ellipsis
                      : TextOverflow.visible,
              softWrap: widget.wrap,
            );

    // Add prefix and suffix icons if needed
    if (widget.prefixIcon != null || widget.suffixIcon != null) {
      List<Widget> rowChildren = [];

      if (widget.prefixIcon != null) {
        rowChildren.add(
          Icon(
            widget.prefixIcon!,
            color:
                widget.prefixIconColor ??
                widget.textColor ??
                effectiveTextStyle.color,
            size: widget.prefixIconSize,
          ),
        );
        rowChildren.add(SizedBox(width: widget.prefixIconSpacing));
      }

      rowChildren.add(Flexible(child: textWidget));

      if (widget.suffixIcon != null) {
        rowChildren.add(SizedBox(width: widget.suffixIconSpacing));
        rowChildren.add(
          Icon(
            widget.suffixIcon!,
            color:
                widget.suffixIconColor ??
                widget.textColor ??
                effectiveTextStyle.color,
            size: widget.suffixIconSize,
          ),
        );
      }

      textWidget = Row(
       // mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: rowChildren,
      );
    }

    // Apply animation if needed
    if (widget.hasAnimation) {
      textWidget = FadeTransition(opacity: _animation, child: textWidget);
    }

    // Apply container styling
    Widget containerWidget = Container(
      width: widget.width,
      height: widget.height,
     // padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.borderColor != null
                ? Border.all(
                  color: widget.borderColor!,
                  width: widget.borderWidth,
                )
                : null,
        boxShadow:
            widget.shadow
                ? [
                  BoxShadow(
                    color: widget.shadowColor ?? Colors.black.withAlpha(51),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 1),
                  ),
                ]
                : null,
      ),
      child: textWidget,
    );

    // Apply advanced interaction properties
    if (widget.onHover != null ||
        widget.onFocus != null ||
        widget.onDoubleTap != null ||
        widget.onTap != null ||
        widget.onLongPress != null) {
      containerWidget = MouseRegion(
        onEnter: (event) {
          if (widget.onHover != null) {
            setState(() {
              _isHovered = true;
            });
            widget.onHover!(true);
          }
        },
        onExit: (event) {
          if (widget.onHover != null) {
            setState(() {
              _isHovered = false;
            });
            widget.onHover!(false);
          }
        },
        cursor:
            widget.onTap != null ? SystemMouseCursors.click : MouseCursor.defer,
        child: GestureDetector(
          onTap:
              widget.onTap != null
                  ? () {
                    // Execute onTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onTap')) {
                      _executeJsonCallback('onTap');
                    }

                    // Call standard callback
                    widget.onTap!();
                  }
                  : null,
          onDoubleTap:
              widget.onDoubleTap != null
                  ? () {
                    // Execute onDoubleTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                      _executeJsonCallback('onDoubleTap');
                    }

                    // Call standard callback
                    widget.onDoubleTap!();
                  }
                  : null,
          onLongPress:
              widget.onLongPress != null
                  ? () {
                    // Execute onLongPress callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onLongPress')) {
                      _executeJsonCallback('onLongPress');
                    }

                    // Call standard callback
                    widget.onLongPress!();
                  }
                  : null,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: widget.onFocus,
            child: containerWidget,
          ),
        ),
      );
    }

    // Add tooltip if needed
    if (widget.tooltip != null) {
      containerWidget = Tooltip(
        message: widget.tooltip!,
        child: containerWidget,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      containerWidget = Semantics(
        //label: widget.semanticsLabel,
        excludeSemantics: false,
        child: containerWidget,
      );
    }

    return containerWidget;
  }
  
  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
}
