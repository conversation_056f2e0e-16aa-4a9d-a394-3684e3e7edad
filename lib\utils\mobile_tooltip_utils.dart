import 'package:flutter/material.dart';
import 'package:nsl/utils/constants.dart';

/// Utility class for handling mobile tooltips with full-width positioning below target text
class MobileTooltipUtils {
  /// Shows a mobile tooltip positioned below the target widget with full width
  ///
  /// [context] - The build context
  /// [targetKey] - GlobalKey of the widget to position tooltip below
  /// [child] - The widget to display in the tooltip
  /// [backgroundColor] - Optional background color for the overlay (default: semi-transparent black)
  static void showMobileTooltip(
    BuildContext context,
    GlobalKey targetKey,
    Widget child, {
    Color? backgroundColor,
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => MobileTooltipOverlay(
        targetKey: targetKey,
        child: child,
        onClose: () => overlayEntry.remove(),
        backgroundColor: backgroundColor,
      ),
    );

    overlay.insert(overlayEntry);
  }

  /// Checks if the current screen size is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.mobileBreakpoint;
  }

  /// Creates a gesture detector that shows tooltip on long press for mobile
  /// or returns the original widget for web
  static Widget createResponsiveTooltipWrapper({
    required BuildContext context,
    required Widget child,
    required Widget tooltipContent,
    Widget? webTooltipChild,
  }) {
    if (isMobile(context)) {
      // Mobile: Long press to show tooltip below text
      final GlobalKey targetKey = GlobalKey();
      return GestureDetector(
        key: targetKey,
        onLongPress: () =>
            showMobileTooltip(context, targetKey, tooltipContent),
        child: child,
      );
    } else {
      // Web: Use standard tooltip or custom web tooltip
      return webTooltipChild ?? child;
    }
  }
}

/// Mobile tooltip overlay widget that displays below the target widget
class MobileTooltipOverlay extends StatelessWidget {
  final GlobalKey targetKey;
  final Widget child;
  final VoidCallback onClose;
  final Color? backgroundColor;

  const MobileTooltipOverlay({
    super.key,
    required this.targetKey,
    required this.child,
    required this.onClose,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onClose,
      behavior: HitTestBehavior.translucent,
      child: Container(
        color: backgroundColor ?? Colors.black.withValues(alpha: 0.3),
        child: Stack(
          children: [
            // Full screen tap area to close
            Positioned.fill(
              child: GestureDetector(
                onTap: onClose,
                child: Container(color: Colors.transparent),
              ),
            ),
            // Tooltip positioned below target
            _buildPositionedTooltip(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPositionedTooltip(BuildContext context) {
    // Get the position of the target widget
    final RenderBox? renderBox =
        targetKey.currentContext?.findRenderObject() as RenderBox?;

    if (renderBox == null) {
      // Fallback to center if we can't find the target
      return Center(
        child: Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          child: child,
        ),
      );
    }

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    final screenSize = MediaQuery.of(context).size;

    // Calculate position below the target widget
    double top = position.dy + size.height + 8; // 8px gap below target
    double left = 16; // Full width with margins
    double right = 16;

    // Adjust if tooltip would go off screen
    if (top + 200 > screenSize.height - 50) {
      // Estimate tooltip height
      // Position above the target instead
      top = position.dy - 200 - 8; // 8px gap above target
    }

    // Ensure tooltip doesn't go above screen
    if (top < 50) {
      top = 50;
    }

    return Positioned(
      top: top,
      left: left,
      right: right,
      child: Material(
        color: Colors.transparent,
        child: child,
      ),
    );
  }
}

/// A responsive tooltip widget that automatically chooses between mobile and web implementations
class ResponsiveTooltip extends StatelessWidget {
  final Widget child;
  final Widget tooltipContent;
  final Widget? webTooltip;

  const ResponsiveTooltip({
    super.key,
    required this.child,
    required this.tooltipContent,
    this.webTooltip,
  });

  @override
  Widget build(BuildContext context) {
    return MobileTooltipUtils.createResponsiveTooltipWrapper(
      context: context,
      child: child,
      tooltipContent: tooltipContent,
      webTooltipChild: webTooltip,
    );
  }
}
