import 'package:flutter/material.dart';
import '../display/app_display.dart';
import '../index.dart';
import '../../models/message.dart';
import '../lists/app_list_view.dart';
import '../pickers/app_date_picker.dart';
import 'quill_editor_showcase.dart';

/// A showcase of all UI components in the library
class ComponentShowcase extends StatefulWidget {
  const ComponentShowcase({super.key, required this.requireAppBar});
  final bool requireAppBar;
  @override
  State<ComponentShowcase> createState() => _ComponentShowcaseState();
}

class _ComponentShowcaseState extends State<ComponentShowcase> {
  final int _currentIndex = 0;
  final bool _isLoading = false;
  final TextEditingController _textController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: widget.requireAppBar
          ? AppHeader(
              title: 'UI Component Library',
              actions: [
                IconButton(
                  icon: const Icon(Icons.brightness_6),
                  onPressed: () {
                    // Toggle theme
                  },
                ),
              ],
            )
          : null,
      body: ListView(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        children: [
          _buildSection(
            title: 'Rich Text Editors',
            children: [
              _buildSubsection(
                title: 'Quill Editor',
                children: const [
                  QuillEditorShowcase(),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Buttons',
            children: [
              _buildSubsection(
                title: 'Button Variants',
                children: [
                  Wrap(
                    spacing: AppTheme.spacingM,
                    runSpacing: AppTheme.spacingM,
                    children: [
                      AppButton(
                        text: 'Primary Button',
                        onPressed: () {},
                      ),
                      AppButton(
                        text: 'Secondary Button',
                        variant: AppButtonVariant.secondary,
                        onPressed: () {},
                      ),
                      AppButton(
                        text: 'Text Button',
                        variant: AppButtonVariant.text,
                        onPressed: () {},
                      ),
                      AppButton(
                        icon: Icons.add,
                        variant: AppButtonVariant.icon,
                        onPressed: () {},
                      ),
                    ],
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Button Sizes',
                children: [
                  Wrap(
                    spacing: AppTheme.spacingM,
                    runSpacing: AppTheme.spacingM,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      AppButton(
                        text: 'Small',
                        size: AppButtonSize.small,
                        onPressed: () {},
                      ),
                      AppButton(
                        text: 'Medium',
                        size: AppButtonSize.medium,
                        onPressed: () {},
                      ),
                      AppButton(
                        text: 'Large',
                        size: AppButtonSize.large,
                        onPressed: () {},
                      ),
                    ],
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Button States',
                children: [
                  Wrap(
                    spacing: AppTheme.spacingM,
                    runSpacing: AppTheme.spacingM,
                    children: [
                      AppButton(
                        text: 'Enabled',
                        onPressed: () {},
                      ),
                      AppButton(
                        text: 'Disabled',
                        onPressed: null,
                      ),
                      AppButton(
                        text: 'Loading',
                        isLoading: true,
                        onPressed: () {},
                      ),
                    ],
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Button with Icon',
                children: [
                  Wrap(
                    spacing: AppTheme.spacingM,
                    runSpacing: AppTheme.spacingM,
                    children: [
                      AppButton(
                        text: 'Button with Icon',
                        icon: Icons.star,
                        onPressed: () {},
                      ),
                      AppButton(
                        text: 'Button with Icon',
                        icon: Icons.star,
                        variant: AppButtonVariant.secondary,
                        onPressed: () {},
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Text Fields',
            children: [
              _buildSubsection(
                title: 'Text Field Types',
                children: [
                  AppTextField(
                    label: 'Text Field',
                    placeholder: 'Enter text',
                    controller: _textController,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppTextField(
                    label: 'Email Field',
                    placeholder: 'Enter email',
                    type: AppTextFieldType.email,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppTextField(
                    label: 'Password Field',
                    placeholder: 'Enter password',
                    type: AppTextFieldType.password,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppTextField(
                    placeholder: 'Search...',
                    type: AppTextFieldType.search,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppTextField(
                    label: 'Multiline Field',
                    placeholder: 'Enter multiple lines of text',
                    type: AppTextFieldType.multiline,
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Text Field States',
                children: [
                  const AppTextField(
                    label: 'Enabled Field',
                    placeholder: 'Enter text',
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppTextField(
                    label: 'Disabled Field',
                    placeholder: 'Enter text',
                    enabled: false,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppTextField(
                    label: 'Field with Error',
                    placeholder: 'Enter text',
                    errorText: 'This field has an error',
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppTextField(
                    label: 'Field with Helper Text',
                    placeholder: 'Enter text',
                    helperText: 'This is helper text',
                  ),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Cards',
            children: [
              _buildSubsection(
                title: 'Card Types',
                children: [
                  AppCard(
                    child: const Text('Elevated Card (Default)'),
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppCard(
                    type: AppCardType.outlined,
                    child: const Text('Outlined Card'),
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppCard(
                    type: AppCardType.filled,
                    child: const Text('Filled Card'),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Interactive Card',
                children: [
                  AppCard(
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Card tapped'),
                        ),
                      );
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(AppTheme.spacingM),
                      child: Text('Tap this card'),
                    ),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Card with Header',
                children: [
                  AppCardWithHeader(
                    header: const Text(
                      'Card Header',
                      style: AppTheme.headingSmall,
                    ),
                    content: const Text(
                      'This is the content of the card. It can contain any widget.',
                    ),
                    footer: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        AppButton(
                          text: 'Cancel',
                          variant: AppButtonVariant.text,
                          onPressed: () {},
                        ),
                        const SizedBox(width: AppTheme.spacingS),
                        AppButton(
                          text: 'Submit',
                          onPressed: () {},
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Chat Components',
            children: [
              _buildSubsection(
                title: 'Message Bubbles',
                children: [
                  ChatMessageBubble(
                    message: Message(
                      content: 'Hello! This is a user message.',
                      role: MessageRole.user,
                    ),
                  ),
                  ChatMessageBubble(
                    message: Message(
                      content:
                          'Hi there! This is an assistant message. I can help you with various tasks and answer your questions.',
                      role: MessageRole.assistant,
                    ),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Typing Indicator',
                children: const [
                  ChatTypingIndicator(),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Layout Components',
            children: [
              _buildSubsection(
                title: 'Floating Action Button',
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      AppFloatingActionButton(
                        icon: Icons.add,
                        onPressed: () {},
                      ),
                      AppFloatingActionButton(
                        icon: Icons.add,
                        mini: true,
                        onPressed: () {},
                      ),
                      AppFloatingActionButton(
                        icon: Icons.add,
                        extended: true,
                        label: 'Create',
                        onPressed: () {},
                      ),
                    ],
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Responsive Grid',
                children: [
                  AppResponsiveGrid(
                    minItemWidth: 150,
                    children: List.generate(
                      6,
                      (index) => AppResponsiveGridItem(
                        elevation: 1,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Grid item $index tapped'),
                            ),
                          );
                        },
                        child: Container(
                          height: 100,
                          alignment: Alignment.center,
                          child: Text('Grid Item ${index + 1}'),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Expandable Panel',
                children: [
                  AppExpandablePanel(
                    title: const Text(
                      'Expandable Panel',
                      style: AppTheme.bodyMedium,
                    ),
                    content: const Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                      child: Text(
                        'This is the content of the expandable panel. It can be collapsed or expanded by tapping on the header.',
                      ),
                    ),
                    initiallyExpanded: true,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppExpandablePanelGroup(
                    panels: [
                      AppExpandablePanelData(
                        title: const Text('Panel 1'),
                        content: const Padding(
                          padding:
                              EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                          child: Text('Content of panel 1'),
                        ),
                      ),
                      AppExpandablePanelData(
                        title: const Text('Panel 2'),
                        content: const Padding(
                          padding:
                              EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                          child: Text('Content of panel 2'),
                        ),
                      ),
                      AppExpandablePanelData(
                        title: const Text('Panel 3'),
                        content: const Padding(
                          padding:
                              EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                          child: Text('Content of panel 3'),
                        ),
                      ),
                    ],
                    initiallyExpandedIndex: 0,
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Tab Layout',
                children: [
                  SizedBox(
                    height: 200,
                    child: AppTabLayout(
                      tabs: [
                        AppTabData(
                          label: 'Tab 1',
                          icon: Icons.home,
                          content: const Center(
                            child: Text('Content of tab 1'),
                          ),
                        ),
                        AppTabData(
                          label: 'Tab 2',
                          icon: Icons.search,
                          content: const Center(
                            child: Text('Content of tab 2'),
                          ),
                        ),
                        AppTabData(
                          label: 'Tab 3',
                          icon: Icons.person,
                          content: const Center(
                            child: Text('Content of tab 3'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  SizedBox(
                    height: 150,
                    child: AppSegmentedTabLayout(
                      tabs: [
                        AppTabData(
                          label: 'Day',
                          content: const Center(
                            child: Text('Day view'),
                          ),
                        ),
                        AppTabData(
                          label: 'Week',
                          content: const Center(
                            child: Text('Week view'),
                          ),
                        ),
                        AppTabData(
                          label: 'Month',
                          content: const Center(
                            child: Text('Month view'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Dividers',
                children: [
                  const AppDivider(),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppDivider(
                    style: AppDividerStyle.dashed,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  const AppDivider(
                    style: AppDividerStyle.dotted,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppLabeledDivider(
                    label: const Text('OR'),
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  Row(
                    children: [
                      const Expanded(child: Text('Left')),
                      const AppDivider.vertical(
                        height: 30,
                      ),
                      const Expanded(child: Text('Right')),
                    ],
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Spacers',
                children: [
                  Container(
                    color: AppTheme.surfaceColor,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Extra Small Spacer'),
                        const AppSpacer.xs(),
                        Container(height: 1, color: AppTheme.primaryColor),
                        const Text('Small Spacer'),
                        const AppSpacer.s(),
                        Container(height: 1, color: AppTheme.primaryColor),
                        const Text('Medium Spacer'),
                        const AppSpacer.m(),
                        Container(height: 1, color: AppTheme.primaryColor),
                        const Text('Large Spacer'),
                        const AppSpacer.l(),
                        Container(height: 1, color: AppTheme.primaryColor),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  Container(
                    color: AppTheme.surfaceColor,
                    child: Row(
                      children: [
                        const Text('XS'),
                        const AppSpacer.horizontalXs(),
                        const Text('S'),
                        const AppSpacer.horizontalS(),
                        const Text('M'),
                        const AppSpacer.horizontalM(),
                        const Text('L'),
                        const AppSpacer.horizontalL(),
                        const Text('End'),
                      ],
                    ),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Split View',
                children: [
                  SizedBox(
                    height: 200,
                    child: AppSplitView(
                      firstPanel: Container(
                        color: AppTheme.surfaceColor,
                        child: const Center(
                          child: Text('Left Panel'),
                        ),
                      ),
                      secondPanel: Container(
                        color: AppTheme.surfaceColor.withAlpha(128),
                        child: const Center(
                          child: Text('Right Panel'),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  SizedBox(
                    height: 200,
                    child: AppMultiSplitView(
                      panels: [
                        AppSplitPanelData(
                          content: Container(
                            color: AppTheme.surfaceColor,
                            child: const Center(
                              child: Text('Panel 1'),
                            ),
                          ),
                        ),
                        AppSplitPanelData(
                          content: Container(
                            color: AppTheme.surfaceColor.withAlpha(179),
                            child: const Center(
                              child: Text('Panel 2'),
                            ),
                          ),
                        ),
                        AppSplitPanelData(
                          content: Container(
                            color: AppTheme.surfaceColor.withAlpha(128),
                            child: const Center(
                              child: Text('Panel 3'),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Typography',
            children: [
              _buildSubsection(
                title: 'Text Variants',
                children: [
                  const AppText.headingLarge('Heading Large'),
                  const AppText.headingMedium('Heading Medium'),
                  const AppText.headingSmall('Heading Small'),
                  const AppText.bodyLarge('Body Large'),
                  const AppText.bodyMedium('Body Medium'),
                  const AppText.bodySmall('Body Small'),
                  const AppText.caption('Caption Text'),
                  const AppText('Default Body Text'),
                ],
              ),
              _buildSubsection(
                title: 'Text Styles',
                children: [
                  AppText(
                    'Custom Color Text',
                    color: AppTheme.primaryColor,
                  ),
                  AppText(
                    'Bold Text',
                    fontWeight: FontWeight.bold,
                  ),
                  AppText(
                    'Custom Size Text',
                    fontSize: 20,
                  ),
                  const AppText(
                    'Center Aligned Text',
                    textAlign: TextAlign.center,
                  ),
                  const AppText(
                    'This is a very long text that demonstrates overflow behavior and will be truncated with an ellipsis at the end because it exceeds the maximum number of lines specified.',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Images',
            children: [
              _buildSubsection(
                title: 'Basic Images',
                children: [
                  AppImage(
                    image: const NetworkImage('https://picsum.photos/200'),
                    width: 200,
                    height: 200,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppImage(
                    image: const NetworkImage('https://picsum.photos/300/200'),
                    width: double.infinity,
                    height: 200,
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Interactive Images',
                children: [
                  AppImage(
                    image: const NetworkImage('https://picsum.photos/200/150'),
                    width: double.infinity,
                    height: 150,
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    elevation: 4,
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Image tapped'),
                        ),
                      );
                    },
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Styled Images',
                children: [
                  AppImage(
                    image: const NetworkImage('https://picsum.photos/200/100'),
                    width: double.infinity,
                    height: 100,
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    border: Border.all(
                      color: AppTheme.primaryColor,
                      width: 2,
                    ),
                    backgroundColor: AppTheme.surfaceColor,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppImage(
                    image: const NetworkImage('https://picsum.photos/200/100'),
                    width: double.infinity,
                    height: 100,
                    fit: BoxFit.contain,
                    backgroundColor: AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                ],
              ),
            ],
          ),
          _buildSection(
            title: 'Lists',
            children: [
              _buildSubsection(
                title: 'Basic List View',
                children: [
                  Container(
                    height: 300, // Fixed height container
                    decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.dividerColor),
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    child: AppListView(
                      children: List.generate(
                        5,
                        (index) => AppListItem(
                          title: Text('List Item ${index + 1}'),
                          subtitle: Text('Subtitle for item ${index + 1}'),
                          leading: const Icon(Icons.circle, size: 8),
                          trailing: const Icon(Icons.chevron_right),
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Tapped item ${index + 1}'),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'List View with Dividers',
                children: [
                  Container(
                    height: 200, // Fixed height container
                    decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.dividerColor),
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    child: AppListView(
                      showDividers: true,
                      dividerColor: AppTheme.dividerColor,
                      children: List.generate(
                        3,
                        (index) => AppListItem(
                          title: Text('Divided Item ${index + 1}'),
                          subtitle: Text('With dividers between items'),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Dynamic List View',
                children: [
                  Container(
                    height: 200, // Fixed height container
                    decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.dividerColor),
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    child: AppDynamicListView<String>(
                      items: ['Item 1', 'Item 2', 'Item 3'],
                      itemBuilder: (context, item, index) => AppListItem(
                        title: Text(item),
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Tapped $item')),
                          );
                        },
                      ),
                      emptyBuilder: (context) => const Center(
                        child: Text('No items available'),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          // _buildSection(
          //   title: 'Tables',
          //   children: [
          //     _buildSubsection(
          //       title: 'Basic Table',
          //       children: [
          //         AppTable<Map<String, dynamic>>(
          //           columns: [
          //             AppTableColumn(
          //               label: 'Name',
          //               value: (item) => item['name'] as String,
          //             ),
          //             AppTableColumn(
          //               label: 'Age',
          //               value: (item) => item['age'].toString(),
          //             ),
          //             AppTableColumn(
          //               label: 'City',
          //               value: (item) => item['city'] as String,
          //             ),
          //           ],
          //           data: [
          //             {'name': 'John Doe', 'age': 30, 'city': 'New York'},
          //             {'name': 'Jane Smith', 'age': 25, 'city': 'London'},
          //             {'name': 'Bob Johnson', 'age': 35, 'city': 'Paris'},
          //           ],
          //           showHeader: true,
          //           showHorizontalDividers: true,
          //         ),
          //       ],
          //     ),
          //     _buildSubsection(
          //       title: 'Paginated Table',
          //       children: [
          //         SizedBox(
          //           height: 300,
          //           child: AppPaginatedTable<Map<String, dynamic>>(
          //             columns: [
          //               AppTableColumn(
          //                 label: 'ID',
          //                 value: (item) => item['id'].toString(),
          //               ),
          //               AppTableColumn(
          //                 label: 'Name',
          //                 value: (item) => item['name'] as String,
          //               ),
          //               AppTableColumn(
          //                 label: 'Status',
          //                 value: (item) => item['status'] as String,
          //               ),
          //             ],
          //             data: List.generate(
          //               5,
          //               (i) => {
          //                 'id': i + 1,
          //                 'name': 'Item ${i + 1}',
          //                 'status': i % 2 == 0 ? 'Active' : 'Inactive',
          //               },
          //             ),
          //             currentPage: 0,
          //             itemsPerPage: 5,
          //             totalItems: 20,
          //             onPageChanged: (page) {
          //               // Handle page change
          //             },
          //           ),
          //         ),
          //       ],
          //     ),
          //   ],
          // ),
          _buildSection(
            title: 'Date & Time Pickers',
            children: [
              _buildSubsection(
                title: 'Date Picker',
                children: [
                  AppDatePicker(
                    selectedDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    onDateSelected: (date) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Selected date: ${date.toString()}'),
                        ),
                      );
                    },
                    label: 'Select Date',
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppDatePicker(
                    selectedDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    onDateSelected: (date) {},
                    label: 'Disabled Date Picker',
                    enabled: false,
                  ),
                ],
              ),
              _buildSubsection(
                title: 'Date Time Picker',
                children: [
                  AppDateTimePicker(
                    selectedDateTime: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    onDateTimeSelected: (dateTime) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Selected: ${dateTime.toString()}'),
                        ),
                      );
                    },
                    label: 'Select Date & Time',
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  AppDateTimePicker(
                    selectedDateTime: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    onDateTimeSelected: (dateTime) {},
                    label: 'With 24-hour format',
                    use24HourFormat: true,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
      // bottomNavigationBar: AppBottomNavigationBar(
      //   currentIndex: _currentIndex,
      //   onTap: (index) {
      //     setState(() {
      //       _currentIndex = index;
      //     });
      //   },
      //   items: const [
      //     AppBottomNavigationItem(
      //       icon: Icons.home,
      //       label: 'Home',
      //     ),
      //     AppBottomNavigationItem(
      //       icon: Icons.search,
      //       label: 'Search',
      //     ),
      //     AppBottomNavigationItem(
      //       icon: Icons.person,
      //       label: 'Profile',
      //     ),
      //     AppBottomNavigationItem(
      //       icon: Icons.settings,
      //       label: 'Settings',
      //     ),
      //   ],
      // ),
      // floatingActionButton: AppFloatingActionButton(
      //   icon: _isLoading ? null : Icons.add,
      //   child: _isLoading
      //       ? const SizedBox(
      //           width: 24,
      //           height: 24,
      //           child: CircularProgressIndicator(
      //             color: Colors.white,
      //             strokeWidth: 2,
      //           ),
      //         )
      //       : null,
      //   onPressed: () {
      //     setState(() {
      //       _isLoading = !_isLoading;
      //     });
      //   },
      // ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
          child: Text(
            title,
            style: AppTheme.headingMedium,
          ),
        ),
        ...children,
        const SizedBox(height: AppTheme.spacingL),
      ],
    );
  }

  Widget _buildSubsection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingS),
          child: Text(
            title,
            style: AppTheme.headingSmall,
          ),
        ),
        ...children,
        const SizedBox(height: AppTheme.spacingM),
      ],
    );
  }
}
