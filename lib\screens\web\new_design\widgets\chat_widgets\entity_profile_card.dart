import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/models/entities_data.dart' as entities_model;

/// A reusable component that displays detailed entity profile information in a card format.
///
/// This widget is typically used in tooltips or detail panels to show comprehensive
/// entity information including profile image, ID, version, creation and modification dates.
class EntityProfileCard extends StatelessWidget {
  /// The entity to display information for
  final entities_model.Entity entity;

  /// Width of the card
  final double width;

  /// Left margin of the card
  final double leftMargin;

  /// Background color of the header
  final Color headerColor;

  /// Border color of the properties section
  final Color propertiesBorderColor;

  /// Path to the profile image asset
  final String profileImagePath;

  const EntityProfileCard({
    super.key,
    required this.entity,
    this.width = 0,
    this.leftMargin = 170,
    this.headerColor = const Color(0xFF0058FF), // Colors.blue.shade700
    this.propertiesBorderColor = const Color(0xFF93B8FF),
    this.profileImagePath = 'assets/images/user_profile_image.png',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: leftMargin,
      ),
      width: width,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Blue header with icon
          _buildHeader(),

          // Profile image and info
          _buildProfileSection(),

          // Editable Properties section
          _buildPropertiesSection(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: headerColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white,
                width: 1.5,
              ),
              color: headerColor,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.person_outline,
              color: Colors.white,
              size: 18,
            ),
          ),
          SizedBox(width: AppSpacing.xs),
          Expanded(
            child: Text(
              entity.title ?? 'Entity',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontFamily: 'TiemposText',
                fontSize: 12,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Profile image
        Container(
          margin: EdgeInsets.all(AppSpacing.sm),
          width: 80,
          height: 85,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.asset(
              profileImagePath,
              fit: BoxFit.cover,
            ),
          ),
        ),

        // Information container - more compact layout
        Expanded(
          child: Container(
            height: 85,
            margin: EdgeInsets.only(
              top: AppSpacing.sm,
              right: AppSpacing.sm,
              bottom: AppSpacing.sm,
            ),
            padding: EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // First row: ID and Version side by side
                Row(
                  children: [
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            const TextSpan(
                              text: 'ID: ',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            TextSpan(
                              text: _extractNumericId(entity.id ?? ''),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            const TextSpan(
                              text: 'Version: ',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            TextSpan(
                              text: entity.version ?? '1.0',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: AppSpacing.xs),

                // Second row: Created date only
                RichText(
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: 'Created: ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      TextSpan(
                        text: _formatDate(entity.createdDate ?? DateTime.now()),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),

                SizedBox(height: AppSpacing.xs),

                // Third row: Modified date only
                RichText(
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: 'Modified: ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      TextSpan(
                        text: entity.modifiedDate != null
                            ? _formatDateFromString(entity.modifiedDate!)
                            : _formatDate(DateTime.now()),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPropertiesSection() {
    return Container(
      margin: const EdgeInsets.only(
        left: 12,
        right: 12,
        bottom: 12,
      ),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: propertiesBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Attributes count
          RichText(
            text: TextSpan(
              children: [
                const TextSpan(
                  text: 'Attributes: ',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                TextSpan(
                  text: '${entity.attributes?.length ?? 0}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),

          // Display Name
          RichText(
            text: TextSpan(
              children: [
                const TextSpan(
                  text: 'Display Name: ',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                TextSpan(
                  text: entity.title ?? 'Unknown',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),

          // Type
          RichText(
            text: TextSpan(
              children: [
                const TextSpan(
                  text: 'Type: ',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const TextSpan(
                  text: 'Core Business Entity',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),

          // Description
          RichText(
            text: TextSpan(
              children: [
                const TextSpan(
                  text: 'Description: ',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                TextSpan(
                  text: entity.description ?? 'No description available',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// Extract numeric ID from full ID string
  String _extractNumericId(String fullId) {
    if (fullId.isEmpty) return 'N/A';
    // Split by underscore and take the last part (the numeric ID)
    final parts = fullId.split('_');
    return parts.isNotEmpty ? parts.last : fullId;
  }

  /// Format DateTime to string
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Format date string (if it's already a string)
  String _formatDateFromString(String dateString) {
    try {
      // Try to parse the date string and reformat it
      final date = DateTime.parse(dateString);
      return _formatDate(date);
    } catch (e) {
      // If parsing fails, return the original string or a default
      return dateString.isNotEmpty ? dateString : 'N/A';
    }
  }
}
