// To parse this JSON data, do
//
//     final solutionSessionModel = solutionSessionModelFromJson(jsonString);

import 'dart:convert';

SolutionSessionModel solutionSessionModelFromJson(String str) =>
    SolutionSessionModel.fromJson(json.decode(str));

String solutionSessionModelToJson(SolutionSessionModel data) =>
    json.encode(data.toJson());

class SolutionSessionModel {
  String? sessionId;
  String? conversationId;
  String? status;
  int? initialNodesUpdated;
  double? currentWeight;
  String? nextQuestion;
  String? question;
  String? nextNodeId;
  QuestionHighlighting? questionHighlighting;
  Progress? progress;
  int? nodesUpdated;
  List<UpdatedNode>? updatedNodes;
  dynamic thinking;

  SolutionSessionModel({
    this.sessionId,
    this.conversationId,
    this.status,
    this.initialNodesUpdated,
    this.currentWeight,
    this.nextQuestion,
    this.nextNodeId,
    this.questionHighlighting,
    this.progress,
    this.nodesUpdated,
    this.updatedNodes,
    this.thinking,
    this.question,
  });

  SolutionSessionModel copyWith({
    String? sessionId,
    String? conversationId,
    String? status,
    int? initialNodesUpdated,
    double? currentWeight,
    String? nextQuestion,
    String? nextNodeId,
    QuestionHighlighting? questionHighlighting,
    Progress? progress,
    int? nodesUpdated,
    List<UpdatedNode>? updatedNodes,
    dynamic thinking,
    String? question,
  }) =>
      SolutionSessionModel(
        sessionId: sessionId ?? this.sessionId,
        conversationId: conversationId ?? this.conversationId,
        status: status ?? this.status,
        initialNodesUpdated: initialNodesUpdated ?? this.initialNodesUpdated,
        currentWeight: currentWeight ?? this.currentWeight,
        nextQuestion: nextQuestion ?? this.nextQuestion,
        nextNodeId: nextNodeId ?? this.nextNodeId,
        questionHighlighting: questionHighlighting ?? this.questionHighlighting,
        progress: progress ?? this.progress,
        nodesUpdated: nodesUpdated ?? this.nodesUpdated,
        updatedNodes: updatedNodes ?? this.updatedNodes,
        thinking: thinking ?? this.thinking,
        question: question ?? this.question,
      );

  factory SolutionSessionModel.fromJson(Map<String, dynamic> json) =>
      SolutionSessionModel(
        sessionId: json["session_id"],
        conversationId: json["conversation_id"],
        status: json["status"],
        initialNodesUpdated: json["initial_nodes_updated"],
        currentWeight: json["current_weight"]?.toDouble(),
        nextQuestion: json["next_question"],
        nextNodeId: json["next_node_id"],
        questionHighlighting: json["question_highlighting"] == null
            ? null
            : QuestionHighlighting.fromJson(json["question_highlighting"]),
        progress: json["progress"] == null
            ? null
            : Progress.fromJson(json["progress"]),
        nodesUpdated: json["nodes_updated"],
        updatedNodes: json["updated_nodes"] == null
            ? []
            : List<UpdatedNode>.from(
                json["updated_nodes"]!.map((x) => UpdatedNode.fromJson(x))),
        thinking: json["thinking"],
        question: json["question"],
      );

  Map<String, dynamic> toJson() => {
        "session_id": sessionId,
        "conversation_id": conversationId,
        "status": status,
        "initial_nodes_updated": initialNodesUpdated,
        "current_weight": currentWeight,
        "next_question": nextQuestion,
        "next_node_id": nextNodeId,
        "question_highlighting": questionHighlighting?.toJson(),
        "progress": progress?.toJson(),
        "nodes_updated": nodesUpdated,
        "updated_nodes": updatedNodes == null
            ? []
            : List<dynamic>.from(updatedNodes!.map((x) => x.toJson())),
        "thinking": thinking,
        "question": question,
      };
}

class Progress {
  double? currentWeight;
  double? completionPercentage;
  bool? mandatoryComplete;
  int? nodesUpdated;
  int? totalNodes;

  Progress({
    this.currentWeight,
    this.completionPercentage,
    this.mandatoryComplete,
    this.nodesUpdated,
    this.totalNodes,
  });

  Progress copyWith({
    double? currentWeight,
    double? completionPercentage,
    bool? mandatoryComplete,
    int? nodesUpdated,
    int? totalNodes,
  }) =>
      Progress(
        currentWeight: currentWeight ?? this.currentWeight,
        completionPercentage: completionPercentage ?? this.completionPercentage,
        mandatoryComplete: mandatoryComplete ?? this.mandatoryComplete,
        nodesUpdated: nodesUpdated ?? this.nodesUpdated,
        totalNodes: totalNodes ?? this.totalNodes,
      );

  factory Progress.fromJson(Map<String, dynamic> json) => Progress(
        currentWeight: json["current_weight"]?.toDouble(),
        completionPercentage: json["completion_percentage"]?.toDouble(),
        mandatoryComplete: json["mandatory_complete"],
        nodesUpdated: json["nodes_updated"],
        totalNodes: json["total_nodes"],
      );

  Map<String, dynamic> toJson() => {
        "current_weight": currentWeight,
        "completion_percentage": completionPercentage,
        "mandatory_complete": mandatoryComplete,
        "nodes_updated": nodesUpdated,
        "total_nodes": totalNodes,
      };
}

class QuestionHighlighting {
  String? rawQuestion;
  List<QuestionPart>? questionParts;

  QuestionHighlighting({
    this.rawQuestion,
    this.questionParts,
  });

  QuestionHighlighting copyWith({
    String? rawQuestion,
    List<QuestionPart>? questionParts,
  }) =>
      QuestionHighlighting(
        rawQuestion: rawQuestion ?? this.rawQuestion,
        questionParts: questionParts ?? this.questionParts,
      );

  factory QuestionHighlighting.fromJson(Map<String, dynamic> json) =>
      QuestionHighlighting(
        rawQuestion: json["raw_question"],
        questionParts: json["question_parts"] == null
            ? []
            : List<QuestionPart>.from(
                json["question_parts"]!.map((x) => QuestionPart.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "raw_question": rawQuestion,
        "question_parts": questionParts == null
            ? []
            : List<dynamic>.from(questionParts!.map((x) => x.toJson())),
      };
}

class QuestionPart {
  String? type;
  String? content;

  QuestionPart({
    this.type,
    this.content,
  });

  QuestionPart copyWith({
    String? type,
    String? content,
  }) =>
      QuestionPart(
        type: type ?? this.type,
        content: content ?? this.content,
      );

  factory QuestionPart.fromJson(Map<String, dynamic> json) => QuestionPart(
        type: json["type"],
        content: json["content"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "content": content,
      };
}

class UpdatedNode {
  String? nodeId;
  String? section;
  String? key;
  String? prompt;
  String? value;
  int? weight;
  bool? isMandatory;
  DateTime? updatedAt;

  UpdatedNode({
    this.nodeId,
    this.section,
    this.key,
    this.prompt,
    this.value,
    this.weight,
    this.isMandatory,
    this.updatedAt,
  });

  UpdatedNode copyWith({
    String? nodeId,
    String? section,
    String? key,
    String? prompt,
    String? value,
    int? weight,
    bool? isMandatory,
    DateTime? updatedAt,
  }) =>
      UpdatedNode(
        nodeId: nodeId ?? this.nodeId,
        section: section ?? this.section,
        key: key ?? this.key,
        prompt: prompt ?? this.prompt,
        value: value ?? this.value,
        weight: weight ?? this.weight,
        isMandatory: isMandatory ?? this.isMandatory,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory UpdatedNode.fromJson(Map<String, dynamic> json) => UpdatedNode(
        nodeId: json["node_id"],
        section: json["section"],
        key: json["key"],
        prompt: json["prompt"],
        value: json["value"],
        weight: json["weight"],
        isMandatory: json["is_mandatory"],
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "node_id": nodeId,
        "section": section,
        "key": key,
        "prompt": prompt,
        "value": value,
        "weight": weight,
        "is_mandatory": isMandatory,
        "updated_at": updatedAt?.toIso8601String(),
      };
}
