import 'package:flutter/material.dart';

class StacksInfoPanel extends StatelessWidget {
  final Map<String, dynamic> metadata;

  const StacksInfoPanel({
    super.key,
    required this.metadata,
  });

  @override
  Widget build(BuildContext context) {
    // Safely extract stacks and permissions with null checks
    final stacks = metadata.containsKey('stacks')
        ? metadata['stacks'] as List<dynamic>? ?? []
        : <dynamic>[];

    final permissions = metadata.containsKey('permissions')
        ? metadata['permissions'] as List<dynamic>? ?? []
        : <dynamic>[];

    final title = metadata.containsKey('title')
        ? metadata['title'] as String? ?? 'Details'
        : 'Details';

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16.0),

          // Permissions section (only if not empty)
          if (permissions.isNotEmpty) ...[
            ..._buildPermissionsSection(permissions),
            const SizedBox(height: 24.0),
          ],

          // Stacks section (only if not empty)
          if (stacks.isNotEmpty) ..._buildStacksSection(stacks),

          // Show message if no data
          if (stacks.isEmpty && permissions.isEmpty)
            const Text(
              'No additional information available for this item.',
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildPermissionsSection(List<dynamic> permissions) {
    return [
      ...permissions.map((permission) {
        final role = permission['role'];
        final rights = permission['rights'];

        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '• ',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: TextStyle(
                      color: Colors.grey.shade800,
                      fontSize: 14.0,
                    ),
                    children: [
                      TextSpan(
                        text: role.startsWith('(') ? role : '($role) ',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextSpan(
                        text: role.startsWith('(') ? ' ' : '',
                      ),
                      TextSpan(
                        text: rights,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    ];
  }

  List<Widget> _buildStacksSection(List<dynamic> stacks) {
    return [
      ...stacks.map((stack) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            children: [
              Container(
                width: 24.0,
                alignment: Alignment.center,
                child: Text(
                  '${stack['id']}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8.0),
              Text(
                stack['name'],
                style: const TextStyle(
                  fontSize: 14.0,
                ),
              ),
            ],
          ),
        );
      }),
    ];
  }
}
