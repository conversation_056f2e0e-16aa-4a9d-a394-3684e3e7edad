import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// A configurable image widget that can display images from various sources with styling options.
class ImageWidget extends StatefulWidget {
  // Source properties
  final String? imageUrl;
  final String? assetPath;
  final File? imageFile;
  final IconData? placeholderIcon;
  final ImageProvider? imageProvider;

  // Content properties
  final String? label;
  final String? caption;
  final String? description;
  final TextStyle? labelStyle;
  final TextStyle? captionStyle;
  final TextStyle? descriptionStyle;
  final bool showLabel;
  final bool showCaption;
  final bool showDescription;
  final TextAlign textAlign;

  // Appearance properties
  final double? width;
  final double? height;
  final BoxFit fit;
  final Color backgroundColor;
  final bool isCircular;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;
  final Color shadowColor;
  final bool isCard;
  final Color? tintColor;
  final BlendMode? colorBlendMode;

  // Layout properties
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final Alignment alignment;

  // Behavior properties
  final bool isTappable;
  final bool isZoomable;
  final bool openInDialog;
  final bool showPopupOnClick;
  final bool enableHero;
  final String? heroTag;

  // Callbacks
  final VoidCallback? onTap;
  final Function(bool)? onLoadComplete;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onSecondaryTap;

  // Image-specific properties
  final bool enableLazyLoading;
  final bool enableCaching;
  final String? cacheKey;
  final int? memCacheWidth;
  final int? memCacheHeight;
  final bool enableGestureTransformations;
  final double minScale;
  final double maxScale;
  final bool enablePinchZoom;
  final bool enablePanning;
  final bool enableDoubleTapZoom;
  final bool enableRotation;
  final bool showLoadingProgress;
  final bool showDownloadProgress;
  final bool enableFullscreenView;
  final bool enableShareOption;
  final bool enableSaveOption;
  final bool enableCopyOption;
  final bool enableCropOption;
  final bool enableFilterOption;
  final List<String>? availableFilters;
  final String? initialFilter;
  final bool enableAnnotation;
  final List<Color>? annotationColors;
  final double annotationStrokeWidth;
  final bool enableImagePicker;
  final bool enableCameraCapture;
  final bool enableGalleryPicker;
  final bool enableUrlInput;
  final bool enableDragDrop;
  final List<String>? allowedExtensions;
  final int? maxFileSizeBytes;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // Image-specific JSON configuration
  final bool useJsonImageConfig;
  final Map<String, dynamic>? imageConfig;

  // Loading properties
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final Duration fadeInDuration;
  final Curve fadeInCurve;
  final bool showLoadingIndicator;
  final String? fallbackAssetPath;
  final String? fallbackUrl;

  const ImageWidget({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.imageFile,
    this.placeholderIcon,
    this.imageProvider,
    this.label,
    this.caption,
    this.description,
    this.labelStyle,
    this.captionStyle,
    this.descriptionStyle,
    this.showLabel = false,
    this.showCaption = false,
    this.showDescription = false,
    this.textAlign = TextAlign.center,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.backgroundColor = Colors.transparent,
    this.isCircular = false,
    this.borderRadius = 16.0,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 4.0,
    this.shadowColor = Colors.black,
    this.isCard = false,
    this.tintColor,
    this.colorBlendMode,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.alignment = Alignment.center,
    this.isTappable = false,
    this.isZoomable = false,
    this.openInDialog = false,
    this.showPopupOnClick = true,
    this.enableHero = false,
    this.heroTag,
    this.onTap,
    this.onLoadComplete,
    this.loadingWidget,
    this.errorWidget,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.fadeInCurve = Curves.easeIn,
    this.showLoadingIndicator = true,
    this.fallbackAssetPath,
    this.fallbackUrl,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    this.onSecondaryTap,
    // Image-specific properties
    this.enableLazyLoading = true,
    this.enableCaching = true,
    this.cacheKey,
    this.memCacheWidth,
    this.memCacheHeight,
    this.enableGestureTransformations = false,
    this.minScale = 0.5,
    this.maxScale = 3.0,
    this.enablePinchZoom = false,
    this.enablePanning = false,
    this.enableDoubleTapZoom = false,
    this.enableRotation = false,
    this.showLoadingProgress = true,
    this.showDownloadProgress = true,
    this.enableFullscreenView = false,
    this.enableShareOption = false,
    this.enableSaveOption = false,
    this.enableCopyOption = false,
    this.enableCropOption = false,
    this.enableFilterOption = false,
    this.availableFilters,
    this.initialFilter,
    this.enableAnnotation = false,
    this.annotationColors,
    this.annotationStrokeWidth = 2.0,
    this.enableImagePicker = false,
    this.enableCameraCapture = false,
    this.enableGalleryPicker = false,
    this.enableUrlInput = false,
    this.enableDragDrop = false,
    this.allowedExtensions,
    this.maxFileSizeBytes,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Image-specific JSON configuration
    this.useJsonImageConfig = false,
    this.imageConfig,
  }) : assert(
         imageUrl != null ||
             assetPath != null ||
             imageFile != null ||
             imageProvider != null ||
             placeholderIcon != null,
         'At least one image source must be provided',
       );

  /// Creates an avatar ImageWidget with 32x32 dimensions
  ///
  /// This factory constructor creates a circular avatar image with fixed dimensions
  /// of 32x32 pixels, optimized for avatar display.
  ///
  /// Example usage:
  /// ```dart
  /// ImageWidget.avatar(
  ///   imageUrl: 'https://example.com/avatar.jpg',
  ///   label: 'User Avatar',
  /// )
  /// ```
  factory ImageWidget.avatar({
    Key? key,
    String? imageUrl,
    String? assetPath,
    File? imageFile,
    ImageProvider? imageProvider,
    String? label,
    TextStyle? labelStyle,
    bool showLabel = false,
    Color backgroundColor = Colors.grey,
    bool showPopupOnClick = true,
    EdgeInsetsGeometry padding = EdgeInsets.zero,
    EdgeInsetsGeometry margin = EdgeInsets.zero,
    VoidCallback? onTap,
    Function(bool)? onLoadComplete,
    Widget? loadingWidget,
    Widget? errorWidget,
    String? fallbackAssetPath,
    String? fallbackUrl,
    IconData? placeholderIcon,
    // Advanced interaction properties
    void Function(bool)? onHover,
    void Function(bool)? onFocus,
    FocusNode? focusNode,
    Color? hoverColor,
    Color? focusColor,
    bool enableFeedback = true,
    VoidCallback? onDoubleTap,
    VoidCallback? onLongPress,
    VoidCallback? onSecondaryTap,
    // JSON configuration properties
    Map<String, dynamic>? jsonCallbacks,
    bool useJsonCallbacks = false,
    Map<String, dynamic>? callbackState,
    Map<String, Function>? customCallbackHandlers,
  }) {
    return ImageWidget(
      key: key,
      imageUrl: imageUrl,
      assetPath: assetPath,
      imageFile: imageFile,
      imageProvider: imageProvider,
      label: label,
      labelStyle: labelStyle,
      showLabel: showLabel,
      // Fixed avatar dimensions
      width: 150.0,
      height:150.0,
      // Avatar-specific styling
      fit: BoxFit.cover,
      backgroundColor: backgroundColor,
      isCircular: true, // Always circular for avatars
      borderRadius: 0.0, // Half of width/height for perfect circle
      showPopupOnClick: showPopupOnClick,
      padding: padding,
      margin: margin,
      alignment: Alignment.center,
      isTappable: onTap != null,
      onTap: onTap,
      onLoadComplete: onLoadComplete,
      loadingWidget: loadingWidget,
      errorWidget: errorWidget,
      fallbackAssetPath: fallbackAssetPath,
      fallbackUrl: fallbackUrl,
      placeholderIcon: placeholderIcon ?? Icons.person,
      // Advanced interaction properties
      onHover: onHover,
      onFocus: onFocus,
      focusNode: focusNode,
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: enableFeedback,
      onDoubleTap: onDoubleTap,
      onLongPress: onLongPress,
      onSecondaryTap: onSecondaryTap,
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: callbackState,
      customCallbackHandlers: customCallbackHandlers,
      // Memory optimization for small avatars
      enableCaching: true,
      memCacheWidth: 32,
      memCacheHeight: 32,
    );
  }

  /// Creates an ImageWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the ImageWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "imageUrl": "https://example.com/image.jpg",
  ///   "width": 300,
  ///   "height": 200,
  ///   "fit": "cover",
  ///   "isCircular": true
  /// }
  /// ```
  factory ImageWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse box fit
    BoxFit parseBoxFit(dynamic fitValue) {
      if (fitValue == null) return BoxFit.cover;

      if (fitValue is String) {
        switch (fitValue.toLowerCase()) {
          case 'contain':
            return BoxFit.contain;
          case 'cover':
            return BoxFit.cover;
          case 'fill':
            return BoxFit.fill;
          case 'fitheight':
          case 'fit_height':
          case 'height':
            return BoxFit.fitHeight;
          case 'fitwidth':
          case 'fit_width':
          case 'width':
            return BoxFit.fitWidth;
          case 'none':
            return BoxFit.none;
          case 'scaledown':
          case 'scale_down':
            return BoxFit.scaleDown;
          default:
            return BoxFit.cover;
        }
      }

      return BoxFit.cover;
    }

    // Parse text align
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.center;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'center':
            return TextAlign.center;
          case 'end':
          case 'right':
            return TextAlign.end;
          case 'start':
          case 'left':
            return TextAlign.start;
          case 'justify':
            return TextAlign.justify;
          default:
            return TextAlign.center;
        }
      }

      return TextAlign.center;
    }

    // Parse alignment
    Alignment parseAlignment(dynamic alignValue) {
      if (alignValue == null) return Alignment.center;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'topleft':
          case 'top_left':
            return Alignment.topLeft;
          case 'topcenter':
          case 'top_center':
          case 'top':
            return Alignment.topCenter;
          case 'topright':
          case 'top_right':
            return Alignment.topRight;
          case 'centerleft':
          case 'center_left':
          case 'left':
            return Alignment.centerLeft;
          case 'center':
            return Alignment.center;
          case 'centerright':
          case 'center_right':
          case 'right':
            return Alignment.centerRight;
          case 'bottomleft':
          case 'bottom_left':
            return Alignment.bottomLeft;
          case 'bottomcenter':
          case 'bottom_center':
          case 'bottom':
            return Alignment.bottomCenter;
          case 'bottomright':
          case 'bottom_right':
            return Alignment.bottomRight;
          default:
            return Alignment.center;
        }
      }

      return Alignment.center;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return EdgeInsets.zero;
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return EdgeInsets.zero;
    }

    // Parse duration
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) {
        return const Duration(milliseconds: 300);
      }

      if (durationValue is int) {
        return Duration(milliseconds: durationValue);
      } else if (durationValue is Map<String, dynamic>) {
        final int milliseconds =
            (durationValue['milliseconds'] as num?)?.toInt() ?? 0;
        final int seconds = (durationValue['seconds'] as num?)?.toInt() ?? 0;
        final int minutes = (durationValue['minutes'] as num?)?.toInt() ?? 0;

        return Duration(
          milliseconds: milliseconds,
          seconds: seconds,
          minutes: minutes,
        );
      } else if (durationValue is String) {
        // Parse strings like "300ms", "2s", "1m"
        final RegExp durationRegExp = RegExp(r'(\d+)(ms|s|m)');
        final match = durationRegExp.firstMatch(durationValue);

        if (match != null) {
          final int value = int.parse(match.group(1)!);
          final String unit = match.group(2)!;

          switch (unit) {
            case 'ms':
              return Duration(milliseconds: value);
            case 's':
              return Duration(seconds: value);
            case 'm':
              return Duration(minutes: value);
            default:
              return const Duration(milliseconds: 300);
          }
        }
      }

      return const Duration(milliseconds: 300);
    }

    // Parse curve
    Curve parseCurve(dynamic curveValue) {
      if (curveValue == null) return Curves.easeIn;

      if (curveValue is String) {
        switch (curveValue.toLowerCase()) {
          case 'linear':
            return Curves.linear;
          case 'decelerate':
            return Curves.decelerate;
          case 'ease':
            return Curves.ease;
          case 'easein':
          case 'ease_in':
            return Curves.easeIn;
          case 'easeout':
          case 'ease_out':
            return Curves.easeOut;
          case 'easeinout':
          case 'ease_in_out':
            return Curves.easeInOut;
          case 'elasticin':
          case 'elastic_in':
            return Curves.elasticIn;
          case 'elasticout':
          case 'elastic_out':
            return Curves.elasticOut;
          case 'elasticinout':
          case 'elastic_in_out':
            return Curves.elasticInOut;
          case 'bouncein':
          case 'bounce_in':
            return Curves.bounceIn;
          case 'bounceout':
          case 'bounce_out':
            return Curves.bounceOut;
          case 'bounceinout':
          case 'bounce_in_out':
            return Curves.bounceInOut;
          default:
            return Curves.easeIn;
        }
      }

      return Curves.easeIn;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'image':
            return Icons.image;
          case 'photo':
            return Icons.photo;
          case 'picture':
            return Icons.picture_in_picture;
          case 'broken_image':
            return Icons.broken_image;
          case 'error':
            return Icons.error;
          case 'warning':
            return Icons.warning;
          case 'info':
            return Icons.info;
          case 'help':
            return Icons.help;
          case 'camera':
            return Icons.camera_alt;
          case 'gallery':
            return Icons.photo_library;
          case 'edit':
            return Icons.edit;
          case 'crop':
            return Icons.crop;
          case 'rotate':
            return Icons.rotate_right;
          case 'filter':
            return Icons.filter;
          case 'fullscreen':
            return Icons.fullscreen;
          case 'share':
            return Icons.share;
          case 'save':
            return Icons.save;
          case 'copy':
            return Icons.copy;
          case 'delete':
            return Icons.delete;
          case 'download':
            return Icons.download;
          case 'upload':
            return Icons.upload;
          case 'link':
            return Icons.link;
          case 'close':
            return Icons.close;
          case 'add':
            return Icons.add;
          case 'remove':
            return Icons.remove;
          case 'zoom_in':
            return Icons.zoom_in;
          case 'zoom_out':
            return Icons.zoom_out;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse string list
    List<String>? parseStringList(dynamic listValue) {
      if (listValue == null) return null;

      if (listValue is List) {
        return List<String>.from(listValue.map((e) => e.toString()));
      } else if (listValue is String) {
        // Handle comma-separated string
        return listValue.split(',').map((e) => e.trim()).toList();
      }

      return null;
    }

    // Parse color list
    List<Color>? parseColorList(dynamic listValue) {
      if (listValue == null) return null;

      if (listValue is List) {
        final List<Color> colors = [];
        for (final item in listValue) {
          final color = parseColor(item);
          if (color != null) {
            colors.add(color);
          }
        }
        return colors.isNotEmpty ? colors : null;
      }

      return null;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final color = parseColor(styleValue['color']);
        final fontSize =
            styleValue['fontSize'] != null
                ? (styleValue['fontSize'] as num).toDouble()
                : null;
        final fontWeight =
            styleValue['fontWeight'] != null
                ? (styleValue['fontWeight'] == 'bold'
                    ? FontWeight.bold
                    : styleValue['fontWeight'] == 'normal'
                    ? FontWeight.normal
                    : FontWeight.normal)
                : null;
        final fontStyle =
            styleValue['fontStyle'] != null
                ? (styleValue['fontStyle'] == 'italic'
                    ? FontStyle.italic
                    : FontStyle.normal)
                : null;
        final letterSpacing =
            styleValue['letterSpacing'] != null
                ? (styleValue['letterSpacing'] as num).toDouble()
                : null;
        final wordSpacing =
            styleValue['wordSpacing'] != null
                ? (styleValue['wordSpacing'] as num).toDouble()
                : null;
        final height =
            styleValue['height'] != null
                ? (styleValue['height'] as num).toDouble()
                : null;
        final decoration =
            styleValue['decoration'] != null
                ? (styleValue['decoration'] == 'underline'
                    ? TextDecoration.underline
                    : styleValue['decoration'] == 'lineThrough'
                    ? TextDecoration.lineThrough
                    : styleValue['decoration'] == 'overline'
                    ? TextDecoration.overline
                    : null)
                : null;
        final fontFamily = styleValue['fontFamily'] as String?;

        return TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          wordSpacing: wordSpacing,
          height: height,
          decoration: decoration,
          fontFamily: fontFamily,
        );
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onLoadComplete'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLoadComplete'] = json['onLoadComplete'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onSecondaryTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onSecondaryTap'] = json['onSecondaryTap'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Parse Image-specific configuration
    Map<String, dynamic>? imageConfig;
    bool useJsonImageConfig = json['useJsonImageConfig'] as bool? ?? false;

    if (json['imageConfig'] != null) {
      if (json['imageConfig'] is Map) {
        imageConfig = Map<String, dynamic>.from(json['imageConfig'] as Map);
        useJsonImageConfig = true;
      } else if (json['imageConfig'] is String) {
        try {
          imageConfig =
              jsonDecode(json['imageConfig'] as String) as Map<String, dynamic>;
          useJsonImageConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return ImageWidget(
      // Source properties
      imageUrl: json['imageUrl'] as String?,
      assetPath: json['assetPath'] as String?,
      imageFile:
          json['imageFile'] != null ? File(json['imageFile'] as String) : null,
      placeholderIcon: parseIconData(json['placeholderIcon']),
      imageProvider: null, // Cannot be created from JSON directly
      // Content properties
      label: json['label'] as String?,
      caption: json['caption'] as String?,
      description: json['description'] as String?,
      labelStyle: parseTextStyle(json['labelStyle']),
      captionStyle: parseTextStyle(json['captionStyle']),
      descriptionStyle: parseTextStyle(json['descriptionStyle']),
      showLabel: json['showLabel'] as bool? ?? false,
      showCaption: json['showCaption'] as bool? ?? false,
      showDescription: json['showDescription'] as bool? ?? false,
      textAlign: parseTextAlign(json['textAlign']),

      // Appearance properties
      width:
          json['width'] != null
              ? (json['width'].toString().toLowerCase() == 'infinity'
                  ? double.infinity
                  : (json['width'] as num).toDouble())
              : null,
      height:
          json['height'] != null
              ? (json['height'].toString().toLowerCase() == 'infinity'
                  ? double.infinity
                  : (json['height'] as num).toDouble())
              : null,
      fit: parseBoxFit(json['fit']),
      backgroundColor:
          parseColor(json['backgroundColor']) ?? Colors.transparent,
      isCircular: json['isCircular'] as bool? ?? false,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 8.0,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: parseColor(json['borderColor']) ?? Colors.grey,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 4.0,
      shadowColor: parseColor(json['shadowColor']) ?? Colors.black,
      isCard: json['isCard'] as bool? ?? false,
      tintColor: parseColor(json['tintColor']),
      colorBlendMode:
          json['colorBlendMode'] != null
              ? BlendMode.values.firstWhere(
                (mode) =>
                    mode.toString().toLowerCase() ==
                    'blendmode.${json['colorBlendMode']}'.toLowerCase(),
                orElse: () => BlendMode.srcIn,
              )
              : null,

      // Layout properties
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      alignment: parseAlignment(json['alignment']),

      // Behavior properties
      isTappable: json['isTappable'] as bool? ?? false,
      isZoomable: json['isZoomable'] as bool? ?? false,
      openInDialog: json['openInDialog'] as bool? ?? false,
      showPopupOnClick: json['showPopupOnClick'] as bool? ?? true,
      enableHero: json['enableHero'] as bool? ?? false,
      heroTag: json['heroTag'] as String?,

      // Loading properties
      fadeInDuration: parseDuration(json['fadeInDuration']),
      fadeInCurve: parseCurve(json['fadeInCurve']),
      showLoadingIndicator: json['showLoadingIndicator'] as bool? ?? true,
      fallbackAssetPath: json['fallbackAssetPath'] as String?,
      fallbackUrl: json['fallbackUrl'] as String?,

      // Advanced interaction properties
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Image-specific properties
      enableLazyLoading: json['enableLazyLoading'] as bool? ?? true,
      enableCaching: json['enableCaching'] as bool? ?? true,
      cacheKey: json['cacheKey'] as String?,
      memCacheWidth: json['memCacheWidth'] as int?,
      memCacheHeight: json['memCacheHeight'] as int?,
      enableGestureTransformations:
          json['enableGestureTransformations'] as bool? ?? false,
      minScale:
          json['minScale'] != null ? (json['minScale'] as num).toDouble() : 0.5,
      maxScale:
          json['maxScale'] != null ? (json['maxScale'] as num).toDouble() : 3.0,
      enablePinchZoom: json['enablePinchZoom'] as bool? ?? false,
      enablePanning: json['enablePanning'] as bool? ?? false,
      enableDoubleTapZoom: json['enableDoubleTapZoom'] as bool? ?? false,
      enableRotation: json['enableRotation'] as bool? ?? false,
      showLoadingProgress: json['showLoadingProgress'] as bool? ?? true,
      showDownloadProgress: json['showDownloadProgress'] as bool? ?? true,
      enableFullscreenView: json['enableFullscreenView'] as bool? ?? false,
      enableShareOption: json['enableShareOption'] as bool? ?? false,
      enableSaveOption: json['enableSaveOption'] as bool? ?? false,
      enableCopyOption: json['enableCopyOption'] as bool? ?? false,
      enableCropOption: json['enableCropOption'] as bool? ?? false,
      enableFilterOption: json['enableFilterOption'] as bool? ?? false,
      availableFilters: parseStringList(json['availableFilters']),
      initialFilter: json['initialFilter'] as String?,
      enableAnnotation: json['enableAnnotation'] as bool? ?? false,
      annotationColors: parseColorList(json['annotationColors']),
      annotationStrokeWidth:
          json['annotationStrokeWidth'] != null
              ? (json['annotationStrokeWidth'] as num).toDouble()
              : 2.0,
      enableImagePicker: json['enableImagePicker'] as bool? ?? false,
      enableCameraCapture: json['enableCameraCapture'] as bool? ?? false,
      enableGalleryPicker: json['enableGalleryPicker'] as bool? ?? false,
      enableUrlInput: json['enableUrlInput'] as bool? ?? false,
      enableDragDrop: json['enableDragDrop'] as bool? ?? false,
      allowedExtensions: parseStringList(json['allowedExtensions']),
      maxFileSizeBytes: json['maxFileSizeBytes'] as int?,

      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : null,
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,

      // Image-specific JSON configuration
      useJsonImageConfig: useJsonImageConfig,
      imageConfig: imageConfig,
    );
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert color to hex string
    String? colorToHex(Color? color) {
      if (color == null) return null;

      // Use a simple approach that works with all Flutter versions
      final hexString = color.toString();
      // Color(0xAARRGGBB) format -> extract RRGGBB part
      final hex = hexString.replaceAll('Color(0x', '').replaceAll(')', '');
      return '#${hex.substring(2)}'; // Skip alpha channel
    }

    // Convert box fit to string
    String? boxFitToString(BoxFit? fit) {
      if (fit == null) return null;

      switch (fit) {
        case BoxFit.contain:
          return 'contain';
        case BoxFit.cover:
          return 'cover';
        case BoxFit.fill:
          return 'fill';
        case BoxFit.fitHeight:
          return 'fitHeight';
        case BoxFit.fitWidth:
          return 'fitWidth';
        case BoxFit.none:
          return 'none';
        case BoxFit.scaleDown:
          return 'scaleDown';
      }
    }

    // Convert text align to string
    String? textAlignToString(TextAlign? align) {
      if (align == null) return null;

      switch (align) {
        case TextAlign.center:
          return 'center';
        case TextAlign.end:
          return 'end';
        case TextAlign.left:
          return 'left';
        case TextAlign.right:
          return 'right';
        case TextAlign.justify:
          return 'justify';
        case TextAlign.start:
          return 'start';
      }
    }

    // Convert alignment to string
    String? alignmentToString(Alignment? alignment) {
      if (alignment == null) return null;

      if (alignment == Alignment.topLeft) return 'topLeft';
      if (alignment == Alignment.topCenter) return 'topCenter';
      if (alignment == Alignment.topRight) return 'topRight';
      if (alignment == Alignment.centerLeft) return 'centerLeft';
      if (alignment == Alignment.center) return 'center';
      if (alignment == Alignment.centerRight) return 'centerRight';
      if (alignment == Alignment.bottomLeft) return 'bottomLeft';
      if (alignment == Alignment.bottomCenter) return 'bottomCenter';
      if (alignment == Alignment.bottomRight) return 'bottomRight';

      return null;
    }

    // Convert edge insets to map
    Map<String, dynamic>? edgeInsetsToMap(EdgeInsetsGeometry? insets) {
      if (insets == null) return null;

      if (insets is EdgeInsets) {
        if (insets.left == insets.top &&
            insets.left == insets.right &&
            insets.left == insets.bottom) {
          return {'all': insets.left};
        } else if (insets.left == insets.right && insets.top == insets.bottom) {
          return {'horizontal': insets.left, 'vertical': insets.top};
        } else {
          return {
            'left': insets.left,
            'top': insets.top,
            'right': insets.right,
            'bottom': insets.bottom,
          };
        }
      }
      return null;
    }

    // Convert duration to map
    Map<String, dynamic>? durationToMap(Duration? duration) {
      if (duration == null) return null;

      return {'milliseconds': duration.inMilliseconds};
    }

    // Convert curve to string
    String? curveToString(Curve? curve) {
      if (curve == null) return null;

      if (curve == Curves.linear) return 'linear';
      if (curve == Curves.decelerate) return 'decelerate';
      if (curve == Curves.ease) return 'ease';
      if (curve == Curves.easeIn) return 'easeIn';
      if (curve == Curves.easeOut) return 'easeOut';
      if (curve == Curves.easeInOut) return 'easeInOut';
      if (curve == Curves.elasticIn) return 'elasticIn';
      if (curve == Curves.elasticOut) return 'elasticOut';
      if (curve == Curves.elasticInOut) return 'elasticInOut';
      if (curve == Curves.bounceIn) return 'bounceIn';
      if (curve == Curves.bounceOut) return 'bounceOut';
      if (curve == Curves.bounceInOut) return 'bounceInOut';

      return 'easeIn'; // Default
    }

    // Convert icon data to string
    String? iconDataToString(IconData? icon) {
      if (icon == null) return null;

      if (icon == Icons.image) return 'image';
      if (icon == Icons.photo) return 'photo';
      if (icon == Icons.picture_in_picture) return 'picture';
      if (icon == Icons.broken_image) return 'broken_image';
      if (icon == Icons.error) return 'error';
      if (icon == Icons.warning) return 'warning';
      if (icon == Icons.info) return 'info';
      if (icon == Icons.help) return 'help';
      if (icon == Icons.camera_alt) return 'camera';
      if (icon == Icons.photo_library) return 'gallery';
      if (icon == Icons.edit) return 'edit';
      if (icon == Icons.crop) return 'crop';
      if (icon == Icons.rotate_right) return 'rotate';
      if (icon == Icons.filter) return 'filter';
      if (icon == Icons.fullscreen) return 'fullscreen';
      if (icon == Icons.share) return 'share';
      if (icon == Icons.save) return 'save';
      if (icon == Icons.copy) return 'copy';
      if (icon == Icons.delete) return 'delete';
      if (icon == Icons.download) return 'download';
      if (icon == Icons.upload) return 'upload';
      if (icon == Icons.link) return 'link';
      if (icon == Icons.close) return 'close';
      if (icon == Icons.add) return 'add';
      if (icon == Icons.remove) return 'remove';
      if (icon == Icons.zoom_in) return 'zoom_in';
      if (icon == Icons.zoom_out) return 'zoom_out';

      return null;
    }

    // Convert text style to map
    Map<String, dynamic>? textStyleToMap(TextStyle? style) {
      if (style == null) return null;

      final Map<String, dynamic> map = {};

      if (style.color != null) {
        map['color'] = colorToHex(style.color);
      }

      if (style.fontSize != null) {
        map['fontSize'] = style.fontSize;
      }

      if (style.fontWeight != null) {
        if (style.fontWeight == FontWeight.bold) {
          map['fontWeight'] = 'bold';
        } else if (style.fontWeight == FontWeight.normal) {
          map['fontWeight'] = 'normal';
        } else {
          map['fontWeight'] =
              (style.fontWeight?.index ?? 3) * 100; // Convert to numeric weight
        }
      }

      if (style.fontStyle != null) {
        map['fontStyle'] =
            style.fontStyle == FontStyle.italic ? 'italic' : 'normal';
      }

      if (style.letterSpacing != null) {
        map['letterSpacing'] = style.letterSpacing;
      }

      if (style.wordSpacing != null) {
        map['wordSpacing'] = style.wordSpacing;
      }

      if (style.height != null) {
        map['height'] = style.height;
      }

      if (style.decoration != null) {
        if (style.decoration == TextDecoration.underline) {
          map['decoration'] = 'underline';
        } else if (style.decoration == TextDecoration.lineThrough) {
          map['decoration'] = 'lineThrough';
        } else if (style.decoration == TextDecoration.overline) {
          map['decoration'] = 'overline';
        }
      }

      if (style.fontFamily != null) {
        map['fontFamily'] = style.fontFamily;
      }

      return map.isNotEmpty ? map : null;
    }

    // Convert blend mode to string
    String? blendModeToString(BlendMode? blendMode) {
      if (blendMode == null) return null;

      // Extract the name from the enum
      return blendMode.toString().split('.').last;
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      // Source properties
      'imageUrl': imageUrl,
      'assetPath': assetPath,
      'imageFile': imageFile?.path,
      'placeholderIcon': iconDataToString(placeholderIcon),

      // Content properties
      'label': label,
      'caption': caption,
      'description': description,
      'labelStyle': textStyleToMap(labelStyle),
      'captionStyle': textStyleToMap(captionStyle),
      'descriptionStyle': textStyleToMap(descriptionStyle),
      'showLabel': showLabel,
      'showCaption': showCaption,
      'showDescription': showDescription,
      'textAlign': textAlignToString(textAlign),

      // Appearance properties
      'width': width == double.infinity ? 'infinity' : width,
      'height': height == double.infinity ? 'infinity' : height,
      'fit': boxFitToString(fit),
      'backgroundColor': colorToHex(backgroundColor),
      'isCircular': isCircular,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'shadowColor': colorToHex(shadowColor),
      'isCard': isCard,
      'tintColor': colorToHex(tintColor),
      'colorBlendMode': blendModeToString(colorBlendMode),

      // Layout properties
      'padding': edgeInsetsToMap(padding),
      'margin': edgeInsetsToMap(margin),
      'alignment': alignmentToString(alignment),

      // Behavior properties
      'isTappable': isTappable,
      'isZoomable': isZoomable,
      'openInDialog': openInDialog,
      'showPopupOnClick': showPopupOnClick,
      'enableHero': enableHero,
      'heroTag': heroTag,

      // Loading properties
      'fadeInDuration': durationToMap(fadeInDuration),
      'fadeInCurve': curveToString(fadeInCurve),
      'showLoadingIndicator': showLoadingIndicator,
      'fallbackAssetPath': fallbackAssetPath,
      'fallbackUrl': fallbackUrl,

      // Advanced interaction properties
      'hoverColor': colorToHex(hoverColor),
      'focusColor': colorToHex(focusColor),
      'enableFeedback': enableFeedback,

      // Image-specific properties
      'enableLazyLoading': enableLazyLoading,
      'enableCaching': enableCaching,
      'cacheKey': cacheKey,
      'memCacheWidth': memCacheWidth,
      'memCacheHeight': memCacheHeight,
      'enableGestureTransformations': enableGestureTransformations,
      'minScale': minScale,
      'maxScale': maxScale,
      'enablePinchZoom': enablePinchZoom,
      'enablePanning': enablePanning,
      'enableDoubleTapZoom': enableDoubleTapZoom,
      'enableRotation': enableRotation,
      'showLoadingProgress': showLoadingProgress,
      'showDownloadProgress': showDownloadProgress,
      'enableFullscreenView': enableFullscreenView,
      'enableShareOption': enableShareOption,
      'enableSaveOption': enableSaveOption,
      'enableCopyOption': enableCopyOption,
      'enableCropOption': enableCropOption,
      'enableFilterOption': enableFilterOption,
      'availableFilters': availableFilters,
      'initialFilter': initialFilter,
      'enableAnnotation': enableAnnotation,
      'annotationColors':
          annotationColors?.map((color) => colorToHex(color)).toList(),
      'annotationStrokeWidth': annotationStrokeWidth,
      'enableImagePicker': enableImagePicker,
      'enableCameraCapture': enableCameraCapture,
      'enableGalleryPicker': enableGalleryPicker,
      'enableUrlInput': enableUrlInput,
      'enableDragDrop': enableDragDrop,
      'allowedExtensions': allowedExtensions,
      'maxFileSizeBytes': maxFileSizeBytes,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonImageConfig': useJsonImageConfig,
    };

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    // Add image config if it exists
    if (imageConfig != null && imageConfig!.isNotEmpty) {
      json['imageConfig'] = imageConfig;
    }

    // Add callback state if it exists
    if (callbackState != null && callbackState!.isNotEmpty) {
      json['callbackState'] = callbackState;
    }

    // Remove null values
    json.removeWhere((key, value) => value == null);

    return json;
  }

  @override
  State<ImageWidget> createState() => _ImageWidgetState();
}

class _ImageWidgetState extends State<ImageWidget>
    with SingleTickerProviderStateMixin {
  bool _isLoaded = false;
  bool _hasError = false;
  bool _isHovered = false;
  bool _isFocused = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Map to store dynamic state for callbacks
  final Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // Map to store image-specific configuration from JSON
  Map<String, dynamic>? _imageConfig;

  // Focus node for handling focus events
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: widget.fadeInDuration,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: widget.fadeInCurve),
    );

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState.addAll(widget.callbackState!);
    }

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Parse image-specific configuration if provided
    if (widget.imageConfig != null) {
      _imageConfig = Map<String, dynamic>.from(widget.imageConfig!);

      // Apply initial image configuration if enabled
      if (widget.useJsonImageConfig) {
        _applyImageConfig();
      }
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  @override
  void dispose() {
    // Clean up focus node if we created it
    if (widget.focusNode == null) {
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    _animationController.dispose();
    super.dispose();
  }

  /// Handles focus changes
  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;

      // Call the onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }

      // Execute JSON callback if defined
      _executeJsonCallback('onFocus', _isFocused);
    });
  }

  /// Handles hover changes
  void _handleHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call the onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute JSON callback if defined
      _executeJsonCallback('onHover', isHovered);
    });
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['isLoaded'] = _isLoaded;
      _callbackState['hasError'] = _hasError;
      _callbackState['isHovered'] = _isHovered;
      _callbackState['isFocused'] = _isFocused;

      // Add image-specific state
      _callbackState['imageUrl'] = widget.imageUrl;
      _callbackState['assetPath'] = widget.assetPath;
      _callbackState['imageFilePath'] = widget.imageFile?.path;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue =
            widget.imageUrl ?? widget.assetPath ?? widget.imageFile?.path;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the image
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply validation rules to image
        // Not fully implemented in this example
      }
    }
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  /// Applies image-specific configuration to the widget
  ///
  /// This method applies image-specific rules defined in the JSON configuration.
  void _applyImageConfig() {
    if (_imageConfig == null || !widget.useJsonImageConfig) return;

    // Example: Apply image-specific configuration
    if (_imageConfig!.containsKey('filters')) {
      // This would be implemented to configure image filters
      // Not fully implemented in this example
    }

    if (_imageConfig!.containsKey('transformations')) {
      // This would be implemented to configure image transformations
      // Not fully implemented in this example
    }

    if (_imageConfig!.containsKey('caching')) {
      // This would be implemented to configure image caching
      // Not fully implemented in this example
    }
  }

  void _handleTap() {
    // Always show image dialog when tapped, unless a custom onTap is provided
    // if (widget.onTap != null) {
    //   widget.onTap!();
    // } else {
    //   // Show popup when image is clicked (default behavior)
    //   _showImageDialog();
    // }
    _showImageDialog();
    // Execute JSON callback if defined
    _executeJsonCallback('onTap');
  }

  void _showImageDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Stack(
              children: [
                // Main image container
                Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Image header with close button
                      Container(
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                widget.label ?? 'Image Preview',
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(fontWeight: FontWeight.w600),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(Icons.close),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.grey[200],
                                foregroundColor: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Image content
                      Flexible(
                        child: Container(
                          padding: const EdgeInsets.all(16.0),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildImage(fit: BoxFit.contain),
                          ),
                        ),
                      ),
                      // Caption and description
                      if (widget.caption != null || widget.description != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(12),
                              bottomRight: Radius.circular(12),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (widget.caption != null)
                                Text(
                                  widget.caption!,
                                  style:
                                      widget.captionStyle ??
                                      Theme.of(
                                        context,
                                      ).textTheme.bodyLarge?.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                  textAlign: widget.textAlign,
                                ),
                              if (widget.description != null) ...[
                                if (widget.caption != null)
                                  const SizedBox(height: 8),
                                Text(
                                  widget.description!,
                                  style:
                                      widget.descriptionStyle ??
                                      Theme.of(context).textTheme.bodyMedium
                                          ?.copyWith(color: Colors.grey[600]),
                                  textAlign: widget.textAlign,
                                ),
                              ],
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildLoadingIndicator() {
    return widget.loadingWidget ??
        Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        );
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ??
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: Colors.red[300], size: 40),
              const SizedBox(height: 8),
              Text(
                'Failed to load image',
                style: TextStyle(color: Colors.red[300]),
              ),
            ],
          ),
        );
  }

  Widget _buildImage({BoxFit? fit}) {
    Widget imageWidget;

    if (_hasError) {
      if (widget.fallbackAssetPath != null) {
        imageWidget = Image.asset(
          widget.fallbackAssetPath!,
          fit: fit ?? widget.fit,
          width: widget.width,
          height: widget.height,
          color: widget.tintColor,
          colorBlendMode: widget.colorBlendMode,
        );
      } else if (widget.fallbackUrl != null) {
        imageWidget = Image.network(
          widget.fallbackUrl!,
          fit: fit ?? widget.fit,
          width: widget.width,
          height: widget.height,
          color: widget.tintColor,
          colorBlendMode: widget.colorBlendMode,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return _buildLoadingIndicator();
          },
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorWidget();
          },
        );
      } else {
        imageWidget = _buildErrorWidget();
      }
    } else if (widget.imageUrl != null) {
      bool isFirstBuild = true;
      imageWidget = Image.network(
        widget.imageUrl!,
        fit: fit ?? widget.fit,
        width: widget.width,
        height: widget.height,
        color: widget.tintColor,
        colorBlendMode: widget.colorBlendMode,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            // Image loaded successfully
            if (isFirstBuild) {
              isFirstBuild = false;
              // Schedule state update after build is complete
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted && !_isLoaded) {
                  setState(() {
                    _isLoaded = true;
                    _hasError = false;
                  });
                  _animationController.forward();
                  if (widget.onLoadComplete != null) {
                    widget.onLoadComplete!(true);
                  }
                }
              });
            }
            return child;
          }
          return _buildLoadingIndicator();
        },
        errorBuilder: (context, error, stackTrace) {
          // Image failed to load
          if (isFirstBuild) {
            isFirstBuild = false;
            // Schedule state update after build is complete
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && !_isLoaded) {
                setState(() {
                  _isLoaded = true;
                  _hasError = true;
                });
                if (widget.onLoadComplete != null) {
                  widget.onLoadComplete!(false);
                }
              }
            });
          }
          return _buildErrorWidget();
        },
      );
    } else if (widget.assetPath != null) {
      try {
        bool isFirstBuild = true;
        imageWidget = Image.asset(
          widget.assetPath!,
          fit: fit ?? widget.fit,
          width: widget.width,
          height: widget.height,
          color: widget.tintColor,
          colorBlendMode: widget.colorBlendMode,
          errorBuilder: (context, error, stackTrace) {
            // Asset failed to load
            if (isFirstBuild) {
              isFirstBuild = false;
              // Schedule state update after build is complete
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted && !_isLoaded) {
                  setState(() {
                    _isLoaded = true;
                    _hasError = true;
                  });
                  if (widget.onLoadComplete != null) {
                    widget.onLoadComplete!(false);
                  }
                }
              });
            }
            return _buildErrorWidget();
          },
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            if (wasSynchronouslyLoaded || frame != null) {
              // Asset loaded successfully
              if (isFirstBuild) {
                isFirstBuild = false;
                // Schedule state update after build is complete
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted && !_isLoaded) {
                    setState(() {
                      _isLoaded = true;
                      _hasError = false;
                    });
                    _animationController.forward();
                    if (widget.onLoadComplete != null) {
                      widget.onLoadComplete!(true);
                    }
                  }
                });
              }
              return child;
            } else {
              return Stack(
                alignment: Alignment.center,
                children: [
                  if (widget.showLoadingIndicator) _buildLoadingIndicator(),
                  Opacity(opacity: 0.0, child: child),
                ],
              );
            }
          },
        );
      } catch (e) {
        // Exception during asset loading
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_isLoaded) {
            setState(() {
              _isLoaded = true;
              _hasError = true;
            });
            if (widget.onLoadComplete != null) {
              widget.onLoadComplete!(false);
            }
          }
        });
        imageWidget = _buildErrorWidget();
      }
    } else if (widget.imageFile != null) {
      try {
        bool isFirstBuild = true;
        imageWidget = Image.file(
          widget.imageFile!,
          fit: fit ?? widget.fit,
          width: widget.width,
          height: widget.height,
          color: widget.tintColor,
          colorBlendMode: widget.colorBlendMode,
          errorBuilder: (context, error, stackTrace) {
            // File failed to load
            if (isFirstBuild) {
              isFirstBuild = false;
              // Schedule state update after build is complete
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted && !_isLoaded) {
                  setState(() {
                    _isLoaded = true;
                    _hasError = true;
                  });
                  if (widget.onLoadComplete != null) {
                    widget.onLoadComplete!(false);
                  }
                }
              });
            }
            return _buildErrorWidget();
          },
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            if (wasSynchronouslyLoaded || frame != null) {
              // File loaded successfully
              if (isFirstBuild) {
                isFirstBuild = false;
                // Schedule state update after build is complete
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted && !_isLoaded) {
                    setState(() {
                      _isLoaded = true;
                      _hasError = false;
                    });
                    _animationController.forward();
                    if (widget.onLoadComplete != null) {
                      widget.onLoadComplete!(true);
                    }
                  }
                });
              }
              return child;
            } else {
              return Stack(
                alignment: Alignment.center,
                children: [
                  if (widget.showLoadingIndicator) _buildLoadingIndicator(),
                  Opacity(opacity: 0.0, child: child),
                ],
              );
            }
          },
        );
      } catch (e) {
        // Exception during file loading
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_isLoaded) {
            setState(() {
              _isLoaded = true;
              _hasError = true;
            });
            if (widget.onLoadComplete != null) {
              widget.onLoadComplete!(false);
            }
          }
        });
        imageWidget = _buildErrorWidget();
      }
    } else if (widget.imageProvider != null) {
      bool isFirstBuild = true;
      imageWidget = Image(
        image: widget.imageProvider!,
        fit: fit ?? widget.fit,
        width: widget.width,
        height: widget.height,
        color: widget.tintColor,
        colorBlendMode: widget.colorBlendMode,
        errorBuilder: (context, error, stackTrace) {
          // Provider failed to load
          if (isFirstBuild) {
            isFirstBuild = false;
            // Schedule state update after build is complete
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && !_isLoaded) {
                setState(() {
                  _isLoaded = true;
                  _hasError = true;
                });
                if (widget.onLoadComplete != null) {
                  widget.onLoadComplete!(false);
                }
              }
            });
          }
          return _buildErrorWidget();
        },
        frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
          if (wasSynchronouslyLoaded || frame != null) {
            // Provider loaded successfully
            if (isFirstBuild) {
              isFirstBuild = false;
              // Schedule state update after build is complete
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted && !_isLoaded) {
                  setState(() {
                    _isLoaded = true;
                    _hasError = false;
                  });
                  _animationController.forward();
                  if (widget.onLoadComplete != null) {
                    widget.onLoadComplete!(true);
                  }
                }
              });
            }
            return child;
          } else {
            return Stack(
              alignment: Alignment.center,
              children: [
                if (widget.showLoadingIndicator) _buildLoadingIndicator(),
                Opacity(opacity: 0.0, child: child),
              ],
            );
          }
        },
      );
    } else if (widget.placeholderIcon != null) {
      imageWidget = Icon(
        widget.placeholderIcon,
        size: widget.width != null ? widget.width! / 2 : 48.0,
        color: widget.tintColor ?? Colors.grey[400],
      );
      // Schedule state update after build is complete
      if (!_isLoaded) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_isLoaded) {
            setState(() {
              _isLoaded = true;
              _hasError = false;
            });
            _animationController.forward();
            if (widget.onLoadComplete != null) {
              widget.onLoadComplete!(true);
            }
          }
        });
      }
    } else {
      imageWidget = _buildErrorWidget();
      // Schedule state update after build is complete
      if (!_isLoaded) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_isLoaded) {
            setState(() {
              _isLoaded = true;
              _hasError = true;
            });
            if (widget.onLoadComplete != null) {
              widget.onLoadComplete!(false);
            }
          }
        });
      }
    }

    // Apply fade animation if image is loaded
    if (_isLoaded && !_hasError) {
      imageWidget = FadeTransition(opacity: _fadeAnimation, child: imageWidget);
    }

    // Apply hero animation if enabled
    if (widget.enableHero) {
      final heroTag =
          widget.heroTag ??
          widget.imageUrl ??
          widget.assetPath ??
          'image-${DateTime.now().millisecondsSinceEpoch}';
      imageWidget = Hero(tag: heroTag, child: imageWidget);
    }

    return imageWidget;
  }

  Widget _buildImageWithContainer() {
    final imageWithContainer = Container(
      width: 120,
      // widget.width,
      height: 120, 
      //widget.height,
      padding: widget.padding,
      margin: widget.margin,
      alignment: widget.alignment,
      // decoration: BoxDecoration(
      //   color: widget.backgroundColor,
      //   borderRadius:
      //       widget.isCircular
      //           ? BorderRadius.circular(1000) // Large value to ensure circle
      //           : BorderRadius.circular(widget.borderRadius),
      //   border:
      //       widget.hasBorder
      //           ? Border.all(
      //             color: widget.borderColor,
      //             width: widget.borderWidth,
      //           )
      //           : null,
      //   boxShadow:
      //       widget.hasShadow
      //           ? [
      //             BoxShadow(
      //               color: widget.shadowColor.withAlpha(50),
      //               blurRadius: widget.elevation,
      //               offset: Offset(0, widget.elevation / 2),
      //             ),
      //           ]
      //           : null,
      //   shape: widget.isCircular ? BoxShape.circle : BoxShape.rectangle,
      // ),
      child: ClipRRect(
        borderRadius:
            widget.isCircular
                ? BorderRadius.circular(16) // Large value to ensure circle
                : BorderRadius.circular(16),
        child: _buildImage(),
      ),
    );

    // Apply card if needed
    if (widget.isCard) {
      return Card(
        elevation: widget.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        child: imageWithContainer,
      );
    }

    return imageWithContainer;
  }

  Widget _buildImageWithText() {
    final List<Widget> children = [];

    // Add label before image if needed
    if (widget.showLabel && widget.label != null) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(
            widget.label!,
            style: widget.labelStyle ?? Theme.of(context).textTheme.titleMedium,
            textAlign: widget.textAlign,
          ),
        ),
      );
    }

    // Add image
    children.add(_buildImageWithContainer());

    // Add caption after image if needed
    if (widget.showCaption && widget.caption != null) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          // child: Text(
          //   widget.caption!,
          //   style:
          //       widget.captionStyle ?? Theme.of(context).textTheme.bodyMedium,
          //   textAlign: widget.textAlign,
          // ),
        ),
      );
    }

    // Add description after caption if needed
    if (widget.showDescription && widget.description != null) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: Text(
            widget.description!,
            style:
                widget.descriptionStyle ??
                Theme.of(context).textTheme.bodySmall,
            textAlign: widget.textAlign,
          ),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: children,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget finalWidget = _buildImageWithText();

    // Add hover detection
    final hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      cursor: SystemMouseCursors.click, // Always show click cursor since images are tappable
      child: finalWidget,
    );

    // Add gesture detection for tap, double tap, and long press
    return GestureDetector(
      onTap: _handleTap, // Always enable tap to show image dialog
      onDoubleTap:
          widget.onDoubleTap != null
              ? () {
                widget.onDoubleTap!();
                _executeJsonCallback('onDoubleTap');
              }
              : null,
      onLongPress:
          widget.onLongPress != null
              ? () {
                widget.onLongPress!();
                _executeJsonCallback('onLongPress');
              }
              : null,
      onSecondaryTap:
          widget.onSecondaryTap != null
              ? () {
                widget.onSecondaryTap!();
                _executeJsonCallback('onSecondaryTap');
              }
              : null,
      child: Focus(focusNode: _focusNode, child: hoverWidget),
    );
  }
}
