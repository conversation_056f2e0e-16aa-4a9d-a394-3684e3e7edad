import 'package:flutter/material.dart';
import 'package:nsl/models/global_objective.dart';
import 'package:nsl/screens/workflow_detail_screen_fixedd.dart';
import 'package:provider/provider.dart';
import '../../ui_components/demo/component_showcase.dart';
import '../../providers/transaction_provider.dart';
import '../../utils/input_value_store.dart';

/// A screen that showcases all UI components in the library for web/desktop devices.
class WebComponentsScreen extends StatelessWidget {
  const WebComponentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Web navigation sidebar
          // const WebNavigationSidebar(currentScreen: 'components'),

          // Main content area
          Expanded(
            child: Column(
              children: [
                // App bar
                Container(
                  height: 64,
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 2,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Text(
                        'UI Components',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                       const Spacer(),
                      Padding(
                        padding: const EdgeInsets.only(right:25.0),
                        child: ElevatedButton(
                          onPressed: () {
                            // Clear any existing input values
                            final inputStore = InputValueStore();
                            inputStore.clear();

                            // Get the selected objective from the transaction provider
                            final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

                            // Create a sample objective for testing
                            final objective = GlobalObjective(
                              objectiveId: '1',
                              name: 'Test Workflow',
                              tenantId: 't001',
                              version: '1.0',
                              status: 'active',
                            );

                            // Navigate to workflow detail screen with the objective
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => WorkflowDetailScreenFixedd(
                                  objective: objective,
                                ),
                              ),
                            );
                          },
                          child: const Text('Create'),
                        ),
                      ),
                    ],
                  ),
                ),

                // Component showcase
                const Expanded(
                  child: ComponentShowcase(requireAppBar: false),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
