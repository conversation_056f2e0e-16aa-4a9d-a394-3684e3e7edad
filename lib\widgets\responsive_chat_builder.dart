import 'package:flutter/material.dart';
import '../screens/chat_screen.dart';
import '../screens/web/web_chat_screen.dart';
import 'base_responsive_builder.dart';

class ResponsiveChatBuilder extends BaseResponsiveBuilder {
  const ResponsiveChatBuilder({super.key}) : super(builderKey: 'chat');

  @override
  BaseResponsiveBuilderState<BaseResponsiveBuilder> createState() => _ResponsiveChatBuilderState();

  @override
  Widget buildWebLayout(BuildContext context, String? currentRoute) {
    return const WebChatScreen();
  }

  @override
  Widget buildMobileLayout(BuildContext context, String? currentRoute) {
    return const ChatScreen();
  }
}

class _ResponsiveChatBuilderState extends BaseResponsiveBuilderState<ResponsiveChatBuilder> {}