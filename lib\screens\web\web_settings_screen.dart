import 'package:flutter/material.dart';
import 'package:nsl/screens/auth/profile_screen.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../utils/logger.dart';

class WebSettingsScreen extends StatefulWidget {
  const WebSettingsScreen({super.key});

  @override
  State<WebSettingsScreen> createState() => _WebSettingsScreenState();
}

class _WebSettingsScreenState extends State<WebSettingsScreen> {
  String _appVersion = '1.0.0'; // Default version
  String _selectedCategory = 'Appearance'; // Default selected category

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
      Logger.info('App version: $_appVersion');
    } catch (e) {
      Logger.error('Error getting app version: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface.withAlpha(245),
      body: Row(
        children: [
          // Navigation Sidebar
          // const WebNavigationSidebar(currentScreen: 'settings'),

          // Main Content Area
          Expanded(
            child: _buildSettingsView(),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsView() {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            height: 64,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Text(
                  context.tr('settings.title'),
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                // Save Button
                ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(context.tr('settings.saveSuccess')),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  icon: Icon(Icons.save),
                  label: Text(context.tr('settings.saveChanges')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24),

          // Settings Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Settings Categories
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left sidebar with categories
                      Container(
                        width: 200,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(12.0),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(5),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            _buildSettingsCategory(
                                context.tr('settings.appearance'),
                                Icons.palette_outlined,
                                _selectedCategory == 'Appearance'),
                            _buildSettingsCategory(
                                context.tr('settings.account'),
                                Icons.person_outline,
                                _selectedCategory == 'Account'),
                            _buildSettingsCategory(
                                context.tr('settings.notifications'),
                                Icons.notifications_outlined,
                                _selectedCategory == 'Notifications'),
                            _buildSettingsCategory(
                                context.tr('settings.privacy'),
                                Icons.lock_outline,
                                _selectedCategory == 'Privacy'),
                            _buildSettingsCategory(
                                context.tr('settings.advanced'),
                                Icons.tune_outlined,
                                _selectedCategory == 'Advanced'),
                          ],
                        ),
                      ),

                      SizedBox(width: 24),

                      // Right content area
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(12.0),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(5),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: EdgeInsets.all(24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _selectedCategory,
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 24),

                              // Show different content based on selected category
                              SingleChildScrollView(
                                  child: _buildCategoryContent()),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build content based on selected category
  Widget _buildCategoryContent() {
    switch (_selectedCategory) {
      case 'Account':
        return Expanded(
          child: SizedBox(
            width: double.infinity,
            child: ProfileScreen(),
          ),
        );
      case 'Appearance':
        return _buildAppearanceContent();
      case 'Notifications':
        return _buildNotificationsContent();
      case 'Privacy':
        return _buildPrivacyContent();
      case 'Advanced':
        return _buildAdvancedContent();
      default:
        return _buildAppearanceContent();
    }
  }

  // Build appearance settings content
  Widget _buildAppearanceContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Theme Settings
        Text(
          context.tr('settings.theme'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),

        Consumer<ThemeProvider>(
          builder: (context, themeProvider, _) {
            return Column(
              children: [
                // Theme Mode Selection
                Row(
                  children: [
                    _buildThemeOption(
                      context.tr('settings.light'),
                      Icons.light_mode_outlined,
                      themeProvider.themeMode == ThemeMode.light,
                      () => themeProvider.setThemeMode(ThemeMode.light),
                    ),
                    SizedBox(width: 16),
                    _buildThemeOption(
                      context.tr('settings.dark'),
                      Icons.dark_mode_outlined,
                      themeProvider.themeMode == ThemeMode.dark,
                      () => themeProvider.setThemeMode(ThemeMode.dark),
                    ),
                    SizedBox(width: 16),
                    _buildThemeOption(
                      context.tr('settings.system'),
                      Icons.settings_outlined,
                      themeProvider.themeMode == ThemeMode.system,
                      () => themeProvider.setThemeMode(ThemeMode.system),
                    ),
                  ],
                ),
              ],
            );
          },
        ),

        SizedBox(height: 32),

        // Language Settings
        Text(
          context.tr('settings.language'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),

        Consumer<LanguageProvider>(
          builder: (context, languageProvider, _) {
            return Row(
              children: [
                _buildLanguageOption(
                  'English',
                  const Locale('en', 'US'),
                  languageProvider.locale.languageCode == 'en',
                  () => languageProvider.setLocale(const Locale('en', 'US')),
                ),
                SizedBox(width: 16),
                _buildLanguageOption(
                  'తెలుగు', // Telugu
                  const Locale('te', 'IN'),
                  languageProvider.locale.languageCode == 'te',
                  () => languageProvider.setLocale(const Locale('te', 'IN')),
                ),
                SizedBox(width: 16),
                _buildLanguageOption(
                  'हिन्दी', // Hindi
                  const Locale('hi', 'IN'),
                  languageProvider.locale.languageCode == 'hi',
                  () => languageProvider.setLocale(const Locale('hi', 'IN')),
                ),
              ],
            );
          },
        ),

        SizedBox(height: 32),

        // UI Settings
        Text(
          context.tr('settings.uiSettings'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),

        // Font Size
        _buildSliderSetting(
          context.tr('settings.fontSize'),
          context.tr('settings.fontSizeDescription'),
          0.8,
          (value) {
            // Update font size
          },
        ),

        SizedBox(height: 16),

        // Density
        _buildSliderSetting(
          context.tr('settings.uiDensity'),
          context.tr('settings.uiDensityDescription'),
          0.5,
          (value) {
            // Update UI density
          },
        ),

        SizedBox(height: 32),

        // Chat Settings
        Text(
          context.tr('settings.chatSettings'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),

        // Toggle Settings
        _buildToggleSetting(
          context.tr('settings.showTimestamps'),
          context.tr('settings.showTimestampsDescription'),
          true,
          (value) {
            // Update timestamp setting
          },
        ),

        SizedBox(height: 12),

        _buildToggleSetting(
          context.tr('settings.showReadReceipts'),
          context.tr('settings.showReadReceiptsDescription'),
          false,
          (value) {
            // Update read receipts setting
          },
        ),

        SizedBox(height: 12),

        _buildToggleSetting(
          context.tr('settings.sendMessageOnEnter'),
          context.tr('settings.sendMessageOnEnterDescription'),
          true,
          (value) {
            // Update enter to send setting
          },
        ),

        SizedBox(height: 32),

        // About section
        Text(
          context.tr('settings.about'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),

        // Version info
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('settings.version'),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    context.tr('settings.versionDescription'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(30),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                _appVersion,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build notifications settings content
  Widget _buildNotificationsContent() {
    return Center(
      child: Text(context.tr('settings.comingSoon',
          args: {'feature': context.tr('settings.notifications')})),
    );
  }

  // Build privacy settings content
  Widget _buildPrivacyContent() {
    return Center(
      child: Text(context.tr('settings.comingSoon',
          args: {'feature': context.tr('settings.privacy')})),
    );
  }

  // Build advanced settings content
  Widget _buildAdvancedContent() {
    return Center(
      child: Text(context.tr('settings.comingSoon',
          args: {'feature': context.tr('settings.advanced')})),
    );
  }

  Widget _buildSettingsCategory(String title, IconData icon, bool isSelected) {
    return InkWell(
      onTap: () {
        // Switch to this category
        setState(() {
          _selectedCategory = title;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              width: 3,
            ),
          ),
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withAlpha(10)
              : Colors.transparent,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).iconTheme.color,
            ),
            SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeOption(
      String label, IconData icon, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withAlpha(30)
              : Theme.of(context).colorScheme.surface.withAlpha(240),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).iconTheme.color,
            ),
            SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliderSetting(String title, String description, double value,
      ValueChanged<double> onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4),
        Text(
          description,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Icon(Icons.text_decrease,
                size: 16, color: Theme.of(context).colorScheme.onSurface),
            Expanded(
              child: Slider(
                value: value,
                onChanged: onChanged,
              ),
            ),
            Icon(Icons.text_increase,
                size: 16, color: Theme.of(context).colorScheme.onSurface),
          ],
        ),
      ],
    );
  }

  Widget _buildToggleSetting(String title, String description, bool value,
      ValueChanged<bool> onChanged) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: Theme.of(context).colorScheme.primary,
        ),
      ],
    );
  }

  Widget _buildLanguageOption(
      String label, Locale locale, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withAlpha(30)
              : Theme.of(context).colorScheme.surface.withAlpha(240),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.language,
              size: 24,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).iconTheme.color,
            ),
            SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
