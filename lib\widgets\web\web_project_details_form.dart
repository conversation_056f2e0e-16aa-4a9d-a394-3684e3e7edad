import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../../models/project_details.dart';
import '../../theme/spacing.dart';

class WebProjectDetailsForm extends StatefulWidget {
  final Function(ProjectDetails) onSubmit;

  const WebProjectDetailsForm({
    super.key,
    required this.onSubmit,
  });

  @override
  State<WebProjectDetailsForm> createState() => _WebProjectDetailsFormState();
}

class _WebProjectDetailsFormState extends State<WebProjectDetailsForm> {
  final _formKey = GlobalKey<FormState>();
  final _projectNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  String? _selectedFilePath;
  String? _selectedFileName;

  @override
  void dispose() {
    _projectNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      setState(() {
        _selectedFilePath = result.files.single.path;
        _selectedFileName = result.files.single.name;
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final projectDetails = ProjectDetails(
        projectName: _projectNameController.text,
        description: _descriptionController.text.isEmpty
            ? null
            : _descriptionController.text,
        filePath: _selectedFilePath,
      );
      widget.onSubmit(projectDetails);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.all(24.0),
      padding: const EdgeInsets.all(32.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Expanded( 
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Center(
                      child: Text(
                        'Project Details',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(height: AppSpacing.xl),
                    
                    // Project Name Field
                    Text(
                      'What would you like to name your project *',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    SizedBox(height: AppSpacing.xs),
                    TextFormField(
                      controller: _projectNameController,
                      decoration: InputDecoration(
                        hintText: 'Enter project name',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Project name is required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: AppSpacing.lg),
                    
                    // File Upload
                    Text(
                      'Upload artefacts',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    SizedBox(height: AppSpacing.xs),
                    Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppSpacing.sm, vertical: AppSpacing.md),
                                decoration: BoxDecoration(
                                  border:
                                  Border.all(color: Theme.of(context).dividerColor),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  _selectedFileName ?? 'No file selected',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: AppSpacing.sm),
                        ElevatedButton(
                          onPressed: _pickFile,
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                                horizontal: AppSpacing.sm, vertical: AppSpacing.md),
                          ),
                          child: Text('Browse'),
                        ),
                      ],
                    ),
                    SizedBox(height: AppSpacing.md),
                    
                    // Description Field
                    Text(
                      'Description',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    SizedBox(height: AppSpacing.xs),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        hintText: 'Enter project description',
                        border: OutlineInputBorder(),
                        alignLabelWithHint: true,
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: AppSpacing.xl),

            // Submit Button
            Center(
              child: ElevatedButton(
                onPressed: _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.xl,
                    vertical: AppSpacing.md,
                  ),
                ),
                child: Text('Create Solution'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
