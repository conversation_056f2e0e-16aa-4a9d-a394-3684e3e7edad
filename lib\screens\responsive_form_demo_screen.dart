import 'package:flutter/material.dart';
import '../models/workflow.dart';
import '../widgets/responsive_form_layout.dart';
import '../utils/logger.dart';

/// A demo screen that showcases the responsive form layout
class ResponsiveFormDemoScreen extends StatefulWidget {
  const ResponsiveFormDemoScreen({super.key});

  @override
  State<ResponsiveFormDemoScreen> createState() => _ResponsiveFormDemoScreenState();
}

class _ResponsiveFormDemoScreenState extends State<ResponsiveFormDemoScreen> {
  // Sample form fields with different UI control types
  late List<InputField> _formFields;
  
  @override
  void initState() {
    super.initState();
    _initializeFormFields();
  }
  
  /// Initializes the sample form fields
  void _initializeFormFields() {
    _formFields = [
      // Text input field (medium control - flex: 2)
      InputField(
        inputId: 'name',
        attributeId: 'name',
        entityId: 'person',
        displayName: 'Full Name',
        dataType: 'String',
        sourceType: 'user',
        required: true,
        uiControl: 'oj-input-text',
        isVisible: true,
        contextualId: 'name',
        metadata: InputFieldMetadata(),
      ),
      
      // Email input field (medium control - flex: 2)
      InputField(
        inputId: 'email',
        attributeId: 'email',
        entityId: 'person',
        displayName: 'Email Address',
        dataType: 'String',
        sourceType: 'user',
        required: true,
        uiControl: 'oj-input-email',
        isVisible: true,
        contextualId: 'email',
        metadata: InputFieldMetadata(),
      ),
      
      // Phone number field (medium control - flex: 2)
      InputField(
        inputId: 'phone',
        attributeId: 'phone',
        entityId: 'person',
        displayName: 'Phone Number',
        dataType: 'String',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-input-mobile',
        isVisible: true,
        contextualId: 'phone',
        metadata: InputFieldMetadata(),
      ),
      
      // Date of birth field (medium control - flex: 2)
      InputField(
        inputId: 'dob',
        attributeId: 'dob',
        entityId: 'person',
        displayName: 'Date of Birth',
        dataType: 'Date',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-input-date',
        isVisible: true,
        contextualId: 'dob',
        metadata: InputFieldMetadata(),
      ),
      
      // Gender dropdown field (medium control - flex: 2)
      InputField(
        inputId: 'gender',
        attributeId: 'gender',
        entityId: 'person',
        displayName: 'Gender',
        dataType: 'String',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-select-one',
        isVisible: true,
        contextualId: 'gender',
        allowedValues: ['Male', 'Female', 'Non-binary', 'Prefer not to say'],
        metadata: InputFieldMetadata(),
      ),
      
      // Checkbox field (small control - flex: 1)
      InputField(
        inputId: 'newsletter',
        attributeId: 'newsletter',
        entityId: 'person',
        displayName: 'Subscribe to Newsletter',
        dataType: 'Boolean',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-checkbox',
        isVisible: true,
        contextualId: 'newsletter',
        metadata: InputFieldMetadata(),
      ),
      
      // Switch field (small control - flex: 1)
      InputField(
        inputId: 'notifications',
        attributeId: 'notifications',
        entityId: 'person',
        displayName: 'Enable Notifications',
        dataType: 'Boolean',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-switch',
        isVisible: true,
        contextualId: 'notifications',
        metadata: InputFieldMetadata(),
      ),
      
      // Text area field (large control - flex: 3)
      InputField(
        inputId: 'bio',
        attributeId: 'bio',
        entityId: 'person',
        displayName: 'Biography',
        dataType: 'String',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-text-area',
        isVisible: true,
        contextualId: 'bio',
        metadata: InputFieldMetadata(),
      ),
      
      // Rich text field (full width control - flex: 6)
      InputField(
        inputId: 'notes',
        attributeId: 'notes',
        entityId: 'person',
        displayName: 'Additional Notes',
        dataType: 'String',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-rich-text',
        isVisible: true,
        contextualId: 'notes',
        metadata: InputFieldMetadata(),
      ),
    ];
  }
  
  /// Handles form field value changes
  void _handleValueChanged(String sectionId, String attributeId, dynamic value, String inputId) {
    Logger.info('Value changed: sectionId=$sectionId, attributeId=$attributeId, value=$value, inputId=$inputId');
    // In a real app, you would update your data model here
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Responsive Form Demo'),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Responsive Form with Flex Mapping',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                'This form automatically arranges fields based on their UI control type. '
                'Resize the window to see how the layout adapts.',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
            const SizedBox(height: 24.0),
            
            // Responsive form layout
            ResponsiveFormLayout(
              fields: _formFields,
              sectionId: 'person',
              onValueChanged: _handleValueChanged,
              readOnly: false,
              gridColumns: 12,
              horizontalSpacing: 16.0,
              verticalSpacing: 24.0,
              padding: const EdgeInsets.all(16.0),
            ),
            
            const SizedBox(height: 32.0),
            
            // Submit button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: () {
                  // Handle form submission
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Form submitted')),
                  );
                },
                child: const Text('Submit'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
