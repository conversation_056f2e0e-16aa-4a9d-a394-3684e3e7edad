import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A widget that allows users to select a time period with start and end dates.
///
/// This widget provides various period selection options including:
/// - Custom date range selection
/// - Predefined periods (today, yesterday, this week, last week, etc.)
/// - Relative periods (last 7 days, last 30 days, etc.)
class PeriodWidget extends StatefulWidget {
  /// Initial start date
  final DateTime? initialStartDate;

  /// Initial end date
  final DateTime? initialEndDate;

  /// Minimum selectable date
  final DateTime? minDate;

  /// Maximum selectable date
  final DateTime? maxDate;

  /// Date format for display (e.g., 'MM/dd/yyyy', 'yyyy-MM-dd')
  final String dateFormat;

  /// Whether to show predefined period options
  final bool showPredefinedPeriods;

  /// Whether to show relative period options
  final bool showRelativePeriods;

  /// Whether to show custom date range selection
  final bool showCustomRange;

  /// Whether to show a calendar view for date selection
  final bool showCalendar;

  /// Whether to allow editing of the dates
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color of the calendar selection
  final Color selectionColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the period changes
  final Function(DateTime, DateTime)? onPeriodChanged;

  /// Creates a period widget.
  const PeriodWidget({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
    this.minDate,
    this.maxDate,
    this.dateFormat = 'MM/dd/yyyy',
    this.showPredefinedPeriods = true,
    this.showRelativePeriods = true,
    this.showCustomRange = true,
    this.showCalendar = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.selectionColor = Colors.blue,
    this.width,
    this.height,
    this.onPeriodChanged,
  });

  @override
  State<PeriodWidget> createState() => _PeriodWidgetState();
}

/// Enum for predefined period types
enum PredefinedPeriodType {
  /// Today
  today,

  /// Yesterday
  yesterday,

  /// This week
  thisWeek,

  /// Last week
  lastWeek,

  /// This month
  thisMonth,

  /// Last month
  lastMonth,

  /// This quarter
  thisQuarter,

  /// Last quarter
  lastQuarter,

  /// This year
  thisYear,

  /// Last year
  lastYear,

  /// Custom date range
  custom,

  /// Last 7 days
  last7Days,

  /// Last 30 days
  last30Days,

  /// Last 90 days
  last90Days,

  /// Last 365 days
  last365Days,
}

class _PeriodWidgetState extends State<PeriodWidget> {
  late DateTime _startDate;
  late DateTime _endDate;
  late PredefinedPeriodType _selectedPeriodType;
  bool _showCalendarView = false;
  DateTime? _calendarSelectedDate;
  bool _isSelectingStartDate = true;
  String? _errorMessage;

  late DateFormat _displayFormat;

  @override
  void initState() {
    super.initState();

    // Initialize date format
    try {
      _displayFormat = DateFormat(widget.dateFormat);
    } catch (e) {
      // If format is invalid, use default
      debugPrint('Invalid date format: ${widget.dateFormat}. Using default format.');
      _displayFormat = DateFormat('MM/dd/yyyy');
    }

    // Initialize with provided dates or defaults
    if (widget.initialStartDate != null && widget.initialEndDate != null) {
      _startDate = widget.initialStartDate!;
      _endDate = widget.initialEndDate!;
      _selectedPeriodType = PredefinedPeriodType.custom;
    } else {
      // Default to today
      final now = DateTime.now();
      _startDate = DateTime(now.year, now.month, now.day);
      _endDate = DateTime(now.year, now.month, now.day);
      _selectedPeriodType = PredefinedPeriodType.today;
    }

    _validateDateRange();
  }

  void _validateDateRange() {
    if (widget.minDate != null && _startDate.isBefore(widget.minDate!)) {
      setState(() {
        _errorMessage = 'Start date cannot be before ${_displayFormat.format(widget.minDate!)}';
      });
      return;
    }

    if (widget.maxDate != null && _endDate.isAfter(widget.maxDate!)) {
      setState(() {
        _errorMessage = 'End date cannot be after ${_displayFormat.format(widget.maxDate!)}';
      });
      return;
    }

    if (_endDate.isBefore(_startDate)) {
      setState(() {
        _errorMessage = 'End date cannot be before start date';
      });
      return;
    }

    setState(() {
      _errorMessage = null;
    });
  }

  void _setPeriod(PredefinedPeriodType periodType) {
    if (widget.isDisabled || widget.isReadOnly) return;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    DateTime newStartDate;
    DateTime newEndDate;

    switch (periodType) {
      case PredefinedPeriodType.today:
        newStartDate = today;
        newEndDate = today;
        break;

      case PredefinedPeriodType.yesterday:
        final yesterday = today.subtract(const Duration(days: 1));
        newStartDate = yesterday;
        newEndDate = yesterday;
        break;

      case PredefinedPeriodType.thisWeek:
        // Assuming week starts on Monday
        final weekday = today.weekday;
        newStartDate = today.subtract(Duration(days: weekday - 1));
        newEndDate = today;
        break;

      case PredefinedPeriodType.lastWeek:
        // Last week (Monday to Sunday)
        final weekday = today.weekday;
        final lastWeekEnd = today.subtract(Duration(days: weekday));
        final lastWeekStart = lastWeekEnd.subtract(const Duration(days: 6));
        newStartDate = lastWeekStart;
        newEndDate = lastWeekEnd;
        break;

      case PredefinedPeriodType.thisMonth:
        newStartDate = DateTime(today.year, today.month, 1);
        newEndDate = today;
        break;

      case PredefinedPeriodType.lastMonth:
        final lastMonth = today.month == 1
            ? DateTime(today.year - 1, 12, 1)
            : DateTime(today.year, today.month - 1, 1);
        final lastDayOfLastMonth = DateTime(today.year, today.month, 0);
        newStartDate = lastMonth;
        newEndDate = lastDayOfLastMonth;
        break;

      case PredefinedPeriodType.thisQuarter:
        final currentQuarter = ((today.month - 1) ~/ 3) + 1;
        final quarterStartMonth = (currentQuarter - 1) * 3 + 1;
        newStartDate = DateTime(today.year, quarterStartMonth, 1);
        newEndDate = today;
        break;

      case PredefinedPeriodType.lastQuarter:
        final currentQuarter = ((today.month - 1) ~/ 3) + 1;
        final lastQuarter = currentQuarter == 1 ? 4 : currentQuarter - 1;
        final lastQuarterYear = currentQuarter == 1 ? today.year - 1 : today.year;
        final lastQuarterStartMonth = (lastQuarter - 1) * 3 + 1;
        final lastQuarterEndMonth = lastQuarterStartMonth + 2;
        final lastQuarterEndYear = lastQuarter == 4 && currentQuarter == 1
            ? today.year - 1
            : today.year;
        newStartDate = DateTime(lastQuarterYear, lastQuarterStartMonth, 1);
        newEndDate = DateTime(lastQuarterEndYear, lastQuarterEndMonth + 1, 0);
        break;

      case PredefinedPeriodType.thisYear:
        newStartDate = DateTime(today.year, 1, 1);
        newEndDate = today;
        break;

      case PredefinedPeriodType.lastYear:
        newStartDate = DateTime(today.year - 1, 1, 1);
        newEndDate = DateTime(today.year - 1, 12, 31);
        break;

      case PredefinedPeriodType.last7Days:
        newStartDate = today.subtract(const Duration(days: 6));
        newEndDate = today;
        break;

      case PredefinedPeriodType.last30Days:
        newStartDate = today.subtract(const Duration(days: 29));
        newEndDate = today;
        break;

      case PredefinedPeriodType.last90Days:
        newStartDate = today.subtract(const Duration(days: 89));
        newEndDate = today;
        break;

      case PredefinedPeriodType.last365Days:
        newStartDate = today.subtract(const Duration(days: 364));
        newEndDate = today;
        break;

      case PredefinedPeriodType.custom:
        // Keep current dates for custom
        newStartDate = _startDate;
        newEndDate = _endDate;
        break;
    }

    setState(() {
      _startDate = newStartDate;
      _endDate = newEndDate;
      _selectedPeriodType = periodType;
      _showCalendarView = false;
    });

    _validateDateRange();
    _notifyPeriodChanged();
  }



  void _selectDate(DateTime date) {
    if (_isSelectingStartDate) {
      setState(() {
        _startDate = date;
        _isSelectingStartDate = false;
        _calendarSelectedDate = date;
      });
    } else {
      setState(() {
        _endDate = date;
        _showCalendarView = false;
        _selectedPeriodType = PredefinedPeriodType.custom;
      });

      _validateDateRange();
      _notifyPeriodChanged();
    }
  }

  void _notifyPeriodChanged() {
    if (widget.onPeriodChanged != null && _errorMessage == null) {
      widget.onPeriodChanged!(_startDate, _endDate);
    }
  }

  String _getPeriodTypeLabel(PredefinedPeriodType type) {
    switch (type) {
      case PredefinedPeriodType.today:
        return 'Today';
      case PredefinedPeriodType.yesterday:
        return 'Yesterday';
      case PredefinedPeriodType.thisWeek:
        return 'This Week';
      case PredefinedPeriodType.lastWeek:
        return 'Last Week';
      case PredefinedPeriodType.thisMonth:
        return 'This Month';
      case PredefinedPeriodType.lastMonth:
        return 'Last Month';
      case PredefinedPeriodType.thisQuarter:
        return 'This Quarter';
      case PredefinedPeriodType.lastQuarter:
        return 'Last Quarter';
      case PredefinedPeriodType.thisYear:
        return 'This Year';
      case PredefinedPeriodType.lastYear:
        return 'Last Year';
      case PredefinedPeriodType.custom:
        return 'Custom';
      case PredefinedPeriodType.last7Days:
        return 'Last 7 Days';
      case PredefinedPeriodType.last30Days:
        return 'Last 30 Days';
      case PredefinedPeriodType.last90Days:
        return 'Last 90 Days';
      case PredefinedPeriodType.last365Days:
        return 'Last 365 Days';
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Period Type Selection
          if (widget.showPredefinedPeriods || widget.showRelativePeriods) ...[
            Text(
              'Period',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // Predefined periods
                if (widget.showPredefinedPeriods) ...[
                  _buildPeriodChip(PredefinedPeriodType.today),
                  _buildPeriodChip(PredefinedPeriodType.yesterday),
                  _buildPeriodChip(PredefinedPeriodType.thisWeek),
                  _buildPeriodChip(PredefinedPeriodType.lastWeek),
                  _buildPeriodChip(PredefinedPeriodType.thisMonth),
                  _buildPeriodChip(PredefinedPeriodType.lastMonth),
                  _buildPeriodChip(PredefinedPeriodType.thisQuarter),
                  _buildPeriodChip(PredefinedPeriodType.lastQuarter),
                  _buildPeriodChip(PredefinedPeriodType.thisYear),
                  _buildPeriodChip(PredefinedPeriodType.lastYear),
                ],

                // Relative periods
                if (widget.showRelativePeriods) ...[
                  _buildPeriodChip(PredefinedPeriodType.last7Days),
                  _buildPeriodChip(PredefinedPeriodType.last30Days),
                  _buildPeriodChip(PredefinedPeriodType.last90Days),
                  _buildPeriodChip(PredefinedPeriodType.last365Days),
                ],

                // Custom period
                if (widget.showCustomRange) ...[
                  _buildPeriodChip(PredefinedPeriodType.custom),
                ],
              ],
            ),
            const SizedBox(height: 16),
          ],

          // Date Range Display
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Start Date',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: widget.isDisabled || widget.isReadOnly
                          ? null
                          : () {
                              setState(() {
                                _showCalendarView = true;
                                _isSelectingStartDate = true;
                                _calendarSelectedDate = _startDate;
                              });
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: widget.borderColor,
                            width: widget.borderWidth,
                          ),
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                          color: widget.isDisabled
                              ? Colors.grey.withAlpha(30)
                              : null,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _displayFormat.format(_startDate),
                              style: TextStyle(
                                color: effectiveTextColor,
                                fontSize: widget.fontSize,
                              ),
                            ),
                            Icon(
                              Icons.calendar_today,
                              color: effectiveTextColor,
                              size: widget.fontSize + 4,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'End Date',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: widget.isDisabled || widget.isReadOnly
                          ? null
                          : () {
                              setState(() {
                                _showCalendarView = true;
                                _isSelectingStartDate = false;
                                _calendarSelectedDate = _endDate;
                              });
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: widget.borderColor,
                            width: widget.borderWidth,
                          ),
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                          color: widget.isDisabled
                              ? Colors.grey.withAlpha(30)
                              : null,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _displayFormat.format(_endDate),
                              style: TextStyle(
                                color: effectiveTextColor,
                                fontSize: widget.fontSize,
                              ),
                            ),
                            Icon(
                              Icons.calendar_today,
                              color: effectiveTextColor,
                              size: widget.fontSize + 4,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Calendar View
          if (_showCalendarView && widget.showCalendar) ...[
            const SizedBox(height: 16),
            Container(
              height: 300,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: CalendarDatePicker(
                initialDate: _calendarSelectedDate ?? (_isSelectingStartDate ? _startDate : _endDate),
                firstDate: widget.minDate ?? DateTime(1900),
                lastDate: widget.maxDate ?? DateTime(2100),
                onDateChanged: _selectDate,
                selectableDayPredicate: (date) {
                  if (_isSelectingStartDate) {
                    // When selecting start date, can't select after end date
                    return !date.isAfter(_endDate);
                  } else {
                    // When selecting end date, can't select before start date
                    return !date.isBefore(_startDate);
                  }
                },
              ),
            ),
          ],

          // Error Message
          if (_errorMessage != null || widget.errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize - 2,
              ),
            ),
          ],

          // Helper Text
          if (widget.helperText != null && _errorMessage == null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],

          // Period Summary
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: widget.backgroundColor.withAlpha(179),
              borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              border: Border.all(
                color: widget.borderColor.withAlpha(128),
                width: widget.borderWidth / 2,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Period: ${_getPeriodTypeLabel(_selectedPeriodType)}',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'From: ${_displayFormat.format(_startDate)} To: ${_displayFormat.format(_endDate)}',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Duration: ${_endDate.difference(_startDate).inDays + 1} days',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodChip(PredefinedPeriodType periodType) {
    final bool isSelected = _selectedPeriodType == periodType;

    return ChoiceChip(
      label: Text(
        _getPeriodTypeLabel(periodType),
        style: TextStyle(
          color: isSelected ? Colors.white : widget.textColor,
          fontSize: widget.fontSize - 2,
        ),
      ),
      selected: isSelected,
      onSelected: widget.isDisabled || widget.isReadOnly
          ? null
          : (selected) {
              if (selected) {
                _setPeriod(periodType);
              }
            },
      backgroundColor: widget.backgroundColor,
      selectedColor: widget.selectionColor,
      disabledColor: Colors.grey.withAlpha(30),
    );
  }
}