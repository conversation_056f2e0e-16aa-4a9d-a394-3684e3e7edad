import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class OrganizationalDepartmentsPanel extends StatelessWidget {
  final Map<String, dynamic>? organizationalStructure;
  
  const OrganizationalDepartmentsPanel({
    super.key,
    this.organizationalStructure,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.0688,
     // padding: EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.xs),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.xs),
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FF),
              border: Border(
                bottom: BorderSide(color: Color(0xFF797676), width: 1),
              ),
            ),
            child: Row(
              children: [
               
               
                 Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Organisation Departments',
                         style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Department levels
          Expanded(
            child: ListView(
              children: _buildDepartmentLevels(),
            ),
          ),
        ],
      ),
    );
  }

List<Widget> _buildDepartmentLevels() {
  final List<Widget> levels = [];
  
  // Extract levels dynamically from the data
  final availableLevels = organizationalStructure?.keys.toList() ?? [];
  
  // Sort levels (M4, M3, M2, M1, etc.) - you might want custom sorting logic
  availableLevels.sort((a, b) {
    // Extract number from level (M4 -> 4, M3 -> 3, etc.)
    final aNum = int.tryParse(a.substring(1)) ?? 0;
    final bNum = int.tryParse(b.substring(1)) ?? 0;
    return bNum.compareTo(aNum); // Descending order (M4, M3, M2, M1)
  });

  for (int i = 0; i < availableLevels.length; i++) {
    final levelKey = availableLevels[i];
    final levelData = organizationalStructure![levelKey];
    if (levelData != null) {
      final levelName = levelData['level_name'] ?? levelKey;
      levels.add(
        _buildDepartmentLevel(
          level: levelKey,
          title: levelName,
          isSelected: false,
        ),
      );
      
     // Add spacing between levels (except after the last one)
     // if (i < availableLevels.length - 1) {
        levels.add(
          Container(
            height: 1,
            color: Color(0xff797676),
          ),
        );
      //}
    }
  }

  return levels;
}
 
 
 
  Widget _buildDepartmentLevel({
    required String level,
    required String title,
    required bool isSelected,
  }) {
    return Container(
      width: double.infinity,
      height: 114,
      padding: EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.sm),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade50 : Colors.transparent,
        border: isSelected 
          ? Border.all(color: Colors.blue.shade200, width: 1)
          : null,
      ),
      child: InkWell(
        onTap: () {
          // Handle level selection if needed
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              level,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.bold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            SizedBox(height: AppSpacing.xs),
            Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey.shade700,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }


}
