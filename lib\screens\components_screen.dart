import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/widgets/navigation_drawer.dart';
import '../ui_components/demo/component_showcase.dart';

/// A screen that showcases all UI components in the library for mobile devices.
class ComponentsScreen extends StatelessWidget {
  const ComponentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const AppNavigationDrawer(currentRoute: 'components'),
      appBar: AppBar(
        title: Text(context.tr('components.title')),
      ),
      body: const ComponentShowcase(
        requireAppBar: false,
      ),
    );
  }
}
