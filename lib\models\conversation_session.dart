// To parse this JSON data, do
//
//     final conversationSession = conversationSessionFromJson(jsonString);

import 'dart:convert';

ConversationSession conversationSessionFromJson(String str) =>
    ConversationSession.fromJson(json.decode(str));

String conversationSessionToJson(ConversationSession data) =>
    json.encode(data.toJson());

class ConversationSession {
  final String? sessionId;
  final String? message;

  ConversationSession({
    this.sessionId,
    this.message,
  });

  ConversationSession copyWith({
    String? sessionId,
    String? message,
  }) =>
      ConversationSession(
        sessionId: sessionId ?? this.sessionId,
        message: message ?? this.message,
      );

  factory ConversationSession.fromJson(Map<String, dynamic> json) =>
      ConversationSession(
        sessionId: json["session_id"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "session_id": sessionId,
        "message": message,
      };
}
