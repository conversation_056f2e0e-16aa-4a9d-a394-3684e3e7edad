import 'package:flutter/material.dart';

/// A widget that calculates and displays quantiles from a dataset.
///
/// This widget allows users to input data values and calculate various
/// quantiles (e.g., quartiles, deciles, percentiles) with visualization options.
class QuantileWidget extends StatefulWidget {
  /// Initial dataset as comma-separated string
  final String? initialDataset;

  /// Initial quantile values to calculate (e.g., [0.25, 0.5, 0.75] for quartiles)
  final List<double>? initialQuantiles;

  /// Whether to show the data distribution visualization
  final bool showDistribution;

  /// Whether to show the quantile markers on the distribution
  final bool showQuantileMarkers;

  /// Whether to show the detailed calculation steps
  final bool showCalculationSteps;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color for the data points in the visualization
  final Color dataPointColor;

  /// The color for the quantile markers
  final Color quantileMarkerColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the quantile results change
  final Function(List<double>, List<double>)? onQuantilesCalculated;

  /// Callback when the dataset changes
  final Function(List<double>)? onDatasetChanged;

  /// Creates a quantile widget.
  const QuantileWidget({
    super.key,
    this.initialDataset,
    this.initialQuantiles,
    this.showDistribution = true,
    this.showQuantileMarkers = true,
    this.showCalculationSteps = false,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.dataPointColor = Colors.blue,
    this.quantileMarkerColor = Colors.red,
    this.width,
    this.height,
    this.onQuantilesCalculated,
    this.onDatasetChanged,
  });

  @override
  State<QuantileWidget> createState() => _QuantileWidgetState();
}

class _QuantileWidgetState extends State<QuantileWidget> {
  final TextEditingController _datasetController = TextEditingController();
  final TextEditingController _quantilesController = TextEditingController();

  List<double> _dataset = [];
  List<double> _quantiles = [0.25, 0.5, 0.75]; // Default to quartiles
  List<double> _quantileResults = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Initialize with provided dataset
    if (widget.initialDataset != null) {
      _datasetController.text = widget.initialDataset!;
      _parseDataset();
    }

    // Initialize with provided quantiles
    if (widget.initialQuantiles != null && widget.initialQuantiles!.isNotEmpty) {
      _quantiles = List<double>.from(widget.initialQuantiles!);
      _quantiles.sort(); // Ensure quantiles are in ascending order
      _updateQuantilesDisplay();
    } else {
      _updateQuantilesDisplay();
    }

    // Calculate initial quantiles if dataset is available
    if (_dataset.isNotEmpty) {
      _calculateQuantiles();
    }
  }

  @override
  void dispose() {
    _datasetController.dispose();
    _quantilesController.dispose();
    super.dispose();
  }

  void _parseDataset() {
    try {
      final input = _datasetController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _dataset = [];
          _errorMessage = 'Please enter data values';
        });
        return;
      }

      // Split by commas, spaces, or semicolons
      final parts = input.split(RegExp(r'[,;\s]+'));
      final parsedData = <double>[];

      for (final part in parts) {
        if (part.trim().isNotEmpty) {
          try {
            parsedData.add(double.parse(part.trim()));
          } catch (e) {
            setState(() {
              _errorMessage = 'Invalid number format: $part';
            });
            return;
          }
        }
      }

      if (parsedData.isEmpty) {
        setState(() {
          _errorMessage = 'No valid data values found';
        });
        return;
      }

      setState(() {
        _dataset = parsedData;
        _errorMessage = null;
      });

      // Notify callback if provided
      if (widget.onDatasetChanged != null) {
        widget.onDatasetChanged!(_dataset);
      }

      _calculateQuantiles();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error parsing dataset: ${e.toString()}';
      });
    }
  }

  void _parseQuantiles() {
    try {
      final input = _quantilesController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _quantiles = [0.25, 0.5, 0.75]; // Default to quartiles
          _errorMessage = 'Using default quartiles (0.25, 0.5, 0.75)';
        });
        return;
      }

      // Split by commas, spaces, or semicolons
      final parts = input.split(RegExp(r'[,;\s]+'));
      final parsedQuantiles = <double>[];

      for (final part in parts) {
        if (part.trim().isNotEmpty) {
          try {
            final value = double.parse(part.trim());
            // Ensure quantile is between 0 and 1
            if (value < 0 || value > 1) {
              setState(() {
                _errorMessage = 'Quantile must be between 0 and 1: $part';
              });
              return;
            }
            parsedQuantiles.add(value);
          } catch (e) {
            // Check if it's a percentage format (e.g., 25%)
            if (part.trim().endsWith('%')) {
              try {
                final percentValue = double.parse(part.trim().substring(0, part.trim().length - 1));
                final quantileValue = percentValue / 100;
                if (quantileValue < 0 || quantileValue > 1) {
                  setState(() {
                    _errorMessage = 'Quantile must be between 0% and 100%: $part';
                  });
                  return;
                }
                parsedQuantiles.add(quantileValue);
              } catch (e) {
                setState(() {
                  _errorMessage = 'Invalid quantile format: $part';
                });
                return;
              }
            } else {
              setState(() {
                _errorMessage = 'Invalid quantile format: $part';
              });
              return;
            }
          }
        }
      }

      if (parsedQuantiles.isEmpty) {
        setState(() {
          _quantiles = [0.25, 0.5, 0.75]; // Default to quartiles
          _errorMessage = 'Using default quartiles (0.25, 0.5, 0.75)';
        });
        return;
      }

      // Sort quantiles in ascending order
      parsedQuantiles.sort();

      setState(() {
        _quantiles = parsedQuantiles;
        _errorMessage = null;
      });

      _calculateQuantiles();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error parsing quantiles: ${e.toString()}';
      });
    }
  }

  void _updateQuantilesDisplay() {
    // Format quantiles as comma-separated values
    final formattedQuantiles = _quantiles.map((q) => q.toString()).join(', ');
    _quantilesController.text = formattedQuantiles;
  }

  void _calculateQuantiles() {
    if (_dataset.isEmpty) {
      setState(() {
        _quantileResults = [];
        _errorMessage = 'No data available for quantile calculation';
      });
      return;
    }

    try {
      // Sort the dataset
      final sortedData = List<double>.from(_dataset)..sort();
      final results = <double>[];

      for (final q in _quantiles) {
        final result = _calculateQuantile(sortedData, q);
        results.add(result);
      }

      setState(() {
        _quantileResults = results;
        _errorMessage = null;
      });

      // Notify callback if provided
      if (widget.onQuantilesCalculated != null) {
        widget.onQuantilesCalculated!(_quantiles, _quantileResults);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error calculating quantiles: ${e.toString()}';
      });
    }
  }

  double _calculateQuantile(List<double> sortedData, double quantile) {
    if (sortedData.isEmpty) return 0;
    if (sortedData.length == 1) return sortedData[0];

    // Calculate the position
    final position = quantile * (sortedData.length - 1);
    final positionIndex = position.floor();
    final remainder = position - positionIndex;

    // If position is an integer, return the value at that position
    if (remainder == 0) {
      return sortedData[positionIndex];
    }

    // Otherwise, interpolate between the two adjacent values
    final lowerValue = sortedData[positionIndex];
    final upperValue = sortedData[positionIndex + 1];

    return lowerValue + remainder * (upperValue - lowerValue);
  }

  String _getQuantileDescription(double quantile) {
    if (quantile == 0.25) return 'First Quartile (Q1)';
    if (quantile == 0.5) return 'Median (Q2)';
    if (quantile == 0.75) return 'Third Quartile (Q3)';
    if (quantile == 0) return 'Minimum';
    if (quantile == 1) return 'Maximum';

    // Check if it's a decile
    for (int i = 1; i <= 9; i++) {
      final decile = i / 10;
      if ((quantile - decile).abs() < 0.001) {
        return 'Decile ${i}0%';
      }
    }

    // Check if it's a percentile
    for (int i = 1; i <= 99; i++) {
      final percentile = i / 100;
      if ((quantile - percentile).abs() < 0.0001) {
        return 'Percentile $i%';
      }
    }

    // Default format as percentage
    return 'Quantile ${(quantile * 100).toStringAsFixed(1)}%';
  }

  String _getCalculationSteps(List<double> sortedData, double quantile) {
    if (sortedData.isEmpty) return 'No data available';

    final buffer = StringBuffer();
    buffer.writeln('Calculating ${_getQuantileDescription(quantile)}:');
    buffer.writeln('1. Sort the data: ${sortedData.map((d) => d.toStringAsFixed(2)).join(', ')}');

    // Calculate the position
    final position = quantile * (sortedData.length - 1);
    final positionIndex = position.floor();
    final remainder = position - positionIndex;

    buffer.writeln('2. Calculate position: $quantile × (${sortedData.length} - 1) = $position');

    // If position is an integer, return the value at that position
    if (remainder == 0) {
      buffer.writeln('3. Position is an integer, so take the value at index $positionIndex: ${sortedData[positionIndex]}');
      return buffer.toString();
    }

    // Otherwise, interpolate between the two adjacent values
    final lowerValue = sortedData[positionIndex];
    final upperValue = sortedData[positionIndex + 1];

    buffer.writeln('3. Position is between indices $positionIndex and ${positionIndex + 1}');
    buffer.writeln('4. Lower value (at index $positionIndex): $lowerValue');
    buffer.writeln('5. Upper value (at index ${positionIndex + 1}): $upperValue');
    buffer.writeln('6. Fractional part: $remainder');
    buffer.writeln('7. Interpolate: $lowerValue + $remainder × ($upperValue - $lowerValue) = ${lowerValue + remainder * (upperValue - lowerValue)}');

    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Dataset Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Dataset:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _datasetController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter comma-separated values (e.g., 10, 20, 30, 40, 50)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parseDataset();
                },
                maxLines: 2,
                minLines: 1,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Quantiles Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Quantiles:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _quantilesController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter comma-separated values between 0 and 1 (e.g., 0.25, 0.5, 0.75)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parseQuantiles();
                },
              ),
            ],
          ),

          // Preset Quantile Buttons
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildPresetButton('Quartiles', [0.25, 0.5, 0.75]),
              _buildPresetButton('Deciles', [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]),
              _buildPresetButton('Median', [0.5]),
              _buildPresetButton('Min/Max', [0, 1]),
              _buildPresetButton('IQR', [0.25, 0.75]),
            ],
          ),

          // Error Message
          if (_errorMessage != null || widget.errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize - 2,
              ),
            ),
          ],

          // Results
          if (_quantileResults.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Results:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int i = 0; i < _quantiles.length; i++)
                    Text(
                      '${_getQuantileDescription(_quantiles[i])}: ${_quantileResults[i].toStringAsFixed(4)}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                ],
              ),
            ),
          ],

          // Data Distribution Visualization
          if (widget.showDistribution && _dataset.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Data Distribution:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 150,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: _buildDistributionChart(),
              ),
            ),
          ],

          // Calculation Steps
          if (widget.showCalculationSteps && _dataset.isNotEmpty && _quantiles.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Calculation Steps:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (final quantile in _quantiles)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Text(
                        _getCalculationSteps(List<double>.from(_dataset)..sort(), quantile),
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize - 1,
                          height: 1.4,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],

          // Helper Text
          if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPresetButton(String label, List<double> quantiles) {
    return ElevatedButton(
      onPressed: widget.isDisabled || widget.isReadOnly
          ? null
          : () {
              setState(() {
                _quantiles = List<double>.from(quantiles);
                _updateQuantilesDisplay();
                _calculateQuantiles();
              });
            },
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.dataPointColor,
        foregroundColor: Colors.white,
        textStyle: TextStyle(fontSize: widget.fontSize - 2),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
        ),
      ),
      child: Text(label),
    );
  }

  Widget _buildDistributionChart() {
    if (_dataset.isEmpty) {
      return const Center(child: Text('No data to display'));
    }

    // Sort the dataset
    final sortedData = List<double>.from(_dataset)..sort();

    // Calculate min and max for scaling
    final minValue = sortedData.first;
    final maxValue = sortedData.last;

    return CustomPaint(
      size: const Size(double.infinity, 150),
      painter: _DistributionPainter(
        dataset: sortedData,
        minValue: minValue,
        maxValue: maxValue,
        quantiles: _quantiles,
        quantileResults: _quantileResults,
        dataPointColor: widget.dataPointColor,
        quantileMarkerColor: widget.quantileMarkerColor,
        showQuantileMarkers: widget.showQuantileMarkers,
      ),
    );
  }
}

class _DistributionPainter extends CustomPainter {
  final List<double> dataset;
  final double minValue;
  final double maxValue;
  final List<double> quantiles;
  final List<double> quantileResults;
  final Color dataPointColor;
  final Color quantileMarkerColor;
  final bool showQuantileMarkers;

  _DistributionPainter({
    required this.dataset,
    required this.minValue,
    required this.maxValue,
    required this.quantiles,
    required this.quantileResults,
    required this.dataPointColor,
    required this.quantileMarkerColor,
    required this.showQuantileMarkers,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = dataPointColor
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    final markerPaint = Paint()
      ..color = quantileMarkerColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final markerFillPaint = Paint()
      ..color = Color.fromRGBO(
        quantileMarkerColor.red,
        quantileMarkerColor.green,
        quantileMarkerColor.blue,
        0.3,
      )
      ..style = PaintingStyle.fill;

    final textPaint = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    final range = maxValue - minValue;
    final effectiveRange = range > 0 ? range : 1.0;

    // Draw horizontal axis
    canvas.drawLine(
      Offset(0, size.height - 20),
      Offset(size.width, size.height - 20),
      paint..color = Colors.black54,
    );

    // Draw min and max labels
    textPaint.text = TextSpan(
      text: minValue.toStringAsFixed(1),
      style: const TextStyle(color: Colors.black54, fontSize: 10),
    );
    textPaint.layout();
    textPaint.paint(canvas, Offset(0, size.height - 18));

    textPaint.text = TextSpan(
      text: maxValue.toStringAsFixed(1),
      style: const TextStyle(color: Colors.black54, fontSize: 10),
    );
    textPaint.layout();
    textPaint.paint(canvas, Offset(size.width - textPaint.width, size.height - 18));

    // Draw data points
    for (final value in dataset) {
      final x = (value - minValue) / effectiveRange * size.width;
      canvas.drawCircle(
        Offset(x, size.height - 30),
        3,
        paint..color = dataPointColor,
      );
    }

    // Draw quantile markers
    if (showQuantileMarkers && quantiles.isNotEmpty && quantileResults.isNotEmpty) {
      for (int i = 0; i < quantiles.length && i < quantileResults.length; i++) {
        final value = quantileResults[i];
        final x = (value - minValue) / effectiveRange * size.width;

        // Draw vertical line
        canvas.drawLine(
          Offset(x, size.height - 40),
          Offset(x, 10),
          paint..color = quantileMarkerColor,
        );

        // Draw marker
        final markerPath = Path()
          ..moveTo(x, 10)
          ..lineTo(x - 5, 0)
          ..lineTo(x + 5, 0)
          ..close();

        canvas.drawPath(markerPath, markerFillPaint);
        canvas.drawPath(markerPath, markerPaint);

        // Draw quantile label
        textPaint.text = TextSpan(
          text: '${(quantiles[i] * 100).toStringAsFixed(0)}%',
          style: TextStyle(color: quantileMarkerColor, fontSize: 10, fontWeight: FontWeight.bold),
        );
        textPaint.layout();
        textPaint.paint(canvas, Offset(x - textPaint.width / 2, 15));

        // Draw value label
        textPaint.text = TextSpan(
          text: value.toStringAsFixed(1),
          style: TextStyle(color: quantileMarkerColor, fontSize: 10),
        );
        textPaint.layout();
        textPaint.paint(canvas, Offset(x - textPaint.width / 2, 30));
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
