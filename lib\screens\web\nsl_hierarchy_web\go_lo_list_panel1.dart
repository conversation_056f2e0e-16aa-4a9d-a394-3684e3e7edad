import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class GoLoListPanel1 extends StatefulWidget {
  final String panelId;
  final String metricType; // 'GOs' or 'LOs'
  final VoidCallback onClose;
  

  const GoLoListPanel1({
    super.key,
    required this.panelId,
    required this.metricType,
    required this.onClose,
  });

  @override
  State<GoLoListPanel1> createState() => _GoLoListPanelState();
}

class _GoLoListPanelState extends State<GoLoListPanel1> {
  // Static data for demonstration
  static const Map<String, List<Map<String, String>>> staticData = {
    'GOs': [
      {
        'title': '1. GO Title',
        'description': 'Application Development Services',
        'details': 'Comprehensive application development including frontend, backend, and mobile solutions.'
      },
      {
        'title': '2. GO Title',
        'description': 'QA & Testing Services',
        'details': 'Quality assurance, automated testing, performance testing, and security testing services.'
      },
      {
        'title': '3. GO Title',
        'description': 'Infrastructure Services',
        'details': 'Cloud infrastructure, DevOps, monitoring, and maintenance services.'
      },
    ],
    'LOs': [
      {
        'title': '1. LO Title',
        'description': 'Learning Objective 1',
        'details': 'Detailed learning outcomes and competency development for objective 1.'
      },
      {
        'title': '2. LO Title',
        'description': 'Learning Objective 2',
        'details': 'Detailed learning outcomes and competency development for objective 2.'
      },
      {
        'title': '3. LO Title',
        'description': 'Learning Objective 3',
        'details': 'Detailed learning outcomes and competency development for objective 3.'
      },
    ],
  };

  List<bool> _expandedStates = [false, false, false];

  @override
  Widget build(BuildContext context) {
    final data = staticData[widget.metricType] ?? [];
    final panelTitle = widget.metricType == 'GOs' ? 'GO List' : 'LO List';

    return Container(
      width: MediaQuery.of(context).size.width * 0.16,
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Color(0xFFD0D0D0),),
        ),
       
      ),
      child: Column(
        children: [
          _buildHeader(panelTitle),
          Expanded(
            child: _buildContent(data),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(String title) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal:AppSpacing.sm, vertical: AppSpacing.size10),
      decoration: BoxDecoration(
        color: Color(0XFFCBDDFF),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: widget.onClose,
            child: Container(
             // padding: EdgeInsets.all(4),
              child: Icon(
                Icons.close,
                size: 24,
                color: Colors.black
              ),
            ),
          ),
        ],
      ),
    );
  }

 Widget _buildContent(List<Map<String, String>> data) {
  return SingleChildScrollView(
    child: Column(
      children: data.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        return Column(
          children: [
            _buildExpandableItem(item, index),
            // Add divider after each item except the last one
            if (index != data.length )
              Divider(
                height: 1,
               
                color: Color(0xFFB4B4B4),
              ),],);
          
        }).toList(),
      ),
    );
  }

  Widget _buildExpandableItem(Map<String, String> item, int index) {
    final isExpanded = index < _expandedStates.length ? _expandedStates[index] : false;

    return Container(
   //   margin: EdgeInsets.only(left:AppSpacing.md,top: AppSpacing.xs, bottom:AppSpacing.xs ,right:AppSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.white,
   
      //  borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                if (index < _expandedStates.length) {
                  _expandedStates[index] = !_expandedStates[index];
                }
              });
            },
            child: Container(
              margin: EdgeInsets.only(left:AppSpacing.md,top: AppSpacing.xs, bottom:AppSpacing.xs ,right:AppSpacing.sm),
             // padding: EdgeInsets.all(AppSpacing.md),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      item['title'] ?? '',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: isExpanded?FontManager.bold:  FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color:isExpanded ? Color(0xFF0058FF):  Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          
          if (isExpanded)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Expanded content
        if (isExpanded)
          Container(
           // margin: EdgeInsets.only(left:AppSpacing.lg,top: AppSpacing.xs, bottom:AppSpacing.xs ,right:AppSpacing.sm),
            // padding: EdgeInsets.only(
            //   left: AppSpacing.md,
            //   right: AppSpacing.md,
            //   bottom: AppSpacing.md,
            // ),
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FF),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(4),
              ),
            ),
            child: Column(
              children: [
                // Add your list items with checkmarks here
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("1. LO Title", true),
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("2. LO Title", true),
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("3. LO Title", true),
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("4. LO Title", true),
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("5. LO Title", true),
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("6. LO Title", true),
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("7. LO Title", true),
                 Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                _buildSubItem("8. LO Title", true),
              
                
              ],
            ),),
        ],
      ),
   ] ),);
  }
  
 // Helper widget for sub-items with checkmarks
Widget _buildSubItem(String title, bool isChecked) {
  return Padding(
    padding: EdgeInsets.symmetric(vertical: AppSpacing.xs,horizontal: AppSpacing.sm),
    child: Row(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(left:AppSpacing.md),
            child: Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ),
        if (isChecked)
          Icon(
           Icons.keyboard_arrow_down,
            color: Colors.black,
            size: 24,
          ),
      ],
    ),
  );
}
}

class GoLoPanel {
  final String id;
  final String metricType;

  GoLoPanel({
    required this.id,
    required this.metricType,
  });
}
