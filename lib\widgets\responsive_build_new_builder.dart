import 'package:flutter/material.dart';
import 'package:nsl/screens/web/web_build_screen_new.dart';
import '../screens/build_screen_new.dart';

class ResponsiveBuildNewBuilder extends StatelessWidget {
  const ResponsiveBuildNewBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use web layout for larger screens
        if (constraints.maxWidth >= 860) {
          return const WebBuildScreenNew();
        }
        // Use mobile layout for smaller screens
        return const BuildScreenNew();
      },
    );
  }
}
